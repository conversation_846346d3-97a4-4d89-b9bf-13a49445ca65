module.exports = bootstrap
const join = require('path').join
const api = require('cf-api')
const componentLoader = require('component-loader')
const glob = require('glob')

function bootstrap(serviceLocator, cb) {
  serviceLocator.logger.info('Starting Bootsrap')
  const componentGlobs = [
    join(__dirname, '/../components/service/**/init.js'),
    join(__dirname, '/../components/api/**/init.js')
  ]

  serviceLocator.logger.info('Grabbing Component Paths')
  const componentPaths = [].concat.apply(
    [],
    componentGlobs.map(function (path) {
      return glob.sync(path)
    })
  )
  serviceLocator.logger.info('Mapping Components: ' + componentPaths.length)

  const components = componentPaths.map(function (p) {
    serviceLocator.logger.info(`Requiring: ${p}`)
    return require(p)
  })

  const apiOptions = {
    logger: serviceLocator.logger,
    allowedDomains: serviceLocator.config.apiAllowedDomains,
    contentTypes: [
      'application/x-www-form-urlencoded',
      'application/json',
      'text/csv'
    ],
    maxBodySize: '0.25mb'
  }

  serviceLocator.logger.info('Initializing API')
  const server = api(apiOptions).initialize()

  serviceLocator.server = server
  serviceLocator.router = server

  serviceLocator.server.get('/debug-sentry', function mainHandler(req, res) {
    throw new Error('My first API Sentry error!')
  })

  serviceLocator.logger.info('Loading Components')
  componentLoader(
    components,
    function (loadComponentFn) {
      return loadComponentFn.bind(null, serviceLocator)
    },
    cb
  )
}
