const crudServiceApiBuilder = require('cf-crud-service-api-builder')
const searchEndpointBuilder = require('../../../../api/lib/search-endpoint-builder')
const authentication = require('cf-auth-middleware')
const authorisation = require('../../../../api/lib/middleware/authorisation')
const authUserResolver = require('../../../../api/lib/middleware/authenticated-user-resolver')
const accountFilter = require('../../account/lib/middleware/account-filter')
const buildSubEntityRoutes = require('../lib/build-sub-entity-routes')
const createLayoutRoutes = require('../lib/create-layout-routes')
const createEventCacheBuster = require('../lib/event-cache-buster')

module.exports = (serviceLocator) => {
  const authenticationMiddleware = authentication(
    serviceLocator.authenticationProvider,
    { defaultTtl: process.env.API_AUTH_TIMEOUT || 60000 }
  )
  const authUserLookupMiddleware = authUserResolver(serviceLocator)
  const authorisationMiddleware = authorisation(
    serviceLocator,
    '/events',
    'event'
  )
  const accountFilterMiddleware = accountFilter(serviceLocator)
  const router = serviceLocator.router

  router.get(
    `/events/layouts`,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      accountFilterMiddleware
    ],
    (req, res, next) => {
      serviceLocator.eventService.findLayouts(null, (err, layouts) => {
        if (err) return next(err)
        res.status(200).send(layouts)
      })
    }
  )

  router.post(
    '/events/:id/layouts/import',
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      accountFilterMiddleware
    ],
    (req, res, next) => {
      serviceLocator.eventService.importLayouts(
        req.params.id,
        req.body,
        (err, layouts) => {
          if (err) return next(err)
          res.status(200).send(layouts)
        }
      )
    }
  )

  router.post(
    '/events/copy',
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      accountFilterMiddleware
    ],
    (req, res, next) => {
      serviceLocator.eventService.copyEvent(
        req.body.sourceEventId,
        req.body.targetEventUmbrellaId,
        (err, event) => {
          if (err) return next(err)
          res.status(200).send(event)
        }
      )
    }
  )

  searchEndpointBuilder(
    serviceLocator.eventService,
    '/events',
    router,
    serviceLocator.logger,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      authorisationMiddleware,
      accountFilterMiddleware
    ]
  )

  crudServiceApiBuilder(
    serviceLocator.eventService,
    '/events',
    router,
    serviceLocator.logger,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      authorisationMiddleware,
      accountFilterMiddleware
    ],
    ['post', 'put', 'patch', 'delete']
  )

  buildSubEntityRoutes(
    router,
    serviceLocator.eventService,
    serviceLocator.logger,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      accountFilterMiddleware
    ],
    'agenda',
    'agendaItem'
  )

  buildSubEntityRoutes(
    router,
    serviceLocator.eventService,
    serviceLocator.logger,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      accountFilterMiddleware
    ],
    'speakers',
    'speaker'
  )

  buildSubEntityRoutes(
    router,
    serviceLocator.eventService,
    serviceLocator.logger,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      accountFilterMiddleware
    ],
    'sponsors',
    'sponsor'
  )

  const layoutEmitter = createLayoutRoutes(
    router,
    '/events',
    serviceLocator.eventService,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      accountFilterMiddleware
    ]
  )

  const bustEventLayoutCache = createEventCacheBuster(serviceLocator)
  layoutEmitter.on('partialUpdate', bustEventLayoutCache)

  router.patch(
    '/events/:id/navigation',
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      accountFilterMiddleware
    ],
    (req, res, next) => {
      serviceLocator.eventService.updateNavigation(
        req.params.id,
        req.body,
        (err, navigation) => {
          if (err) return next(err)
          res.status(200).send(navigation)
        }
      )
    }
  )
}
