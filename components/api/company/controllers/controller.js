const crudServiceApiBuilder = require('cf-crud-service-api-builder')
const searchEndpointBuilder = require('../../../../api/lib/search-endpoint-builder')
const authentication = require('cf-auth-middleware')
const authorisation = require('../../../../api/lib/middleware/authorisation')
const authUserResolver = require('../../../../api/lib/middleware/authenticated-user-resolver')
const accountFilter = require('../../account/lib/middleware/account-filter')
const createApolloDataRetriever = require('./lib/apollo-data-retriever')
const retrieveAsset = require('./lib/asset-retriever')
const retrieveDescription = require('./lib/description-retriever')
const createCompanyCacheBuster = require('../lib/company-cache-buster')

module.exports = (serviceLocator) => {
  const authenticationMiddleware = authentication(
    serviceLocator.authenticationProvider,
    { defaultTtl: process.env.API_AUTH_TIMEOUT || 60000 }
  )
  const authUserLookupMiddleware = authUserResolver(serviceLocator)
  const authorisationMiddleware = authorisation(
    serviceLocator,
    '/companies',
    'company'
  )
  const accountFilterMiddleware = accountFilter(serviceLocator)
  const router = serviceLocator.router

  searchEndpointBuilder(
    serviceLocator.companyService,
    '/companies',
    router,
    serviceLocator.logger,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      authorisationMiddleware,
      accountFilterMiddleware
    ]
  )

  const api = crudServiceApiBuilder(
    serviceLocator.companyService,
    '/companies',
    router,
    serviceLocator.logger,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      authorisationMiddleware,
      accountFilterMiddleware
    ],
    ['post', 'put', 'patch', 'delete']
  )

  const bustCompanyCache = createCompanyCacheBuster(serviceLocator)
  api.on('update', bustCompanyCache)
  api.on('partialUpdate', bustCompanyCache)

  router.post(
    '/companies/apollo',
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      accountFilterMiddleware
    ],
    async (req, res) => {
      if (!req.body.accountId) {
        const error = 'Account is required'
        serviceLocator.logger.error(error)
        return res.status(400).send({ error })
      }
      const retrieveApolloData = createApolloDataRetriever(serviceLocator)
      const { error, statusCode, data, info } = await retrieveApolloData(
        req,
        res
      )
      if (!data) return res.status(statusCode).send({ error, info })

      let assetToReturn = null

      const handleAssetRetrieval = async () => {
        try {
          if (!data.logo_url) {
            const warning =
              'No logo generated as logo_url not returned from Apolllo'
            serviceLocator.logger.error(warning)
            if (info.warnings) info.warnings.push(warning)
          } else {
            assetToReturn = await retrieveAsset(serviceLocator, {
              req,
              data,
              info
            })
            serviceLocator.logger.info('assetToReturn', assetToReturn)
          }
        } catch (error) {
          serviceLocator.logger.error('Error retrieving company asset:', error)
          if (info.warnings)
            info.warnings.push(
              `Error retrieving company logo: ${error.message}`
            )
        }
      }

      const handleDescriptionRetrieval = async () => {
        try {
          if (
            req.body.options &&
            req.body.options?.projection &&
            req.body.options?.projection?.ai_description
          ) {
            const { description, meta } = await retrieveDescription(
              serviceLocator,
              {
                data: info.originalOrganization,
                info
              }
            )
            data.ai_description = description
            data.__ai_meta = meta
          }
        } catch (error) {
          serviceLocator.logger.error(
            'Error retrieving company description:',
            error
          )
          if (info.warnings)
            info.warnings.push(
              `Error retrieving company description: ${error.message}`
            )
        }
      }

      await Promise.all([handleAssetRetrieval(), handleDescriptionRetrieval()])

      return res
        .status(statusCode)
        .send({ error, data, info, asset: assetToReturn })
    }
  )
}
