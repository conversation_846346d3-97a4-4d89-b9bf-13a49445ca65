const OpenAI = require('openai')

JSON.extractObject = (jsonString) => {
  try {
    // Remove any leading/trailing whitespace
    let cleaned = jsonString.trim()

    // If the string contains code block markers, extract content between them
    const codeBlockMatch = cleaned.match(/```(?:json)?([\s\S]*?)```/)
    if (codeBlockMatch) {
      cleaned = codeBlockMatch[1].trim()
    }

    // Parse the JSON string into an object
    return JSON.parse(cleaned)
  } catch (error) {
    console.error('Error parsing JSON:', error)
    return null
  }
}

// safely handles circular references
JSON.safeStringify = (obj, indent = 2) => {
  let cache = []
  const retVal = JSON.stringify(
    obj,
    (key, value) =>
      typeof value === 'object' && value !== null
        ? cache.includes(value)
          ? undefined // Duplicate reference found, discard key
          : cache.push(value) && value // Store value in our collection
        : value,
    indent
  )
  cache = null
  return retVal
}

const getGPTMessage = async (serviceLocator, client, instructions) => {
  let response = ''
  let error = null
  const thread = await client.beta.threads.create()
  await client.beta.threads.messages.create(thread.id, {
    role: 'user',
    content: instructions
  })
  const run = await client.beta.threads.runs.createAndPoll(thread.id, {
    assistant_id: 'asst_voNhbIFALYoc3yMBLG3jMagm'
  })
  if (run.status === 'completed') {
    const messages = await client.beta.threads.messages.list(run.thread_id)
    for (const message of messages.data.reverse()) {
      if (message.role === 'assistant')
        response += message.content[0].text.value
      serviceLocator.logger.info(message.content[0].text.value)
    }
    return [error, response]
  } else {
    serviceLocator.logger.error('OpenAI request status: ', run)
    error = new Error(
      'OpenAI request failed: ' +
        run.last_error?.code +
        ' | ' +
        run.last_error?.message
    )
    return [error, response]
  }
}

const retrieveDescription = async (serviceLocator, context) => {
  serviceLocator.logger.info('Generating AI description')
  const { data, info } = context
  const openai = new OpenAI({ apiKey: serviceLocator.secrets.openai.apiKey })

  const [htmlErr, html] = await getGPTMessage(
    serviceLocator,
    openai,
    `Using the additional JSON data below, generate a HTML document for ${data.primary_domain}: ` +
      JSON.stringify({
        annual_revenue: data.annual_revenue,
        foundedYear: data.founded_year,
        estimatedNumEmployees: data.estimated_num_employees
      })
  )
  if (htmlErr) {
    if (info.warnings) info.warnings.push(htmlErr.message)
  }
  serviceLocator.logger.info('html: ', html)

  let [metaErr, meta] = await getGPTMessage(
    serviceLocator,
    openai,
    `can you create me a meta object with the following fields based on this html (the content within the list). Do not return any text such as "Certaintly" along with the response, only send the json object. The first character of your response should be a { symbol, and the last should be a }` +
      `
    {
      location, employeeCount, ceo, revenue, keywordsAndServices (should be an array of strings found within one of the list items)
    }` +
      html
  )
  if (metaErr) {
    if (info.warnings) info.warnings.push(metaErr.message)
  }

  if (meta) {
    info.meta = meta
    serviceLocator.logger.info('meta (non-extracted): ', meta)
    meta = JSON.extractObject(meta)
  }

  const firstPIndex = html.indexOf('<p>')
  const lastPIndex = html.lastIndexOf('</p>') + 4
  let description = ''
  if (firstPIndex !== -1 && lastPIndex !== -1 && lastPIndex > firstPIndex) {
    description = html.substring(firstPIndex, lastPIndex)
  } else {
    description = html
  }

  serviceLocator.logger.info(
    'returning (meta=): ',
    meta,
    '(description=):',
    description
  )

  return { meta, description }
}

module.exports = retrieveDescription
