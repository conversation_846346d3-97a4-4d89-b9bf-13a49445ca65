const { promisify } = require('util')
const path = require('path')
const createDarkroomHelper = require('../../../article/lib/darkroom-helper')
const createImageUploader = require('../../../article/lib/image-uploader')
const assetSchema = require('../../../../service/asset/schema')()

const retrieveAsset = async (serviceLocator, context) => {
  let assetToReturn = null
  const { req, data, info } = context
  const fileName = path.basename(data.photo_url)
  const darkroomHelper = createDarkroomHelper(serviceLocator, 'executive')
  const uploadImage = createImageUploader(serviceLocator)

  serviceLocator.logger.info(
    'Retrieving executive photo asset from:',
    data.photo_url
  )

  const uploadData = await uploadImage([data.photo_url])

  if (!uploadData.id) {
    serviceLocator.logger.error('No upload data returned for executive photo')
    if (info.warnings)
      info.warnings.push('No upload data returned for executive photo')
    return null
  }

  try {
    const sizes = uploadData.exists
      ? { height: uploadData.height, width: uploadData.width }
      : await promisify(darkroomHelper.getImageSize)({
          binaryUri: uploadData.id
        })

    const assetTagNames = [req.body.__name || 'Executive', 'Headshot']
    const assetTags = assetTagNames.map(async (tag) => {
      let newTag
      const existingTagResults = await promisify(
        serviceLocator.tagService.find
      )({
        tag,
        type: 'Image',
        account: req.body.accountId
      })

      serviceLocator.logger.info(
        'existing tagData for executive:',
        existingTagResults
      )

      if (!existingTagResults.length) {
        serviceLocator.logger.info(
          `No ${tag} tag found, creating new ${tag} tag for executive`
        )
        newTag = await promisify(serviceLocator.tagService.create)({
          tag,
          type: 'Image',
          account: req.body.accountId,
          meta: []
        })
        serviceLocator.logger.info('Executive tag created:', newTag)
      }
      return existingTagResults[0] || newTag
    })

    const tags = await Promise.all(assetTags)
    const asset = assetSchema.makeDefault({
      name: fileName,
      type: 'image',
      account: req.body.accountId,
      width: sizes.width,
      height: sizes.height,
      originalUrl: data.photo_url,
      binaryUri: uploadData.id,
      caption: `${req.body.__name || 'Executive'} Headshot`,
      tags
    })

    if (!uploadData.exists) {
      assetToReturn = await promisify(serviceLocator.assetService.create)(asset)
      serviceLocator.logger.info(
        'Created new executive asset:',
        assetToReturn._id
      )
    } else {
      assetToReturn = await promisify(serviceLocator.assetService.findOne)({
        binaryUri: uploadData.id
      })
      assetToReturn.caption = asset.caption
      assetToReturn.tags = asset.tags
      serviceLocator.logger.info(
        'Found existing executive asset:',
        assetToReturn._id
      )
    }

    return assetToReturn
  } catch (error) {
    serviceLocator.logger.error('Error retrieving executive asset:', error)
    if (info.warnings)
      info.warnings.push(`Error retrieving executive photo: ${error.message}`)
    return null
  }
}

module.exports = retrieveAsset
