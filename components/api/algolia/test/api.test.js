const serviceLocator = require('../../../../api/test/test-service-locator')()
const request = require('supertest')
const bootstrap = require('../../../../api/test/bootstrap')

describe('Algolia API Tests', function () {
  let app

  before(function (done) {
    bootstrap(serviceLocator, (err) => {
      if (err) return done(err)
      app = serviceLocator.app
      done()
    })
  })

  after(function (done) {
    if (serviceLocator.serviceDatabase) {
      serviceLocator.serviceDatabase.dropDatabase(() => {
        serviceLocator.serviceDatabaseClient.close(done)
      })
    } else {
      done()
    }
  })

  describe('GET /algolia/top-searches', function () {
    it('should return 200 with default parameters', function (done) {
      request(app)
        .get('/algolia/top-searches')
        .expect(200)
        .end((err, res) => {
          if (err) return done(err)
          // Should return either data or error message about no client
          if (res.body.error) {
            // Expected when no Algolia client is configured
            done()
          } else {
            // Should have searches array if client is configured
            done()
          }
        })
    })

    it('should accept query parameters', function (done) {
      request(app)
        .get('/algolia/top-searches')
        .query({
          index: 'executive',
          limit: 50,
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        })
        .expect(200)
        .end(done)
    })

    it('should reject invalid date format', function (done) {
      request(app)
        .get('/algolia/top-searches')
        .query({
          startDate: 'invalid-date'
        })
        .expect(400)
        .end((err, res) => {
          if (err) return done(err)
          if (res.body.error && res.body.error.includes('Invalid startDate')) {
            done()
          } else {
            done(new Error('Expected invalid date error'))
          }
        })
    })

    it('should limit to maximum 1000', function (done) {
      request(app)
        .get('/algolia/top-searches')
        .query({
          limit: 2000
        })
        .expect(200)
        .end(done)
    })
  })

  describe('GET /algolia/top-hits', function () {
    it('should return 200 with default parameters', function (done) {
      request(app).get('/algolia/top-hits').expect(200).end(done)
    })

    it('should accept query parameters', function (done) {
      request(app)
        .get('/algolia/top-hits')
        .query({
          index: 'article',
          limit: 25,
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        })
        .expect(200)
        .end(done)
    })
  })

  describe('GET /algolia/searches-no-results', function () {
    it('should return 200 with default parameters', function (done) {
      request(app).get('/algolia/searches-no-results').expect(200).end(done)
    })

    it('should accept query parameters', function (done) {
      request(app)
        .get('/algolia/searches-no-results')
        .query({
          index: 'company',
          limit: 75,
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        })
        .expect(200)
        .end(done)
    })
  })
})
