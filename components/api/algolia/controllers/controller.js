const { algoliasearch } = require('algoliasearch')
const { promisify } = require('util')

module.exports = (serviceLocator) => {
  let analyticsClient
  try {
    if (serviceLocator?.secrets?.algolia?.analyticsApiKey) {
      analyticsClient = algoliasearch(
        serviceLocator.secrets.algolia.applicationId,
        serviceLocator.secrets.algolia.analyticsApiKey
      ).initAnalytics({ region: 'de' })
    } else {
      serviceLocator.logger.info(
        'No Algolia search API key provided. Either something went wrong or this is a test'
      )
    }
  } catch (error) {
    serviceLocator.logger.error('Error initializing Algolia client:', error)
  }

  serviceLocator.router.get('/algolia/top-searches', async (req, res) => {
    if (!analyticsClient)
      return res
        .status(200)
        .json({ data: [], error: 'No Algolia or Analytics client' })

    // Parse query parameters
    const index = req.query.index || 'company'
    const limit = Math.min(parseInt(req.query.limit) || 100, 1000)

    // Parse and validate dates
    let startDate, endDate
    if (req.query.startDate) {
      startDate = new Date(req.query.startDate)
      if (isNaN(startDate.getTime())) {
        return res.status(400).json({ error: 'Invalid startDate format' })
      }
      startDate = startDate.toISOString().split('T')[0] // Convert to YYYY-MM-DD
    }

    if (req.query.endDate) {
      endDate = new Date(req.query.endDate)
      if (isNaN(endDate.getTime())) {
        return res.status(400).json({ error: 'Invalid endDate format' })
      }
      endDate = endDate.toISOString().split('T')[0] // Convert to YYYY-MM-DD
    }

    const params = { index, limit }
    if (startDate) params.startDate = startDate
    if (endDate) params.endDate = endDate

    let response, statusCode

    try {
      statusCode = 200
      const data = await analyticsClient.getTopSearches(params)
      response = { ...data }
    } catch (error) {
      serviceLocator.logger.error('Error fetching top searches:', error)
      statusCode = 500
      response = { error: 'Error fetching top searches' }
    }

    return res.status(statusCode).json(response)
  })

  /** EXAMPLE getTopSearches DATA
    {
        "searches": [
          {
            "search": "separator",
            "count": 504,
            "nbHits": 20
          }
        ]
      }
   */

  serviceLocator.router.get('/algolia/top-hits', async (req, res) => {
    if (!analyticsClient)
      return res
        .status(200)
        .json({ data: [], error: 'No Algolia or Analytics client' })

    // Parse query parameters
    const index = req.query.index || 'company'
    const limit = Math.min(parseInt(req.query.limit) || 100, 1000)

    // Parse and validate dates
    let startDate, endDate
    if (req.query.startDate) {
      startDate = new Date(req.query.startDate)
      if (isNaN(startDate.getTime())) {
        return res.status(400).json({ error: 'Invalid startDate format' })
      }
      startDate = startDate.toISOString().split('T')[0] // Convert to YYYY-MM-DD
    }

    if (req.query.endDate) {
      endDate = new Date(req.query.endDate)
      if (isNaN(endDate.getTime())) {
        return res.status(400).json({ error: 'Invalid endDate format' })
      }
      endDate = endDate.toISOString().split('T')[0] // Convert to YYYY-MM-DD
    }

    const params = { index, limit }
    if (startDate) params.startDate = startDate
    if (endDate) params.endDate = endDate

    let response, statusCode

    try {
      statusCode = 200
      const data = await analyticsClient.getTopHits(params)
      response = { ...data }
    } catch (error) {
      serviceLocator.logger.error('Error fetching top hits:', error)
      statusCode = 500
      response = { error: 'Error fetching top hits' }
    }

    return res.status(statusCode).json(response)
  })

  /** EXAMPLE getTopHits DATA
    {
      "hits": [
        {
          "hit": "method-export-rules-php",
          "count": 2
        }
      ]
    }
   */

  serviceLocator.router.get(
    '/algolia/searches-no-results',
    async (req, res) => {
      if (!analyticsClient)
        return res
          .status(200)
          .json({ data: [], error: 'No Algolia or Analytics client' })

      // Parse query parameters
      const index = req.query.index || 'company'
      const limit = Math.min(parseInt(req.query.limit) || 100, 1000)

      // Parse and validate dates
      let startDate, endDate
      if (req.query.startDate) {
        startDate = new Date(req.query.startDate)
        if (isNaN(startDate.getTime())) {
          return res.status(400).json({ error: 'Invalid startDate format' })
        }
        startDate = startDate.toISOString().split('T')[0] // Convert to YYYY-MM-DD
      }

      if (req.query.endDate) {
        endDate = new Date(req.query.endDate)
        if (isNaN(endDate.getTime())) {
          return res.status(400).json({ error: 'Invalid endDate format' })
        }
        endDate = endDate.toISOString().split('T')[0] // Convert to YYYY-MM-DD
      }

      const params = { index, limit }
      if (startDate) params.startDate = startDate
      if (endDate) params.endDate = endDate

      let response, statusCode
      try {
        statusCode = 200
        const data = await analyticsClient.getSearchesNoResults(params)
        response = { ...data }
      } catch (error) {
        serviceLocator.logger.error(
          'Error fetching searches no results:',
          error
        )
        statusCode = 500
        response = { error: 'Error fetching searches no results' }
      }

      return res.status(statusCode).json(response)
    }
  )

  serviceLocator.router.get('/algolia/hit-name', async (req, res) => {
    const methodMap = {
      company: serviceLocator.companyService.findOne,
      executive: serviceLocator.executiveService.findOne,
      article: serviceLocator.articleService.findOne
    }

    const hitId = req.query.hitId
    const index = req.query.index

    if (!hitId) return res.status(400).json({ error: 'No hitId provided' })
    if (!index) return res.status(400).json({ error: 'No index provided' })

    if (!methodMap[index]) {
      return res.status(400).json({ error: 'Invalid index provided' })
    }

    try {
      const projection = {}
      if (index === 'article') projection.headline = 1
      if (index === 'company' || index === 'executive') projection.name = 1
      const data = await promisify(methodMap[index])(
        { _id: hitId },
        { projection }
      )
      return res.status(200).json(data)
    } catch (error) {
      serviceLocator.logger.error('Error fetching hit name:', error)
      return res.status(500).json({ error: 'Error fetching hit name' })
    }
  })
}

/** EXAMPLE getSearchesNoResults DATA
    {
      "searches": [
        {
          "search": "separator",
          "count": 2,
          "withFilterCount": 5
        }
      ]
    }
   */
