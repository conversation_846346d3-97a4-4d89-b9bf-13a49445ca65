const { promisify } = require('util')
const path = require('path')
const createDarkroomHelper = require('../../../article/lib/darkroom-helper')
const createImageUploader = require('../../../article/lib/image-uploader')
const assetSchema = require('../../../../service/asset/schema')()

const retrieveAsset = async (serviceLocator, context) => {
  let assetToReturn = null
  const { req, data, info } = context
  const fileName = path.basename(data.logo_url)
  const darkroomHelper = createDarkroomHelper(serviceLocator, 'testimonial')
  const uploadImage = createImageUploader(serviceLocator)
  const uploadData = await uploadImage([data.logo_url])

  if (!uploadData.id) {
    serviceLocator.logger.error('No upload data returned')
    if (info.warnings) info.warnings.push('No upload data returned')
    return null
  }
  try {
    const sizes = uploadData.exists
      ? { height: uploadData.height, width: uploadData.width }
      : await promisify(darkroomHelper.getImageSize)({
          binaryUri: uploadData.id
        })
    const assetTagNames = [req.body.__name, 'Logo']
    const assetTags = assetTagNames.map(async (tag) => {
      let newTag
      const existingTagResults = await promisify(
        serviceLocator.tagService.find
      )({
        tag,
        type: 'Image',
        account: req.body.accountId
      })
      serviceLocator.logger.info('existing tagData', existingTagResults)
      if (!existingTagResults.length) {
        serviceLocator.logger.info(
          `No ${tag} tag found, creating new ${tag} tag`
        )
        newTag = await promisify(serviceLocator.tagService.create)({
          tag,
          type: 'Image',
          account: req.body.accountId,
          meta: []
        })
        serviceLocator.logger.info('Tag created', newTag)
      }
      return existingTagResults[0] || newTag
    })
    const tags = await Promise.all(assetTags)
    const asset = assetSchema.makeDefault({
      name: fileName,
      type: 'image',
      account: req.body.accountId,
      width: sizes.width,
      height: sizes.height,
      originalUrl: data.logo_url,
      binaryUri: uploadData.id,
      caption: `${req.body.__name} Logo`,
      tags
    })

    if (!uploadData.exists) {
      assetToReturn = await promisify(serviceLocator.assetService.create)(asset)
    } else {
      assetToReturn = await promisify(serviceLocator.assetService.findOne)({
        binaryUri: uploadData.id
      })
      assetToReturn.caption = asset.caption
      assetToReturn.tags = asset.tags
    }
    return assetToReturn
  } catch (error) {
    serviceLocator.logger.error(error)
  }
}

module.exports = retrieveAsset
