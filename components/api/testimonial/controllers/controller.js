const crudServiceApiBuilder = require('cf-crud-service-api-builder')
const searchEndpointBuilder = require('../../../../api/lib/search-endpoint-builder')
const authentication = require('cf-auth-middleware')
const authorisation = require('../../../../api/lib/middleware/authorisation')
const authUserResolver = require('../../../../api/lib/middleware/authenticated-user-resolver')
const accountFilter = require('../../account/lib/middleware/account-filter')

module.exports = (serviceLocator) => {
  const authenticationMiddleware = authentication(
    serviceLocator.authenticationProvider,
    { defaultTtl: process.env.API_AUTH_TIMEOUT || 60000 }
  )
  const authUserLookupMiddleware = authUserResolver(serviceLocator)
  const authorisationMiddleware = authorisation(
    serviceLocator,
    '/testimonials',
    'testimonial'
  )
  const accountFilterMiddleware = accountFilter(serviceLocator)
  const router = serviceLocator.router

  searchEndpointBuilder(
    serviceLocator.testimonialService,
    '/testimonials',
    router,
    serviceLocator.logger,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      authorisationMiddleware,
      accountFilterMiddleware
    ]
  )

  crudServiceApiBuilder(
    serviceLocator.testimonialService,
    '/testimonials',
    router,
    serviceLocator.logger,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      authorisationMiddleware,
      accountFilterMiddleware
    ],
    ['post', 'put', 'patch', 'delete']
  )
}
