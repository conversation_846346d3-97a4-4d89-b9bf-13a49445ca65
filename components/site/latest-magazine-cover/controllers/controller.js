const { promisify } = require('util')
const path = require('path')
const fs = require('fs')
const createImageUrlBuilder = require('cf-image-url-builder')

const createController = (serviceLocator) => {
  serviceLocator.router.get(
    '/api/latest-magazine-cover',
    async (req, res, next) => {
      const serveFallbackImage = (reason) => {
        serviceLocator.logger.error(
          '(latest-magazine-cover) Serving fallback image:',
          reason
        )

        const fallbackImagePath = path.join(
          __dirname,
          '../assets/fallback-image.png'
        )

        if (!fs.existsSync(fallbackImagePath)) {
          return res.status(404).json({
            error: 'No magazine found and fallback image not available'
          })
        }

        res.setHeader('Content-Type', 'image/png')
        res.setHeader('Cache-Control', 'public, max-age=3600') // Cache for 1 hour

        const fallbackStream = fs.createReadStream(fallbackImagePath)
        fallbackStream.pipe(res)

        fallbackStream.on('error', (streamError) => {
          serviceLocator.logger.error(
            'Error streaming fallback image:',
            streamError
          )
          if (!res.headersSent) {
            res.status(500).json({ error: 'Failed to serve fallback image' })
          }
        })
      }

      try {
        const query = { instance: req.instance._id }

        /**
         * TOOD - Add category based filter (I regret not defining the key for categories as human readible - i.e. monthly_edition, top100, top200)
         */

        const options = { sort: { issueDate: -1 }, limit: 1 }

        const latestMagazine = await promisify(
          serviceLocator.magazineIssueService.findOne
        )(query, options)

        if (!latestMagazine) {
          return serveFallbackImage('No latest magazine found')
        }

        const urlBuilder = createImageUrlBuilder(
          serviceLocator.config.darkroom.url,
          serviceLocator.config.darkroom.salt,
          latestMagazine.images.widgets
        )

        const imageUrl = urlBuilder.getImage('Cover').crop().url()
        serviceLocator.logger.info(
          '(latest-magazine-cover) Fetching image from',
          imageUrl
        )

        let response = { ok: false }
        const maxRetries = 5
        let lastError = null

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            serviceLocator.logger.info(
              `(latest-magazine-cover) Fetch attempt ${attempt}/${maxRetries} for ${imageUrl}`
            )
            response = await fetch(imageUrl)

            if (response.ok) {
              serviceLocator.logger.info(
                `(latest-magazine-cover) Successful fetch on attempt ${attempt}`
              )
              break
            } else {
              lastError = new Error(
                `HTTP ${response.status}: ${response.statusText}`
              )
              serviceLocator.logger.warn(
                `(latest-magazine-cover) Attempt ${attempt} failed with status ${response.status}`
              )
            }
          } catch (error) {
            lastError = error
            serviceLocator.logger.warn(
              `(latest-magazine-cover) Attempt ${attempt} failed with error:`,
              error.message
            )
          }

          // If this wasn't the last attempt, wait before retrying
          if (attempt < maxRetries) {
            const delay = 500 // Fixed 500ms delay between retries
            serviceLocator.logger.info(
              `(latest-magazine-cover) Waiting ${delay}ms before retry...`
            )
            await new Promise((resolve) => setTimeout(resolve, delay))
          }
        }

        if (!response.ok) {
          serviceLocator.logger.error(
            `(latest-magazine-cover) All ${maxRetries} fetch attempts failed. Last error:`,
            lastError
          )
          const errorMessage = response.status
            ? `Failed to fetch image: ${response.status} ${response.statusText}`
            : `Failed to fetch image after ${maxRetries} attempts: ${
                lastError?.message || 'Unknown error'
              }`
          return serveFallbackImage(errorMessage)
        }

        serviceLocator.logger.info(
          '(latest-magazine-cover) Final fetch response:',
          {
            ok: response.ok,
            status: response.status,
            statusText: response.statusText,
            headers: response.headers ? [...response.headers.entries()] : []
          }
        )

        //  Validating image content type
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.startsWith('image/')) {
          return serveFallbackImage(`Invalid content type: ${contentType}`)
        }

        res.setHeader('Content-Type', contentType)
        res.setHeader('Cache-Control', 'public, max-age=3600')

        const reader = response.body.getReader()

        const pump = async () => {
          try {
            while (true) {
              const { done, value } = await reader.read()
              if (done) break
              res.write(value)
            }
            res.end()
          } catch (streamError) {
            serviceLocator.logger.error('Error streaming image:', streamError)
            if (!res.headersSent) {
              // If streaming fails, try to serve fallback image
              return serveFallbackImage(
                `Streaming error: ${streamError.message}`
              )
            }
          }
        }

        // Handle client disconnect
        req.on('close', () => {
          reader.cancel()
        })

        await pump()
      } catch (error) {
        return serveFallbackImage(`Unexpected error: ${error.message}`)
      }
    }
  )
}

module.exports = createController
