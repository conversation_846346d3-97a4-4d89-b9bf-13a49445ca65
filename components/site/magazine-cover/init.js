const { promisify } = require('util')
const { GraphQLString, GraphQLNonNull } = require('graphql')
const convertSchemataToGraphQl = require('../../../api/lib/schemata-graphql-adaptor')
const createUrlDeterminer = require('../resource/lib/entity-determiner')
const createMagazineIssueSchema = require('../../service/magazine-issue/graphql-schema')

const args = {
  url: { type: GraphQLNonNull(GraphQLString) }
}

const init = (serviceLocator, done) => {
  const determineUrlEntities = createUrlDeterminer(serviceLocator)
  serviceLocator.graphqlFactory.query.set('latestMagazineIssue', (types) => ({
    type: convertSchemataToGraphQl(
      createMagazineIssueSchema(serviceLocator),
      types
    ),
    args,
    resolve: async (source, args, context) => {
      context.res.startTime(
        `grapqhl-latest-magazine`,
        'GRAPHQL Latest Magazine Query'
      )
      const { instance } = await determineUrlEntities(args.url)
      if (!instance) {
        serviceLocator.logger.error(
          'No instance found for latest magazine query',
          args.url
        )
        context.res.endTime(`grapqhl-latest-magazine`)
        return null
      }
      const query = { instance: instance._id }
      const options = { sort: { issueDate: -1 } }
      const magazine = await promisify(
        serviceLocator.magazineIssueService.findOne
      )(query, options)
      context.res.endTime(`grapqhl-latest-magazine`)
      return magazine
    }
  }))

  done()
}

module.exports = () => ({
  magazineCoverApi: ['magazineIssueService', 'graphqlApi', init]
})
