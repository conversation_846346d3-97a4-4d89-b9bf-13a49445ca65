const {
  GraphQLString,
  GraphQLNonNull,
  GraphQLInt,
  GraphQLInputObjectType,
  GraphQLList
} = require('graphql')
const convertSchemataToGraphQl = require('../../../api/lib/schemata-graphql-adaptor')
const createUrlEntitiesDeterminer = require('../resource/lib/entity-determiner')
const createSchema = require('./schema')
const createSearch = require('./lib/searcher')
const { promisify } = require('util')

const chosenFacetsType = new GraphQLInputObjectType({
  name: 'ChosenFacets',
  fields: {
    name: { type: GraphQLString },
    values: { type: GraphQLList(GraphQLString) }
  }
})

const args = {
  url: { type: GraphQLNonNull(GraphQLString) },
  searchTerm: { type: GraphQLString },
  chosenFacets: { type: Graph<PERSON>List(chosenFacetsType), defaultValue: [] },
  sort: { type: GraphQLString, defaultValue: 'Most Relevant' },
  page: { type: GraphQLInt, defaultValue: 1 },
  type: { type: GraphQLString, defaultValue: 'Simple' }
}

const pickFacets = (chosenFacets) => {
  const picked = {}
  chosenFacets.forEach((facet) => {
    picked[facet.name] = facet.values
  })
  return picked
}

// -------- tick ✅

const init = (serviceLocator, done) => {
  const determineUrlEntities = createUrlEntitiesDeterminer(serviceLocator)
  serviceLocator.graphqlFactory.query.set('search', (types) => ({
    type: convertSchemataToGraphQl(createSchema(serviceLocator), types),
    args,
    resolve: async (source, args, context) => {
      if (!args.searchTerm) {
        return {
          total: 0,
          results: [],
          facets: [],
          companiesResults: [],
          executivesResults: []
        }
      }
      const { instance } = await determineUrlEntities(args.url)

      if (
        args.type === 'Advanced' &&
        instance.featureFlags &&
        instance.featureFlags.includes('advancedSearch')
      ) {
        return advancedSearch(serviceLocator, args, instance)
      } else {
        return simpleSearch(serviceLocator, args, instance, context)
      }
    }
  }))

  done()
}

async function companySearch(serviceLocator, args) {
  let companiesResults = []

  try {
    companiesResults = await new Promise((resolve, reject) => {
      serviceLocator.companyService.algoliaSearch(
        args.searchTerm,
        (err, companies) => {
          if (err) return reject(err)
          resolve(companies)
        }
      )
    })

    // eslint-disable-next-line
    // console.log('companiesResults: ', companiesResults)
    if (!companiesResults?.hits) companiesResults = []
    else {
      companiesResults = companiesResults.hits
      companiesResults = companiesResults.slice(0, 4)
    }
  } catch (error) {
    serviceLocator.logger.error('Error searching companies:', error)
    companiesResults = []
  }

  return companiesResults
}

async function executiveSearch(serviceLocator, args) {
  let executivesResults = []
  try {
    const companyId = await new Promise((resolve, reject) => {
      serviceLocator.companyService.findOne(
        {
          name: { $regex: args.searchTerm, $options: 'i' },
          showProfilePage: true
        },
        { projection: { _id: 1 } },
        (err, company) => {
          if (err) return reject(err)
          if (!company) return resolve(null)
          resolve(company._id)
        }
      )
    })

    let companyExecutives = []
    if (companyId) {
      companyExecutives = await new Promise((resolve, reject) => {
        serviceLocator.executiveService.find(
          { companyId },
          { limit: 4, sort: { dateCreated: -1 } },
          (err, executives) => {
            if (err) return reject(err)
            resolve(executives)
          }
        )
      })

      if (companyExecutives?.length) {
        executivesResults = companyExecutives
      }
    }

    let otherExecutives = await new Promise((resolve, reject) => {
      serviceLocator.executiveService.algoliaSearch(
        args.searchTerm,
        (err, executives) => {
          if (err) return reject(err)
          resolve(executives)
        }
      )
    })

    // eslint-disable-next-line
    // console.log('otherExecutives: ', otherExecutives)

    if (!otherExecutives?.hits) otherExecutives = []
    else {
      otherExecutives = otherExecutives.hits.filter(
        (executive) => !executivesResults.find((e) => e._id === executive._id)
      )
    }

    executivesResults = [...executivesResults, ...otherExecutives]
    executivesResults = executivesResults.slice(0, 4)
  } catch (error) {
    serviceLocator.logger.error('Error searching executives:', error)
    executivesResults = []
  }
  return executivesResults
}

async function simpleSearch(serviceLocator, args, instance, context) {
  const search = createSearch(serviceLocator)
  const searchResults = await search(
    args.searchTerm,
    pickFacets(args.chosenFacets),
    args.sort,
    args.page,
    instance._id,
    context.region
  )

  const finalFacets = []
  Object.keys(searchResults.facets).forEach((facetName) => {
    const facet = { name: facetName, values: [] }
    const facetValues = searchResults.facets[facetName]
    Object.keys(facetValues).forEach((valueName) => {
      const valueCount = facetValues[valueName]
      facet.values.push({ value: valueName, count: valueCount })
    })
    finalFacets.push(facet)
  })

  let companiesResults = []
  if (
    instance.featureFlags &&
    instance.featureFlags.includes('companySearch')
  ) {
    try {
      companiesResults = await companySearch(serviceLocator, args)
    } catch (error) {
      serviceLocator.logger.error('Error searching companies:', error)
      companiesResults = []
    }
  }

  let executivesResults = []
  if (
    instance.featureFlags &&
    instance.featureFlags.includes('executiveSearch')
  ) {
    try {
      executivesResults = await executiveSearch(serviceLocator, args)
    } catch (error) {
      serviceLocator.logger.error('Error searching executives:', error)
      executivesResults = []
    }
  }

  return {
    ...searchResults,
    facets: finalFacets,
    companiesResults: companiesResults,
    executivesResults: executivesResults
  }
}

async function advancedSearch(serviceLocator, args, instance) {
  // Build facet filters from chosen facets
  const facetFilters = [`instance:${instance._id}`]
  const chosenFacets = pickFacets(args.chosenFacets)

  // Add contentType filters
  if (chosenFacets.contentType && chosenFacets.contentType.length > 0) {
    chosenFacets.contentType.forEach((contentType) => {
      facetFilters.push(`contentType:${contentType}`)
    })
  }

  // Add category filters
  if (chosenFacets.category && chosenFacets.category.length > 0) {
    chosenFacets.category.forEach((category) => {
      facetFilters.push(`category:${category}`)
    })
  }

  let algoliaTestResults = { hits: [], nbHits: 0 }

  try {
    algoliaTestResults = await promisify(
      serviceLocator.articleService.algoliaSearch
    )(args.searchTerm, {
      facetFilters,
      page: args.page,
      hitsPerPage: 5,
      facets: ['contentType', 'category'] // Request facets in response
    })
  } catch (error) {
    serviceLocator.logger.error('Error searching articles:', error)
    algoliaTestResults = { hits: [], nbHits: 0 }
  }

  const batch = algoliaTestResults.hits.slice(0, 100)

  const batchPromise = await Promise.allSettled(
    batch.map(async (article) => {
      let fullUrlPath = ''
      const articleDoc = await promisify(serviceLocator.articleService.findOne)(
        {
          _id: article._id
        },
        { projection: { sections: 1, slug: 1 } }
      )
      // console.log('article: ', articleDoc?._id)
      const section = await promisify(serviceLocator.sectionService.findOne)(
        {
          _id: articleDoc.sections[0]
        },
        { projection: { fullUrlPath: 1 } }
      )
      // console.log('section: ', section?._id)
      if (!section) return null
      fullUrlPath = (section.fullUrlPath + '/' + article.slug).replace(
        '//',
        '/'
      )
      // console.log('fullUrlPath: ', fullUrlPath)
      return {
        _id: article._id,
        headline: article.headline,
        sell: article.sell,
        __fullUrlPath: fullUrlPath, // not fullUrlPath because of graphql resolver
        images: article.images,
        contentType: article.contentType,
        category: article.category,
        sections: article.sections,
        slug: article.slug,
        eventId: article.eventId,
        eventBaseSlug: article.eventBaseSlug,
        eventArticleCategoryKey: article.eventArticleCategoryKey
      }
    })
  )

  const projectedBatch = batchPromise
    .filter((result) => result.status === 'fulfilled')
    .map((result) => result.value)

  // Build facets from Algolia response in the same format as simple search
  const finalFacets = []
  if (algoliaTestResults.facets) {
    Object.keys(algoliaTestResults.facets).forEach((facetName) => {
      const facet = { name: facetName, values: [] }
      const facetValues = algoliaTestResults.facets[facetName]
      Object.keys(facetValues).forEach((valueName) => {
        const valueCount = facetValues[valueName]
        facet.values.push({ value: valueName, count: valueCount })
      })
      finalFacets.push(facet)
    })
  }

  const articleSearchResults = {
    facets: finalFacets,
    results: projectedBatch,
    total: algoliaTestResults.nbHits
  }

  let companiesResults = []
  if (
    instance.featureFlags &&
    instance.featureFlags.includes('companySearch')
  ) {
    try {
      companiesResults = await companySearch(serviceLocator, args)
    } catch (error) {
      serviceLocator.logger.error('Error searching companies:', error)
      companiesResults = []
    }
  }

  let executivesResults = []
  if (
    instance.featureFlags &&
    instance.featureFlags.includes('executiveSearch')
  ) {
    try {
      executivesResults = await executiveSearch(serviceLocator, args)
    } catch (error) {
      serviceLocator.logger.error('Error searching executives:', error)
      executivesResults = []
    }
  }

  return {
    ...articleSearchResults,
    companiesResults,
    executivesResults
  }
}

module.exports = () => ({
  search: ['graphqlApi', init]
})
