import createSitemap from './sitemap-creator'

const createCategoryImageSitemap = async (
  serviceLocator,
  req,
  categorySlug
) => {
  const instanceId = req.instance && req.instance._id
  const accountId = req.account && req.account._id

  if (!instanceId || !accountId) {
    throw new Error('Instance or account not found')
  }

  // Query sitemap collection for category image entries
  const options = {
    type: 'category-image',
    categorySlug: categorySlug,
    instance: instanceId,
    account: accountId
  }

  const { data, sitemap } = await createSitemap(serviceLocator, req, options)

  if (!data || data.length === 0) {
    // Return empty sitemap if no images found for this category
    return sitemap.toXML()
  }

  // Group images by article URL to avoid duplicate URLs in sitemap
  const articleGroups = {}

  data.forEach((item) => {
    const { _id, images, headline } = item || {}
    if (!articleGroups[_id]) {
      articleGroups[_id] = {
        url: _id,
        headline: headline,
        images: []
      }
    }

    // Add images from this sitemap entry
    if (images && Array.isArray(images)) {
      images.forEach((image) => {
        if (!image.loc) return
        if (!image.caption) return
        articleGroups[_id].images.push({
          url: image.loc,
          caption: image.caption
        })
      })
    }
  })

  // Add each article with its images to the sitemap
  Object.values(articleGroups).forEach((articleGroup) => {
    sitemap.add({
      url: articleGroup.url,
      img: articleGroup.images
    })
  })

  return sitemap.toXML()
}

export default createCategoryImageSitemap
