import slugg from 'slugg'

const createCategoryImageSitemapUrls = async (
  serviceLocator,
  hostname,
  instance
) => {
  const isDev = serviceLocator.env === 'development'
  const baseUrl = `${hostname}${isDev ? `:${serviceLocator.config.port}` : ''}`

  if (
    !instance ||
    !instance.categories ||
    !Array.isArray(instance.categories)
  ) {
    return []
  }

  // Create sitemap URLs for each category in the instance
  const categoryUrls = instance.categories.map((category) => {
    const categorySlug = slugg(category.toLowerCase())
    return `${baseUrl}/sitemap-image-${categorySlug}.xml`
  })

  return categoryUrls
}

export default createCategoryImageSitemapUrls
