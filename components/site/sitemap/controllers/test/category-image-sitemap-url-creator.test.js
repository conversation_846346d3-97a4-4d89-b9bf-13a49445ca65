import assert from 'assert'
import hat from 'hat'
import categoryImageSitemapUrlCreator from '../lib/category-image-sitemap-url-creator'

describe('category image sitemap url creator', () => {
  const hostname = 'https://example.com'
  const serviceLocator = {
    config: {},
    env: 'production'
  }

  it('should produce a list of category image sitemap urls', async () => {
    const instance = {
      _id: hat(),
      categories: ['Sustainability', 'Technology', 'Finance & Banking']
    }

    const urls = await categoryImageSitemapUrlCreator(
      serviceLocator,
      hostname,
      instance
    )

    assert.deepStrictEqual(urls, [
      'https://example.com/sitemap-image-sustainability.xml',
      'https://example.com/sitemap-image-technology.xml',
      'https://example.com/sitemap-image-finance-banking.xml'
    ])
  })

  it('should produce no urls if instance has no categories', async () => {
    const instance = {
      _id: hat(),
      categories: []
    }

    const urls = await categoryImageSitemapUrlCreator(
      serviceLocator,
      hostname,
      instance
    )

    assert.strictEqual(urls.length, 0)
  })

  it('should produce no urls if instance categories is null', async () => {
    const instance = {
      _id: hat(),
      categories: null
    }

    const urls = await categoryImageSitemapUrlCreator(
      serviceLocator,
      hostname,
      instance
    )

    assert.strictEqual(urls.length, 0)
  })

  it('should handle development environment with port', async () => {
    const devServiceLocator = {
      config: { port: 3000 },
      env: 'development'
    }

    const instance = {
      _id: hat(),
      categories: ['Sustainability']
    }

    const urls = await categoryImageSitemapUrlCreator(
      devServiceLocator,
      hostname,
      instance
    )

    assert.deepStrictEqual(urls, [
      'https://example.com:3000/sitemap-image-sustainability.xml'
    ])
  })

  it('should handle categories with special characters', async () => {
    const instance = {
      _id: hat(),
      categories: ['5G & IoT', 'Finance & Banking', 'AI/ML']
    }

    const urls = await categoryImageSitemapUrlCreator(
      serviceLocator,
      hostname,
      instance
    )

    assert.deepStrictEqual(urls, [
      'https://example.com/sitemap-image-5g-iot.xml',
      'https://example.com/sitemap-image-finance-banking.xml',
      'https://example.com/sitemap-image-ai-ml.xml'
    ])
  })
})
