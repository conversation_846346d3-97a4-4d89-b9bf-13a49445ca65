import createXml from '../lib/xml-creator'
import createVideoXml from '../lib/video-xml-creator'
import createEventVideoXml from '../lib/event-video-xml-creator'
import { retrieveGenericSitemapTypes } from '../lib/sitemap-types'
import createCategoryImageXml from '../lib/category-image-xml-creator'

import createNewsXml from '../lib/news-xml-creator'

const createInstanceMiddleware = require('../../../instance/lib/middleware')

function createRoutes(serviceLocator) {
  const sitemapTypes = retrieveGenericSitemapTypes()
  const instanceMiddleware = createInstanceMiddleware(serviceLocator)
  sitemapTypes.forEach((type) => {
    serviceLocator.router.get(
      `/sitemap-${type.plural}.xml`,
      instanceMiddleware,
      async (req, res, next) => {
        // Use specialized video sitemap for event-videos
        if (type.singular === 'event-video') {
          const xml = await createEventVideoXml(serviceLocator, req)
          res.set('Cache-Control', 'max-age=900')
          res.set('Content-Type', 'application/xml')
          res.send(xml)
        } else {
          const options = { type: type.singular }
          const xml = await createXml(serviceLocator, req, options)
          res.set('Cache-Control', 'max-age=900')
          res.set('Content-Type', 'application/xml')
          res.send(xml)
        }
      }
    )
  })

  serviceLocator.router.get(
    `/sitemap-video.xml`,
    instanceMiddleware,
    async (req, res, next) => {
      const xml = await createVideoXml(serviceLocator, req)
      res.set('Cache-Control', 'max-age=900')
      res.set('Content-Type', 'application/xml')
      res.send(xml)
    }
  )

  serviceLocator.router.get(
    `/googlenews.xml`,
    instanceMiddleware,
    async (req, res, next) => {
      const xml = await createNewsXml(serviceLocator, req)
      res.set('Cache-Control', 'max-age=900')
      res.set('Content-Type', 'application/xml')
      res.send(xml)
    }
  )

  // Category-based image sitemaps
  serviceLocator.router.get(
    `/sitemap-image-:categorySlug.xml`,
    instanceMiddleware,
    async (req, res, next) => {
      try {
        const { categorySlug } = req.params
        const xml = await createCategoryImageXml(
          serviceLocator,
          req,
          categorySlug
        )
        res.set('Cache-Control', 'max-age=900')
        res.set('Content-Type', 'application/xml')
        res.send(xml)
      } catch (error) {
        serviceLocator.logger.error(
          'Error generating category image sitemap:',
          error
        )
        res.status(404).send('Category image sitemap not found')
      }
    }
  )
}
module.exports = createRoutes
