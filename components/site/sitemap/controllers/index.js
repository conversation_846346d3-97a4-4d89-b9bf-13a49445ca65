import createInstanceMiddleware from '../../instance/lib/middleware'
import createArticleSitemapUrls from './lib/article-sitemap-url-creator'
import createMonthGroupRetriever from './lib/month-group-retriever'
import { retrieveSitemapTypes } from './lib/sitemap-types'
import createCategoryImageSitemapUrls from './lib/category-image-sitemap-url-creator'

const sitemapGenerator = require('sitemap')

module.exports = createRoutes

const types = retrieveSitemapTypes()

function createRoutes(serviceLocator) {
  const instanceMiddleware = createInstanceMiddleware(serviceLocator)
  serviceLocator.router.get('/sitemap.xml', instanceMiddleware, async function (
    req,
    res,
    next
  ) {
    const hostname = req.protocol + '://' + req.hostname
    const options = {
      hostname,
      cacheTime: 900000
    }

    const articleSitemapUrls = await createArticleSitemapUrls(
      serviceLocator,
      hostname,
      req.instance,
      createMonthGroupRetriever(serviceLocator)
    )

    const categoryImageSitemapUrls = await createCategoryImageSitemapUrls(
      serviceLocator,
      hostname,
      req.instance
    )

    const isDev = serviceLocator.env === 'development'
    const baseUrl = `${hostname}${
      isDev ? `:${serviceLocator.config.port}` : ''
    }`

    const sitemapUrls =
      types && types.map((type) => `${baseUrl}/sitemap-${type.plural}.xml`)
    sitemapUrls.push(`${baseUrl}/googlenews.xml`)

    const xml = sitemapGenerator.buildSitemapIndex({
      urls: [
        ...sitemapUrls,
        ...articleSitemapUrls,
        ...categoryImageSitemapUrls
      ],
      ...options
    })

    res.set('Cache-Control', 'max-age=900')
    res.set('Content-Type', 'application/xml')
    res.send(xml)
  })
}
