const { GraphQLString, GraphQLBoolean } = require('graphql')

const init = (serviceLocator, done) => {
  // Register REST endpoint for YouTube API statistics
  serviceLocator.router.get(
    '/api/v1/event-video/youtube-stats',
    (_req, res) => {
      serviceLocator.logger.info('REST API: Fetching YouTube API statistics')

      serviceLocator.eventVideoService.getYouTubeApiStats((err, stats) => {
        if (err) {
          serviceLocator.logger.error(
            'Failed to fetch YouTube API statistics:',
            err
          )
          return res.status(500).json({
            error: 'Failed to fetch YouTube API statistics',
            message: err.message
          })
        }

        serviceLocator.logger.info(
          'Successfully fetched YouTube API statistics'
        )
        res.json({
          success: true,
          data: stats
        })
      })
    }
  )

  // Register REST endpoint for refreshing YouTube cache
  serviceLocator.router.post(
    '/api/v1/event-video/:id/refresh-youtube-cache',
    (req, res) => {
      const { id: eventVideoId } = req.params

      if (!eventVideoId) {
        return res.status(400).json({
          error: 'Event video ID is required'
        })
      }

      serviceLocator.logger.info(
        `REST API: Refreshing YouTube cache for event video: ${eventVideoId}`
      )

      serviceLocator.eventVideoService.refreshYouTubeCache(
        eventVideoId,
        (err, updatedVideo) => {
          if (err) {
            serviceLocator.logger.error(
              `Failed to refresh YouTube cache for ${eventVideoId}:`,
              err
            )
            return res.status(500).json({
              error: 'Failed to refresh YouTube cache',
              message: err.message
            })
          }

          serviceLocator.logger.info(
            `Successfully refreshed YouTube cache for: ${eventVideoId}`
          )
          res.json({
            success: true,
            message: 'YouTube cache refreshed successfully',
            data: updatedVideo
          })
        }
      )
    }
  )

  // Register GraphQL mutation for refreshing YouTube cache
  serviceLocator.graphqlFactory.mutation.set(
    'refreshEventVideoYouTubeCache',
    () => ({
      type: GraphQLBoolean,
      args: {
        eventVideoId: { type: GraphQLString }
      },
      resolve: async (_parent, args, _context) => {
        const { eventVideoId } = args

        if (!eventVideoId) {
          throw new Error('Event video ID is required')
        }

        try {
          serviceLocator.logger.info(
            `GraphQL mutation: Refreshing YouTube cache for event video: ${eventVideoId}`
          )

          // Use the service's refresh cache method
          await new Promise((resolve, reject) => {
            serviceLocator.eventVideoService.refreshYouTubeCache(
              eventVideoId,
              (err, updatedVideo) => {
                if (err) return reject(err)
                resolve(updatedVideo)
              }
            )
          })

          serviceLocator.logger.info(
            `Successfully refreshed YouTube cache via GraphQL for: ${eventVideoId}`
          )
          return true
        } catch (error) {
          serviceLocator.logger.info(
            `Failed to refresh YouTube cache via GraphQL for ${eventVideoId}:`,
            error
          )
          throw new Error(`Failed to refresh YouTube cache: ${error.message}`)
        }
      }
    })
  )

  done()
}

module.exports = () => ({
  eventVideoApi: ['eventVideoService', init]
})
