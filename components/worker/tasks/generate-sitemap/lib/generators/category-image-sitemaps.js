import { promisify } from 'util'
import createImageUrlBuilder from 'cf-image-url-builder'
import slugg from 'slugg'

// Helper function to extract images from article (reused from articles.js)
const extractArticleImages = (article, serviceLocator) => {
  const images = []

  // Extract header image
  if (
    article.images &&
    article.images.widgets &&
    article.images.widgets.length > 0
  ) {
    try {
      const urlBuilder = createImageUrlBuilder(
        serviceLocator.config.darkroom.url,
        serviceLocator.config.darkroom.salt,
        article.images.widgets
      )

      const heroImage = urlBuilder
        .getImage('Hero')
        .crop('Landscape')
        .constrain(1200)
        .url()

      if (heroImage) {
        // Ensure JPG extension for sitemap compatibility
        const imageUrl = heroImage.includes('.webp')
          ? heroImage.replace('.webp', '.jpg')
          : heroImage.endsWith('.jpg')
          ? heroImage
          : `${heroImage}.jpg`

        images.push({
          loc: imageUrl,
          caption: article.headline || ''
        })
      }
    } catch (error) {
      // Silent error handling
    }
  }

  // Extract inline images from body widgets
  if (article.body && article.body.widgets) {
    article.body.widgets.forEach((widget) => {
      if (widget.type === 'inlineImage' && widget.images) {
        widget.images.forEach((imageWidget) => {
          if (imageWidget.images && imageWidget.images.widgets) {
            try {
              const urlBuilder = createImageUrlBuilder(
                serviceLocator.config.darkroom.url,
                serviceLocator.config.darkroom.salt,
                imageWidget.images.widgets
              )

              const inlineImage = urlBuilder
                .getImage('Inline')
                .crop('Landscape')
                .constrain(1200)
                .url()

              if (inlineImage) {
                // Ensure JPG extension for sitemap compatibility
                const imageUrl = inlineImage.includes('.webp')
                  ? inlineImage.replace('.webp', '.jpg')
                  : inlineImage.endsWith('.jpg')
                  ? inlineImage
                  : `${inlineImage}.jpg`

                const caption =
                  imageWidget.images.widgets?.[0]?.caption ||
                  imageWidget.caption ||
                  imageWidget.alt

                if (!caption) {
                  serviceLocator.logger.info(
                    'No caption found for inline image, skipping'
                  )
                  return
                }

                images.push({
                  loc: imageUrl,
                  caption
                })
              }
            } catch (error) {
              // Silent error handling
            }
          }
        })
      }
    })
  }

  return images
}

const generateCategoryImageSitemaps = async (
  urls,
  instances,
  serviceLocator,
  dedupe
) => {
  try {
    serviceLocator.logger.info('Starting generateCategoryImageSitemaps')

    // Filter instances that have categories
    const instancesWithCategories = instances.filter(
      (instance) =>
        instance.categories &&
        Array.isArray(instance.categories) &&
        instance.categories.length > 0
    )

    if (!instancesWithCategories.length) {
      serviceLocator.logger.info(
        'No instances with categories found, skipping category image sitemap generation'
      )
      return 0
    }

    let count = 0

    // Get all sections for URL building
    const sections = await promisify(serviceLocator.sectionService.find)(
      {},
      {
        fields: {
          visible: 1,
          instance: 1,
          account: 1,
          fullUrlPath: 1,
          slug: 1
        }
      }
    )

    const collection = serviceLocator.serviceDatabase.collection('article')
    const now = new Date()
    const publicQuery = {
      displayDate: { $ne: null },
      state: 'Published',
      category: { $ne: null, $nin: ['', null] }, // Only articles with categories
      $and: [
        { $or: [{ liveDate: null }, { liveDate: { $lte: now } }] },
        { $or: [{ expiryDate: null }, { expiryDate: { $gte: now } }] }
      ]
    }

    const BATCH_SIZE = 1000
    const pubCount = await collection.count(publicQuery)
    const totalPages = Math.ceil(pubCount / BATCH_SIZE)

    serviceLocator.logger.info(
      `Processing ${pubCount} articles in ${totalPages} batches`
    )

    // Process articles in batches
    for (let page = 0; page < totalPages; page++) {
      const articles = await promisify(
        serviceLocator.articleService.findPublic
      )(
        { displayDate: { $ne: null }, category: { $nin: ['', null] } },
        {
          fields: {
            sections: 1,
            slug: 1,
            account: 1,
            instance: 1,
            category: 1,
            displayDate: 1,
            headline: 1,
            images: 1,
            body: 1
          },
          skip: page * BATCH_SIZE,
          limit: BATCH_SIZE
        }
      )

      for (const article of articles) {
        if (!article.category || !article.instance) continue

        // Find the instance for this article
        const instance = instancesWithCategories.find(
          (i) => i._id === article.instance
        )
        if (
          !instance ||
          !instance.categories ||
          !instance.categories.includes(article.category)
        ) {
          continue
        }

        // Extract images from article
        const articleImages = extractArticleImages(article, serviceLocator)
        if (articleImages.length === 0) continue // Skip articles without images

        // Find the section for URL building
        const sectionIds = article.sections || []
        for (const sectionId of sectionIds) {
          const section = sections.find((s) => s._id === sectionId)
          if (section && section.instance === article.instance) {
            const articleUrl = `https://${instance.subdomain}${section.fullUrlPath}/${article.slug}`

            // Create sitemap entries for each image with unique IDs
            articleImages.forEach((image, imageIndex) => {
              const uniqueId = `${articleUrl}-image-${imageIndex}-${Date.now()}`
              const sitemapEntry = {
                _id: uniqueId,
                url: articleUrl,
                type: 'category-image',
                category: article.category,
                categorySlug: slugg(article.category.toLowerCase()),
                instance: article.instance,
                account: article.account,
                displayDate: article.displayDate,
                headline: article.headline,
                images: [image] // Store as array to match sitemap schema
              }

              if (!dedupe.has(uniqueId)) {
                urls.push(sitemapEntry)
                dedupe(uniqueId)
                count += 1
              }
            })
            break // Only need one section per article
          }
        }
      }
    }

    serviceLocator.logger.info(
      `Category image sitemap entries generated: ${count}`
    )
    return count
  } catch (error) {
    serviceLocator.logger.error(
      'Error in generateCategoryImageSitemaps:',
      error.message
    )
    return 0
  }
}

export default generateCategoryImageSitemaps
