import { promisify } from 'util'

const generateCompanyUrls = async (urls, instances, serviceLocator, dedupe) => {
  let count = 0
  const companies = await promisify(serviceLocator.companyService.find)(
    { showProfilePage: true, slug: { $ne: null } },
    { projection: { slug: 1, _id: 1, name: 1 } }
  )

  if (!companies || !companies.length) {
    serviceLocator.logger.info('No companies found, skipping')
    return 0
  }

  serviceLocator.logger.info(`Found ${companies.length} companies`)

  for (const company of companies) {
    const companyArticles = await promisify(
      serviceLocator.articleService.findPublic
    )(
      { 'companies.company': company._id },
      { projection: { slug: 1, account: 1, instance: 1 } }
    )
    if (!companyArticles || !companyArticles.length) {
      serviceLocator.logger.info(
        `No articles found for company ${company.name} (${company._id}), skipping`
      )
      continue
    }

    const instanceMap = {}
    companyArticles.forEach((article) => {
      instanceMap[article.instance] = true
    })

    const instancesWithArticles = Object.keys(instanceMap)
    if (!instancesWithArticles.length) {
      serviceLocator.logger.info(
        `No instances found for company ${company.name}, skipping`
      )
      continue
    }

    const filteredInstances = instances.filter((instance) =>
      instancesWithArticles.includes(instance._id.toString())
    )

    for (const instance of filteredInstances) {
      if (instance.subdomain && company && instance.account) {
        const companyUrl = {
          _id: `https://${instance.subdomain}/company/${company.slug}`,
          account: instance.account,
          instance: instance._id,
          type: 'company'
        }
        if (dedupe.has(companyUrl._id)) {
          serviceLocator.logger.info('Duplicate company URL: ' + companyUrl._id)
        } else {
          urls.push(companyUrl)
          dedupe(companyUrl._id)
          count += 1
        }
      }
    }
  }
  serviceLocator.logger.info('Company URLs generated: ' + count)
  return count
}

export default generateCompanyUrls
