import { promisify } from 'util'

import generateArticleUrls from './generators/articles'
import generateSectionUrls from './generators/sections'
import generateAuthorUrls from './generators/authors'
import generateMagazineUrls from './generators/magazines'
import generateExecutiveUrls from './generators/executives'
import generateCompanyUrls from './generators/companies'
import generateEventUrls from './generators/events'
import generateEventVideoUrls from './generators/event-videos'
import generateCategoryImageSitemaps from './generators/category-image-sitemaps'
import createDeduper from 'doorman'

module.exports = createSitemapGenerator

function createSitemapGenerator(serviceLocator) {
  const generateSitemap = async () => {
    const urls = []
    const dedupe = createDeduper()
    const instances = await promisify(serviceLocator.instanceService.find)(
      {},
      { fields: { subdomain: 1, account: 1, categories: 1 } }
    )
    if (!instances.length) {
      serviceLocator.logger.info(
        'No instances found, skipping sitemap generation'
      )
      return []
    } else {
      serviceLocator.logger.info('Found ' + instances.length + ' instances')
    }
    const sections = await promisify(serviceLocator.sectionService.find)(
      {},
      {
        fields: {
          visible: 1,
          instance: 1,
          account: 1,
          fullUrlPath: 1,
          slug: 1
        }
      }
    )

    if (!sections.length) {
      serviceLocator.logger.info(
        'No sections found, skipping sitemap generation'
      )
      return []
    } else {
      serviceLocator.logger.info('Found ' + sections.length + ' sections')
    }

    // PERFORMS MORE QUICKLY BUT IS LESS MEMORY EFFICIENT
    // await Promise.all([
    //   await generateArticleUrls(
    //     urls,
    //     instances,
    //     sections,
    //     serviceLocator,
    //     dedupe
    //   ),
    //   await generateSectionUrls(
    //     urls,
    //     instances,
    //     sections,
    //     serviceLocator,
    //     dedupe
    //   ),
    //   await generateAuthorUrls(urls, instances, serviceLocator, dedupe),
    //   await generateMagazineUrls(urls, instances, serviceLocator, dedupe),
    //   await generateExecutiveUrls(urls, instances, serviceLocator, dedupe),
    //   await generateCompanyUrls(urls, instances, serviceLocator, dedupe),
    //   await generateEventUrls(urls, instances, serviceLocator, dedupe),
    //   await generateEventVideoUrls(urls, instances, serviceLocator, dedupe)
    // ])

    // SLOWER BUT MORE MEMORY EFFICIENT
    const functions = [
      generateArticleUrls,
      generateSectionUrls,
      generateAuthorUrls,
      generateMagazineUrls,
      generateExecutiveUrls,
      generateCompanyUrls,
      generateEventUrls,
      generateEventVideoUrls,
      generateCategoryImageSitemaps
    ]

    for (const func of functions) {
      try {
        // eslint-disable-next-line no-console
        console.time(func.name)
        const args = ['generateArticleUrls', 'generateSectionUrls'].includes(
          func.name
        )
          ? [urls, instances, sections, serviceLocator, dedupe]
          : [urls, instances, serviceLocator, dedupe]
        await func(...args)
        // eslint-disable-next-line no-console
        console.timeEnd(func.name)
      } catch (error) {
        serviceLocator.logger.error(`Error in ${func.name}:`, error.message)
        // eslint-disable-next-line no-console
        console.timeEnd(func.name)
        // Continue with next function instead of crashing
        continue
      }
    }

    serviceLocator.logger.info('Sitemap URLs generated: ' + urls.length)
    return urls
  }
  return generateSitemap
}
