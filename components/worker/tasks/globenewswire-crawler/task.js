#!/usr/bin/env node

const { MongoClient } = require('mongodb')
const { join } = require('path')
const axios = require('axios')
const createConfigury = require('@clocklimited/configury')
const jsonFeedUrls = require('./../../../service/globenewswire/lib/json-feed-urls.json')
const schema = require('../../../service/globenewswire/schema')()
const config = createConfigury(join(__dirname, '/../../../../config.json'))(
  process.env.NODE_ENV
)

// Common constants
const MAX_ITEMS = 150
const FALLBACK_DATE = '2025-08-01'

// Utility functions
const sleep = async (ms) => {
  // eslint-disable-next-line no-console
  console.log(`⏳ Waiting for ${ms}ms...`)
  await new Promise((resolve) => setTimeout(resolve, ms))
}

const warnings = []
const errors = []

const parseArgs = () => {
  const args = process.argv.slice(2)
  let mode = 'sync'
  args.forEach((arg) => {
    if (arg.startsWith('--mode=')) mode = arg.split('=')[1]
  })
  return { mode }
}

// Data fetching and processing
const fetchAndParse = async (instanceId, start, max) => {
  try {
    if (!instanceId) {
      warnings.push('[fetchAndParse] instanceId not found')
      return null
    }
    const url = jsonFeedUrls[instanceId]
    if (!url) {
      warnings.push('[fetchAndParse] failed get URL from config')
      return null
    }
    // eslint-disable-next-line no-console
    console.log(
      `🌐 (${instanceId}) Fetching data from ${url}/Start/${start}/Max/${max}`
    )
    const response = await axios.get(`${url}/Start/${start}/Max/${max}`)
    // eslint-disable-next-line no-console
    console.log(
      `✅ (${instanceId}) Response received with status: ${response.status}`
    )
    const json = response.data
    if (!json) {
      warnings.push('[fetchAndParse] failed to get response data')
      return null
    }
    // eslint-disable-next-line no-console
    console.log(`📦 (${instanceId}) Successfully parsed JSON data`)
    return json
  } catch (e) {
    // eslint-disable-next-line no-console
    console.log(`💥 Error in fetchAndParse for instance ${instanceId}:`, e)
    errors.push('[fetchAndParse]', e)
    return null
  }
}

const createExtractProperties = (schema, latestDatesByInstance) => {
  return (items, instanceId) => {
    const cuttoffDate = latestDatesByInstance[instanceId]
    // eslint-disable-next-line no-console
    console.log(`📅 Using cutoff date: ${cuttoffDate.toISOString()}`)
    // eslint-disable-next-line no-console
    console.log('🔍 Extracting properties from items...')
    if (!items) return []

    const properties = schema.getProperties()
    const keys = Object.keys(properties)
    // eslint-disable-next-line no-console
    console.log(
      `📊 Processing ${items.length} items with ${keys.length} properties`
    )

    const stripItem = (item) => {
      const stripped = {}
      keys.forEach((key) => {
        const property = properties[key]
        if (!property.tag) return

        const equalsTag = property.tag.find((tag) => tag.startsWith('equals='))
        if (!equalsTag) return

        const originalKey = equalsTag.split('=')[1]
        const value = item[originalKey]
        stripped[key] =
          property.type === Date && value ? new Date(value) : value
      })
      stripped.crawlDate = new Date()
      stripped.instanceId = instanceId
      return stripped
    }

    return items
      .map(stripItem)
      .filter((item) => {
        if (!item['gn:ReleaseDateTime']) return null
        const itemDate = new Date(item['gn:ReleaseDateTime'])
        return itemDate > cuttoffDate ? item : null
      })
      .filter(Boolean)
  }
}

const processInstance = async (
  db,
  instanceId,
  extractProperties,
  processedInstances,
  totalInstances
) => {
  // eslint-disable-next-line no-console
  console.log(
    `🔄 Processing instance ${
      processedInstances + 1
    }/${totalInstances} (${instanceId})`
  )
  let start = 0

  while (true) {
    const pressReleases = await fetchAndParse(instanceId, start, MAX_ITEMS)
    if (!pressReleases) {
      // eslint-disable-next-line no-console
      console.log('⚠️ No press releases found, moving to next instance')
      break
    }

    const strippedData = extractProperties(pressReleases, instanceId)
    if (!strippedData || strippedData.length === 0) {
      // eslint-disable-next-line no-console
      console.log('⚠️ No new data found')
      break
    }

    // eslint-disable-next-line no-console
    console.log(`📥 Inserting ${strippedData.length} documents into MongoDB`)
    await db.collection('gnPressRelease').insertMany(strippedData)
    await sleep(250)
    start += MAX_ITEMS
  }
  // eslint-disable-next-line no-console
  console.log(`✅ Completed processing instance ${instanceId}`)
}

// Main tasks
async function importTask(db, schema) {
  // eslint-disable-next-line no-console
  console.log('📥 Starting import task...')
  const cuttoffDate = new Date(FALLBACK_DATE)
  const instanceIds = Object.keys(jsonFeedUrls)
  // eslint-disable-next-line no-console
  console.log(`🎯 Found ${instanceIds.length} instances to process`)

  const extractProperties = createExtractProperties(schema, cuttoffDate)

  for (let i = 0; i < instanceIds.length; i++) {
    await processInstance(
      db,
      instanceIds[i],
      extractProperties,
      i,
      instanceIds.length
    )
  }
}

async function syncTask(db, schema) {
  // eslint-disable-next-line no-console
  console.log('🔄 Starting sync task...')

  const instanceIds = Object.keys(jsonFeedUrls)
  // eslint-disable-next-line no-console
  console.log(`🎯 Found ${instanceIds.length} instances to process`)

  const latestPressReleasesByInstance = await db
    .collection('gnPressRelease')
    .aggregate([
      {
        $match: { instanceId: { $in: instanceIds } }
      },
      {
        $group: {
          _id: '$instanceId',
          latestReleaseDateTime: { $max: '$gn:ReleaseDateTime' }
        }
      }
    ])
    .toArray()

  // Convert to object and ensure all instanceIds are present
  const latestDatesByInstance = instanceIds.reduce((acc, instanceId) => {
    const found = latestPressReleasesByInstance.find(
      (item) => item._id === instanceId
    )
    acc[instanceId] = found
      ? found.latestReleaseDateTime
      : new Date(FALLBACK_DATE)
    return acc
  }, {})

  const extractProperties = createExtractProperties(
    schema,
    latestDatesByInstance
  )

  for (let i = 0; i < instanceIds.length; i++) {
    await processInstance(
      db,
      instanceIds[i],
      extractProperties,
      i,
      instanceIds.length
    )
  }

  // eslint-disable-next-line no-console
  console.log('🔄 Sync task complete')
}

// Helper function for temp collection operations
const processInstanceForTemp = async (
  db,
  instanceId,
  extractProperties,
  processedInstances,
  totalInstances
) => {
  // eslint-disable-next-line no-console
  console.log(
    `🔄 Processing instance ${
      processedInstances + 1
    }/${totalInstances} (${instanceId}) for temp collection`
  )
  let start = 0
  let totalInserted = 0

  while (true) {
    const pressReleases = await fetchAndParse(instanceId, start, MAX_ITEMS)
    if (!pressReleases) {
      // eslint-disable-next-line no-console
      console.log('⚠️ No press releases found, moving to next instance')
      break
    }

    const strippedData = extractProperties(pressReleases, instanceId)
    if (!strippedData || strippedData.length === 0) {
      // eslint-disable-next-line no-console
      console.log('⚠️ No new data found')
      break
    }

    // eslint-disable-next-line no-console
    console.log(
      `📥 Inserting ${strippedData.length} documents into temp collection`
    )
    await db.collection('temp_gn_press_release').insertMany(strippedData)
    totalInserted += strippedData.length
    await sleep(250)
    start += MAX_ITEMS
  }

  // eslint-disable-next-line no-console
  console.log(
    `✅ Completed processing instance ${instanceId}, inserted ${totalInserted} documents`
  )
  return totalInserted
}

const compareAndUpdateDocuments = async (db) => {
  // eslint-disable-next-line no-console
  console.log('🔍 Starting document comparison and update process...')

  let updatedCount = 0
  let skippedCount = 0
  let processedCount = 0

  // Get all documents from gnPressRelease
  const gnCursor = db.collection('gnPressRelease').find({})

  while (await gnCursor.hasNext()) {
    const gnDoc = await gnCursor.next()
    processedCount++

    if (processedCount % 1000 === 0) {
      // eslint-disable-next-line no-console
      console.log(`📊 Processed ${processedCount} documents so far...`)
    }

    // Find matching document in temp collection by gn:Identifier
    const tempDoc = await db.collection('temp_gn_press_release').findOne({
      'gn:Identifier': gnDoc['gn:Identifier']
    })

    if (!tempDoc) {
      skippedCount++
      continue
    }

    // Compare gn:ModifiedDate
    const gnModifiedDate = new Date(gnDoc['gn:ModifiedDate'])
    const tempModifiedDate = new Date(tempDoc['gn:ModifiedDate'])

    if (gnModifiedDate.getTime() !== tempModifiedDate.getTime()) {
      // eslint-disable-next-line no-console
      console.log(
        `🔄 Updating document ${
          gnDoc['gn:Identifier']
        } - Modified date changed from ${gnModifiedDate.toISOString()} to ${tempModifiedDate.toISOString()}`
      )

      // Preserve original _id, instanceId, crawlDate and add latestRecrawlDate
      const updateDoc = {
        ...tempDoc,
        _id: gnDoc._id,
        instanceId: gnDoc.instanceId,
        crawlDate: gnDoc.crawlDate,
        latestRecrawlDate: new Date()
      }

      // Remove the temp document's _id to avoid conflicts
      delete updateDoc._id

      await db
        .collection('gnPressRelease')
        .replaceOne({ _id: gnDoc._id }, updateDoc)

      updatedCount++
    } else {
      skippedCount++
    }
  }

  return { updatedCount, skippedCount, processedCount }
}

async function checkForChangesTask(db, schema) {
  // eslint-disable-next-line no-console
  console.log('🔍 Starting check for changes task...')

  // Calculate cutoff date
  let cuttoffDate
  const sixMonthsAgo = new Date(new Date().setMonth(new Date().getMonth() - 6))

  if (sixMonthsAgo > new Date(FALLBACK_DATE)) {
    cuttoffDate = sixMonthsAgo
  } else {
    cuttoffDate = new Date(FALLBACK_DATE)
  }

  // eslint-disable-next-line no-console
  console.log(`📅 Using cutoff date: ${cuttoffDate.toISOString()}`)

  // Step 1: Clear and prepare temp collection
  // eslint-disable-next-line no-console
  console.log('🗑️ Clearing temp collection...')
  await db.collection('temp_gn_press_release').deleteMany({})

  // Step 2: Crawl and populate temp collection
  const instanceIds = Object.keys(jsonFeedUrls)
  // eslint-disable-next-line no-console
  console.log(`🎯 Found ${instanceIds.length} instances to process`)

  const extractProperties = createExtractProperties(schema, cuttoffDate)
  let totalInserted = 0

  for (let i = 0; i < instanceIds.length; i++) {
    const inserted = await processInstanceForTemp(
      db,
      instanceIds[i],
      extractProperties,
      i,
      instanceIds.length
    )
    totalInserted += inserted
  }

  // Step 3: Count documents in temp collection
  const tempCount = await db
    .collection('temp_gn_press_release')
    .countDocuments()
  // eslint-disable-next-line no-console
  console.log(`📊 Total documents in temp collection: ${tempCount}`)

  if (tempCount === 0) {
    // eslint-disable-next-line no-console
    console.log('⚠️ No documents found in temp collection, skipping comparison')
    return
  }

  // Step 4: Compare and update documents
  const {
    updatedCount,
    skippedCount,
    processedCount
  } = await compareAndUpdateDocuments(db)

  // Step 5: Cleanup temp collection
  // eslint-disable-next-line no-console
  console.log('🗑️ Cleaning up temp collection...')
  await db.collection('temp_gn_press_release').drop()

  // Step 6: Output summary
  // eslint-disable-next-line no-console
  console.log('📋 Check for changes task summary:')
  // eslint-disable-next-line no-console
  console.log(`   📥 Total documents crawled: ${totalInserted}`)
  // eslint-disable-next-line no-console
  console.log(`   📊 Documents in temp collection: ${tempCount}`)
  // eslint-disable-next-line no-console
  console.log(`   🔍 Documents processed for comparison: ${processedCount}`)
  // eslint-disable-next-line no-console
  console.log(`   🔄 Documents updated: ${updatedCount}`)
  // eslint-disable-next-line no-console
  console.log(`   ⏭️ Documents skipped (no changes): ${skippedCount}`)
  // eslint-disable-next-line no-console
  console.log('✅ Check for changes task complete')
}

// Main function
async function main() {
  // eslint-disable-next-line no-console
  console.log('🚀 Starting main process...')
  let mongoClient
  const { mode } = parseArgs()

  try {
    // eslint-disable-next-line no-console
    console.log('🔄 Connecting to MongoDB...')
    const connectionUri =
      process.env.MONGO_URL ||
      process.env.NF_DATABASE_MONGO_SRV ||
      config.databaseUrl
    mongoClient = await MongoClient.connect(connectionUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    })

    const db = mongoClient.db()
    // eslint-disable-next-line no-console
    console.log('✅ Database connected successfully')
    // eslint-disable-next-line no-console
    console.log('📋 Current schema configuration:', schema)

    // eslint-disable-next-line no-console
    console.log(`🔄 Running in ${mode} mode`)
    switch (mode) {
      case 'import':
        await importTask(db, schema)
        break
      case 'sync':
        await syncTask(db, schema)
        break
      case 'check':
        await checkForChangesTask(db, schema)
        break
      default:
        // eslint-disable-next-line no-console
        console.log('⚠️ Unknown mode specified')
        break
    }

    if (warnings.length > 0) {
      // eslint-disable-next-line no-console
      console.log('📝 Warning Summary:')
      // eslint-disable-next-line no-console
      warnings.forEach((w) => console.log('⚠️', w))
    }

    if (errors.length > 0) {
      // eslint-disable-next-line no-console
      console.log('❌ Error Summary:')
      // eslint-disable-next-line no-console
      errors.forEach((w) => console.log('❌', w))
    }
  } catch (error) {
    console.error('💥 Fatal Error:', error)
  } finally {
    if (mongoClient) {
      // eslint-disable-next-line no-console
      console.log('🔒 Ensuring database connection is closed...')
      await mongoClient.close()
    }
  }
}

const createTask = (serviceLocator) => {
  const task = async (job, cb) => {
    try {
      await main()
      cb()
    } catch (error) {
      return cb(error)
    }
  }
  return task
}

module.exports = createTask

// Allow direct execution from command line
if (require.main === module) {
  main().catch(console.error)
}

/** TODO
 * - Currently if you add 1 new item into the scheme, it doesn't go back and update all the previous ones. Easy to do for certain fields, but for example instanceId which is a custom field not from their feed, would require a custom migration method
 */
