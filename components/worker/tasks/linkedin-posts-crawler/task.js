#!/usr/bin/env node

const { MongoClient } = require('mongodb')
const { join } = require('path')
const createConfigury = require('@clocklimited/configury')
const fetch = require('cross-fetch')

const config = createConfigury(join(__dirname, '/../../../../config.json'))(
  process.env.NODE_ENV
)
const secrets = createConfigury(join(__dirname, '/../../../../secrets.json'))(
  process.env.NODE_ENV
)

// Common constants
const POSTS_LIMIT = 4
const REQUEST_DELAY = 250 // ms between requests

// Utility functions
const sleep = async (ms) => {
  // eslint-disable-next-line no-console
  console.log(`⏳ Waiting for ${ms}ms...`)
  await new Promise((resolve) => setTimeout(resolve, ms))
}

const warnings = []
const errors = []

const parseArgs = () => {
  const args = process.argv.slice(2)
  let mode = 'sync' // Options: sync, check
  let entityType = 'all' // Options: executives, companies, all

  args.forEach((arg) => {
    if (arg.startsWith('--mode=')) mode = arg.split('=')[1]
    if (arg.startsWith('--entity=')) entityType = arg.split('=')[1]
  })

  // eslint-disable-next-line no-console
  console.log(`🎯 Mode: ${mode}, Entity Type: ${entityType}`)
  return { mode, entityType }
}

// API request functions
const fetchExecutivePosts = async (linkedinUrl) => {
  const url = `https://api.scrapin.io/v1/enrichment/persons/activities/posts?apikey=${secrets.scrapin.apiKey}&linkedInUrl=${linkedinUrl}`
  const options = {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' }
  }

  try {
    // eslint-disable-next-line no-console
    console.log(`🔍 Fetching executive posts for: ${linkedinUrl}`)
    const response = await fetch(url, options)
    const data = await response.json()

    if (!response.ok) {
      warnings.push(
        `Executive posts API error for ${linkedinUrl}: ${
          data.error || 'Unknown error'
        }`
      )
      return null
    }

    return data
  } catch (error) {
    warnings.push(
      `Executive posts fetch error for ${linkedinUrl}: ${error.message}`
    )
    return null
  }
}

const fetchCompanyPosts = async (linkedinUrl) => {
  const url = `https://api.scrapin.io/v1/enrichment/companies/activities/posts?apikey=${secrets.scrapin.apiKey}&linkedInUrl=${linkedinUrl}`
  const options = {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' }
  }

  try {
    // eslint-disable-next-line no-console
    console.log(`🔍 Fetching company posts for: ${linkedinUrl}`)
    const response = await fetch(url, options)
    const data = await response.json()

    if (!response.ok) {
      warnings.push(
        `Company posts API error for ${linkedinUrl}: ${
          `${data.title} | ${data.msg}` || 'Unknown error'
        }`
      )
      return null
    }

    return data
  } catch (error) {
    warnings.push(
      `Company posts fetch error for ${linkedinUrl}: ${error.message}`
    )
    return null
  }
}

const fetchExecutiveProfile = async (linkedinUrl) => {
  const url = `https://api.scrapin.io/v1/enrichment/profile?apikey=${secrets.scrapin.apiKey}`
  const options = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      includes: {
        includeSummary: true,
        includeExperience: false,
        includeCompany: false
      },
      linkedInUrl: linkedinUrl
    })
  }

  try {
    // eslint-disable-next-line no-console
    console.log(`🔍 Fetching executive profile for: ${linkedinUrl}`)
    const response = await fetch(url, options)
    const data = await response.json()

    if (!response.ok) {
      warnings.push(
        `Executive profile API error for ${linkedinUrl}: ${
          data.error || 'Unknown error'
        }`
      )
      return null
    }

    return data
  } catch (error) {
    warnings.push(
      `Executive profile fetch error for ${linkedinUrl}: ${error.message}`
    )
    return null
  }
}

// Data processing functions
const processExecutivePosts = (postsData, executiveLinkedInIdentifier) => {
  if (!postsData || !postsData.posts || !Array.isArray(postsData.posts)) {
    return []
  }

  // Filter posts by authorId matching executive's linkedInIdentifier
  const filteredPosts = postsData.posts.filter(
    (post) =>
      post.author && post.author.authorId === executiveLinkedInIdentifier
  )

  // Sort by activityDate (newest first) and take the latest 4
  const sortedPosts = filteredPosts
    .sort((a, b) => new Date(b.activityDate) - new Date(a.activityDate))
    .slice(0, POSTS_LIMIT)

  const strippedPosts = sortedPosts.map((post) => ({
    activityId: post.activityId,
    text: post.text,
    activityDate: new Date(post.activityDate)
  }))

  return strippedPosts
}

const processCompanyPosts = (postsData) => {
  if (!postsData || !postsData.posts || !Array.isArray(postsData.posts)) {
    return []
  }

  // Sort by activityDate (newest first) and take the latest 4
  const sortedPosts = postsData.posts
    .sort((a, b) => new Date(b.activityDate) - new Date(a.activityDate))
    .slice(0, POSTS_LIMIT)

  return sortedPosts
}

// Executive processing
const processExecutive = async (db, executive) => {
  try {
    // eslint-disable-next-line no-console
    console.log(`👤 Processing executive: ${executive.name} (${executive._id})`)

    if (!executive.linkedinProfileUrlV2) {
      warnings.push(`Executive ${executive.name} has no LinkedIn URL`)
      return
    }

    let linkedInIdentifier = executive.linkedinPrivateIdentifier

    // If no linkedInIdentifier, fetch it from profile API
    if (!linkedInIdentifier) {
      // eslint-disable-next-line no-console
      console.log(`🔄 Fetching LinkedIn identifier for ${executive.name}`)
      const profileData = await fetchExecutiveProfile(
        executive.linkedinProfileUrlV2
      )

      if (
        profileData &&
        profileData.person &&
        profileData.person.linkedInIdentifier
      ) {
        linkedInIdentifier = profileData.person.linkedInIdentifier

        // Update executive with the linkedInIdentifier
        await db.collection('executive').updateOne(
          { _id: executive._id },
          {
            $set: {
              linkedinPrivateIdentifier: linkedInIdentifier,
              modifiedDate: new Date()
            }
          }
        )
        // eslint-disable-next-line no-console
        console.log(
          `✅ Updated executive ${executive.name} with LinkedIn identifier`
        )
      } else {
        warnings.push(
          `Could not fetch LinkedIn identifier for executive ${executive.name}`
        )
        return
      }

      await sleep(REQUEST_DELAY)
    }

    // Fetch posts
    const postsData = await fetchExecutivePosts(executive.linkedinProfileUrlV2)
    if (!postsData) {
      return
    }

    // Process and filter posts
    const linkedinPosts = processExecutivePosts(postsData, linkedInIdentifier)

    // Update executive with posts
    await db.collection('executive').updateOne(
      { _id: executive._id },
      {
        $set: {
          linkedinPosts: linkedinPosts,
          modifiedDate: new Date()
        }
      }
    )

    // eslint-disable-next-line no-console
    console.log(
      `✅ Updated executive ${executive.name} with ${linkedinPosts.length} LinkedIn posts`
    )

    await sleep(REQUEST_DELAY)
  } catch (error) {
    errors.push(
      `Error processing executive ${executive.name}: ${error.message}`
    )
  }
}

// Company processing
const processCompany = async (db, company) => {
  try {
    // eslint-disable-next-line no-console
    console.log(`🏢 Processing company: ${company.name} (${company._id})`)

    if (!company.linkedinUrl) {
      warnings.push(`Company ${company.name} has no LinkedIn URL`)
      return
    }

    // Fetch posts
    const postsData = await fetchCompanyPosts(company.linkedinUrl)
    if (!postsData) {
      return
    }

    // Process posts (no filtering needed for companies)
    const linkedinPosts = processCompanyPosts(postsData)

    // Update company with posts
    await db.collection('company').updateOne(
      { _id: company._id },
      {
        $set: {
          linkedinPosts: linkedinPosts,
          modifiedDate: new Date()
        }
      }
    )

    // eslint-disable-next-line no-console
    console.log(
      `✅ Updated company ${company.name} with ${linkedinPosts.length} LinkedIn posts`
    )

    await sleep(REQUEST_DELAY)
  } catch (error) {
    errors.push(`Error processing company ${company.name}: ${error.message}`)
  }
}

// Main task functions
async function syncTask(db, entityType) {
  // eslint-disable-next-line no-console
  console.log('🔄 Starting LinkedIn posts sync task...')

  if (entityType === 'executives' || entityType === 'all') {
    // eslint-disable-next-line no-console
    console.log('👥 Processing verified executives...')

    const verifiedExecutives = await db
      .collection('executive')
      .find({
        verifiedInstances: { $ne: [], $exists: true }
      })
      .toArray()

    // eslint-disable-next-line no-console
    console.log(`📊 Found ${verifiedExecutives.length} verified executives`)

    for (let i = 0; i < verifiedExecutives.length; i++) {
      const executive = verifiedExecutives[i]
      // eslint-disable-next-line no-console
      console.log(
        `📋 Processing executive ${i + 1}/${verifiedExecutives.length}`
      )
      await processExecutive(db, executive)
    }
  }

  if (entityType === 'companies' || entityType === 'all') {
    // eslint-disable-next-line no-console
    console.log('🏢 Processing verified companies...')

    const verifiedCompanies = await db
      .collection('company')
      .find({
        verifiedInstances: { $ne: [], $exists: true }
      })
      .toArray()

    // eslint-disable-next-line no-console
    console.log(`📊 Found ${verifiedCompanies.length} verified companies`)

    for (let i = 0; i < verifiedCompanies.length; i++) {
      const company = verifiedCompanies[i]
      // eslint-disable-next-line no-console
      console.log(`📋 Processing company ${i + 1}/${verifiedCompanies.length}`)
      await processCompany(db, company)
    }
  }

  // eslint-disable-next-line no-console
  console.log('✅ LinkedIn posts sync task complete')

  // Show preview of what was processed
  // eslint-disable-next-line no-console
  console.log('')
  // eslint-disable-next-line no-console
  console.log('📋 Preview of processed entities:')

  if (entityType === 'executives' || entityType === 'all') {
    await previewExecutivePosts(db)
  }

  if (entityType === 'companies' || entityType === 'all') {
    await previewCompanyPosts(db)
  }
}

// Preview functions
const previewExecutivePosts = async (db) => {
  const executives = await db
    .collection('executive')
    .find({
      verifiedInstances: { $ne: [], $exists: true },
      linkedinPosts: { $exists: true, $ne: [] }
    })
    .limit(3)
    .toArray()

  if (executives.length === 0) {
    // eslint-disable-next-line no-console
    console.log('   📝 No executives with LinkedIn posts found')
    return
  }

  // eslint-disable-next-line no-console
  console.log('   📝 Preview of executives with LinkedIn posts:')
  executives.forEach((executive, index) => {
    const latestPost = executive.linkedinPosts[0] || {}
    const postPreview = latestPost.text
      ? latestPost.text.substring(0, 100) + '...'
      : 'No text available'
    const postDate = latestPost.activityDate
      ? new Date(latestPost.activityDate).toLocaleDateString()
      : 'No date'

    // eslint-disable-next-line no-console
    console.log(
      `      ${index + 1}. ${executive.name || 'Unknown'} (${
        executive.linkedinPosts.length
      } posts)`
    )
    // eslint-disable-next-line no-console
    console.log(`         Latest: "${postPreview}" (${postDate})`)
  })
}

const previewCompanyPosts = async (db) => {
  const companies = await db
    .collection('company')
    .find({
      verifiedInstances: { $ne: [], $exists: true },
      linkedinPosts: { $exists: true, $ne: [] }
    })
    .limit(3)
    .toArray()

  if (companies.length === 0) {
    // eslint-disable-next-line no-console
    console.log('   📝 No companies with LinkedIn posts found')
    return
  }

  // eslint-disable-next-line no-console
  console.log('   📝 Preview of companies with LinkedIn posts:')
  companies.forEach((company, index) => {
    const latestPost = company.linkedinPosts[0] || {}
    const postPreview = latestPost.text
      ? latestPost.text.substring(0, 100) + '...'
      : 'No text available'
    const postDate = latestPost.activityDate
      ? new Date(latestPost.activityDate).toLocaleDateString()
      : 'No date'

    // eslint-disable-next-line no-console
    console.log(
      `      ${index + 1}. ${company.name || 'Unknown'} (${
        company.linkedinPosts.length
      } posts)`
    )
    // eslint-disable-next-line no-console
    console.log(`         Latest: "${postPreview}" (${postDate})`)
  })
}

async function checkTask(db, entityType) {
  // eslint-disable-next-line no-console
  console.log('🔍 Starting LinkedIn posts check task...')

  const executiveStats = {
    total: 0,
    verified: 0,
    withLinkedIn: 0,
    withPosts: 0
  }
  const companyStats = { total: 0, verified: 0, withLinkedIn: 0, withPosts: 0 }

  if (entityType === 'executives' || entityType === 'all') {
    // Executive statistics
    executiveStats.total = await db.collection('executive').countDocuments({})
    executiveStats.verified = await db.collection('executive').countDocuments({
      verifiedInstances: { $ne: [], $exists: true }
    })
    executiveStats.withLinkedIn = await db
      .collection('executive')
      .countDocuments({
        verifiedInstances: { $ne: [], $exists: true },
        linkedinProfileUrlV2: {
          $and: [{ $exists: true }, { $ne: null }, { $ne: '' }]
        }
      })
    executiveStats.withPosts = await db.collection('executive').countDocuments({
      verifiedInstances: { $ne: [], $exists: true },
      linkedinPosts: { $exists: true, $ne: [] }
    })

    // eslint-disable-next-line no-console
    console.log('👥 Executive Statistics:')
    // eslint-disable-next-line no-console
    console.log(`   Total executives: ${executiveStats.total}`)
    // eslint-disable-next-line no-console
    console.log(`   Verified executives: ${executiveStats.verified}`)
    // eslint-disable-next-line no-console
    console.log(`   Verified with LinkedIn URL: ${executiveStats.withLinkedIn}`)
    // eslint-disable-next-line no-console
    console.log(`   Verified with LinkedIn posts: ${executiveStats.withPosts}`)

    // Show preview if there are posts
    if (executiveStats.withPosts > 0) {
      await previewExecutivePosts(db)
    }
  }

  if (entityType === 'companies' || entityType === 'all') {
    // Company statistics
    companyStats.total = await db.collection('company').countDocuments({})
    companyStats.verified = await db.collection('company').countDocuments({
      verifiedInstances: { $ne: [], $exists: true }
    })
    companyStats.withLinkedIn = await db.collection('company').countDocuments({
      verifiedInstances: { $ne: [], $exists: true },
      linkedinUrl: { $and: [{ $exists: true }, { $ne: null }, { $ne: '' }] }
    })
    companyStats.withPosts = await db.collection('company').countDocuments({
      verifiedInstances: { $ne: [], $exists: true },
      linkedinPosts: { $exists: true, $ne: [] }
    })

    // eslint-disable-next-line no-console
    console.log('🏢 Company Statistics:')
    // eslint-disable-next-line no-console
    console.log(`   Total companies: ${companyStats.total}`)
    // eslint-disable-next-line no-console
    console.log(`   Verified companies: ${companyStats.verified}`)
    // eslint-disable-next-line no-console
    console.log(`   Verified with LinkedIn URL: ${companyStats.withLinkedIn}`)
    // eslint-disable-next-line no-console
    console.log(`   Verified with LinkedIn posts: ${companyStats.withPosts}`)

    // Show preview if there are posts
    if (companyStats.withPosts > 0) {
      await previewCompanyPosts(db)
    }
  }

  return { executiveStats, companyStats }
}

// Main function
async function main() {
  // eslint-disable-next-line no-console
  console.log('🚀 Starting LinkedIn Posts Crawler...')
  let mongoClient
  const { mode, entityType } = parseArgs()

  try {
    // eslint-disable-next-line no-console
    console.log('🔄 Connecting to MongoDB...')
    const connectionUri =
      process.env.MONGO_URL ||
      process.env.NF_DATABASE_MONGO_SRV ||
      config.databaseUrl
    mongoClient = await MongoClient.connect(connectionUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    })

    const db = mongoClient.db()
    // eslint-disable-next-line no-console
    console.log('✅ Database connected successfully')

    // eslint-disable-next-line no-console
    console.log(`🔄 Running in ${mode} mode for ${entityType}`)
    switch (mode) {
      case 'sync':
        await syncTask(db, entityType)
        break
      case 'check':
        await checkTask(db, entityType)
        break
      default:
        // eslint-disable-next-line no-console
        console.log('⚠️ Unknown mode specified. Available modes: sync, check')
        break
    }

    if (warnings.length > 0) {
      // eslint-disable-next-line no-console
      console.log('📝 Warning Summary:')
      // eslint-disable-next-line no-console
      warnings.forEach((w) => console.log('⚠️', w))
    }

    if (errors.length > 0) {
      // eslint-disable-next-line no-console
      console.log('❌ Error Summary:')
      // eslint-disable-next-line no-console
      errors.forEach((w) => console.log('❌', w))
    }
  } catch (error) {
    console.error('💥 Fatal Error:', error)
  } finally {
    if (mongoClient) {
      // eslint-disable-next-line no-console
      console.log('🔒 Ensuring database connection is closed...')
      await mongoClient.close()
    }
  }
}

const createTask = (serviceLocator) => {
  const task = async (job, cb) => {
    try {
      await main()
      cb()
    } catch (error) {
      return cb(error)
    }
  }
  return task
}

module.exports = createTask

// Allow direct execution from command line
if (require.main === module) {
  main().catch(console.error)
}
