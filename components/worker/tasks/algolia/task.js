const { MongoClient } = require('mongodb')
const { join } = require('path')
const createConfigury = require('@clocklimited/configury')
const { algoliasearch } = require('algoliasearch')
const { convert } = require('html-to-text')
const { ObjectId } = require('mongodb')

const config = createConfigury(join(__dirname, '/../../../../config.json'))(
  process.env.NODE_ENV
)
const secrets = createConfigury(join(__dirname, '/../../../../secrets.json'))(
  process.env.NODE_ENV
)

const warnings = []
const errors = []

// Sleep function for delay between requests
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

// Initialize Algolia client
const initAlgoliaClient = () => {
  let applicationId, importApiKey

  if (
    process.env.ALGOLIA_APPLICATION_ID &&
    process.env.ALGOLIA_IMPORT_API_KEY
  ) {
    applicationId = process.env.ALGOLIA_APPLICATION_ID
    importApiKey = process.env.ALGOLIA_IMPORT_API_KEY
  } else {
    applicationId = secrets.algolia.applicationId
    importApiKey = secrets.algolia.importApiKey
  }

  if (!applicationId) {
    // eslint-disable-next-line no-console
    console.log('No Algolia application ID provided. Exiting...')
    process.exit(1)
  }

  if (!importApiKey) {
    // eslint-disable-next-line no-console
    console.log('No Algolia import api key provided. Exiting...')
    process.exit(1)
  }

  // eslint-disable-next-line no-console
  console.log(
    '🔑 Initializing Algolia client...',
    applicationId,
    importApiKey?.slice(0, 5) + '...'
  )
  return algoliasearch(applicationId, importApiKey)
}

// Content type middleware functions
const articleMiddleware = (article) => {
  const compressedBody = article.body.widgets
    .filter((w) => w.type === 'text')
    .map((w) => convert(w.html))
    .join(' ')

  return {
    _id: article._id,
    headline: article.headline,
    sell: article.sell,
    displayDate: article.displayDate,
    __fullUrlPath: article.__fullUrlPath,
    images: article.images,
    author: article.author,
    legacyAuthorName: article.legacyAuthorName,
    featured: article.featured,
    body: compressedBody,
    contentType: article.contentType,
    slug: article.slug,
    objectID: String(article._id),
    instance: article.instance,
    category: article.category,
    __importDate: new Date()
  }
}

const companyMiddleware = (company) => {
  // TODO: Implement company-specific transformation
  return {
    _id: company._id,
    name: company.name,
    slug: company.slug,
    description: company.description,
    objectID: String(company._id),
    city: company.city,
    country: company.country,
    industries: company.industries,
    keywords: company.keywords,
    __importDate: new Date()
  }
}

const executiveMiddleware = async (executive, { db }) => {
  // TODO: Implement executive-specific transformation
  let companyName
  if (db) {
    const company = await db
      .collection('company')
      .findOne({ _id: ObjectId(executive.companyId) })
    if (company) companyName = company.name
  }

  const out = {
    _id: executive._id,
    name: executive.name,
    slug: executive.slug,
    jobTitle: executive.jobTitle,
    objectID: String(executive._id),
    __importDate: new Date()
  }

  if (companyName) out.__companyName = companyName
  return out
}

// Content type configurations
const contentTypeConfigs = {
  article: {
    indexName: 'article',
    middleware: articleMiddleware,
    query: {
      state: 'Published',
      displayDate: { $gte: new Date('2024-01-01') }
    },
    projection: {
      _id: 1,
      headline: 1,
      sell: 1,
      displayDate: 1,
      __fullUrlPath: 1,
      images: 1,
      author: 1,
      legacyAuthorName: 1,
      featured: 1,
      body: 1,
      contentType: 1,
      slug: 1,
      modifiedDate: 1,
      algoliaLastCrawlDate: 1,
      instance: 1,
      category: 1
    },
    sort: { displayDate: -1 }
  },
  company: {
    indexName: 'company',
    middleware: companyMiddleware,
    query: {
      // TODO: Add company-specific query filters
    },
    projection: {
      _id: 1,
      name: 1,
      slug: 1,
      description: 1,
      city: 1,
      country: 1,
      industries: 1,
      keywords: 1,
      modifiedDate: 1,
      algoliaLastCrawlDate: 1
      // TODO: Add more company fields
    },
    sort: { name: 1 }
  },
  executive: {
    indexName: 'executive',
    middleware: executiveMiddleware,
    query: {
      // TODO: Add executive-specific query filters
    },
    projection: {
      _id: 1,
      name: 1,
      slug: 1,
      jobTitle: 1,
      modifiedDate: 1,
      algoliaLastCrawlDate: 1,
      companyId: 1
      // TODO: Add more executive fields
    },
    sort: { name: 1 }
  }
}

const parseArgs = () => {
  const args = process.argv.slice(2)
  let mode = 'sync' // Options: import, sync, check
  let collections = [] // Options: article, company, executive
  // eslint-disable-next-line no-console
  console.log('\n')
  // eslint-disable-next-line no-console
  console.group('Args')
  // eslint-disable-next-line no-console
  console.log('Raw args: ', args, '\n')
  args.forEach((arg) => {
    if (arg.startsWith('--mode=')) mode = arg.split('=')[1]
    if (arg.startsWith('--collections=')) {
      collections = arg.split('=')[1].split(',')
    }
  })

  // eslint-disable-next-line no-console
  console.log('Mode: ', mode)
  if (!collections.length) {
    // eslint-disable-next-line no-console
    console.log('No collections specified')
  }
  collections.forEach((c) => {
    // eslint-disable-next-line no-console
    console.log('Collection: ', c)
  })
  // eslint-disable-next-line no-console
  console.log('\n')
  // eslint-disable-next-line no-console
  console.groupEnd()
  return { mode, collections }
}

// Generalized import function for any content type
async function importContentType(db, contentType, algoliaClient) {
  const config = contentTypeConfigs[contentType]
  if (!config) {
    throw new Error(`Unknown content type: ${contentType}`)
  }

  const batchSize = 100
  const collection = await db.collection(contentType)
  const count = await collection.countDocuments(config.query)
  const numBatches = Math.ceil(count / batchSize)
  let page = 1

  // eslint-disable-next-line no-console
  console.log(`📊 Total ${contentType}s: ${count}`)
  // eslint-disable-next-line no-console
  console.log(`📦 Batch size: ${batchSize}`)
  // eslint-disable-next-line no-console
  console.log(`🔄 Total batches: ${numBatches}`)

  const importBatch = async (page) => {
    // eslint-disable-next-line no-console
    console.log(`\n🔍 Fetching ${contentType} batch ${page}/${numBatches}...`)
    const startTime = Date.now()

    const batch = await collection
      .find(config.query)
      .limit(batchSize)
      .skip((page - 1) * batchSize)
      .project(config.projection)
      .sort(config.sort)
      .toArray()

    // eslint-disable-next-line no-console
    console.log(`📄 Processing ${batch.length} ${contentType}s...`)

    const compressedBatch = (
      await Promise.all(
        batch.map(async (item) => {
          const compressedItem = await config.middleware(item, { db })
          const size = Buffer.byteLength(JSON.stringify(compressedItem), 'utf8')

          if (size > 10000) {
            // eslint-disable-next-line no-console
            console.log(
              `⚠️  Further compression needed for ${contentType} "${
                item.headline || item.name
              }" - size exceeds 10KB (${size - 10000} bytes too large)`
            )

            // For articles, compress the body field
            if (contentType === 'article' && compressedItem.body) {
              const bytesTooLarge = size - 10000
              const hyperCompressedBody = compressedItem.body.slice(
                0,
                compressedItem.body.length - bytesTooLarge
              )
              compressedItem.body = hyperCompressedBody

              const newSize = Buffer.byteLength(
                JSON.stringify(compressedItem),
                'utf8'
              )
              // eslint-disable-next-line no-console
              console.log(
                `✅ ${contentType} "${
                  item.headline || item.name
                }" - size now ${newSize} bytes - ${bytesTooLarge} bytes removed`
              )

              if (newSize > 10000) {
                // eslint-disable-next-line no-console
                console.log(
                  `❌ ${contentType} "${
                    item.headline || item.name
                  }" - size still exceeds 10KB after hyper compression - skipping`
                )
                return null
              }
            } else {
              // For non-article content types, skip if too large
              // eslint-disable-next-line no-console
              console.log(
                `❌ ${contentType} "${item.name}" - size exceeds 10KB - skipping`
              )
              return null
            }
          }

          return compressedItem
        })
      )
    ).filter(Boolean)

    // eslint-disable-next-line no-console
    console.log(
      `📥 Uploading ${compressedBatch.length} ${contentType}s to Algolia...`
    )

    await algoliaClient.saveObjects({
      indexName: config.indexName,
      objects: compressedBatch
    })

    // Update the last crawl date for each item in the batch
    await collection.updateMany(
      { _id: { $in: batch.map((item) => item._id) } },
      { $set: { algoliaLastCrawlDate: new Date() } }
    )

    // eslint-disable-next-line no-console
    console.log(
      `🔄 Updated last crawl date for ${batch.length} ${contentType}s`
    )

    const duration = ((Date.now() - startTime) / 1000).toFixed(2)
    // eslint-disable-next-line no-console
    console.log(`✅ Batch ${page} completed in ${duration}s`)
    // eslint-disable-next-line no-console
    console.log(`📈 Progress: ${((page / numBatches) * 100).toFixed(1)}%`)
  }

  while (page <= numBatches) {
    await importBatch(page)
    if (page < numBatches) {
      // eslint-disable-next-line no-console
      console.log(`😴 Sleeping for 1 second to avoid rate limits...`)
      await sleep(1000)
    }
    page += 1
  }

  // eslint-disable-next-line no-console
  console.log(`\n🎉 ${contentType} import completed successfully!`)
}

// Main tasks
async function importTask(db, collections) {
  const algoliaClient = initAlgoliaClient()

  // eslint-disable-next-line no-console
  console.log('🚀 Starting import to Algolia...')

  for (const contentType of collections) {
    try {
      // eslint-disable-next-line no-console
      console.log(`\n📋 Importing ${contentType}s...`)
      await importContentType(db, contentType, algoliaClient)
    } catch (error) {
      const errorMsg = `Failed to import ${contentType}: ${error.message}`
      errors.push(errorMsg)
      // eslint-disable-next-line no-console
      console.error(`❌ ${errorMsg}`)
    }
  }
}

// Generalized sync function for any content type
async function syncContentType(db, contentType, algoliaClient) {
  const config = contentTypeConfigs[contentType]
  if (!config) {
    throw new Error(`Unknown content type: ${contentType}`)
  }

  // Build sync query - only documents that need syncing
  const syncQuery = {
    ...config.query,
    $or: [
      // Documents that have never been crawled
      { algoliaLastCrawlDate: { $exists: false } },
      // Documents that have been modified since last crawl
      { $expr: { $gt: ['$modifiedDate', '$algoliaLastCrawlDate'] } }
    ]
  }

  const batchSize = 100
  const collection = await db.collection(contentType)
  const count = await collection.countDocuments(syncQuery)
  if (count === 0) {
    // eslint-disable-next-line no-console
    console.log(`✅ No ${contentType}s need syncing - all up to date!`)
    return
  }

  const numBatches = Math.ceil(count / batchSize)
  let page = 1

  // eslint-disable-next-line no-console
  console.log(`📊 ${contentType}s needing sync: ${count}`)
  // eslint-disable-next-line no-console
  console.log(`📦 Batch size: ${batchSize}`)
  // eslint-disable-next-line no-console
  console.log(`🔄 Total batches: ${numBatches}`)

  // Snapshot all IDs that need syncing upfront to avoid pagination issues
  // eslint-disable-next-line no-console
  console.log(`📸 Snapshotting IDs that need syncing...`)
  const idsNeedingSync = await collection
    .find(syncQuery)
    .project({ _id: 1 })
    .sort(config.sort)
    .toArray()

  const allIds = idsNeedingSync.map((doc) => doc._id)
  // eslint-disable-next-line no-console
  console.log(`✅ Snapshotted ${allIds.length} IDs for processing`)

  const syncBatch = async (page) => {
    // eslint-disable-next-line no-console
    console.log(
      `\n🔍 Fetching ${contentType} sync batch ${page}/${numBatches}...`
    )
    const startTime = Date.now()

    // Include _id in projection for updating algoliaLastCrawlDate
    const projectionWithId = { ...config.projection, _id: 1 }

    // Get the IDs for this specific batch
    const startIndex = (page - 1) * batchSize
    const endIndex = startIndex + batchSize
    const batchIds = allIds.slice(startIndex, endIndex)

    if (batchIds.length === 0) {
      // eslint-disable-next-line no-console
      console.log(`⚠️  No more IDs to process in batch ${page}`)
      return []
    }

    const batch = await collection
      .find({ _id: { $in: batchIds } })
      .project(projectionWithId)
      .sort(config.sort)
      .toArray()

    // eslint-disable-next-line no-console
    console.log(`📄 Processing ${batch.length} ${contentType}s for sync...`)

    const compressedBatch = batch
      .map((item) => {
        const compressedItem = config.middleware(item, { db })
        const size = Buffer.byteLength(JSON.stringify(compressedItem), 'utf8')

        if (size > 10000) {
          // eslint-disable-next-line no-console
          console.log(
            `⚠️  Further compression needed for ${contentType} "${
              item.headline || item.name
            }" - size exceeds 10KB (${size - 10000} bytes too large)`
          )

          // For articles, compress the body field
          if (contentType === 'article' && compressedItem.body) {
            const bytesTooLarge = size - 10000
            const hyperCompressedBody = compressedItem.body.slice(
              0,
              compressedItem.body.length - bytesTooLarge
            )
            compressedItem.body = hyperCompressedBody

            const newSize = Buffer.byteLength(
              JSON.stringify(compressedItem),
              'utf8'
            )
            // eslint-disable-next-line no-console
            console.log(
              `✅ ${contentType} "${
                item.headline || item.name
              }" - size now ${newSize} bytes - ${bytesTooLarge} bytes removed`
            )

            if (newSize > 10000) {
              // eslint-disable-next-line no-console
              console.log(
                `❌ ${contentType} "${
                  item.headline || item.name
                }" - size still exceeds 10KB after hyper compression - skipping`
              )
              return null
            }
          } else {
            // For non-article content types, skip if too large
            // eslint-disable-next-line no-console
            console.log(
              `❌ ${contentType} "${item.name}" - size exceeds 10KB - skipping`
            )
            return null
          }
        }

        return compressedItem
      })
      .filter(Boolean)

    if (compressedBatch.length === 0) {
      // eslint-disable-next-line no-console
      console.log(
        `⚠️  No ${contentType}s in this batch were suitable for upload`
      )
      return []
    }

    // eslint-disable-next-line no-console
    console.log(
      `📥 Uploading ${compressedBatch.length} ${contentType}s to Algolia...`
    )

    await algoliaClient.saveObjects({
      indexName: config.indexName,
      objects: compressedBatch
    })

    // Update algoliaLastCrawlDate for successfully processed items
    const processedIds = compressedBatch.map((item) => item._id)
    const updateResult = await collection.updateMany(
      { _id: { $in: processedIds } },
      { $set: { algoliaLastCrawlDate: new Date() } }
    )

    // eslint-disable-next-line no-console
    console.log(
      `📝 Updated algoliaLastCrawlDate for ${updateResult.modifiedCount} ${contentType}s`
    )

    const duration = ((Date.now() - startTime) / 1000).toFixed(2)
    // eslint-disable-next-line no-console
    console.log(`✅ Sync batch ${page} completed in ${duration}s`)
    // eslint-disable-next-line no-console
    console.log(`📈 Progress: ${((page / numBatches) * 100).toFixed(1)}%`)

    return processedIds
  }

  let totalProcessed = 0
  while (page <= numBatches) {
    const processedIds = await syncBatch(page)
    totalProcessed += processedIds.length

    if (page < numBatches) {
      // eslint-disable-next-line no-console
      console.log(`😴 Sleeping for 1 second to avoid rate limits...`)
      await sleep(1000)
    }
    page += 1
  }

  // eslint-disable-next-line no-console
  console.log(
    `\n🎉 ${contentType} sync completed! Processed ${totalProcessed} items.`
  )
}

async function syncTask(db, collections) {
  const algoliaClient = initAlgoliaClient()

  // eslint-disable-next-line no-console
  console.log('🔄 Starting sync with Algolia...')

  for (const contentType of collections) {
    try {
      // eslint-disable-next-line no-console
      console.log(`\n📋 Syncing ${contentType}s...`)
      await syncContentType(db, contentType, algoliaClient)
    } catch (error) {
      const errorMsg = `Failed to sync ${contentType}: ${error.message}`
      errors.push(errorMsg)
      // eslint-disable-next-line no-console
      console.error(`❌ ${errorMsg}`)
    }
  }
}

// Check what needs syncing for a specific content type
async function checkContentType(db, contentType) {
  const config = contentTypeConfigs[contentType]
  if (!config) {
    throw new Error(`Unknown content type: ${contentType}`)
  }

  const collection = await db.collection(contentType)

  // Build sync query - only documents that need syncing
  const syncQuery = {
    ...config.query,
    $or: [
      // Documents that have never been crawled
      { algoliaLastCrawlDate: { $exists: false } },
      // Documents that have been modified since last crawl
      { $expr: { $gt: ['$modifiedDate', '$algoliaLastCrawlDate'] } }
    ]
  }

  // Get counts for different categories
  const totalCount = await collection.countDocuments(config.query)
  const needsSyncCount = await collection.countDocuments(syncQuery)
  const neverCrawledCount = await collection.countDocuments({
    ...config.query,
    algoliaLastCrawlDate: { $exists: false }
  })
  const modifiedSinceLastCrawlCount = await collection.countDocuments({
    ...config.query,
    algoliaLastCrawlDate: { $exists: true },
    $expr: { $gt: ['$modifiedDate', '$algoliaLastCrawlDate'] }
  })
  const upToDateCount = totalCount - needsSyncCount

  // Get some sample documents that need syncing
  const sampleNeedingSync = await collection
    .find(syncQuery)
    .project({
      _id: 1,
      headline: 1,
      name: 1,
      modifiedDate: 1,
      algoliaLastCrawlDate: 1
    })
    .limit(5)
    .toArray()

  // Get oldest and newest documents needing sync
  let oldestNeedingSync = null
  let newestNeedingSync = null

  if (needsSyncCount > 0) {
    const oldestResult = await collection
      .find(syncQuery)
      .project({ _id: 1, headline: 1, name: 1, modifiedDate: 1 })
      .sort({ modifiedDate: 1 })
      .limit(1)
      .toArray()

    const newestResult = await collection
      .find(syncQuery)
      .project({ _id: 1, headline: 1, name: 1, modifiedDate: 1 })
      .sort({ modifiedDate: -1 })
      .limit(1)
      .toArray()

    oldestNeedingSync = oldestResult[0]
    newestNeedingSync = newestResult[0]
  }

  // Calculate estimated processing time (based on 100 items per batch + 1s delay)
  const estimatedBatches = Math.ceil(needsSyncCount / 100)
  const estimatedTimeMinutes =
    estimatedBatches > 1 ? (estimatedBatches - 1) / 60 : 0

  return {
    contentType,
    totalCount,
    needsSyncCount,
    neverCrawledCount,
    modifiedSinceLastCrawlCount,
    upToDateCount,
    sampleNeedingSync,
    oldestNeedingSync,
    newestNeedingSync,
    estimatedBatches,
    estimatedTimeMinutes
  }
}

async function checkForChangesTask(db, collections) {
  // eslint-disable-next-line no-console
  console.log('🔍 Checking for changes that need syncing to Algolia...\n')

  const results = []
  let totalNeedingSync = 0
  let totalEstimatedTime = 0

  for (const contentType of collections) {
    try {
      // eslint-disable-next-line no-console
      console.log(`📋 Analyzing ${contentType}s...`)
      const result = await checkContentType(db, contentType)
      results.push(result)
      totalNeedingSync += result.needsSyncCount
      totalEstimatedTime += result.estimatedTimeMinutes

      // eslint-disable-next-line no-console
      console.log(`📊 ${contentType.toUpperCase()} SUMMARY:`)
      // eslint-disable-next-line no-console
      console.log(`   Total ${contentType}s: ${result.totalCount}`)
      // eslint-disable-next-line no-console
      console.log(`   ✅ Up to date: ${result.upToDateCount}`)
      // eslint-disable-next-line no-console
      console.log(`   🔄 Need syncing: ${result.needsSyncCount}`)
      // eslint-disable-next-line no-console
      console.log(`   🆕 Never crawled: ${result.neverCrawledCount}`)
      // eslint-disable-next-line no-console
      console.log(
        `   📝 Modified since last crawl: ${result.modifiedSinceLastCrawlCount}`
      )

      if (result.needsSyncCount > 0) {
        // eslint-disable-next-line no-console
        console.log(
          `   ⏱️  Estimated sync time: ${result.estimatedTimeMinutes.toFixed(
            1
          )} minutes`
        )
        // eslint-disable-next-line no-console
        console.log(`   📦 Estimated batches: ${result.estimatedBatches}`)

        if (result.oldestNeedingSync) {
          // eslint-disable-next-line no-console
          console.log(
            `   📅 Oldest needing sync: "${
              result.oldestNeedingSync.headline || result.oldestNeedingSync.name
            }" (${result.oldestNeedingSync.modifiedDate?.toISOString()})`
          )
        }

        if (result.newestNeedingSync) {
          // eslint-disable-next-line no-console
          console.log(
            `   🆕 Newest needing sync: "${
              result.newestNeedingSync.headline || result.newestNeedingSync.name
            }" (${result.newestNeedingSync.modifiedDate?.toISOString()})`
          )
        }

        if (result.sampleNeedingSync.length > 0) {
          // eslint-disable-next-line no-console
          console.log(`   📝 Sample items needing sync:`)
          result.sampleNeedingSync.forEach((item, index) => {
            const lastCrawled = item.algoliaLastCrawlDate
              ? item.algoliaLastCrawlDate.toISOString()
              : 'Never'
            // eslint-disable-next-line no-console
            console.log(
              `      ${index + 1}. "${
                item.headline || item.name
              }" (Last crawled: ${lastCrawled})`
            )
          })
        }
      } else {
        // eslint-disable-next-line no-console
        console.log(`   ✨ All ${contentType}s are up to date!`)
      }

      // eslint-disable-next-line no-console
      console.log('')
    } catch (error) {
      const errorMsg = `Failed to check ${contentType}: ${error.message}`
      errors.push(errorMsg)
      // eslint-disable-next-line no-console
      console.error(`❌ ${errorMsg}`)
    }
  }

  // Overall summary
  // eslint-disable-next-line no-console
  console.log('🎯 OVERALL SUMMARY:')
  // eslint-disable-next-line no-console
  console.log(`   Total items needing sync: ${totalNeedingSync}`)
  // eslint-disable-next-line no-console
  console.log(
    `   Total estimated sync time: ${totalEstimatedTime.toFixed(1)} minutes`
  )

  if (totalNeedingSync > 0) {
    // eslint-disable-next-line no-console
    console.log(`\n💡 To sync these changes, run:`)
    // eslint-disable-next-line no-console
    console.log(
      `   node components/worker/tasks/algolia/task.js --mode=sync --collections=${collections.join(
        ','
      )}`
    )
  } else {
    // eslint-disable-next-line no-console
    console.log(`\n✨ Everything is up to date! No sync needed.`)
  }
}

// Main function
async function main() {
  // eslint-disable-next-line no-console
  console.log('🚀 Starting main process...')
  let mongoClient
  const { mode, collections } = parseArgs()

  try {
    // eslint-disable-next-line no-console
    console.log('🔄 Connecting to MongoDB...')
    const connectionUri =
      process.env.MONGO_URL ||
      process.env.NF_DATABASE_MONGO_SRV ||
      config.databaseUrl
    mongoClient = await MongoClient.connect(connectionUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    })

    const db = mongoClient.db()
    // eslint-disable-next-line no-console
    console.log('✅ Database connected successfully')
    // eslint-disable-next-line no-console

    // eslint-disable-next-line no-console
    console.log(`🔄 Running in ${mode} mode`)
    switch (mode) {
      case 'import':
        await importTask(db, collections)
        break
      case 'sync':
        await syncTask(db, collections)
        break
      case 'check':
        await checkForChangesTask(db, collections)
        break
      default:
        // eslint-disable-next-line no-console
        console.log('⚠️ Unknown mode specified')
        break
    }

    if (warnings.length > 0) {
      // eslint-disable-next-line no-console
      console.log('📝 Warning Summary:')
      // eslint-disable-next-line no-console
      warnings.forEach((w) => console.log('⚠️', w))
    }

    if (errors.length > 0) {
      // eslint-disable-next-line no-console
      console.log('❌ Error Summary:')
      // eslint-disable-next-line no-console
      errors.forEach((w) => console.log('❌', w))
    }
  } catch (error) {
    console.error('💥 Fatal Error:', error)
  } finally {
    if (mongoClient) {
      // eslint-disable-next-line no-console
      console.log('🔒 Ensuring database connection is closed...')
      await mongoClient.close()
    }
  }
}

const createTask = (serviceLocator) => {
  const task = async (job, cb) => {
    try {
      await main(serviceLocator)
      cb()
    } catch (error) {
      return cb(error)
    }
  }
  return task
}

module.exports = createTask

// Allow direct execution from command line
if (require.main === module) {
  main().catch(console.error)
}

/** TODO
 * - Currently if you add 1 new item into the scheme, it doesn't go back and update all the previous ones. Easy to do for certain fields, but for example instanceId which is a custom field not from their feed, would require a custom migration method
 */
