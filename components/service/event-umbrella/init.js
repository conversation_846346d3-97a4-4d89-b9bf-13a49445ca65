import createService from './service'

const init = (serviceLocator, done) => {
  serviceLocator.persistence.register('eventUmbrella')
  serviceLocator.acl.addResource('eventUmbrella', {
    actions: [
      'discover',
      'create',
      'read',
      'update',
      'delete',
      'editLayouts',
      'manageLayouts',
      'manageNavigation',
      'manageMobileApp',
      '*'
    ]
  })
  const service = createService(serviceLocator)
  serviceLocator.register('eventUmbrellaService', service)
  serviceLocator.register('createEventUmbrellaService', (options) => {
    options.uncached = true
    return createService(serviceLocator, options)
  })
  done()
}

module.exports = () => ({
  eventUmbrellaService: [
    'database',
    'acl',
    'sectionService',
    'eventService',
    init
  ]
})
