const request = require('request')

const getUserAccessList = (serviceLocator) => async (
  pianoUserId,
  pianoApplicationId,
  pianoApiToken,
  cb = () => {}
) => {
  // Select Piano endpoint according to environment
  const pianoApiUrl =
    serviceLocator.config.piano.apiPath +
    `/publisher/user/access/list?api_token=${pianoApiToken}&aid=${pianoApplicationId}&uid=${pianoUserId}`

  const options = {
    method: 'GET',
    redirect: 'follow'
  }

  request.get(pianoApiUrl, options, (err, res) => {
    if (err) {
      serviceLocator.logger.warn('Piano User Access response:', err.message)
      cb(err, null)
    } else if (res.statusCode === 200) {
      // Note: Piano will return a 200 even if there is an error, e.g. if user does not exist
      serviceLocator.logger.info('Piano User Access response: ', res.statusCode)
      const data = JSON.parse(res.body)
      if (typeof data.accesses !== 'undefined') {
        // <PERSON> responds with a 200, and a user access list
        const userAccessList = data.accesses.map(
          (access) => access.resource.rid
        )
        cb(null, userAccessList)
      } else if (typeof data.message !== 'undefined') {
        // Piano responds with a 200, but an error message, e.g. user ID does not exist
        cb(data.message, null)
      } else {
        // Here be dragons: Piano responds with a 200, but no error message or user access data...
        const message =
          'Piano User Access response: 200 response from Piano, but neither access list nor message in response body'
        cb(message, null)
        serviceLocator.logger.error(message)
      }
    } else {
      serviceLocator.logger.warn(
        'Piano User Access not found for: ',
        pianoUserId
      )
      serviceLocator.logger.info(res.body)
      cb(new Error(res.body.title || 'Unknown error'))
    }
  })
}

module.exports = getUserAccessList
