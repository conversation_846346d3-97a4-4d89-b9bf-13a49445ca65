const schemata = require('@clocklimited/schemata')
const { promisify } = require('util')
const buildImageVariations = require('../../../asset/lib/image-variation-builder')
const {
  createImageSchema
} = require('../../../asset/lib/graphql-image-builder-property')

const imageVariations = [
  {
    name: 'image_768',
    crop: '16:9',
    context: 'Image',
    size: { width: 768 }
  }
]

const createSchema = (serviceLocator) => {
  return schemata({
    name: 'ExecutiveGallery',
    properties: {
      galleryImages: {
        type: createImageSchema(imageVariations, 'ExecutiveGalleryImages'),
        resolve: async (parent, args, context) => {
          const { executiveId } = context
          const executive = await promisify(
            serviceLocator.executiveService.findOne
          )({ _id: executiveId })

          return buildImageVariations(
            serviceLocator.config.darkroom,
            imageVariations,
            executive.galleryImages
          )
        }
      }
    }
  })
}

module.exports = createSchema
