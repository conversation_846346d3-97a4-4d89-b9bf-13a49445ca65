const schemata = require('@clocklimited/schemata')
const createArticleSchema = require('../../../article/graphql-schema')
const { promisify } = require('util')

const createSchema = (serviceLocator) => {
  return schemata({
    name: 'ExecutiveLatestPostsWidget',
    properties: {
      latestPosts: {
        type: schemata.Array(createArticleSchema(serviceLocator)),
        resolve: async (parent, args, context) => {
          const { display, skip = 0 } = parent
          const { executiveId, instanceId, accountId } = context
          const executives = await promisify(
            serviceLocator.executiveService.find
          )({
            _id: executiveId
          })
          const executive = executives?.[0]
          const limit = display || 4
          const filters = {
            instance: instanceId,
            account: accountId
          }
          const options = {
            limit,
            sort: { displayDate: -1 }
          }

          const execArticles =
            (await promisify(serviceLocator.articleService.findPublic)(
              {
                'executives.executive': executiveId,
                ...filters
              },
              {
                ...options,
                skip: skip || 0
              }
            )) || []

          const amountToFetch = limit - execArticles.length
          const amountToSkip = skip
            ? Math.max(skip - execArticles.length, 0)
            : 0
          if (executive && amountToFetch > 0) {
            const companyArticles = amountToFetch
              ? await promisify(serviceLocator.articleService.findPublic)(
                  {
                    'companies.company': executive.companyId,
                    ...filters
                  },
                  {
                    ...options,
                    skip: amountToSkip
                  }
                )
              : []

            return [...execArticles, ...companyArticles]
          }

          return execArticles
        }
      }
    }
  })
}

module.exports = createSchema
