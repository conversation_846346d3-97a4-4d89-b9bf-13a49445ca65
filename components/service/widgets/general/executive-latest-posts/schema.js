const schemata = require('@clocklimited/schemata')
const createBaseWidget = require('../../../widget/schema')

const createSchema = () =>
  schemata({
    name: 'ExecutiveLatestPostsWidget',
    properties: {
      ...createBaseWidget(),
      showTitle: {
        type: Boolean,
        default: true
      },
      skip: {
        type: Number,
        default: 0
      },
      display: {
        type: Number,
        default: 1
      }
    }
  })

module.exports = createSchema
