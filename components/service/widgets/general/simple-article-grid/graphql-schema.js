const schemata = require('@clocklimited/schemata')
const createSchema = require('./schema')
const extendSchemata = require('../../../../../api/lib/schemata-extender')
const createListGraphqlSchema = require('../list/graphql-schema')
const createWidgetSchema = require('../../../widget/widget-schema')
const createGraphqlListWithInstanceFallbackAggregator = require('../../../widget/lib/graphql-list-with-instance-fallback-aggregator')

const createGraphqlSchema = (serviceLocator) => {
  const schema = createSchema(serviceLocator)
  const listGraphqlSchema = createListGraphqlSchema(
    serviceLocator,
    'SimpleArticleGridWidget'
  )
  const extendedSchema = extendSchemata(schema, listGraphqlSchema)
  const articlesOverrideSchema = schemata({
    name: 'SimpleArticleGridWidget',
    properties: {
      articles: {
        type: extendedSchema.getProperties().articles.type,
        resolveArgs: extendedSchema.getProperties().articles.resolveArgs,
        resolve: createGraphqlListWithInstanceFallbackAggregator(serviceLocator)
      }
    }
  })
  const finalGraphqlSchema = extendSchemata(
    extendedSchema,
    articlesOverrideSchema
  )

  finalGraphqlSchema.implements = [createWidgetSchema()]
  finalGraphqlSchema.isTypeOf = (item) => item.type === 'simpleArticleGrid'

  return finalGraphqlSchema
}

module.exports = createGraphqlSchema
