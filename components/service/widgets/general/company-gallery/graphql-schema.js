const schemata = require('@clocklimited/schemata')
const { promisify } = require('util')
const buildImageVariations = require('../../../asset/lib/image-variation-builder')
const {
  createImageSchema
} = require('../../../asset/lib/graphql-image-builder-property')

const imageVariations = [
  {
    name: 'image_768',
    crop: '16:9',
    context: 'Image',
    size: { width: 768 }
  }
]

const createSchema = (serviceLocator) => {
  return schemata({
    name: 'CompanyGallery',
    properties: {
      galleryImages: {
        type: createImageSchema(imageVariations, 'CompanyGalleryImages'),
        resolve: async (parent, args, context) => {
          const { companyId } = context
          const company = await promisify(
            serviceLocator.companyService.findOne
          )({ _id: companyId })

          return buildImageVariations(
            serviceLocator.config.darkroom,
            imageVariations,
            company.galleryImages
          )
        }
      }
    }
  })
}

module.exports = createSchema
