const schemata = require('@clocklimited/schemata')
const createGraphqlImageBuilderProperty = require('../../../asset/lib/graphql-image-builder-property')
const { createSpeakerField } = require('../../../event/lib/graphql-fields')
const {
  createEventButtonSchema,
  createEventButtonGroupResolver
} = require('../../../event/lib/schema-creators')

const createSchema = (serviceLocator) => {
  const imageVariations = [
    {
      name: 'desktop_background_72x17_720',
      crop: '72:17',
      context: 'Desktop Background',
      size: { width: 720 }
    },
    {
      name: 'desktop_background_72x17_1440',
      crop: '72:17',
      context: 'Desktop Background',
      size: { width: 1440 }
    },
    {
      name: 'mobile_background_72x17_320',
      crop: '72:17',
      context: 'Mobile Background',
      size: { width: 320 }
    },
    {
      name: 'mobile_background_72x17_640',
      crop: '72:17',
      context: 'Mobile Background',
      size: { width: 640 }
    },
    {
      name: 'mobile_background_72x17_640',
      crop: '72:17',
      context: 'Mobile Background',
      size: { width: 640 }
    },
    {
      name: 'headshot_1x1_320',
      crop: '1x1',
      context: 'Headshot',
      size: { width: 320 }
    }
  ]

  return schemata({
    name: 'EventFeaturedTestimonialWidget',
    properties: {
      buttonGroup: {
        type: schemata.Array(createEventButtonSchema()),
        resolve: createEventButtonGroupResolver(serviceLocator)
      },
      images: createGraphqlImageBuilderProperty(
        serviceLocator,
        imageVariations,
        'EventFeaturedTestimonialWidget'
      ),
      speaker: createSpeakerField(serviceLocator)
    }
  })
}

module.exports = createSchema
