const schemata = require('@clocklimited/schemata')
const createBaseWidget = require('../../../widget/schema')
const createSlideSchema = require('./slide-schema')

const createSchema = () =>
  schemata({
    name: 'FullWidthMagPromoCarousel',
    properties: {
      ...createBaseWidget(),
      slides: {
        type: schemata.Array(createSlideSchema())
      }
    }
  })

module.exports = createSchema
