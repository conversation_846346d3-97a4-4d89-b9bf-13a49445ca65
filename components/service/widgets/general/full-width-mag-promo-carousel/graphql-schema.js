const schemata = require('@clocklimited/schemata')
const createSlideSchema = require('./slide-schema')
const extendSchemata = require('../../../../../api/lib/schemata-extender')
const createGraphqlImageBuilderProperty = require('../../../asset/lib/graphql-image-builder-property')

const createSchema = (serviceLocator) => {
  const createGraphqlSlideSchema = () => {
    const typeName = 'FullWidthMagPromoCarousel_Slide'
    const imageVariations = [
      {
        name: 'logo_640',
        crop: 'Free',
        context: 'Logo',
        size: { width: 640 }
      },
      {
        name: 'main_640',
        crop: 'Free',
        context: 'Main',
        size: { width: 640 }
      },
      {
        name: 'main_768',
        crop: 'Free',
        context: 'Main',
        size: { width: 768 }
      },
      {
        name: 'background_640',
        crop: 'Free',
        context: 'Background',
        size: { width: 640 }
      },
      {
        name: 'background_768',
        crop: 'Free',
        context: 'Background',
        size: { width: 768 }
      },
      {
        name: 'background_992',
        crop: 'Free',
        context: 'Background',
        size: { width: 992 }
      },
      {
        name: 'background_1200',
        crop: 'Free',
        context: 'Background',
        size: { width: 1200 }
      },
      {
        name: 'background_1600',
        crop: 'Free',
        context: 'Background',
        size: { width: 1600 }
      },
      {
        name: 'background_2000',
        crop: 'Free',
        context: 'Background',
        size: { width: 1200 }
      }
    ]

    return schemata({
      name: typeName,
      properties: {
        images: createGraphqlImageBuilderProperty(
          serviceLocator,
          imageVariations,
          typeName
        )
      }
    })
  }

  return schemata({
    name: 'FullWidthMagPromoCarouselWidget',
    properties: {
      slides: {
        type: schemata.Array(
          extendSchemata(createSlideSchema(), createGraphqlSlideSchema())
        )
      }
    }
  })
}

module.exports = createSchema
