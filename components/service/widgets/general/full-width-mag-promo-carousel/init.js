const createSchema = require('./schema')
const createGraphqlSchema = require('./graphql-schema')
const extendSchemata = require('../../../../../api/lib/schemata-extender')

const init = (serviceLocator, done) => {
  const schema = createSchema(serviceLocator)
  const graphqlSchema = extendSchemata(
    schema,
    createGraphqlSchema(serviceLocator)
  )

  serviceLocator.widgetFactory.register('fullWidthMagPromoCarousel', {
    schema,
    graphqlSchema
  })

  done()
}

module.exports = () => ({
  fullWidthMagPromoCarouselWidget: ['widgetService', init]
})
