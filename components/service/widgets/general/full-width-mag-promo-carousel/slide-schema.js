const schemata = require('@clocklimited/schemata')
const createContextValidator = require('validity-cf-image-context-selection')
const createWidgetAreaSchema = require('../../../widget/widget-area-schema')
const createImageWidgetSchema = require('../../../asset/image-schema')
const createCropValidator = require('../../../../../lib/validators/crop-integrity-validator')
const imageConfig = require('../../../../admin/widgets/general/full-width-mag-promo-carousel/image-config.json')
const getContrastColor = require('../../../../../lib/get-contrast-color')

const requiredCrops = imageConfig.crops.map((crop) => crop.name)

const urlValidity = require('validity-url-optional-tlds')()

const urlOrPathValidator = (key, keyDisplayName, object, cb) => {
  if (object[key] && object[key].substr(0, 4) === 'http') {
    return urlValidity(key, keyDisplayName, object, cb)
  }

  return urlPathValidator(key, keyDisplayName, object, cb)
}

const urlPathValidator = (key, keyDisplayName, object, cb) => {
  if (object[key] && object[key].substr(0, 1) !== '/') {
    return cb(null, keyDisplayName + ' should be a valid URL path')
  }

  return cb(null, undefined)
}

const createSchema = () =>
  schemata({
    name: 'FullWidthMagPromoCarousel_Slide',
    properties: {
      images: {
        type: createWidgetAreaSchema(createImageWidgetSchema()),
        defaultValue: () => ({}),
        validators: {
          all: [
            createContextValidator(['Background', 'Logo', 'Main']),
            createCropValidator(requiredCrops)
          ]
        }
      },
      title: {
        type: String,
        resolve: async (parent, _args, _context) => {
          const { title } = parent

          if (title) {
            const convertBracketsToSpans = (text) => {
              const matches = text.match(/\[(.*?)]/g)
              if (!matches) {
                return text
              }

              let result = text
              matches.forEach((match) => {
                const content = match.slice(1, -1)
                result = result.replace(match, `<span>${content}</span>`)
              })

              // Remove any unmatched brackets
              return result.replace(/[[\]]/g, '')
            }

            return convertBracketsToSpans(title)
          }

          return null
        }
      },
      content: {
        type: String
      },
      linkText: {
        type: String
      },
      destination: {
        type: String,
        validators: [urlOrPathValidator]
      },
      opensInNewTab: {
        type: Boolean
      },
      hasBorder: {
        type: Boolean
      },
      primaryColor: {
        type: String
      },
      contrastColor: {
        type: String,
        resolve: async (parent, _args, _context) => {
          const { primaryColor } = parent

          if (primaryColor) {
            return getContrastColor(primaryColor)
          }

          return null
        }
      }
    }
  })

module.exports = createSchema
