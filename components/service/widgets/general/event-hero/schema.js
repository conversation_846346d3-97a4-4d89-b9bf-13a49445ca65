const schemata = require('@clocklimited/schemata')
const createBaseWidget = require('../../../widget/schema')
const {
  createEventButtonSchema
} = require('../../../../service/event/lib/schema-creators')
const {
  config: validationConfig,
  createMaxLengthValidator
} = require('../../../../service/event/validation-config')
const required = require('@clocklimited/validity-required')

const createSchema = () => {
  const name = 'EventHeroWidget'
  return schemata({
    name,
    properties: {
      title: {
        type: String,
        validators: [
          createMaxLengthValidator(validationConfig[name].title, {
            useHtmlConverter: true
          }),
          required
        ]
      },
      subtitle: {
        type: String,
        validators: [
          createMaxLengthValidator(validationConfig[name].subtitle, {
            useHtmlConverter: true
          })
        ]
      },
      description: {
        type: String,
        validators: [
          createMaxLengthValidator(validationConfig[name].description, {
            useHtmlConverter: true
          })
        ]
      },
      buttonGroup: {
        type: schemata.Array(createEventButtonSchema()),
        defaultValue: () => []
      },
      theme: {
        type: String,
        defaultValue: 'light'
      },
      themeColorOverride: {
        type: String
      },
      useMaxHeight: {
        type: Boolean,
        defaultValue: false
      },
      proxyEventId: {
        type: String
      },
      parallax: {
        type: Boolean,
        defaultValue: false
      },
      backgroundVideoYoutubeId: {
        type: String
      },
      useAsHeading: {
        type: Boolean,
        defaultValue: false
      },
      hideEventLogo: {
        type: Boolean,
        defaultValue: false
      },
      align: {
        type: String,
        defaultValue: 'center'
      },
      sponsorId: {
        type: String
      },
      hasBigButton: {
        type: Boolean,
        defaultValue: false
      },
      isTitleVisuallyHidden: {
        type: Boolean,
        defaultValue: false
      },
      ...createBaseWidget()
    }
  })
}

module.exports = createSchema
