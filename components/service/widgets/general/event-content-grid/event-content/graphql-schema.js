const schemata = require('@clocklimited/schemata')
const {
  createImageSchema
} = require('../../../../asset/lib/graphql-image-builder-property')
const buildImageVariations = require('../../../../asset/lib/image-variation-builder')
const extendSchemata = require('../../../../../../api/lib/schemata-extender')
const createSchema = require('./schema')
const {
  createSpeakerField,
  createSponsorField,
  createAgendaItemField,
  createVideoField,
  createArticleField,
  createEventField,
  createEventUmbrellaField,
  createExtendedSpeakerType
} = require('../../../../event/lib/graphql-fields')
const { promisify } = require('util')
const { imageVariations } = require('../../../../company/graphql-schema')
const createAgendaItemSpeakerSchema = require('../../../../event/sub-content-types/agenda-item/speaker-schema')

const createGraphqlSchema = (serviceLocator) => {
  const schema = createSchema(serviceLocator)
  const typeName = 'GridEventContent'

  const { type: speakerFieldType } = createSpeakerField(serviceLocator)
  const { type: agendaItemFieldType } = createAgendaItemField(serviceLocator)
  const { type: videoFieldType } = createVideoField(serviceLocator)

  const extendedSpeakerType = extendSchemata(
    speakerFieldType,
    schemata({
      name: 'ExtendedSpeaker',
      properties: {
        companyName: {
          type: String,
          resolve: async (parent, _, context) => {
            const companyId = parent.companyId
            if (!companyId) return null
            try {
              const company = await promisify(
                serviceLocator.companyService.read
              )(companyId)
              return company.name
            } catch (e) {
              serviceLocator.logger.error(e)
              return null
            }
          }
        },
        companyImages: {
          type: createImageSchema(imageVariations, 'ExtendedSpeaker'),
          resolve: async (parent) => {
            const companyId = parent.companyId
            if (!companyId) return null
            try {
              const company = await promisify(
                serviceLocator.companyService.read
              )(companyId)
              return buildImageVariations(
                serviceLocator.config.darkroom,
                imageVariations,
                company.images
              )
            } catch (e) {
              serviceLocator.logger.error(e)
              return null
            }
          }
        },
        roles: {
          type: Array,
          resolve: async (parent) => {
            const agendaItems = await promisify(
              serviceLocator.eventService.findAgenda
            )(parent.eventId, {
              'speakers.speakerId': parent._id
            })
            if (!agendaItems.length) return []

            const speakerSessionTypes = []
            for (const item of agendaItems) {
              const sessionType = await promisify(
                serviceLocator.eventService.findSubCollectionItem
              )(parent.eventId, 'sessionTypes', item.sessionTypeKey)
              if (sessionType) {
                speakerSessionTypes.push(sessionType.shortName)
              }
            }

            const dedupedSessionTypes = Array.from(new Set(speakerSessionTypes))
            return dedupedSessionTypes
          }
        },
        nextAgendaItem: {
          type: agendaItemFieldType,
          resolve: async (parent, _, context) => {
            const agendaItems = await promisify(
              serviceLocator.eventService.findAgenda
            )(parent.eventId, {
              'speakers.speakerId': parent._id
            })
            if (!agendaItems.length) return null
            const now = new Date()
            const nextAgendaItem = agendaItems
              .filter((item) => new Date(item.startDate) > now)
              .sort((a, b) => new Date(a.startDate) - new Date(b.startDate))[0]
            if (!nextAgendaItem) return null
            return nextAgendaItem
          }
        },
        nextAgendaItems: {
          type: schemata.Array(agendaItemFieldType),
          resolve: async (parent, _, _context) => {
            const agendaItems = await promisify(
              serviceLocator.eventService.findAgenda
            )(parent.eventId, {
              'speakers.speakerId': parent._id
            })

            if (!agendaItems.length) {
              return null
            }

            const now = new Date()
            const nextAgendaItems = agendaItems
              .filter((item) => new Date(item.startDate) > now)
              .sort((a, b) => new Date(a.startDate) - new Date(b.startDate))

            if (!nextAgendaItems) {
              return null
            }

            return nextAgendaItems
          }
        }
      }
    })
  )

  const agendaItemSpeakerType = schemata({
    name: 'Speaker',
    properties: {
      ...createAgendaItemSpeakerSchema().getProperties(),
      ...createExtendedSpeakerType(serviceLocator).getProperties()
    }
  })

  const extendedAgendaItemType = extendSchemata(
    agendaItemFieldType,
    schemata({
      name: 'ExtendedAgendaItem',
      properties: {
        speakers: {
          type: schemata.Array(agendaItemSpeakerType),
          resolve: async (parent, _, context) => {
            const speakers = []
            for (const speaker of parent.speakers) {
              const speakerId = speaker.speakerId

              try {
                const speakerData = await promisify(
                  serviceLocator.eventService.findSpeaker
                )(parent.eventId, speakerId)
                let company = await promisify(
                  serviceLocator.companyService.read
                )(speakerData.companyId)
                company = company || { name: 'No company' }
                speakers.push({
                  ...speakerData,
                  name: speakerData.name,
                  companyName: company.name
                })
              } catch (e) {
                serviceLocator.logger.error(e, speakerId)
              }
            }
            return speakers
          }
        }
      }
    })
  )

  const extendedVideoType = extendSchemata(
    videoFieldType,
    schemata({
      name: 'EventVideo',
      properties: {
        eventName: {
          type: String
        },
        fullUrlPath: {
          type: String
        },
        agendaItemSessionType: {
          type: String
        },
        previewId: {
          type: String
        }
      }
    })
  )

  const graphqlSchema = schemata({
    name: typeName,
    properties: {
      sponsor: createSponsorField(serviceLocator),
      speaker: {
        type: extendedSpeakerType,
        resolve: async (parent, _, context) => {
          if (!parent.speakerId) return null
          try {
            const id = context?.event?._id
            const speaker = await promisify(
              serviceLocator.eventService.findSpeaker
            )(id, parent.speakerId)
            return speaker
          } catch (e) {
            serviceLocator.logger.error(e)
            return null
          }
        }
      },
      agendaItem: {
        type: extendedAgendaItemType,
        resolve: async (parent, _, context) => {
          if (!parent.agendaItemId) return null
          try {
            const id = context?.event?._id
            const agendaItem = await promisify(
              serviceLocator.eventService.findAgendaItem
            )(id, parent.agendaItemId)
            return agendaItem
          } catch (e) {
            serviceLocator.logger.error(e)
            return null
          }
        }
      },
      article: createArticleField(serviceLocator),
      video: {
        type: extendedVideoType,
        resolve: async (parent, _, context) => {
          let video
          if (!parent.videoId) return null
          try {
            const query = { _id: parent.videoId }
            video = await promisify(serviceLocator.eventVideoService.findOne)(
              query
            )
            const event = await promisify(serviceLocator.eventService.findOne)({
              _id: video.eventId
            })
            let eventUmbrella
            if (event) {
              eventUmbrella = await promisify(
                serviceLocator.eventUmbrellaService.findOne
              )({ _id: event.eventUmbrellaId })
            }

            const queryString = context.resourceUrl.includes('?')
              ? context.resourceUrl.substring(
                  context.resourceUrl.indexOf('?') + 1
                )
              : ''
            const searchParams = new URLSearchParams(queryString)
            const previewId = searchParams.get('previewId')
            if (
              previewId === event?.previewId ||
              previewId === eventUmbrella?.previewId
            ) {
              video.previewId = eventUmbrella ? eventUmbrella.previewId : null
            }

            video.eventName = event ? event.name : 'BizClik Event'
            video.agendaItemSessionType = 'On Demand'
            // http://{account.domain}{instance.subdomain}/events/{eventUmbrella.slug}{event.slug}/on-demand/{video.slug}

            if (video.agendaItemId) {
              const agendaItem = await promisify(
                serviceLocator.eventService.findAgendaItem
              )(video.eventId, video.agendaItemId)

              if (agendaItem) {
                const sessionType = await promisify(
                  serviceLocator.eventService.findSubCollectionItem
                )(agendaItem.eventId, 'sessionTypes', agendaItem.sessionTypeKey)

                if (sessionType) {
                  video.agendaItemSessionType = sessionType.name
                }
              }
            }

            if (video.eventId !== context?.event?._id && event) {
              const account = await promisify(
                serviceLocator.accountService.findOne
              )({ _id: event.account })
              if (!account) return video
              const eventUmbrella = await promisify(
                serviceLocator.eventUmbrellaService.findOne
              )({ _id: event.eventUmbrellaId })
              if (!eventUmbrella) return video
              const instance = await promisify(
                serviceLocator.instanceService.findOne
              )({ _id: eventUmbrella.primaryInstance })
              if (!instance) return video

              if (!account || !eventUmbrella) return video
              if (account.domain) {
                video.fullUrlPath = `http://${instance.subdomain}.${account.domain}/events/${eventUmbrella.slug}/on-demand/${video.slug}`
              } else {
                video.fullUrlPath = `http://${instance.subdomain}/events/${eventUmbrella.slug}/on-demand/${video.slug}`
              }
            } else {
              const eventUmbrella = await promisify(
                serviceLocator.eventUmbrellaService.findOne
              )({ _id: context.event.eventUmbrellaId })
              video.fullUrlPath =
                'events/' + eventUmbrella.slug + '/on-demand/' + video.slug
            }
            return video
          } catch (e) {
            serviceLocator.logger.error(e)
            return null
          }
        }
      },
      event: createEventField(serviceLocator),
      eventUmbrella: createEventUmbrellaField(serviceLocator)
    }
  })
  const extendedSchema = extendSchemata(schema, graphqlSchema)
  return extendedSchema
}

module.exports = createGraphqlSchema
