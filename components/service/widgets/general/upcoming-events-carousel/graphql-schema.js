const { promisify } = require('util')
const schemata = require('@clocklimited/schemata')
const createSponsorSchema = require('../../../event/sub-content-types/sponsor/graphql-schema')
const createEventSchema = require('../../../event/graphql-schema')
const extendSchemata = require('../../../../../api/lib/schemata-extender')
const {
  createEventButtonSchema,
  createEventButtonResolver
} = require('../../../event/lib/schema-creators')
const slugg = require('slugg')
const createDarkroomUrlBuilder = require('@clocklimited/darkroom-url-builder')

const createUrlBuilder = (serviceLocator) =>
  createDarkroomUrlBuilder(
    serviceLocator.config.darkroom.url,
    serviceLocator.config.darkroom.salt
  )

const eventSchema = (serviceLocator) => {
  const urlBuilder = createUrlBuilder(serviceLocator)

  return extendSchemata(
    createEventSchema(serviceLocator),
    schemata({
      name: 'UpcomingEventsCarousel_Event',
      properties: {
        buttonGroup: {
          type: schemata.Array(createEventButtonSchema()),
          resolve: async (parent, _args) => {
            const { layout: layouts } = await promisify(
              serviceLocator.eventService.findLayoutBySlug
            )(parent._id, '')

            const buttons = []

            if (layouts && layouts.length) {
              // eslint-disable-next-line no-labels
              layoutLoop: for (const layout of layouts) {
                for (const cols of layout.cols) {
                  const { widgets } = cols.widgetArea

                  for (const widget of widgets) {
                    if ('buttonGroup' in widget && widget.buttonGroup.length) {
                      let layout
                      if (!widget.buttonGroup) {
                        serviceLocator.logger.error(
                          'No button group found for ',
                          widget
                        )
                        return null
                      }

                      const resolveButton = createEventButtonResolver(
                        serviceLocator
                      )

                      const embellishedButtonGroup = widget.buttonGroup.map(
                        async (button) => await resolveButton(layout, button)
                      )

                      const resolvedButtonGroup = await Promise.all(
                        embellishedButtonGroup
                      )

                      buttons.push(...resolvedButtonGroup)
                      // eslint-disable-next-line no-labels
                      break layoutLoop
                    }
                  }
                }
              }
            }

            return buttons
          }
        },
        backgroundVideoId: {
          type: String,
          resolve: async (parent, _args) => {
            const { layout: layouts } = await promisify(
              serviceLocator.eventService.findLayoutBySlug
            )(parent._id, '')

            let videoId = null

            if (layouts && layouts.length) {
              // eslint-disable-next-line no-labels
              layoutLoop: for (const layout of layouts) {
                for (const cols of layout.cols) {
                  const { widgets } = cols.widgetArea

                  for (const widget of widgets) {
                    if (
                      'backgroundVideoYoutubeId' in widget &&
                      widget.backgroundVideoYoutubeId &&
                      widget.backgroundVideoYoutubeId.length
                    ) {
                      videoId = widget.backgroundVideoYoutubeId
                      // eslint-disable-next-line no-labels
                      break layoutLoop
                    }
                  }
                }
              }
            }

            return videoId
          }
        },
        sponsors: {
          type: schemata.Array(createSponsorSchema(serviceLocator)),
          resolve: async (parent) => {
            return await promisify(serviceLocator.eventService.findSponsors)(
              parent._id
            )
          }
        },
        darkLogoUrl: {
          type: String,
          resolve: async (parent, _args, _context) => {
            if (parent?.darkLogo) {
              return urlBuilder()
                .resource(parent.darkLogo)
                .filename(slugg(parent.name) + '-dark-logo.png')
                ?.url()
            }

            return null
          }
        },
        lightLogoUrl: {
          type: String,
          resolve: async (parent, _args, _context) => {
            if (parent?.lightLogo) {
              return urlBuilder()
                .resource(parent.lightLogo)
                .filename(slugg(parent.name) + '-light-logo.png')
                ?.url()
            }

            return null
          }
        }
      }
    })
  )
}

const createSchema = (serviceLocator) => {
  const urlBuilder = createUrlBuilder(serviceLocator)

  return schemata({
    name: 'UpcomingEventsCarouselWidget',
    properties: {
      overArchingEventLightLogoUrl: {
        type: String,
        resolve: async (parent, args, context) => {
          const eventUmbrella = await promisify(
            serviceLocator.eventUmbrellaService.findOne
          )({
            $or: [
              { primaryInstance: context.instanceId },
              { secondaryInstances: { $in: [context.instanceId] } }
            ]
          })

          if (eventUmbrella && eventUmbrella?.lightLogo) {
            return urlBuilder()
              .resource(eventUmbrella.lightLogo)
              .filename(
                slugg(eventUmbrella?.name || 'overarching-event') +
                  '-light-logo.png'
              )
              ?.url()
          }

          return null
        }
      },
      overArchingEventDarkLogoUrl: {
        type: String,
        resolve: async (parent, args, context) => {
          const eventUmbrella = await promisify(
            serviceLocator.eventUmbrellaService.findOne
          )({
            $or: [
              { primaryInstance: context.instanceId },
              { secondaryInstances: { $in: [context.instanceId] } }
            ]
          })

          if (eventUmbrella && eventUmbrella?.darkLogo) {
            return urlBuilder()
              .resource(eventUmbrella?.darkLogo)
              .filename(
                slugg(eventUmbrella?.name || 'overarching-event') +
                  '-dark-logo.png'
              )
              ?.url()
          }

          return null
        }
      },
      events: {
        type: schemata.Array(eventSchema(serviceLocator)),
        resolve: async (parent, args, context) => {
          const eventUmbrella = await promisify(
            serviceLocator.eventUmbrellaService.findOne
          )({
            $or: [
              { primaryInstance: context.instanceId },
              { secondaryInstances: { $in: [context.instanceId] } }
            ]
          })

          if (!eventUmbrella) {
            return []
          }

          const { _id } = eventUmbrella

          const query = {
            eventUmbrellaId: _id,
            startDate: { $gte: new Date() },
            state: 'Published'
          }

          const options = {
            sort: { startDate: 1 }
          }

          return await promisify(serviceLocator.eventService.find)(
            query,
            options
          )
        }
      }
    }
  })
}

module.exports = createSchema
