const schemata = require('@clocklimited/schemata')
const createBaseWidget = require('../../../widget/schema')
const createArticleSchema = require('../../../article/graphql-schema')
const { promisify } = require('util')

const createSchema = (serviceLocator) => {
  const articleSchema = createArticleSchema()
  articleSchema.type = 'CompanyFeaturedArticlesWidget_Article'
  return schemata({
    name: 'CompanyFeaturedArticlesWidget',
    properties: {
      ...createBaseWidget(),
      featuredArticles: {
        type: schemata.Array(articleSchema),
        default: () => [],
        resolve: async (parent, args, context) => {
          const { companyId } = context
          const query = {
            instance: context.instance._id,
            state: 'Published',
            companies: {
              $elemMatch: {
                company: companyId,
                isFeaturedArticle: true
              }
            }
          }
          return await promisify(serviceLocator.articleService.findPublic)(
            query,
            {
              skip: parent.skip || 0,
              limit: parent.display || 1,
              sort: { displayDate: -1 }
            }
          )
        }
      }
    }
  })
}

module.exports = createSchema
