const schemata = require('@clocklimited/schemata')
const validateLength = require('validity-length')
const validateInteger = require('@clocklimited/validity-integer')
const validateIfSet = require('@clocklimited/validity-validate-if-set')
const createBaseWidget = require('../../../widget/schema')

module.exports = () =>
  schemata({
    name: 'CarouselWidget',
    properties: {
      lists: {
        type: Array,
        validators: [validateLength(1)]
      },
      dedupe: {
        type: Boolean,
        defaultValue: true
      },
      limit: {
        type: Number,
        validators: [validateIfSet(validateInteger)]
      },
      itemsPerRow: {
        type: Number
      },
      fallbackInstances: {
        type: Array,
        defaultValue: () => []
      },
      ...createBaseWidget()
    }
  })
