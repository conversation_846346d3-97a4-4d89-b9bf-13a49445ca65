const schemata = require('@clocklimited/schemata')
const createSchema = require('./schema')
const createListGraphqlSchema = require('../list/graphql-schema')
const extendSchemata = require('../../../../../api/lib/schemata-extender')
const createGraphqlListWithInstanceFallbackAggregator = require('../../../widget/lib/graphql-list-with-instance-fallback-aggregator')

const init = (serviceLocator, done) => {
  const schema = createSchema(serviceLocator)
  const listGraphqlSchema = createListGraphqlSchema(serviceLocator)
  const graphqlSchema = extendSchemata(listGraphqlSchema, schema)
  const articlesOverrideSchema = schemata({
    name: 'CarouselWidget',
    properties: {
      articles: {
        type: graphqlSchema.getProperties().articles.type,
        resolveArgs: graphqlSchema.getProperties().articles.resolveArgs,
        resolve: createGraphqlListWithInstanceFallbackAggregator(serviceLocator)
      }
    }
  })
  const finalGraphqlSchema = extendSchemata(
    graphqlSchema,
    articlesOverrideSchema
  )
  serviceLocator.widgetFactory.register('carousel', {
    schema,
    graphqlSchema: finalGraphqlSchema
  })
  done()
}

module.exports = () => ({ carouselWidget: ['widgetService', init] })
