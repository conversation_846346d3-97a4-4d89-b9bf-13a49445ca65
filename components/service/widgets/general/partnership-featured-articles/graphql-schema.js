const schemata = require('@clocklimited/schemata')
const createBaseWidget = require('../../../widget/schema')
const createArticleSchema = require('../../../article/graphql-schema')
const { promisify } = require('util')

const createSchema = (serviceLocator) => {
  const articleSchema = createArticleSchema()
  articleSchema.type = 'PartnershipsFeaturedArticlesWidget_Article'
  return schemata({
    name: 'PartnershipFeaturedArticlesWidget',
    properties: {
      ...createBaseWidget(),
      featuredArticles: {
        type: schemata.Array(articleSchema),
        default: () => [],
        resolve: async (parent, args, context) => {
          const { partnershipId } = context
          const query = {
            instance: context.instance._id,
            state: 'Published',
            'partnerships.partnership': partnershipId
          }
          return await promisify(serviceLocator.articleService.findPublic)(
            query,
            {
              skip: parent.skip || 0,
              limit: parent.display || 1,
              sort: { displayDate: -1 }
            }
          )
        }
      }
    }
  })
}

module.exports = createSchema
