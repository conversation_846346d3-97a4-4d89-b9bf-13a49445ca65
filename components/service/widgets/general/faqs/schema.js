const schemata = require('@clocklimited/schemata')
const createBaseWidget = require('../../../widget/schema')
const createFaqSchema = require('./faq-item-schema')

const createSchema = () =>
  schemata({
    name: 'FaqsWidget',
    properties: {
      ...createBaseWidget(),
      title: {
        type: String
      },
      faqs: {
        type: schemata.Array(createFaqSchema())
      }
    }
  })

module.exports = createSchema
