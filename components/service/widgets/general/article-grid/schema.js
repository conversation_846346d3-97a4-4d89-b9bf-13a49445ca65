const schemata = require('@clocklimited/schemata')
const createBaseWidget = require('../../../widget/schema')
const validateLength = require('validity-length')
const required = require('@clocklimited/validity-required')
const validateIfSet = require('@clocklimited/validity-validate-if-set')
const validateInteger = require('@clocklimited/validity-integer')

module.exports = () =>
  schemata({
    name: 'ArticleGridWidget',
    properties: {
      ...createBaseWidget(),
      displayType: {
        type: String,
        validators: [required]
      },
      lists: {
        type: Array,
        validators: [validateLength(1)]
      },
      dedupe: {
        type: Boolean,
        defaultValue: true
      },
      displayCategory: {
        type: Boolean,
        defaultValue: false
      },
      limit: {
        type: Number,
        validators: [validateIfSet(validateInteger)]
      },
      fallbackInstances: {
        type: Array,
        defaultValue: () => []
      }
    }
  })
