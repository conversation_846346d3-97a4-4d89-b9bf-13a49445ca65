const createSchema = require('./schema')
const extendSchemata = require('../../../../../api/lib/schemata-extender')
const createListGraphqlSchema = require('../list/graphql-schema')
const schemata = require('@clocklimited/schemata')
const createGraphqlListWithInstanceFallbackAggregator = require('../../../widget/lib/graphql-list-with-instance-fallback-aggregator')

const createWidgetSchema = require('../../../widget/widget-schema')

const createGraphqlSchema = (serviceLocator) => {
  const schema = createSchema(serviceLocator)
  const modifyProps = (parent) => {
    const limitMap = { '1': 7, '2': 5, '5': 5, '10': 13, '11': 3 }

    if (parent.displayType === '1') {
      const limit = limitMap[parent.displayType]

      return { ...parent, limit }
    }

    const limit = limitMap[parent.displayType]
      ? Math.min(limitMap[parent.displayType], parent.limit || Infinity)
      : parent.limit

    return { ...parent, limit }
  }
  const listGraphqlSchema = createListGraphqlSchema(
    serviceLocator,
    'ArticleGridWidget',
    modifyProps
  )
  const extendedSchema = extendSchemata(schema, listGraphqlSchema)

  // Override the articles resolver to support fallback instances
  const articlesOverrideSchema = schemata({
    name: 'ArticleGridWidget',
    properties: {
      articles: {
        type: extendedSchema.getProperties().articles.type,
        resolveArgs: extendedSchema.getProperties().articles.resolveArgs,
        resolve: createGraphqlListWithInstanceFallbackAggregator(
          serviceLocator,
          { modifyProps }
        )
      }
    }
  })

  const finalSchema = extendSchemata(extendedSchema, articlesOverrideSchema)

  finalSchema.implements = [createWidgetSchema()]
  finalSchema.isTypeOf = (item) => item.type === 'articleGrid'

  return finalSchema
}

module.exports = createGraphqlSchema
