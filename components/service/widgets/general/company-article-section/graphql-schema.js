const schemata = require('@clocklimited/schemata')
const createBaseWidget = require('../../../widget/schema')
const createArticleSchema = require('../../../article/graphql-schema')
const { promisify } = require('util')

const createSchema = (serviceLocator) => {
  const articleSchema = createArticleSchema()
  articleSchema.type = 'CompanyArticleSectionWidget_Article'
  return schemata({
    name: 'CompanyArticleSectionWidget',
    properties: {
      ...createBaseWidget(),
      items: {
        type: schemata.Array(articleSchema),
        default: () => [],
        resolve: async (parent, args, context) => {
          const { companyId } = context
          const query = {
            instance: context.instance._id,
            state: 'Published',
            contentType: parent.articleType || 'Article',
            companies: {
              $elemMatch: {
                company: companyId,
                $or: [
                  { isFeaturedArticle: false },
                  { isFeaturedArticle: { $exists: false } }
                ]
              }
            }
          }

          const results = await promisify(
            serviceLocator.articleService.findPublic
          )(query, {
            limit: parent.display || 6,
            sort: { displayDate: -1 }
          })

          return results
        }
      }
    }
  })
}

module.exports = createSchema
