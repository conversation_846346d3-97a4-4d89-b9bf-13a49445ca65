import crudService from 'crud-service'
import cachedCrudService from '../lib/cached-crud-service'
import createSchema from './schema'
import textSearch from '@clocklimited/cf-text-search'
import createYouTubeCacheManager from './lib/youtube-cache-manager'

export default (serviceLocator) => {
  const save = serviceLocator.persistence('eventVideo')
  const schema = createSchema(save.findOne)
  const service = crudService('EventVideo', save, schema, {})
  const youtubeCache = createYouTubeCacheManager(serviceLocator)

  service.read = save.read
  service.find = save.find
  service.findOne = save.findOne
  service.search = textSearch(service)

  // Add method to get YouTube API statistics
  service.getYouTubeApiStats = (callback) => {
    service.find({}, (err, videos) => {
      if (err) return callback(err)

      const stats = {
        totalVideos: videos.length,
        videosWithMetadata: 0,
        totalSuccesses: 0,
        totalFailures: 0,
        totalAttempts: 0,
        averageSuccessRate: 0,
        videosWithConsecutiveFailures: 0,
        maxConsecutiveFailures: 0,
        recentFailures: [],
        slowestResponses: [],
        fastestResponses: []
      }

      const responseTimes = []
      const recentFailureThreshold = new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours

      videos.forEach((video) => {
        if (video.cachedYoutubeDetailsMeta) {
          stats.videosWithMetadata++
          const meta = video.cachedYoutubeDetailsMeta

          stats.totalSuccesses += meta.successCount || 0
          stats.totalFailures += meta.failureCount || 0
          stats.totalAttempts += meta.totalAttempts || 0

          if (meta.consecutiveFailures > 0) {
            stats.videosWithConsecutiveFailures++
          }

          if (meta.consecutiveFailures > stats.maxConsecutiveFailures) {
            stats.maxConsecutiveFailures = meta.consecutiveFailures
          }

          // Track recent failures
          if (
            meta.lastFailureDate &&
            new Date(meta.lastFailureDate) > recentFailureThreshold
          ) {
            stats.recentFailures.push({
              youtubeId: video.youtubeId,
              videoName: video.name,
              lastFailureDate: meta.lastFailureDate,
              lastErrorMessage: meta.lastErrorMessage,
              consecutiveFailures: meta.consecutiveFailures
            })
          }

          // Track response times
          if (meta.lastResponseTime) {
            responseTimes.push({
              youtubeId: video.youtubeId,
              responseTime: meta.lastResponseTime
            })
          }
        }
      })

      // Calculate success rate
      if (stats.totalAttempts > 0) {
        stats.averageSuccessRate = (
          (stats.totalSuccesses / stats.totalAttempts) *
          100
        ).toFixed(2)
      }

      // Sort and get top 10 slowest/fastest responses
      responseTimes.sort((a, b) => b.responseTime - a.responseTime)
      stats.slowestResponses = responseTimes.slice(0, 10)
      stats.fastestResponses = responseTimes.slice(-10).reverse()

      // Sort recent failures by consecutive failures (most problematic first)
      stats.recentFailures.sort(
        (a, b) => b.consecutiveFailures - a.consecutiveFailures
      )

      callback(null, stats)
    })
  }

  // Cache YouTube details on create
  service.pre('create', (data, cb) => {
    if (data.youtubeId) {
      serviceLocator.logger.info(
        `Caching YouTube details for new video: ${data.youtubeId}`
      )
      const currentMeta = data.cachedYoutubeDetailsMeta || {}
      youtubeCache.fetchAndCacheYouTubeDetails(
        data.youtubeId,
        currentMeta,
        (err, cachedDetails, updatedMeta) => {
          if (err) {
            serviceLocator.logger.error(
              'Error caching YouTube details on create:',
              err
            )
            // Don't fail the create operation if YouTube caching fails
            return cb(null, data)
          }

          // Always update metadata, even if caching failed
          data.cachedYoutubeDetailsMeta = updatedMeta || currentMeta

          if (cachedDetails) {
            data.cachedYoutubeDetails = cachedDetails
            data.cachedYoutubeDetailsExpiryDate = youtubeCache.createExpiryDate()
            serviceLocator.logger.info(
              `Successfully cached YouTube details for: ${data.youtubeId}`
            )
          }
          cb(null, data)
        }
      )
    } else {
      cb(null, data)
    }
  })

  // Cache YouTube details on update if youtubeId changed
  service.pre('update', (data, cb) => {
    const modifiedData = { ...data, modifiedDate: new Date() }

    // Check if youtubeId is being updated
    if (data.youtubeId) {
      // Get the existing video to compare youtubeId
      service.read(data._id, (err, existingVideo) => {
        if (err) {
          serviceLocator.logger.error(
            'Error reading existing video on update:',
            err
          )
          // Don't fail the update operation if read fails
          return cb(null, modifiedData)
        }

        // If youtubeId changed or cache is invalid, refresh cache
        if (
          !existingVideo ||
          existingVideo.youtubeId !== data.youtubeId ||
          !youtubeCache.isCacheValid(
            existingVideo.cachedYoutubeDetailsExpiryDate
          )
        ) {
          serviceLocator.logger.info(
            `Refreshing YouTube cache for updated video: ${data.youtubeId}`
          )
          const currentMeta = existingVideo?.cachedYoutubeDetailsMeta || {}
          youtubeCache.fetchAndCacheYouTubeDetails(
            data.youtubeId,
            currentMeta,
            (fetchErr, cachedDetails, updatedMeta) => {
              if (fetchErr) {
                serviceLocator.logger.error(
                  'Error caching YouTube details on update:',
                  fetchErr
                )
                // Don't fail the update operation if YouTube caching fails
                return cb(null, modifiedData)
              }

              // Always update metadata, even if caching failed
              modifiedData.cachedYoutubeDetailsMeta = updatedMeta || currentMeta

              if (cachedDetails) {
                modifiedData.cachedYoutubeDetails = cachedDetails
                modifiedData.cachedYoutubeDetailsExpiryDate = youtubeCache.createExpiryDate()
                serviceLocator.logger.info(
                  `Successfully refreshed YouTube cache for: ${data.youtubeId}`
                )
              }
              cb(null, modifiedData)
            }
          )
        } else {
          // No need to refresh cache
          cb(null, modifiedData)
        }
      })
    } else {
      cb(null, modifiedData)
    }
  })

  // Add refresh cache method to service
  service.refreshYouTubeCache = youtubeCache.refreshCache

  return cachedCrudService(serviceLocator, service)
}
