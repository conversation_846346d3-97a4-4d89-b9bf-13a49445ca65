const schemata = require('@clocklimited/schemata')
const extendSchemata = require('../../../api/lib/schemata-extender')
const createGraphqlImageBuilderProperty = require('../asset/lib/graphql-image-builder-property')
const generateImageVariations = require('../event/lib/image-variation-generator')
const createSchema = require('./schema')
const { promisify } = require('util')
const convertRichTextToText = require('../../../lib/convert-richtext-to-text')
const YouTube = require('youtube-sr').default
const createEventGraphQlSchema = require('../event/graphql-schema')

/**
 * Converts duration from milliseconds to ISO 8601 format
 * @param {number} durationMs - Duration in milliseconds
 * @returns {string} - ISO 8601 duration format (e.g., "PT10M30S")
 */
const convertToISO8601Duration = (durationMs) => {
  if (!durationMs || durationMs <= 0) return null

  const totalSeconds = Math.floor(durationMs / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  let duration = 'PT'
  if (hours > 0) duration += `${hours}H`
  if (minutes > 0) duration += `${minutes}M`
  if (seconds > 0) duration += `${seconds}S`

  return duration === 'PT' ? 'PT0S' : duration
}

/**
 * Fetches YouTube video details with caching
 * @param {string} youtubeId - YouTube video ID
 * @param {Object} serviceLocator - Service locator for logging
 * @returns {Object|null} - YouTube video details or null if error
 */
const fetchYouTubeVideoDetails = async (youtubeId, serviceLocator) => {
  if (!youtubeId) return null

  try {
    serviceLocator.logger.info('Fetching YouTube video details:', youtubeId)
    const youtubeVideo = await YouTube.getVideo(
      `https://www.youtube.com/watch?v=${youtubeId}`
    )
    serviceLocator.logger.info('youtubeVdieo: ', youtubeVideo)
    return youtubeVideo
  } catch (error) {
    serviceLocator.logger.error('Error fetching YouTube video details:', error)
    return null
  }
}

const createGraphqlSchema = (serviceLocator) => {
  const schema = createSchema(serviceLocator)
  const imageVariations = [
    ...generateImageVariations('Thumbnail', '1x1', '1:1', [320, 668]),
    ...generateImageVariations('Thumbnail', '4x3', '4:4', [320, 668]),
    ...generateImageVariations('Thumbnail', '16x9', '16:9', [320, 668]),
    ...generateImageVariations('Cover', '1x1', '1:1', [
      320,
      668,
      900,
      1336,
      1800
    ]),
    ...generateImageVariations('Cover', '4x3', '4:3', [
      320,
      668,
      900,
      1336,
      1800
    ]),
    ...generateImageVariations('Cover', '16x9', '16:9', [
      320,
      668,
      900,
      1336,
      1800
    ]),
    ...generateImageVariations('Cover', '21x9', '21:9', [
      320,
      668,
      900,
      1336,
      1800
    ])
  ]

  const typeName = 'EventVideo'
  const graphqlSchema = schemata({
    name: typeName,
    properties: {
      images: createGraphqlImageBuilderProperty(
        serviceLocator,
        imageVariations,
        typeName
      ),
      description: {
        type: String,
        resolve: (parent) => {
          return convertRichTextToText(parent.description)
        }
      },
      eventName: {
        type: String,
        resolve: async (parent, args, context) => {
          const event = await promisify(serviceLocator.eventService.findOne)({
            _id: parent.eventId
          })
          return event.name
        }
      },
      event: {
        type: createEventGraphQlSchema(serviceLocator),
        resolve: async (parent, args, context) => {
          return await promisify(serviceLocator.eventService.findOne)({
            _id: parent.eventId
          })
        }
      },
      eventFullUrlPath: {
        type: String,
        resolve: async (parent, args, context) => {
          const event = await promisify(serviceLocator.eventService.findOne)({
            _id: parent.eventId
          })
          if (!event) return null
          const eventUmbrella = await promisify(
            serviceLocator.eventUmbrellaService.findOne
          )({
            _id: event.eventUmbrellaId
          })
          if (!eventUmbrella) return null
          return `events/${eventUmbrella.slug}/${event.slug}`
        }
      },
      duration: {
        type: String,
        resolve: async (parent) => {
          // Use cached data if available and valid
          if (
            parent.cachedYoutubeDetails &&
            parent.cachedYoutubeDetails.durationISO8601
          ) {
            return parent.cachedYoutubeDetails.durationISO8601
          }

          // Fallback to fetching fresh data if cache is missing
          const youtubeVideo = await fetchYouTubeVideoDetails(
            parent.youtubeId,
            serviceLocator
          )
          if (!youtubeVideo || !youtubeVideo.duration) return null

          return convertToISO8601Duration(youtubeVideo.duration)
        }
      },
      uploadDate: {
        type: String,
        resolve: async (parent) => {
          // Use cached data if available and valid
          if (
            parent.cachedYoutubeDetails &&
            parent.cachedYoutubeDetails.uploadDate
          ) {
            return parent.cachedYoutubeDetails.uploadDate
          }

          // Fallback to fetching fresh data if cache is missing
          const youtubeVideo = await fetchYouTubeVideoDetails(
            parent.youtubeId,
            serviceLocator
          )
          if (!youtubeVideo || !youtubeVideo.uploadDate)
            return parent.createdDate

          // YouTube.uploadDate should be a Date object, convert to ISO string
          return youtubeVideo.uploadDate instanceof Date
            ? youtubeVideo.uploadDate.toISOString()
            : youtubeVideo.uploadDate
        }
      }
    }
  })
  const extendedSchema = extendSchemata(schema, graphqlSchema)
  return extendedSchema
}

module.exports = createGraphqlSchema
