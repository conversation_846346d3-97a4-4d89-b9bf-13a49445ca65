const schemata = require('@clocklimited/schemata')
const imageConfig = require('./image-config.json')
const requiredCrops = imageConfig.crops.map((crop) => crop.name)
const requiredContexts = imageConfig.contexts.map((context) => context.name)
const required = require('@clocklimited/validity-required')
const createUniqueValidator = require('validity-unique-property')
const customValidityMessage = require('../lib/custom-validity-message')
const createContextValidator = require('validity-cf-image-context-selection')
const createCropValidator = require('../../../lib/validators/crop-integrity-validator')
const createImageCaptionValidator = require('../../../lib/validators/image-caption-validator')
const {
  config: validationConfig,
  createMaxLengthValidator
} = require('../event/validation-config')

module.exports = (findOne) => {
  const uniqueSlugValidator = customValidityMessage(
    createUniqueValidator(findOne),
    'This slug is already in use'
  )
  const name = 'EventVideo'
  return schemata({
    name,
    properties: {
      _id: {
        type: String
      },
      account: {
        type: String,
        validators: [required]
      },
      name: {
        type: String,
        validators: [required]
      },
      slug: {
        type: String,
        validators: [required, uniqueSlugValidator]
      },
      youtubeId: {
        type: String,
        validators: [required]
      },
      description: {
        type: String,
        validators: [
          createMaxLengthValidator(validationConfig[name].description, {
            useHtmlConverter: true
          })
        ]
      },
      createdDate: {
        type: Date,
        defaultValue: () => new Date()
      },
      images: {
        type: Object,
        defaultValue: () => ({ widgets: [] }),
        validators: [
          createContextValidator(requiredContexts),
          createCropValidator(requiredCrops),
          createImageCaptionValidator()
        ]
      },
      eventId: {
        type: String,
        validators: [required]
      },
      agendaItemId: {
        type: String
      },
      orderIndex: {
        type: Number,
        defaultValue: 0
      },
      cachedYoutubeDetails: {
        type: Object,
        defaultValue: () => ({})
      },
      cachedYoutubeDetailsExpiryDate: {
        type: Date,
        validators: []
      },
      cachedYoutubeDetailsMeta: {
        type: Object,
        defaultValue: () => ({
          successCount: 0,
          failureCount: 0,
          lastSuccessDate: null,
          lastFailureDate: null,
          lastErrorMessage: null,
          totalAttempts: 0,
          consecutiveFailures: 0,
          firstAttemptDate: null,
          averageResponseTime: null,
          lastResponseTime: null
        })
      }
    }
  })
}
