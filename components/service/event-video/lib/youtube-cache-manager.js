const YouTube = require('youtube-sr').default

/**
 * Creates a YouTube cache manager for event videos
 * @param {Object} serviceLocator - The service locator
 * @returns {Object} - YouTube cache manager functions
 */
const createYouTubeCacheManager = (serviceLocator) => {
  const { logger } = serviceLocator

  /**
   * Converts duration from milliseconds to ISO 8601 format
   * @param {number} durationMs - Duration in milliseconds
   * @returns {string} - ISO 8601 duration format (e.g., "PT10M30S")
   */
  const convertToISO8601Duration = (durationMs) => {
    if (!durationMs || durationMs <= 0) return null

    const totalSeconds = Math.floor(durationMs / 1000)
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    let duration = 'PT'
    if (hours > 0) duration += `${hours}H`
    if (minutes > 0) duration += `${minutes}M`
    if (seconds > 0) duration += `${seconds}S`

    return duration === 'PT' ? 'PT0S' : duration
  }

  /**
   * Converts duration from milliseconds to seconds for sitemap
   * @param {number} durationMs - Duration in milliseconds
   * @returns {number} - Duration in seconds
   */
  const convertToSeconds = (durationMs) => {
    if (!durationMs || durationMs <= 0) return null
    return Math.floor(durationMs / 1000)
  }

  /**
   * Updates YouTube cache metadata
   * @param {Object} currentMeta - Current metadata object
   * @param {boolean} success - Whether the operation was successful
   * @param {string} errorMessage - Error message if failed
   * @param {number} responseTime - Response time in milliseconds
   * @returns {Object} - Updated metadata object
   */
  const updateYouTubeMeta = (
    currentMeta = {},
    success,
    errorMessage = null,
    responseTime = null
  ) => {
    const now = new Date()
    const meta = {
      successCount: currentMeta.successCount || 0,
      failureCount: currentMeta.failureCount || 0,
      lastSuccessDate: currentMeta.lastSuccessDate || null,
      lastFailureDate: currentMeta.lastFailureDate || null,
      lastErrorMessage: currentMeta.lastErrorMessage || null,
      totalAttempts: (currentMeta.totalAttempts || 0) + 1,
      consecutiveFailures: currentMeta.consecutiveFailures || 0,
      firstAttemptDate: currentMeta.firstAttemptDate || now,
      averageResponseTime: currentMeta.averageResponseTime || null,
      lastResponseTime: responseTime
    }

    if (success) {
      meta.successCount += 1
      meta.lastSuccessDate = now
      meta.consecutiveFailures = 0
      meta.lastErrorMessage = null
    } else {
      meta.failureCount += 1
      meta.lastFailureDate = now
      meta.consecutiveFailures += 1
      meta.lastErrorMessage = errorMessage
    }

    // Calculate average response time if we have response time data
    if (responseTime !== null) {
      if (meta.averageResponseTime === null) {
        meta.averageResponseTime = responseTime
      } else {
        // Simple moving average calculation
        const totalSuccessfulAttempts = meta.successCount
        meta.averageResponseTime =
          (meta.averageResponseTime * (totalSuccessfulAttempts - 1) +
            responseTime) /
          totalSuccessfulAttempts
      }
    }

    return meta
  }

  /**
   * Fetches YouTube video details and caches them
   * @param {string} youtubeId - YouTube video ID
   * @param {Function} callback - Callback function (err, cachedDetails, metadata)
   */
  const fetchAndCacheYouTubeDetails = (
    youtubeId,
    currentMeta = {},
    callback
  ) => {
    // Handle both old and new function signatures for backward compatibility
    if (typeof currentMeta === 'function') {
      callback = currentMeta
      currentMeta = {}
    }

    if (!youtubeId) return callback(null, null, currentMeta)

    logger.info(`Fetching YouTube details for: ${youtubeId}`)
    const startTime = Date.now()

    YouTube.getVideo(`https://www.youtube.com/watch?v=${youtubeId}`)
      .then((youtubeVideo) => {
        const responseTime = Date.now() - startTime

        if (!youtubeVideo) {
          logger.warn(`No YouTube video found for ID`, { youtubeId })
          const updatedMeta = updateYouTubeMeta(
            currentMeta,
            false,
            `No YouTube video found for ID: ${youtubeId}`,
            responseTime
          )
          return callback(null, null, updatedMeta)
        }

        // Create cached details object
        const cachedDetails = {
          duration: youtubeVideo.duration || null,
          durationISO8601: convertToISO8601Duration(youtubeVideo.duration),
          durationSeconds: convertToSeconds(youtubeVideo.duration),
          uploadDate:
            youtubeVideo.uploadedAt instanceof Date
              ? youtubeVideo.uploadedAt.toISOString()
              : youtubeVideo.uploadedAt,
          views: youtubeVideo.views || 0,
          title: youtubeVideo.title || null,
          description: youtubeVideo.description || null,
          thumbnailUrl: `https://i.ytimg.com/vi/${youtubeId}/maxresdefault.jpg`,
          cachedAt: new Date().toISOString()
        }

        const updatedMeta = updateYouTubeMeta(
          currentMeta,
          true,
          null,
          responseTime
        )
        logger.info(
          `Successfully cached YouTube details for: ${youtubeId} (${responseTime}ms)`
        )
        logger.info(
          `YouTube API stats for ${youtubeId}: ${updatedMeta.successCount} successes, ${updatedMeta.failureCount} failures, ${updatedMeta.consecutiveFailures} consecutive failures`
        )

        callback(null, cachedDetails, updatedMeta)
      })
      .catch((error) => {
        const responseTime = Date.now() - startTime
        const errorMessage = error.message || 'Unknown YouTube API error'

        logger.warn(`Error fetching YouTube details for id`, {
          errorMessage,
          youtubeId,
          responseTime
        })

        const updatedMeta = updateYouTubeMeta(
          currentMeta,
          false,
          errorMessage,
          responseTime
        )
        logger.warn(`YouTube API failure stats for id`, {
          youtubeId,
          failureCount: updatedMeta.failureCount,
          consecutiveFailures: updatedMeta.consecutiveFailures
        })

        // Log rate limiting suspicion if we have consecutive failures
        if (updatedMeta.consecutiveFailures >= 3) {
          logger.warn(`Possible rate limiting detected for YouTube API`, {
            youtubeId,
            consecutiveFailures: updatedMeta.consecutiveFailures
          })
        }

        callback(null, null, updatedMeta) // Don't fail the operation, just return null
      })
  }

  /**
   * Checks if cached YouTube details are still valid
   * @param {Date} expiryDate - The expiry date
   * @returns {boolean} - True if cache is still valid
   */
  const isCacheValid = (expiryDate) => {
    if (!expiryDate) return false
    return new Date() < new Date(expiryDate)
  }

  /**
   * Creates an expiry date 1 month from now
   * @returns {Date} - Expiry date
   */
  const createExpiryDate = () => {
    const expiryDate = new Date()
    expiryDate.setMonth(expiryDate.getMonth() + 1)
    return expiryDate
  }

  /**
   * Gets YouTube details from cache or fetches fresh data
   * @param {Object} eventVideo - Event video object
   * @param {Function} callback - Callback function (err, youtubeDetails, metadata)
   */
  const getYouTubeDetails = (eventVideo, callback) => {
    if (!eventVideo.youtubeId) return callback(null, null, null)

    // Check if cache is valid
    if (
      eventVideo.cachedYoutubeDetails &&
      Object.keys(eventVideo.cachedYoutubeDetails).length > 0 &&
      isCacheValid(eventVideo.cachedYoutubeDetailsExpiryDate)
    ) {
      logger.debug(`Using cached YouTube details for: ${eventVideo.youtubeId}`)
      return callback(
        null,
        eventVideo.cachedYoutubeDetails,
        eventVideo.cachedYoutubeDetailsMeta
      )
    }

    // Cache is invalid or doesn't exist, fetch fresh data
    logger.info(
      `Cache expired or missing for: ${eventVideo.youtubeId}, fetching fresh data`
    )
    const currentMeta = eventVideo.cachedYoutubeDetailsMeta || {}
    fetchAndCacheYouTubeDetails(eventVideo.youtubeId, currentMeta, callback)
  }

  /**
   * Updates event video with fresh YouTube cache
   * @param {string} eventVideoId - Event video ID
   * @param {Function} callback - Callback function
   */
  const refreshCache = (eventVideoId, callback) => {
    const eventVideoService = serviceLocator.eventVideoService

    // Get the event video
    eventVideoService.read(eventVideoId, (err, eventVideo) => {
      if (err) {
        logger.error('Error reading event video:', err)
        return callback(err)
      }

      if (!eventVideo) {
        return callback(new Error('Event video not found'))
      }

      if (!eventVideo.youtubeId) {
        return callback(new Error('Event video has no YouTube ID'))
      }

      // Fetch fresh YouTube details with current metadata
      const currentMeta = eventVideo.cachedYoutubeDetailsMeta || {}
      fetchAndCacheYouTubeDetails(
        eventVideo.youtubeId,
        currentMeta,
        (fetchErr, freshDetails, updatedMeta) => {
          if (fetchErr) {
            logger.warn('Error fetching YouTube details:', fetchErr)
            return callback(fetchErr)
          }

          // Update the event video with fresh cache and metadata (even if fetch failed)
          const updateData = {
            cachedYoutubeDetailsMeta: updatedMeta || currentMeta
          }

          if (freshDetails) {
            updateData.cachedYoutubeDetails = freshDetails
            updateData.cachedYoutubeDetailsExpiryDate = createExpiryDate()
          }

          eventVideoService.partialUpdate(
            {
              _id: eventVideoId,
              ...updateData
            },
            (updateErr, updatedVideo) => {
              if (updateErr) {
                logger.error('Failed to update event video cache:', updateErr)
                return callback(updateErr)
              }

              if (freshDetails) {
                logger.info(
                  `Successfully refreshed cache for event video: ${eventVideoId}`
                )
              } else {
                logger.warn(
                  `Failed to fetch YouTube details for event video, but updated metadata`,
                  { eventVideoId }
                )
              }
              callback(null, updatedVideo)
            }
          )
        }
      )
    })
  }

  return {
    fetchAndCacheYouTubeDetails,
    getYouTubeDetails,
    refreshCache,
    isCacheValid,
    createExpiryDate,
    convertToISO8601Duration,
    convertToSeconds
  }
}

module.exports = createYouTubeCacheManager
