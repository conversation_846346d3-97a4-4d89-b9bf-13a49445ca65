const extend = require('lodash.assign')
const validNew = require('./valid-new')

module.exports = function () {
  return extend({}, validNew(), {
    _id: 1,
    tracks: [],
    locations: [],
    tags: [],
    articleBaseSlugs: [],
    showLogoInNavigation: false,
    shareTitle: null,
    shareDescription: null,
    metaTitle: null,
    metaDescription: null,
    startDate: null,
    endDate: null,
    startDateISOWithoutTZ: null,
    endDateISOWithoutTZ: null,
    navigation: [],
    eventPresetId: null,
    navigationCtaLabel: null,
    navigationCtaActionKey: null,
    navigationProxyEventId: null,
    createdDate: new Date().toISOString(),
    buildingName: null,
    pianoActions: [],
    tiers: [],
    speakerRoles: [],
    sessionTypes: [],
    articleCategories: [],
    useNewNavigationSidebar: false,
    useNavigationDarkTheme: false,
    navigationCtas: [],
    shortDescription: null,
    stateLog: [],
    appOnly: {
      homepageSplashVideoUrl: null,
      broughtToYouByTitle: null,
      broughtToYouByDescription: null,
      whiteUmbrellaLogoUrl: null,
      whiteBrandLogoUrl: null,
      darkCombinedLogoUrl: null
    }
  })
}
