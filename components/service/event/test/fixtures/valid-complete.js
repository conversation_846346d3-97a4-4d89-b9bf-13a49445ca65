const extend = require('lodash.assign')
const validNew = require('./valid-new')

module.exports = function (suffix) {
  return extend({}, validNew(suffix), {
    startDate: new Date().toISOString(),
    endDate: new Date().toISOString(),
    startDateISOWithoutTZ: new Date().toISOString(),
    endDateISOWithoutTZ: new Date().toISOString(),
    articleBaseSlugs: [],
    locations: [],
    tags: [],
    tracks: [],
    navigation: [],
    navigationProxyEventId: null,
    navigationCtaLabel: 'Register',
    navigationCtaActionKey: 'register',
    createdDate: new Date().toISOString(),
    buildingName: 'Building name',
    showLogoInNavigation: false,
    pianoActions: [],
    tiers: [],
    speakerRoles: [],
    sessionTypes: [],
    articleCategories: [],
    navigationCtas: [],
    useNewNavigationSidebar: false,
    useNavigationDarkTheme: false,
    shortDescription: 'Short description',
    stateLog: [],
    appOnly: {
      homepageSplashVideoUrl: null,
      broughtToYouByTitle: null,
      broughtToYouByDescription: null,
      whiteUmbrellaLogoUrl: null,
      whiteBrandLogoUrl: null,
      darkCombinedLogoUrl: null
    }
  })
}
