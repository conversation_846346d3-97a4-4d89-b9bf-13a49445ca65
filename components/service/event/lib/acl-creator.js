const createAcl = (serviceLocator) => {
  // Event
  serviceLocator.acl.addResource('event', {
    actions: [
      'discover',
      'create',
      'read',
      'update',
      'delete',
      'editLayouts',
      'managePreset',
      'manageLayouts',
      'manageNavigation',
      'manageMobileApp',
      'publish',
      'review',
      '*'
    ]
  })

  // Event Article
  serviceLocator.acl.addResource('eventArticle', {
    actions: ['discover', 'read', 'update', '*']
  })

  // Event Speaker
  serviceLocator.acl.addResource('eventSpeaker', {
    actions: ['discover', 'create', 'read', 'update', 'delete', '*']
  })

  // Event Sponsor
  serviceLocator.acl.addResource('eventSponsor', {
    actions: ['discover', 'create', 'read', 'update', 'delete', '*']
  })

  // Event Agenda Item
  serviceLocator.acl.addResource('eventAgendaItem', {
    actions: ['discover', 'create', 'read', 'update', 'delete', '*']
  })
}

module.exports = createAcl
