const convertRichTextToText = require('../../../lib/convert-richtext-to-text')

const config = {
  EventHeroWidget: {
    title: 70,
    subtitle: 100,
    description: 180
  },
  EventStatsWidget: {
    title: 70
  },
  EventStatsWidgetStat: {
    label: 35,
    statistic: 10
  },
  EventImageSplitHeroWidget: {
    title: 70,
    description: 1275
  },
  EventMapSplitHeroWidget: {
    title: 70,
    description: 1275
  },
  EventAgendaSplitHeroWidget: {
    title: 70,
    description: 180
  },
  EventIconSplitHeroWidget: {
    title: 70,
    description: 1275
  },
  EventStepsWidget: {
    title: 70,
    description: 500
  },
  EventStepsWidgetStep: {
    title: 70,
    description: 300
  },
  EventDetailsWidget: {
    title: 70,
    description: 180
  },
  EventDetilsWidgetDetail: {
    title: 70,
    description: 100
  },
  EventContentGridWidget: {
    title: 70
  },
  EventArticleGridSnippetWidget: {
    title: 70
  },
  Event: {
    description: 1000
  },
  EventVideo: {
    description: 1000
  },
  EventAgendaItem: {
    description: 1000
  },
  EventSponsor: {
    description: 1000
  },
  EventButton: {
    label: 22
  }
}

const createMaxLengthValidator = (maxLength, options) => (
  key,
  _,
  object,
  callback
) => {
  // Early early bailout - button labels were still restricted so may as well completely remove for now
  // eslint-disable-next-line no-constant-condition
  if (true) return callback(null, undefined)

  if (
    !object[key] ||
    // Bailout HTML validation early, to be removed at a later date
    (typeof options === 'object' && options.useHtmlConverter)
  ) {
    return callback(null, undefined)
  }

  let stringToCheck = object[key]

  if (typeof options === 'object' && options.useHtmlConverter) {
    stringToCheck = convertRichTextToText(stringToCheck)
  }

  if (stringToCheck.length > maxLength) {
    callback(
      null,
      `${
        key.charAt(0).toUpperCase() + key.slice(1)
      } should be ${maxLength} characters or lessings`
    )
  }

  return callback(null, undefined)
}

module.exports = { config, createMaxLengthValidator }
