// Packages
const schemata = require('@clocklimited/schemata')
const required = require('@clocklimited/validity-required')
// Image Sizes and config
const imageConfig = require('./image-config.json')
// Validation
const createUniqueValidator = require('validity-unique-property')
const customValidityMessage = require('../lib/custom-validity-message')
const createContextValidator = require('validity-cf-image-context-selection')
const createCropValidator = require('../../../lib/validators/crop-integrity-validator')
const createImageCaptionValidator = require('../../../lib/validators/image-caption-validator')
// Schemas for sub-content
const {
  createTierSchema,
  createLocationSchema,
  createTrackSchema,
  createPianoActionSchema,
  createSpeakerRoleSchema,
  createSessionTypeSchema,
  createEventArticleCategorySchema,
  createEventNavigationCtaSchema
} = require('./lib/schema-creators')

const hat = require('hat')
const {
  config: validationConfig,
  createMaxLengthValidator
} = require('./validation-config')

const createAppOnlySchema = () => {
  return schemata({
    name: 'AppOnly',
    properties: {
      homepageSplashVideoUrl: {
        type: String
      },
      broughtToYouByTitle: {
        type: String
      },
      broughtToYouByDescription: {
        type: String
      },
      whiteUmbrellaLogoUrl: {
        type: String
      },
      whiteBrandLogoUrl: {
        type: String
      },
      darkCombinedLogoUrl: {
        type: String
      }
    },
    defaultValue: () => ({
      homepageSplashVideoUrl: null,
      broughtToYouByTitle: null,
      broughtToYouByDescription: null,
      whiteUmbrellaLogoUrl: null,
      whiteBrandLogoUrl: null,
      darkCombinedLogoUrl: null
    })
  })
}

const createEventSchema = (serviceLocator, findOne) => {
  const requiredCrops = imageConfig.crops.map((crop) => crop.name)
  const requiredContexts = imageConfig.contexts.map((context) => context.name)
  const uniqueSlugValidator = customValidityMessage(
    createUniqueValidator(findOne, { keys: ['eventUmbrellaId'] }),
    'This slug is already in use within the same Event Portfolio'
  )
  const createMinLength = (length) => (key, keyDisplayName, object, cb) => {
    const message =
      length === 1
        ? 'Must have at least 1 Piano Resource ID'
        : `Must have at least ${length} Piano Resource IDs`
    return cb(null, object[key].length < length ? message : null)
  }

  const validatePianoResourceIdLengths = (key, keyDisplayName, object, cb) => {
    const pianoResourceIds = object[key]
    const invalidId = pianoResourceIds.find((id) => id.length !== 7)
    return cb(
      null,
      invalidId ? 'Piano Resource IDs must be 7 characters' : null
    )
  }

  const validateBrandColor = (key, keyDisplayName, object, cb) => {
    const brandColor = object[key]
    const validColor = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(brandColor)
    return cb(null, validColor ? null : 'Brand color must be a valid hex color')
  }

  const validateSideBarOverrideColor = (key, keyDisplayName, object, cb) => {
    const sidebarOverrideColor = object[key]

    if (!sidebarOverrideColor) {
      return cb(null, null)
    }

    const validColor = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(
      sidebarOverrideColor
    )

    return cb(
      null,
      validColor ? null : 'Sidebar override color must be a valid hex color'
    )
  }

  const validateArticleBaseSlugs = (key, keyDisplayName, object, cb) => {
    const articleBaseSlugs = object[key]
    if (articleBaseSlugs.includes('sponsor'))
      return cb(
        null,
        'Article base slugs cannot include "sponsor" as its a protected slug for sponsor booth pages'
      )

    const invalidSlug = articleBaseSlugs.find(
      (slug) => !/^[a-z0-9-]+$/.test(slug)
    )
    return cb(
      null,
      invalidSlug
        ? 'Article base slugs must be lowercase alphanumeric with hyphens'
        : null
    )
  }

  const name = 'Event'
  const schema = schemata({
    name,
    properties: {
      useNewNavigationSidebar: {
        // TEMP FEAT FLAG
        type: Boolean,
        defaultValue: () => false
      },
      _id: {
        type: String
      },
      account: {
        type: String,
        validators: [required],
        tag: ['copy-event:required'] // <feature>:<validation condition>
      },
      eventUmbrellaId: {
        type: String,
        validators: [required],
        tag: ['copy-event:required']
      },
      name: {
        type: String,
        validators: [required],
        tag: ['copy-event:required']
      },
      slug: {
        type: String,
        validators: {
          all: [required, uniqueSlugValidator]
        },
        tag: ['copy-event:required']
      },
      city: {
        type: String,
        validators: [required],
        tag: ['copy-event:required']
      },
      shortDescription: {
        type: String
      },
      pianoResourceIds: {
        type: Array,
        validators: [createMinLength(1), validatePianoResourceIdLengths],
        defaultValue: () => []
      },
      state: {
        type: String,
        options: ['Draft', 'Review', 'Published'],
        defaultValue: 'Draft',
        validators: [required],
        tag: ['copy-event:required']
      },
      stateLog: {
        type: schemata.Array(
          schemata({
            name: 'StateLogEntry',
            properties: {
              state: {
                type: String,
                options: ['Draft', 'Review', 'Published'],
                validators: [required]
              },
              timestamp: {
                type: Date,
                defaultValue: () => new Date(),
                validators: [required]
              },
              note: {
                type: String
              }
            }
          })
        ),
        defaultValue: () => [],
        description: 'Log of state changes with timestamps and notes'
      },
      previewId: {
        type: String,
        defaultValue: () =>
          Math.round(Math.random() * 100000000000).toString(36),
        validators: [required],
        tag: ['copy-event:required']
      },
      country: {
        type: String,
        validators: [required],
        tag: ['copy-event:required']
      },
      startDate: {
        type: Date,
        validators: [required],
        tag: ['copy-event:required']
      },
      endDate: {
        type: Date,
        validators: [required],
        tag: ['copy-event:required']
      },
      timezone: {
        type: String,
        validators: [required],
        tag: ['copy-event:required']
      },
      startDateISOWithoutTZ: {
        type: String
      },
      endDateISOWithoutTZ: {
        type: String
      },
      buildingName: {
        type: String
      },
      description: {
        type: String,
        validators: [
          createMaxLengthValidator(validationConfig[name].description, {
            useHtmlConverter: true
          })
        ]
      },
      metaTitle: {
        type: String
      },
      metaDescription: {
        type: String
      },
      shareTitle: {
        type: String
      },
      shareDescription: {
        type: String
      },
      navigationCtaLabel: {
        type: String
      },
      navigationCtaActionKey: {
        type: String
      },
      eventPresetId: {
        type: String
      },
      // Until we use the formBuilder, this shall be commented out
      // sponsorForm: {
      //   type: String
      // },
      // speakerForm: {
      //   type: String
      // },
      articleBaseSlugs: {
        type: Array,
        validators: [validateArticleBaseSlugs],
        defaultValue: () => ['topics', 'news', 'speaker']
      },
      locations: {
        type: schemata.Array(createLocationSchema()),
        validators: [],
        defaultValue: () => [
          { key: hat(), name: 'Stage 1', vimeoId: '524933864' },
          { key: hat(), name: 'Stage 2', vimeoId: '515302320' }
        ]
      },
      pianoActions: {
        type: schemata.Array(createPianoActionSchema()),
        validators: [],
        defaultValue: () => [{ key: 'register', type: 'piano' }]
      },
      tracks: {
        type: schemata.Array(createTrackSchema()),
        validators: [],
        defaultValue: () => [
          { key: hat(), name: 'Track 1', description: '...' },
          { key: hat(), name: 'Track 2', description: '...' }
        ]
      },
      speakerRoles: {
        type: schemata.Array(createSpeakerRoleSchema()),
        validators: [],
        defaultValue: () => [
          { key: hat(), name: 'Speaker' },
          { key: hat(), name: 'Moderator' },
          { key: hat(), name: 'Panelist' }
        ]
      },
      sessionTypes: {
        type: schemata.Array(createSessionTypeSchema()),
        validators: [],
        defaultValue: () => [
          { key: hat(), shortName: 'Keynote', name: 'Keynote Presentation' },
          { key: hat(), shortName: 'Panel', name: 'Panel Discussion' },
          { key: hat(), shortName: 'Workshop', name: 'Workshop' },
          { key: hat(), shortName: 'Networking', name: 'Networking Break' },
          { key: hat(), shortName: 'Fireside', name: 'Fireside Chat' },
          { key: hat(), shortName: 'Opening ', name: 'Opening Remarks' },
          { key: hat(), shortName: 'Closing', name: 'Closing Remarks' }
        ]
      },
      tags: {
        type: Array,
        validators: [],
        defaultValue: () => []
      },
      tiers: {
        type: schemata.Array(createTierSchema()),
        validators: [],
        defaultValue: () => [
          { key: hat(), name: 'Headline', rank: 1, tierColour: 'white' },
          { key: hat(), name: 'Corperate', rank: 2, tierColour: 'white' },
          { key: hat(), name: 'Platinum', rank: 3, tierColour: 'white' },
          { key: hat(), name: 'Gold', rank: 4, tierColour: 'white' },
          { key: hat(), name: 'Silver', rank: 5, tierColour: 'white' },
          { key: hat(), name: 'Bronze', rank: 6, tierColour: 'white' },
          {
            key: hat(),
            name: 'Media Partner',
            rank: 7,
            tierColour: 'white'
          }
        ]
      },
      articleCategories: {
        type: schemata.Array(createEventArticleCategorySchema()),
        validators: [],
        defaultValue: () => [
          { key: hat(), name: 'News', articleBaseSlug: 'news' }
        ]
      },
      navigation: {
        type: Array
      },
      navigationProxyEventId: {
        type: String
      },
      showLogoInNavigation: {
        type: Boolean,
        defaultValue: true
      },
      brandColor: {
        type: String,
        validators: [validateBrandColor]
      },
      sidebarColorOverride: {
        type: String,
        validators: [validateSideBarOverrideColor]
      },
      images: {
        type: Object,
        defaultValue: () => ({ widgets: [] }),
        validators: [
          createContextValidator(requiredContexts),
          createCropValidator(requiredCrops),
          createImageCaptionValidator()
        ],
        tag: ['copy-event:required']
      },
      lightLogo: {
        type: String,
        validators: [required],
        tag: ['copy-event:required']
      },
      darkLogo: {
        type: String,
        validators: [required],
        tag: ['copy-event:required']
      },
      portfolioLogoOverride: {
        type: String
      },
      createdDate: {
        type: Date,
        defaultValue: () => new Date()
      },
      useNavigationDarkTheme: {
        type: Boolean,
        defaultValue: false
      },
      navigationCtas: {
        type: schemata.Array(createEventNavigationCtaSchema()),
        defaultValue: () => []
      },
      locationName: {
        type: String
      },
      locationStreetAddress: {
        type: String
      },
      locationAddressLocality: {
        type: String
      },
      locationPostalCode: {
        type: String
      },
      locationAddressRegion: {
        type: String
      },
      locationAddressCountry: {
        type: String
      },
      offersUrl: {
        type: String
      },
      appOnly: {
        type: createAppOnlySchema()
      }
    }
  })

  return schema
}

module.exports = createEventSchema
module.exports.createAppOnlySchema = createAppOnlySchema

// article.eventArticleCategory

// event.articleCategories
// - key
// - name

// some kinda articleGrid which instead of taking a list of lists, it can take a list of event.articleCategoris categories
// besides this it'll work in the same way as a normal articleGrid

// eventArticleGrid
// - displayType
// - eventArticleCategories
// - limit
// - dedupe (ADDS COMPLEXITY)

// event.articleCategories cannot be deleted if article.eventArticleCategory is in use

// Hmmm how will deduping work?
// Same way that it is currently? Can it use the same deduper? Probably should right
