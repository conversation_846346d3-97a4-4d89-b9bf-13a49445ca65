import crudService from 'crud-service'
import cachedCrudService from '../lib/cached-crud-service'
import createSchema from './schema'
import textSearch from '@clocklimited/cf-text-search'
import { promisify } from 'util'
// Sub Services
import createEventAgendaItemService from './sub-content-types/agenda-item/service'
import createEventSpeakerService from './sub-content-types/speaker/service'
import createEventSponsorService from './sub-content-types/sponsor/service'
import createEventLayoutService from './sub-content-types/layout/service'
// Resolvers
import createEventEntityHelpers from './lib/service-entity-helpers'
import createEventLayoutHelpers from './lib/layout-service-helpers'
// Layouts
import createDefaultEventHomeLayout from './lib/default-home-layout'
import createDefaultEventAboutLayout from './lib/default-about-layout'
import createDefaultEventSponsorLayout from './lib/default-sponsor-layout'
import createDefaultEventAgendaLayout from './lib/default-agenda-layout'
import createDefaultArticleLayout from './lib/default-article-layout'

import { cloneDeepWith } from 'lodash'

const defaultQuery = {
  keywords: '',
  filter: {},
  sort: [],
  pagination: {},
  projection: {},
  options: { expand: false }
}

const createDefaultArticleLayoutBySlug = (slug) => ({
  key: `article-${slug}`,
  isBuiltIn: true,
  type: 'eventArticle',
  name: `${slug.charAt(0).toUpperCase()}${slug.slice(1)} Article Layout`,
  description: `The layout for every news article`,
  slug,
  layout: createDefaultArticleLayout().layout,
  account: '61a75c7b3b654f0c7aa2215e',
  visible: true
})

const defaultLayouts = [
  createDefaultEventHomeLayout(),
  createDefaultEventAboutLayout(),
  createDefaultEventSponsorLayout(),
  createDefaultEventAgendaLayout()
]

export default (serviceLocator) => {
  const save = serviceLocator.persistence('event')
  const schema = createSchema(serviceLocator, save.findOne)

  const service = crudService('Event', save, schema, {})
  const agendaItemService = createEventAgendaItemService(serviceLocator)
  const speakerService = createEventSpeakerService(serviceLocator)
  const sponsorService = createEventSponsorService(serviceLocator)
  const layoutService = createEventLayoutService(serviceLocator)

  service.subServices = {
    agenda: agendaItemService,
    speakers: speakerService,
    sponsors: sponsorService,
    layouts: layoutService
  }

  const originalCreate = service.create
  const originalUpdate = service.update
  const originalDelete = service.delete

  const {
    findAll: findAgenda,
    create: createAgendaItem,
    find: findAgendaItem,
    update: updateAgendaItem,
    delete: deleteAgendaItem,
    deleteAll: deleteAgenda
  } = createEventEntityHelpers(
    serviceLocator,
    save,
    'agenda',
    agendaItemService
  )

  const {
    findAll: findSpeakers,
    create: createSpeaker,
    find: findSpeaker,
    update: updateSpeaker,
    delete: deleteSpeaker,
    deleteAll: deleteSpeakers
  } = createEventEntityHelpers(serviceLocator, save, 'speakers', speakerService)

  const {
    findAll: findSponsors,
    create: createSponsor,
    find: findSponsor,
    update: updateSponsor,
    delete: deleteSponsor,
    deleteAll: deleteSponsors
  } = createEventEntityHelpers(serviceLocator, save, 'sponsors', sponsorService)

  const {
    findAll: findLayouts,
    findAllPublic: findPublicLayouts,
    create: createLayout,
    find: findLayout,
    findBySlug: findLayoutBySlug,
    findById: findLayoutById,
    update: updateLayout,
    delete: deleteLayout,
    deleteAll: deleteLayouts
  } = createEventLayoutHelpers(serviceLocator, layoutService, 'eventId')

  service.read = save.read
  service.find = save.find
  service.findOne = save.findOne
  service.search = textSearch(service)

  service.findAgenda = findAgenda
  service.createAgendaItem = createAgendaItem
  service.findAgendaItem = findAgendaItem
  service.updateAgendaItem = updateAgendaItem
  service.deleteAgendaItem = deleteAgendaItem
  service.deleteAgenda = deleteAgenda

  service.findSponsors = findSponsors
  service.createSponsor = createSponsor
  service.findSponsor = findSponsor
  service.updateSponsor = updateSponsor
  service.deleteSponsor = deleteSponsor
  service.deleteSponsors = deleteSponsors

  service.findSpeakers = findSpeakers
  service.createSpeaker = createSpeaker
  service.findSpeaker = findSpeaker
  service.updateSpeaker = updateSpeaker
  service.deleteSpeaker = deleteSpeaker
  service.deleteSpeakers = deleteSpeakers

  service.findLayouts = findLayouts
  service.findPublicLayouts = findPublicLayouts
  service.createLayout = createLayout
  service.findLayout = findLayout
  service.findLayoutBySlug = findLayoutBySlug
  service.updateLayout = updateLayout
  service.deleteLayout = deleteLayout
  service.deleteLayouts = deleteLayouts

  service.importLayouts = async (
    eventId,
    payload = { layoutIds: [], confirmed: false, preserveVisibility: false },
    cb
  ) => {
    const { layoutIds, confirmed } = payload
    const findLayoutQuery = {
      ...defaultQuery,
      projection: {
        _id: 1,
        name: 1,
        type: 1,
        slug: 1,
        key: 1,
        eventId: 1,
        pianoRegWallEnabled: 1
      }
    }
    const currentEventLayout = await promisify(findLayouts)(
      eventId,
      findLayoutQuery
    )

    const checkLayout = async (layoutId) => {
      const out = { id: layoutId, state: 'unknown' }
      const layout = await promisify(findLayoutById)(layoutId)
      out.type = layout.type
      out.name = layout.name

      if (!layout) {
        out.state = 'not-found'
        return out
      }

      // Check if it exists in current event already
      if (layout.type !== 'eventArticle') {
        const existingLayout = currentEventLayout.find(
          (l) =>
            l.key === layout.key ||
            l.slug === layout.slug ||
            l._id === layout._id
        )
        if (existingLayout) {
          out.state = 'overwrite-warning'
          out.existingLayout = existingLayout
          return out
        }
      }

      // Check if it is an article layout and the article layout exists
      if (layout.type === 'eventArticle') {
        const existingArticleLayout = currentEventLayout.find(
          (l) => l.key === `article-${layout.slug}`
        )
        if (!existingArticleLayout) {
          out.state = 'article-not-found'
          return out
        } else {
          out.existingLayout = existingArticleLayout
          out.state = 'overwrite-warning'
          return out
        }
      }

      // All the others
      out.state = 'ready'
      return out
    }
    const returnLayouts = []
    for (const layoutId of layoutIds) {
      try {
        const layout = await checkLayout(layoutId)
        returnLayouts.push(layout)
      } catch (error) {
        return cb(error)
      }
    }

    // if "confirmed" is true, overwrite the layout
    if (confirmed) {
      // I want to make an algorithm that goes x layers deep looking for buttonGroups and blanking the action, eventLayoutKey, eventId, eventUmbrellaId etc
      const wrangleLayout = (obj) => {
        const buttonGroupUpdatedLayout = cloneDeepWith(obj, (value) => {
          if (value && value?.buttonGroup && value.buttonGroup?.length > 0) {
            return {
              ...value,
              buttonGroup: [
                ...value.buttonGroup.map((button) => {
                  return {
                    ...button,
                    link: '#placeholder',
                    buttonId: null,
                    buttonClassName: null,
                    action: null,
                    eventLayoutKey: null,
                    eventId: null,
                    eventUmbrellaId: null,
                    type: 'link'
                  }
                })
              ]
            }
          }
        })
        return cloneDeepWith(buttonGroupUpdatedLayout, (value) => {
          if (value && value?.eventIds) {
            return {
              ...value,
              eventIds: [eventId]
            }
          }
        })
      }

      const performImport = async () => {
        // Remove all existing layouts
        const layoutsToPerformOn = returnLayouts.filter(
          (layout) =>
            layout.state === 'overwrite-warning' || layout.state === 'ready'
        )
        const creates = layoutsToPerformOn
          .filter((layout) => layout.state === 'ready')
          .map(async (layout) => {
            const layoutToCopy = await promisify(findLayoutById)(layout.id)
            delete layoutToCopy._id
            const layoutToCreate = {
              ...layoutToCopy,
              eventId,
              layout: wrangleLayout(layoutToCopy.layout),
              metaTitle: null,
              metaDescription: null,
              shareTitle: null,
              shareDescription: null,
              upcomingEventRedirectUrl: null,
              liveEventRedirectUrl: null,
              pastEventRedirectUrl: null
            }
            return promisify(createLayout)(eventId, layoutToCreate)
          })

        const overwrites = layoutsToPerformOn
          .filter((layout) => layout.state === 'overwrite-warning')
          .map(async (layout) => {
            const layoutToCopy = await promisify(findLayoutById)(layout.id)
            const layoutToOverwrite = {
              ...layoutToCopy,
              eventId,
              _id: layout.existingLayout._id,
              layout: wrangleLayout(layoutToCopy.layout)
            }
            return promisify(updateLayout)(eventId, layoutToOverwrite)
          })
        Promise.all([...creates, ...overwrites])
      }
      await performImport()
    }

    return cb(null, returnLayouts)
  }

  service.findSubCollectionItem = async (id, type, key, cb) => {
    const event = await promisify(serviceLocator.eventService.read)(id)
    if (!event) return cb(new Error('Event not found', id))
    const subCollection = event[type]
    if (!subCollection) return cb(new Error('Sub collection not found', type))
    const item = subCollection.find((item) => item.key === key)
    return cb(null, item)
  }

  service.findPublic = (query, options, callback) => {
    if (typeof options === 'function') {
      callback = options
      options = {}
    }

    return service.find(
      {
        ...query,
        state: 'Published'
      },
      options,
      (err, events) => {
        if (err) return callback(err)
        callback(null, events)
      }
    )
  }

  service.create = (data, cb) => {
    originalCreate(data, async (error, event) => {
      if (error) return cb(error)
      const layoutsToCreate = defaultLayouts.concat(
        data.articleBaseSlugs.map((slug) =>
          createDefaultArticleLayoutBySlug(slug)
        )
      )
      await Promise.all(
        layoutsToCreate.map((layout) =>
          promisify(createLayout)(event._id, layout)
        )
      )
      return cb(null, event)
    })
  }

  service.update = async (data, updateOptions, cb) => {
    if (typeof updateOptions === 'function') {
      cb = updateOptions
      updateOptions = {}
    }

    // ----------------------------------------

    let newSlugs = []
    let oldSlugs = []
    let slugsToAdd = []
    let slugsToRemove = []
    const validationError = new Error('Validation Error')
    validationError.errors = []
    const preUpdateEvent = await promisify(serviceLocator.eventService.read)(
      data._id
    )

    if (!preUpdateEvent) {
      return cb(new Error('Event not found', data._id))
    }

    if (preUpdateEvent && data.articleBaseSlugs) {
      newSlugs = data.articleBaseSlugs
      oldSlugs = preUpdateEvent.articleBaseSlugs
      slugsToAdd = newSlugs.filter((slug) => !oldSlugs.includes(slug))
      slugsToRemove = oldSlugs.filter((slug) => !newSlugs.includes(slug))

      // Instead of checking on articles, we need to check on the event.articleCategories
      // Find a affectedArticleCategory that is using the slugsToRemove

      const affectedArticleCategories = preUpdateEvent.articleCategories.filter(
        (category) => slugsToRemove.includes(category.articleBaseSlug)
      )

      if (affectedArticleCategories.length > 0) {
        validationError.errors.push(
          `Cannot remove base slugs that are in use by article categories: ` +
            affectedArticleCategories
              .map((category) => category.name)
              .join(', ')
        )
      }
    }

    // ----------------------------------------

    const subCollectionKeyNames = [
      'speakerRoles',
      'locations',
      'tracks',
      'sessionTypes',
      'tiers',
      'articleCategories'
    ]
    const subCollectionItemsToRemove = {}
    for (const key of subCollectionKeyNames) {
      const currentItems = preUpdateEvent[key] || []
      const newItems = data[key] || []
      subCollectionItemsToRemove[key] = currentItems.filter((item) => {
        const notInNewItems = !newItems.find(
          (newItem) => newItem.key === item.key
        )
        return notInNewItems
      })
    }

    const handleAffectedItems = (
      affectedItems,
      removalItem,
      eventKey,
      affectedItemNameField,
      removalItemNameField,
      collectionName
    ) => {
      if (affectedItems.length > 0) {
        validationError.errors.push(
          `Cannot remove ${eventKey} (${removalItem[removalItemNameField]}) that are in use by ${collectionName} collection: ` +
            affectedItems.map((item) => item[affectedItemNameField]).join(', ')
        )
      }
    }

    for (const eventKey of subCollectionKeyNames) {
      const itemsToRemove = subCollectionItemsToRemove[eventKey]
      if (itemsToRemove.length === 0) continue
      for (const item of itemsToRemove) {
        switch (eventKey) {
          case 'speakerRoles': {
            const affectedAgendaItems = await promisify(
              findAgenda
            )(preUpdateEvent._id, { 'speakers.roleKey': item.key })
            handleAffectedItems(
              affectedAgendaItems,
              item,
              eventKey,
              'name',
              'name',
              'eventAgendaItem'
            )
            break
          }
          case 'locations': {
            const affectedAgendaItems = await promisify(
              findAgenda
            )(preUpdateEvent._id, { locationKey: item.key })
            handleAffectedItems(
              affectedAgendaItems,
              item,
              eventKey,
              'name',
              'name',
              'eventAgendaItem'
            )
            break
          }
          case 'tracks': {
            const affectedAgendaItems = await promisify(
              findAgenda
            )(preUpdateEvent._id, { trackKey: item.key })
            handleAffectedItems(
              affectedAgendaItems,
              item,
              eventKey,
              'name',
              'name',
              'eventAgendaItem'
            )
            break
          }
          case 'sessionTypes': {
            const affectedAgendaItems = await promisify(
              findAgenda
            )(preUpdateEvent._id, { sessionTypeKey: item.key })
            handleAffectedItems(
              affectedAgendaItems,
              item,
              eventKey,
              'name',
              'name',
              'eventAgendaItem'
            )
            break
          }
          case 'tiers': {
            const affectedSponsors = await promisify(
              findSponsors
            )(preUpdateEvent._id, { tierKey: item.key })

            handleAffectedItems(
              affectedSponsors,
              item,
              eventKey,
              'displayName',
              'name',
              'eventSponsor'
            )
            break
          }
          case 'articleCategories': {
            const affectedArticles = await promisify(
              serviceLocator.articleService.find
            )({
              eventId: preUpdateEvent._id,
              eventArticleCategoryKey: item.key
            })

            handleAffectedItems(
              affectedArticles,
              item,
              eventKey,
              'headline',
              'name',
              'article'
            )
            break
          }
          default:
            break
        }
      }
    }

    if (validationError.errors.length) {
      return cb(validationError)
    }

    originalUpdate(data, async (error, event) => {
      if (error) return cb(error)

      await Promise.all([
        ...slugsToAdd.map((slug) =>
          promisify(createLayout)(
            event._id,
            createDefaultArticleLayoutBySlug(slug)
          )
        ),
        ...slugsToRemove.map((slug) =>
          promisify(deleteLayout)(event._id, `article-${slug}`)
        )
      ])
      return cb(null, event)
    })
  }

  service.delete = (id, cb) => {
    originalDelete(id, async (error, event) => {
      if (error) return cb(error)
      await Promise.all([
        promisify(deleteLayouts)(id),
        promisify(deleteSpeakers)(id),
        promisify(deleteSponsors)(id),
        promisify(deleteAgenda)(id)
      ])
      const articleCollection = serviceLocator.serviceDatabase.collection(
        'article'
      )
      await articleCollection.updateMany(
        { eventId: id },
        { $set: { eventId: null, eventArticleCategoryKey: null } }
      )
      return cb(null, event)
    })
  }

  service.updateNavigation = async (id, data, cb) => {
    const event = await promisify(serviceLocator.eventService.read)(id)
    if (!event) return cb(new Error('Event not found', id))
    event.navigation = data
    await save.update(event)
    return cb(null, data)
  }

  service.copyEvent = async (sourceEventId, targetEventUmbrellaId, cb) => {
    try {
      // Read the source event
      const sourceEvent = await promisify(serviceLocator.eventService.read)(
        sourceEventId
      )
      if (!sourceEvent)
        return cb(new Error('Source event not found', sourceEventId))

      // Create a copy of the event data
      const eventData = { ...sourceEvent }
      delete eventData._id
      eventData.slug = eventData.slug + '-copy'
      eventData.name = `Copy of ${sourceEvent.name}`
      eventData.state = 'Draft'
      eventData.eventUmbrellaId = targetEventUmbrellaId
      eventData.previewId = Math.round(Math.random() * 100000000000).toString(
        36
      )
      eventData.stateLog = [
        {
          state: 'Draft',
          timestamp: new Date(),
          note: `Event copied from "${sourceEvent.name}"`
        }
      ]

      // Create the new event
      const newEvent = await promisify(originalCreate)(eventData)

      // Get layouts from source event
      const sourceLayouts = await promisify(findLayouts)(sourceEventId, {})

      // Copy layouts to new event
      if (sourceLayouts.length > 0) {
        const layoutIds = sourceLayouts.map((layout) => layout._id)
        await promisify(service.importLayouts)(newEvent._id, {
          layoutIds,
          confirmed: true
        })
      }

      return cb(null, newEvent)
    } catch (error) {
      serviceLocator.logger.error('Error copying event:', error)
      return cb(error)
    }
  }

  service.pre('update', (data, cb) =>
    cb(null, { ...data, modifiedDate: new Date() })
  )

  return cachedCrudService(serviceLocator, service)
}
