const schemata = require('@clocklimited/schemata')
const required = require('@clocklimited/validity-required')
const hat = require('hat')
const createContextValidator = require('validity-cf-image-context-selection')
const createCropValidator = require('../../../../../lib/validators/crop-integrity-validator')
const createImageCaptionValidator = require('../../../../../lib/validators/image-caption-validator')
const imageConfig = require('./image-config.json')
const visibility = require('./visibility-config')
const {
  config: validationConfig,
  createMaxLengthValidator
} = require('../../validation-config')

module.exports = (serviceLocator) => {
  const requiredCrops = imageConfig.crops.map((crop) => crop.name)
  const requiredContexts = imageConfig.contexts.map((context) => context.name)
  const name = 'EventAgendaItem'
  return schemata({
    name,
    properties: {
      _id: {
        type: String
      },
      id: {
        type: String,
        defaultValue: () => hat(),
        validators: [required]
      },
      eventId: {
        type: String,
        validators: [required]
      },
      name: {
        type: String,
        validators: [required]
      },
      sessionTypeKey: {
        type: String
      },
      visibility: {
        type: String,
        validators: [required],
        defaultValue: () => visibility[0] // Should break if undefined
      },
      startDate: {
        type: Date,
        validators: [required]
      },
      endDate: {
        type: Date,
        validators: [required]
      },
      timezoneCorrectStartDate: {
        type: Date
      },
      timezoneCorrectEndDate: {
        type: Date
      },
      startDateISOWithoutTZ: {
        type: String
      },
      endDateISOWithoutTZ: {
        type: String
      },
      sponsorId: {
        type: String
      },
      sponsor: {
        type: Object,
        validators: []
      },
      speakers: {
        type: Array,
        validators: []
      },
      locationKey: {
        type: String,
        validators: [required]
      },
      trackKey: {
        type: String,
        validators: []
      },
      tags: {
        type: Array,
        validators: []
      },
      createdDate: {
        type: Date,
        defaultValue: () => new Date()
      },
      modifiedDate: {
        type: Date,
        defaultValue: () => new Date()
      },
      description: {
        type: String,
        validators: [
          createMaxLengthValidator(validationConfig[name].description, {
            useHtmlConverter: true
          })
        ]
      },
      images: {
        type: Object,
        defaultValue: () => ({ widgets: [] }),
        validators: [
          createContextValidator(requiredContexts),
          createCropValidator(requiredCrops),
          createImageCaptionValidator()
        ]
      },
      account: {
        type: String,
        validators: [required]
      }
    }
  })
}
