const schemata = require('@clocklimited/schemata')
const extendSchemata = require('../../../../../api/lib/schemata-extender')
const createGraphqlImageBuilderProperty = require('../../../asset/lib/graphql-image-builder-property')
const generateImageVariations = require('../../lib/image-variation-generator')
const createSchema = require('./schema')
const createSpeakerSchema = require('../../sub-content-types/speaker/schema')
const createSponserSchema = require('../../sub-content-types/sponsor/schema')
const { promisify } = require('util')
const {
  createSessionTypeSchema,
  createLocationSchema,
  createTrackSchema
} = require('../../lib/schema-creators')
const moment = require('moment-timezone')

const createGraphqlSchema = (serviceLocator) => {
  const schema = createSchema(serviceLocator)

  const imageVariations = [
    ...generateImageVariations('Thumbnail', '1x1', '1:1', [320, 668]),
    ...generateImageVariations('Thumbnail', '4x3', '4:4', [320, 668]),
    ...generateImageVariations('Thumbnail', '16x9', '16:9', [320, 668]),
    ...generateImageVariations('Cover', '1x1', '1:1', [
      320,
      668,
      900,
      1336,
      1800
    ]),
    ...generateImageVariations('Cover', '4x3', '4:3', [
      320,
      668,
      900,
      1336,
      1800
    ]),
    ...generateImageVariations('Cover', '16x9', '16:9', [
      320,
      668,
      900,
      1336,
      1800
    ]),
    ...generateImageVariations('Cover', '21x9', '21:9', [
      320,
      668,
      900,
      1336,
      1800
    ])
  ]

  const typeName = 'EventAgendaItem'
  const graphqlSchema = schemata({
    name: typeName,
    properties: {
      description: {
        type: String,
        resolve: (parent) => parent?.description
      },
      images: createGraphqlImageBuilderProperty(
        serviceLocator,
        imageVariations,
        typeName
      ),
      sessionType: {
        type: createSessionTypeSchema(serviceLocator),
        resolve: async (parent, _, context) => {
          const fallback = { name: 'Session', shortName: 'Session' }
          try {
            const sessionType = await promisify(
              serviceLocator.eventService.findSubCollectionItem
            )(context.event._id, 'sessionTypes', parent.sessionTypeKey)
            if (!sessionType) return fallback
            return sessionType
          } catch (err) {
            serviceLocator.logger.error(err)
            return fallback
          }
        }
      },
      sponsor: {
        type: createSponserSchema(serviceLocator),
        resolve: async (parent, _, context) => {
          if (!parent.sponsorId) return null
          try {
            const id = context.event._id
            const sponsor = await promisify(
              serviceLocator.eventService.findSponsor
            )(id, parent.sponsorId)
            return sponsor
          } catch (e) {
            serviceLocator.logger.error(e)
            return null
          }
        }
      },
      speakers: {
        type: schemata.Array(createSpeakerSchema()),
        resolve: async (parent, _, context) => {
          try {
            const id = context.event._id
            const speakers = await promisify(
              serviceLocator.eventService.findSpeakers
            )(id, { _id: { $in: parent.speakers.map((s) => s.speakerId) } })
            return speakers
          } catch (error) {
            serviceLocator.logger.error(error)
            return []
          }
        }
      },
      location: {
        type: createLocationSchema(serviceLocator),
        resolve: async (parent, _, context) => {
          try {
            const location = await promisify(
              serviceLocator.eventService.findSubCollectionItem
            )(context.event._id, 'locations', parent.locationKey)
            return location
          } catch (error) {
            serviceLocator.logger.error(error)
            return null
          }
        }
      },
      track: {
        type: createTrackSchema(serviceLocator),
        resolve: async (parent, _, context) => {
          try {
            const track = await promisify(
              serviceLocator.eventService.findSubCollectionItem
            )(context.event._id, 'tracks', parent.trackKey)
            return track
          } catch (error) {
            serviceLocator.logger.error(error)
            return null
          }
        }
      },
      localeSafeStartTime: {
        type: String,
        resolve: async (parent) => {
          if (!parent.startDate) return 'unset'
          if (!parent.startDateISOWithoutTZ) return 'N/A'
          return moment.utc(parent.startDateISOWithoutTZ).format('HH:mm')
        }
      },
      localeSafeEndTime: {
        type: String,
        resolve: async (parent) => {
          if (!parent.endDate) return 'unset'
          if (!parent.endDateISOWithoutTZ) return 'N/A'
          return moment.utc(parent.endDateISOWithoutTZ).format('HH:mm')
        }
      }
    }
  })
  const extendedSchema = extendSchemata(schema, graphqlSchema)
  return extendedSchema
}

module.exports = createGraphqlSchema
