const createArticleListAggregator = require('../../lib/article-list-aggregator')
const createGraphqlListAggregator = require('./graphql-list-aggregator')
const graphqlFields = require('graphql-fields')

const defaultModifyProps = (parent) => parent

const createGraphqlListWithInstanceFallbackAggregater = (
  serviceLocator,
  modifiers = { modifyProps: defaultModifyProps }
) => {
  const { modifyProps } = modifiers
  const aggregateList = createGraphqlListAggregator(serviceLocator)
  return async (parent, args, context, info) => {
    const parentProps = modifyProps ? modifyProps(parent) : parent
    let skip = null
    let options = {}
    if (parentProps.loadMoreEnabled) {
      const page = args.page || 1
      const numPerPage = parseInt(parentProps.numPerPage, 10) || 8
      const index = Math.max((page - 1) * numPerPage, 0)
      parentProps.limit = numPerPage
      skip = index
      options = { includeTotalCount: true }
    }
    if (context.event) {
      const queryString = context.resourceUrl.includes('?')
        ? context.resourceUrl.substring(context.resourceUrl.indexOf('?') + 1)
        : ''
      const searchParams = new URLSearchParams(queryString)
      const previewId = searchParams.get('previewId')
      if (previewId === context.event.previewId) {
        options.includeDraftArticles = true
      }
    }
    parentProps.limit = parentProps.limit || 8
    // Get articles from the primary instance
    const list = await aggregateList(parentProps, context, info, skip, options)
    // If we have fallback instances and didn't get enough articles, fetch from fallback instances
    if (
      parent.fallbackInstances &&
      parent.fallbackInstances.length > 0 &&
      list.results.length < parentProps.limit
    ) {
      const requestedFields = Object.keys(
        graphqlFields(info, {}, { processArguments: true }).results
      )
      const remainingLimit = parentProps.limit - list.results.length

      try {
        const fallbackAggregate = createArticleListAggregator(
          serviceLocator,
          null, // No instance filtering for fallback instances
          context.accountId,
          context.dedupe,
          requestedFields,
          context.region
        )

        const fallbackList = await context.widgetQueue.add(() =>
          fallbackAggregate(
            parentProps.lists,
            parentProps.dedupe ? context.dedupe : null,
            remainingLimit,
            null, // No section filtering for fallback instances
            skip,
            options
          )
        )

        serviceLocator.logger.info(
          'ArticleGridWidget: Fallback instance results',
          {
            resultsCount: fallbackList.results.length,
            total: fallbackList.total
          }
        )

        // Add fallback articles to results
        list.results = list.results.concat(fallbackList.results)

        // log results
        serviceLocator.logger.info('ArticleGridWidget: Combined results', {
          resultsCount: list.results.length,
          total: list.total
        })

        // Log last result
        if (list.results.length > 0) {
          serviceLocator.logger.info(
            'ArticleGridWidget: Last result: ',
            list.results[list.results.length - 1]
          )
        }

        list.total = (list.total || 0) + (fallbackList.total || 0)

        throw new Error('Test error')
      } catch (error) {
        serviceLocator.logger.error(
          error,
          'Error fetching articles from fallback instances'
        )
      }
    }
    return list
  }
}

module.exports = createGraphqlListWithInstanceFallbackAggregater
