const createInstanceFilteredServiceRetriever = (serviceLocator) => async (
  instanceId,
  accountId
) => {
  if (!instanceId) {
    return {
      sectionService: serviceLocator.sectionService,
      articleService: serviceLocator.articleService,
      applyArticleFilter: () => {}
    }
  }
  const sectionFilter = {
    $and: [
      { $or: [{ instance: instanceId }, { instance: null }] },
      { account: accountId }
    ]
  }

  const articleFilter = { instance: instanceId }

  const applyArticleFilter = () => {
    return articleFilter
  }
  const sectionService = serviceLocator.createSectionService({
    filter: sectionFilter
  })
  const articleService = serviceLocator.createArticleService({
    filter: applyArticleFilter,
    sectionFilter
  })

  return { sectionService, articleService, applyArticleFilter }
}

module.exports = createInstanceFilteredServiceRetriever
