const { promisify } = require('util')
const { URL } = require('url')

module.exports = (serviceLocator, baseUrl) => async (
  url,
  accountId,
  instance
) => {
  // Query with the actual url and also the decoded one to account for any escaped
  // url-unsafe chars (%20 for space etc.) in the redirect url
  const parsedUrl = new URL(url, baseUrl)
  const pathWithoutQuery = parsedUrl.pathname
  const pathWithQuery = parsedUrl.pathname + parsedUrl.search

  // Extract the base path from the parsed url
  const basePath = pathWithoutQuery.split('/')[1]
  const wildcardPattern = `/${basePath}/*`

  const query = {
    account: accountId,
    instances: instance._id,
    $or: [
      { path: decodeURI(pathWithoutQuery) },
      { path: decodeURI(pathWithoutQuery + '/') },
      { path: decodeURI(pathWithQuery) },
      { path: decodeURI(pathWithQuery + '/') },
      { path: wildcardPattern },
      { path: wildcardPattern + '/' }
    ]
  }
  const response = await promisify(serviceLocator.redirectService.findOne)(
    query
  )
  return response
}
