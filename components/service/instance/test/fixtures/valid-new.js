module.exports = function () {
  return {
    name: 'Butlins',
    account: '123',
    subdomain: 'en',
    enabled: true,
    disableRegionPicker: false,
    brandType: 'B2B',
    logo: 'darkroom-uri-********',
    lightLogo: 'darkroom-uri-********',
    oldBrandId: 'abc',
    theme: 'ai',
    primaryColorOverride: '#BADA55',
    secondaryColorOverride: '#BADA55',
    headerAdvertSlotName: 'Default',
    headerAdvertVisible: true,
    preventAdsOnMobile: false,
    headerAdvertBelowNav: true,
    headerType: 'Default',
    holdingPageTitle: 'Title',
    holdingPageSubtitle: 'Subtitle',
    holdingImageAlt: 'alt',
    images: { widgets: [] },
    iconType: 'Light',
    instagramId: 'Default',
    issuPublicationId: 'abc',
    categories: ['value'],
    deprecatedCategories: [],
    keyValueTargeting: [{ key: 'Key', value: 'Value' }],
    navigation: [],
    footerNavigation: [],
    footerNavigationInstances: [],
    mailChimpNewsletterAudienceId: '123',
    googleTagManagerId: '123',
    googleOptimizeContainerId: '123',
    googleOptimizeAsynchronous: false,
    editorialContactFormToEmailAddress: '<EMAIL>',
    salesforceId: 'TESTER-123',
    cookieConsentId: '123',
    robotsTxt: '# Robots string',
    pianoEnabled: true,
    pianoApplicationId: `123abc`,
    pianoApiToken: `123abc`,
    pianoSubscriptionId: `123abc`,
    pianoDmpSiteGroupId: `123abc`,
    featureFlags: [],
    globenewswireRssFeedUrl:
      'https://www.globenewswire.com/Services/Feed/PressRelease?feedId=123456',
    cloudflareZoneId: 'abc123def456',
    anchorColor: '',
    buttonBackgroundColor: '',
    buttonForegroundColor: '',
    enableAnchorColor: false,
    enableButtonForegroundColor: false,
    enableButtonBackgroundColor: false
  }
}
