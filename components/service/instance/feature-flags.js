/**
 * @typedef {Object} FeatureFlag
 * @property {string} label - Display name of the feature
 * @property {string} description - Detailed description of the feature's purpose
 */

/**
 * @type {Object.<string, FeatureFlag>}
 * Collection of feature flags used to toggle functionality across the application
 */

const featureFlags = {
  companyPortal: {
    label: '👔 New Company Portals',
    description: 'This is to display the new company portal reskin'
  },
  pianoAds: {
    label: '🎹 Piano Ads',
    description: 'This is to add pinao targeting to google ads'
  },
  advancedSearch: {
    label: '🔍 Advanced Search',
    description:
      'Enables companies, executives and new Algolia based search system'
  },
  companySearch: {
    label: '🔍 Company Search',
    description: 'Enables company search'
  },
  executiveSearch: {
    label: '🔍 Executives Search',
    description: 'Enables executives search'
  },
  pictureSrcSet: {
    label: '🖼️ Picture SrcSet',
    description: 'Enables <picture /> tag in <ResponiveImage /> component'
  }
}

module.exports = { featureFlags }
