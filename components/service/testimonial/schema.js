const required = require('@clocklimited/validity-required')
const schemata = require('@clocklimited/schemata')
const tagSchema = require('../tag/schema')
const createMinLength = (length, label) => (
  key,
  keyDisplayName,
  object,
  cb
) => {
  const message =
    length === 1
      ? `Must have at least 1 ${label}`
      : `Must have at least ${length} ${label}s`
  return cb(null, object[key].length < length ? message : null)
}

module.exports = (_findOne) => {
  return schemata({
    name: 'Testimonial',
    properties: {
      _id: {
        type: String
      },
      name: {
        type: String,
        validators: [required]
      },
      content: {
        type: String,
        validators: [required]
      },
      instances: {
        type: Array,
        defaultValue: () => [],
        validators: [createMinLength(1, 'instance')]
      },
      createdDate: {
        type: Date,
        defaultValue: () => new Date()
      },
      images: {
        type: Object,
        defaultValue: () => ({ widgets: [] }),
        validators: [required]
      },
      account: {
        type: String
      },
      tags: {
        type: schemata.Array(tagSchema()),
        validators: [createMinLength(1, 'tag')]
      }
    }
  })
}
