import service from './service'

const init = (serviceLocator, done) => {
  serviceLocator.persistence.register('testimonial')
  serviceLocator.acl.addResource('testimonial', {
    actions: ['discover', 'create', 'read', 'update', 'delete', '*']
  })
  serviceLocator.register('testimonialService', service(serviceLocator))

  done()
}

module.exports = () => ({
  testimonialService: ['database', 'acl', 'sectionService', init]
})
