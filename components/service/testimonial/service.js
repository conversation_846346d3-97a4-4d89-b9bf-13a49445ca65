import crudService from 'crud-service'
import cachedCrudService from '../lib/cached-crud-service'
import createSchema from './schema'
import textSearch from '@clocklimited/cf-text-search'
import persistTags from '../tag/persist'

export default (serviceLocator) => {
  const save = serviceLocator.persistence('testimonial')
  const schema = createSchema(save.findOne)
  const service = crudService('Testimonial', save, schema, {})

  service.findOne = save.findOne
  persistTags(serviceLocator, service)

  service.search = textSearch(service)

  service.pre('update', (data, cb) =>
    cb(null, { ...data, modifiedDate: new Date() })
  )

  return cachedCrudService(serviceLocator, service)
}
