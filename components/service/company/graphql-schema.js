const schemata = require('@clocklimited/schemata')
const extendSchemata = require('../../../api/lib/schemata-extender')
const createGraphqlImageBuilderProperty = require('../asset/lib/graphql-image-builder-property')
const createSchema = require('./schema')
const { promisify } = require('util')
const createSectionSchema = require('../section/schema')
const createArticleSchema = require('../article/schema')
const createInstanceSchema = require('../instance/graphql-schema')
const createArticleByContentTypeRetriever = require('../widgets/general/author-articles/lib/article-by-content-type-retriever')
const createStatsSchema = require('./stats/schema')
const createGraphQlStatsSchema = require('./stats/graphql-schema')
const createExecutiveSchema = require('../executive/schema')
const buildImageVariations = require('../asset/lib/image-variation-builder')
const {
  createImageSchema
} = require('../asset/lib/graphql-image-builder-property')

const imageVariations = [
  {
    name: 'logo_free_127',
    crop: 'Free',
    context: 'Logo',
    size: { width: 127 }
  },
  {
    name: 'white_logo_free_480',
    crop: 'Free',
    context: 'White logo',
    size: { width: 480 }
  },
  {
    name: 'thumbnail_1x1_220',
    crop: '1:1',
    context: 'Thumbnail',
    size: { width: 220 }
  },
  {
    name: 'thumbnail_1x1_576',
    crop: '1:1',
    context: 'Thumbnail',
    size: { width: 576 }
  },
  {
    name: 'thumbnail_1x1_720',
    crop: '1:1',
    context: 'Thumbnail',
    size: { width: 720 }
  },
  {
    name: 'thumbnail_1x1_900',
    crop: '1:1',
    context: 'Thumbnail',
    size: { width: 900 }
  },
  {
    name: 'desktop_background_72x17_720',
    crop: '72:17',
    context: 'Desktop Background',
    size: { width: 720 }
  },
  {
    name: 'desktop_background_72x17_1440',
    crop: '72:17',
    context: 'Desktop Background',
    size: { width: 1440 }
  },
  {
    name: 'mobile_background_72x17_320',
    crop: '72:17',
    context: 'Mobile Background',
    size: { width: 320 }
  },
  {
    name: 'mobile_background_72x17_640',
    crop: '72:17',
    context: 'Mobile Background',
    size: { width: 640 }
  }
]

const instanceHeroVariations = (instance) => [
  {
    name: 'hero_72x17_720',
    crop: '72:17',
    context: `${instance} Hero`,
    size: { width: 720 }
  },
  {
    name: 'hero_72x17_1440',
    crop: '72:17',
    context: `${instance} Hero`,
    size: { width: 1440 }
  },
  {
    name: 'hero_72x17_320',
    crop: '72:17',
    context: `${instance} Hero`,
    size: { width: 320 }
  },
  {
    name: 'hero_72x17_640',
    crop: '72:17',
    context: `${instance} Hero`,
    size: { width: 640 }
  }
]

const CLIENT_LOGO_WIDTH = 480
const clientImageVariations = [
  {
    name: `logo_free_${CLIENT_LOGO_WIDTH}`,
    crop: 'Free',
    context: 'Logo',
    size: { width: CLIENT_LOGO_WIDTH }
  }
]

const createPlatformsResolver = (serviceLocator) => {
  return async (companyId) => {
    if (!companyId) {
      return []
    }

    const collection = await serviceLocator.serviceDatabase.collection(
      'article'
    )

    const instanceIds = await collection.distinct('instance', {
      $and: [{ 'companies.company': companyId }, { state: 'Published' }]
    })

    if (!instanceIds || !instanceIds.length) {
      return []
    }

    const instances = await promisify(serviceLocator.instanceService.find)(
      {
        _id: { $in: instanceIds }
      },
      {
        sort: { name: 1 }
      }
    )

    return instances || []
  }
}

const createGraphqlSchema = (serviceLocator) => {
  const getArticlesByContentType = createArticleByContentTypeRetriever(
    serviceLocator
  )
  const schema = createSchema()
  const typeName = 'Company'

  const platformsResolver = createPlatformsResolver(serviceLocator)

  const properties = {
    images: createGraphqlImageBuilderProperty(
      serviceLocator,
      imageVariations,
      typeName
    ),
    clients: createGraphqlImageBuilderProperty(
      serviceLocator,
      clientImageVariations,
      `${typeName}Client`,
      'clients'
    ),

    galleryImages: createGraphqlImageBuilderProperty(
      serviceLocator,
      [
        {
          name: 'image_768',
          crop: '16:9',
          context: 'Image',
          size: { width: 768 }
        }
      ],
      `${typeName}GalleryImages`
    ),

    heroImages: {
      type: createImageSchema(
        instanceHeroVariations(''),
        `${typeName}HeroImages`
      ),
      resolve: (parent, _args, context) => {
        const { instance } = context

        if (!('name' in instance)) {
          return null
        }

        return buildImageVariations(
          serviceLocator.config.darkroom,
          instanceHeroVariations(instance.name),
          parent.images
        )
      }
    },

    hasArticles: {
      type: Boolean,
      resolve: async (parent, args, context) => {
        const articleQuery = {
          'companies.company': parent._id,
          instance: context.instance._id,
          state: 'Published'
        }

        const articleCount = await promisify(
          serviceLocator.articleService.count
        )(articleQuery)

        return articleCount > 0
      }
    },

    companyArticles: {
      type: schemata.Array(
        schemata({
          name: 'ArticlesByContentType',
          properties: {
            type: {
              type: String
            },
            articles: {
              type: schemata.Array(createArticleSchema(serviceLocator))
            }
          }
        })
      ),
      resolve: async (parent, args, context) => {
        return await getArticlesByContentType({
          entityId: parent._id,
          propertyName: 'companies.company',
          region: context.region || 0,
          accountId: context.account._id,
          instanceId: context.instance._id,
          limit: 18
        })
      }
    },

    latestContent: {
      type: schemata.Array(createArticleSchema(serviceLocator)),
      resolve: async (parent, args, context) => {
        const now = new Date()
        const articleQuery = {
          headline: { $regex: parent.name },
          instance: context.instance._id,
          $or: [{ contentType: 'Article' }, { contentType: 'Video' }],
          state: 'Published',
          $and: [
            { $or: [{ liveDate: null }, { liveDate: { $lte: now } }] },
            { $or: [{ expiryDate: null }, { expiryDate: { $gte: now } }] }
          ]
        }

        const latestContent = await promisify(
          serviceLocator.articleService.find
        )(articleQuery, { limit: 4, sort: { displayDate: -1 } })

        return latestContent
      }
    },
    entitySection: {
      type: createSectionSchema(serviceLocator),
      resolve: async (parent, args, context) => {
        context.companyId = parent._id
        const section = await serviceLocator.sectionService.cachedFindOne({
          slug: 'company',
          instance: context.instance._id
        })
        return section
      }
    },
    platforms: {
      type: schemata.Array(createInstanceSchema(serviceLocator)),
      resolve: async (parent, _args, _context) => {
        if (!parent._id) return []
        return platformsResolver(parent._id)
      }
    },

    stats: {
      type: schemata.Array(
        extendSchemata(
          createStatsSchema(),
          createGraphQlStatsSchema(serviceLocator)
        )
      )
    },

    executives: {
      type: schemata.Array(
        extendSchemata(
          createExecutiveSchema(serviceLocator),
          schemata({
            name: 'Executive',
            properties: {
              images: createGraphqlImageBuilderProperty(
                serviceLocator,
                [
                  {
                    name: 'headshot_220x347_220',
                    crop: '220:347',
                    context: 'Headshot',
                    size: { width: 220 }
                  },
                  {
                    name: 'headshot_220x347_576',
                    crop: '220:347',
                    context: 'Headshot',
                    size: { width: 576 }
                  },
                  {
                    name: 'headshotv2_1x1_220',
                    crop: '1:1',
                    context: 'HeadshotV2',
                    size: { width: 220 }
                  },
                  {
                    name: 'headshotv2_1x1_576',
                    crop: '1:1',
                    context: 'HeadshotV2',
                    size: { width: 576 }
                  }
                ],
                'Executive'
              )
            }
          })
        )
      ),
      resolve: async (parent, args, context) => {
        const companyId = parent?._id
        if (!companyId) {
          return []
        }

        return promisify(serviceLocator.executiveService.find)({ companyId })
      }
    },

    isVerified: {
      type: Boolean,
      resolve: async (parent, args, context) => {
        const { verifiedInstances = [] } = parent
        const { instance } = context

        return verifiedInstances.includes(instance._id)
      }
    },

    portalPermissions: {
      type: schemata({
        name: 'PortalPermission',
        properties: {
          isVerified: { type: Boolean },
          enableContentHubNavigation: { type: Boolean },
          enableExecutivesNavigation: { type: Boolean },
          enablePartnershipsNavigation: { type: Boolean },
          enableGetInTouchNavigation: { type: Boolean },
          enableContentHubArticle: { type: Boolean },
          enableContentHubEvent: { type: Boolean },
          enableContentHubInterview: { type: Boolean },
          enableContentHubPodcast: { type: Boolean },
          enableContentHubCompanyReport: { type: Boolean },
          enableContentHubVideo: { type: Boolean },
          enableContentHubWebinar: { type: Boolean },
          enableContentHubWhitepaper: { type: Boolean }
        }
      }),
      resolve: async (parent, args, context) => {
        const { instance } = context

        if (!instance?._id) {
          return null
        }

        if (!parent?.instancePermissions) {
          return null
        }

        if (instance._id in parent?.instancePermissions) {
          return parent.instancePermissions[instance._id]
        }

        return null
      }
    }
  }

  const graphqlSchema = schemata({
    name: typeName,
    properties
  })

  return extendSchemata(schema, graphqlSchema)
}

module.exports = createGraphqlSchema
module.exports.imageVariations = imageVariations
module.exports.createPlatformsResolver = createPlatformsResolver
