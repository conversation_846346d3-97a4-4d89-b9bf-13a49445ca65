const required = require('@clocklimited/validity-required')
const schemata = require('@clocklimited/schemata')

const imageConfig = require('./image-config.json')
const createUniqueValidator = require('validity-unique-property')
const customValidityMessage = require('../lib/custom-validity-message')
const createContextValidator = require('validity-cf-image-context-selection')
const validateIfPropertyEquals = require('@clocklimited/validity-validate-if-property-equals')
const createCropValidator = require('../../../lib/validators/crop-integrity-validator')
const createAiMetaSchema = require('./lib/ai-meta-schema')
const createStatsSchema = require('./stats/schema')

module.exports = (findOne) => {
  const requiredCrops = imageConfig.crops.map((crop) => crop.name)
  const requiredContexts = ['Logo', 'White logo', 'Thumbnail']
  const uniqueSlugValidator = customValidityMessage(
    createUniqueValidator(findOne),
    'This slug is already in use'
  )

  const schema = schemata({
    name: 'Company',
    properties: {
      _id: {
        type: String,
        tag: ['import']
      },
      name: {
        type: String,
        validators: [required],
        tag: ['import']
      },
      slug: {
        type: String,
        validators: [
          validateIfPropertyEquals('showProfilePage', true, required),
          validateIfPropertyEquals('showProfilePage', true, uniqueSlugValidator)
        ],
        tag: ['import']
      },
      oneLiner: {
        type: String
      },
      shortDescription: {
        type: String,
        tag: ['import']
      },
      description: {
        type: String,
        validators: {
          all: [validateIfPropertyEquals('showProfilePage', true, required)]
        },
        tag: ['import']
      },
      linkedinId: {
        type: String,
        tag: ['import']
      },
      website: {
        type: String,
        tag: ['import']
      },
      emailAddress: {
        type: String,
        tag: ['import']
      },
      showProfilePage: {
        type: Boolean,
        defaultValue: true,
        tag: ['import']
      },
      images: {
        type: Object,
        defaultValue: () => ({ widgets: [] }),
        validators: {
          all: [
            validateIfPropertyEquals(
              'showProfilePage',
              true,
              createContextValidator(requiredContexts)
            ),
            validateIfPropertyEquals(
              'showProfilePage',
              true,
              createCropValidator(requiredCrops)
            )
          ]
        },
        tag: ['import']
      },
      createdDate: {
        type: Date,
        defaultValue: () => new Date(),
        tag: ['import']
      },
      account: {
        type: String,
        validators: { all: [required] },
        tag: ['import']
      },
      migratedFromId: {
        type: String,
        tag: ['import']
      },
      duplicateCompanyIds: {
        type: Array
      },
      migratedToCompanyId: {
        type: String
      },
      // APOLLO DATA
      streetAddress: {
        type: String,
        tag: ['apollo']
      },
      city: {
        type: String,
        tag: ['apollo']
      },
      state: {
        type: String,
        tag: ['apollo']
      },
      country: {
        type: String,
        tag: ['apollo']
      },
      postalCode: {
        type: String,
        tag: ['apollo']
      },
      hqPhone: {
        type: String,
        tag: ['apollo']
      },
      linkedinUrl: {
        type: String,
        tag: ['apollo']
      },
      twitterUrl: {
        type: String,
        tag: ['apollo']
      },
      facebookUrl: {
        type: String,
        tag: ['apollo']
      },
      websiteUrl: {
        type: String,
        tag: ['apollo']
      },
      primaryDomain: {
        type: String,
        tag: ['apollo']
      },
      industries: {
        type: Array,
        tag: ['apollo']
      },
      keywords: {
        type: Array,
        tag: ['apollo']
      },
      foundedYear: {
        type: Number,
        tag: ['apollo']
      },
      publiclyTradedSymbol: {
        type: String,
        tag: ['apollo']
      },
      publiclyTradedExchange: {
        type: String,
        tag: ['apollo']
      },
      marketCap: {
        type: Number,
        tag: ['apollo']
      },
      annualRevenue: {
        type: Number,
        tag: ['apollo']
      },
      estimatedNumEmployees: {
        type: Number,
        tag: ['apollo']
      },
      currentTechnologies: {
        type: Array,
        tag: ['apollo']
      },
      aiMeta: {
        type: schemata.Array(createAiMetaSchema()),
        tag: ['apollo']
      },
      salesforceId: {
        type: String,
        tag: ['apollo']
      },
      apolloId: {
        type: String,
        tag: ['apollo']
      },
      cacheLastClearedDate: {
        type: Date
      },
      heroVideoId: {
        type: String
      },
      clients: {
        type: Object,
        defaultValue: () => ({ widgets: [] })
      },
      galleryImages: {
        type: Object,
        defaultValue: () => ({ widgets: [] })
      },
      stats: {
        type: schemata.Array(createStatsSchema())
      },
      executiveSortOrder: {
        type: Array
      },
      leadershipTeam: {
        type: Array,
        defaultValue: () => []
      },
      formCraftId: {
        type: String,
        defaultValue: () => ''
      },
      enableExecutivesNavigation: {
        type: Boolean,
        defaultValue: () => false
      },
      enablePartnershipsNavigation: {
        type: Boolean,
        defaultValue: () => false
      },
      enableContentHubNavigation: {
        type: Boolean,
        defaultValue: () => false
      },
      enableGetInTouchNavigation: {
        type: Boolean,
        defaultValue: () => false
      },
      algoliaLastCrawlDate: {
        type: Date
      },
      modifiedDate: {
        type: Date
      },
      linkedinPosts: {
        type: schemata.Array(linkedinPostSchema),
        defaultValue: () => []
      },
      instancePermissions: {
        type: Object,
        defaultValue: () => ({})
      },
      isVerified: {
        type: Boolean,
        defaultValue: () => false
      }
    }
  })

  return schema
}

const linkedinPostSchema = schemata({
  name: 'CompanyLinkedinPosts',
  properties: {
    activityId: { type: String },
    text: { type: String },
    activityDate: { type: Date }
  }
})
