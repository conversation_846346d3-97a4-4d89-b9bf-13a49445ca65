module.exports.validNewModel = require('./valid-new')
module.exports.validFirstSavedModel = require('./valid-saved')
module.exports.completeValidModel = require('./valid-complete')
module.exports.completeModelResponseWithAllData = require('./valid-complete-saved')

module.exports.validPartialModel = { name: 'Company Name' }

module.exports.sortColumns = {
  sortColumn: '_id',
  filterColumn: 'name',
  filterValue: 'Company Name'
}

module.exports.invalidModel = {}

module.exports.createErrorResponse = {
  errors: {
    name: 'Name is required',
    account: 'Account is required',
    description: 'Description is required',
    images: 'Image contexts require selection: Logo, White logo, Thumbnail',
    slug: 'Slug is required'
  }
}
