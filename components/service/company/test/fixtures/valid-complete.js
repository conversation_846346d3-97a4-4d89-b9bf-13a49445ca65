const extend = require('lodash.assign')
const validNew = require('./valid-new')

module.exports = function (suffix) {
  return extend({}, validNew(suffix), {
    _id: 1,
    linkedinId: 'a',
    shortDescription: 'a',
    website: 'https://www.google.com',
    emailAddress: 'a',
    migratedFromId: 'a',
    migratedToCompanyId: 'a',
    duplicateCompanyIds: [],
    createdDate: new Date().toISOString(),
    modifiedDate: new Date().toISOString(),
    streetAddress: '123 Mock St',
    city: 'Mockville',
    state: 'CA',
    country: 'USA',
    postalCode: '12345',
    hqPhone: '******-555-1234',
    linkedinUrl: 'https://www.linkedin.com/company/mockcompany',
    twitterUrl: 'https://twitter.com/mockcompany',
    facebookUrl: 'https://www.facebook.com/mockcompany',
    websiteUrl: 'https://www.example.com',
    primaryDomain: 'example.com',
    industries: ['Technology', 'Software'],
    keywords: ['SaaS', 'Cloud Computing', 'Enterprise Solutions'],
    foundedYear: 2023,
    publiclyTradedSymbol: 'MOCK',
    publiclyTradedExchange: 'NASDAQ',
    marketCap: 5000000000,
    annualRevenue: 1000000000,
    estimatedNumEmployees: 500,
    currentTechnologies: ['JavaScript', 'AWS', 'React'],
    salesforceId: '0012w00000XyZAB',
    apolloId: '1234567890',
    aiMeta: [{ label: 'ai-label', value: 'ai-value' }],
    cacheLastClearedDate: new Date().toISOString(),
    heroVideoId: '1234567890',
    oneLiner: 'a',
    formCraftId: '12345',
    enableExecutivesNavigation: false,
    enablePartnershipsNavigation: false,
    enableContentHubNavigation: false,
    enableGetInTouchNavigation: false,
    algoliaLastCrawlDate: new Date().toISOString(),
    galleryImages: {
      widgets: []
    },
    linkedinPosts: []
  })
}
