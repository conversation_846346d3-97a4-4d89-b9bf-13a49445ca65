const extend = require('lodash.assign')
const validNew = require('./valid-new')

module.exports = function () {
  return extend({}, validNew(), {
    _id: 1,
    shortDescription: null,
    website: null,
    emailAddress: null,
    linkedinId: null,
    migratedFromId: null,
    migratedToCompanyId: null,
    duplicateCompanyIds: null,
    createdDate: new Date().toISOString(),
    modifiedDate: new Date().toISOString(),
    streetAddress: null,
    city: null,
    state: null,
    country: null,
    postalCode: null,
    hqPhone: null,
    linkedinUrl: null,
    twitterUrl: null,
    facebookUrl: null,
    websiteUrl: null,
    primaryDomain: null,
    industries: [],
    keywords: [],
    foundedYear: null,
    publiclyTradedSymbol: null,
    publiclyTradedExchange: null,
    marketCap: null,
    annualRevenue: null,
    estimatedNumEmployees: null,
    currentTechnologies: [],
    salesforceId: null,
    apolloId: null,
    aiMeta: [],
    cacheLastClearedDate: null,
    heroVideoId: null,
    oneLiner: null,
    formCraftId: '12345',
    enableExecutivesNavigation: false,
    enablePartnershipsNavigation: false,
    enableContentHubNavigation: false,
    enableGetInTouchNavigation: false,
    algoliaLastCrawlDate: null,
    linkedinPosts: []
  })
}
