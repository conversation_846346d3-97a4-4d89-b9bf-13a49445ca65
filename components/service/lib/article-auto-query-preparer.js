const { promisify } = require('util')

const queryExpression = {
  tags: {
    key: 'tags.tag',
    fn: (tags) => ({ $in: tags.map((tag) => tag.tag) })
  },
  sections: { key: 'sections', fn: (sections) => ({ $in: sections }) }
}

const prepareAutoQuery = (region, serviceLocator, instanceId) => async (
  list
) => {
  var q = { query: {}, options: {}, overrides: null }

  if (instanceId) {
    Object.keys(queryExpression).forEach((property) => {
      if (Array.isArray(list[property]) && list[property].length) {
        q.query[queryExpression[property].key] = queryExpression[property].fn(
          list[property]
        )
      }
    })
  }

  if (list.featured) {
    q.query.featured = true
  }

  if (list.upcoming) {
    q.query.startDate = { $gte: new Date() }
  }

  if (list.contentTypes && list.contentTypes.length) {
    q.query.contentType = { $in: list.contentTypes }
  }

  if (list.subContentTypes && list.subContentTypes.length) {
    q.query.subContentType = { $in: list.subContentTypes }
  }

  if (list.tags && list.tags.length) {
    q.query['tags.tag'] = { $in: list.tags.map((t) => t.tag) }
  }

  if (list.tagsExcluded && list.tagsExcluded.length) {
    if (!q.query['tags.tag']) {
      q.query['tags.tag'] = {}
    }
    q.query['tags.tag'].$nin = list.tagsExcluded.map((t) => t.tag)
  }

  if (instanceId) {
    if (list.categories && list.categories.length) {
      const instance = await promisify(serviceLocator.instanceService.read)(
        instanceId
      )
      const categories = []
      list.categories.forEach((index) => {
        const categoryName = instance.categories[Number(index)]
        if (categoryName) {
          categories.push(categoryName)
        }
      })
      q.query.category = { $in: categories }
    }
  } else {
    serviceLocator.logger.error(
      'Instance ID is required to filter by categories'
    )
  }

  if (region) {
    q.query.$or = [{ region }, { region: { $size: 0 } }]
  }

  switch (list.order) {
    case 'recent':
      q.options.sort = { displayDate: -1 }
      break
    case 'upcoming':
      q.options.sort = { startDate: 1 }
      break
    case 'furthest':
      q.options.sort = { startDate: -1 }
      break
    case 'mostPopular30Days': {
      const thirtyDaysAgoDate = new Date(
        new Date().getTime() - 30 * 24 * 60 * 60 * 1000
      )
      q.query.displayDate = { $gte: thirtyDaysAgoDate }
      q.options.sort = { last30DaysNumViews: -1 }
      break
    }
  }

  return q
}

module.exports = prepareAutoQuery
