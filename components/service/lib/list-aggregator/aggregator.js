const { promisify } = require('util')
const extrapolateSectionIds = require('cf-section-extrapolator')
const aggregateList = require('./single-list-aggregator')

const aggregateLists = (
  listService,
  sectionService,
  crudService,
  options
) => async (lists, deduper, limit, section, skip = 0) => {
  const foundLists = await promisify(listService.find)({ _id: { $in: lists } })
  foundLists.sort((a, b) => lists.indexOf(a._id) - lists.indexOf(b._id))

  let foundItems = []
  let totalCount = 0

  for (const list of foundLists) {
    if (limit && foundItems.length === limit) break
    const itemLimit =
      Math.min(list.limit, limit || Infinity) ||
      (limit ? limit - foundItems.length : null)
    if (section && list.sections && list.sections.length) {
      const ids = await promisify(extrapolateSectionIds)(
        sectionService,
        section._id,
        list.sections,
        typeof options.ensurePublic !== 'undefined'
          ? { ensurePublic: options.ensurePublic }
          : {}
      )
      list.sections = ids
    }
    const { items, total } = await aggregateList(
      list,
      itemLimit,
      skip,
      deduper,
      crudService,
      options
    )
    if (total) totalCount += total
    if (deduper) items.map((item) => deduper(item._id))
    foundItems = foundItems.concat(items)
  }

  return { articles: foundItems, total: totalCount }
}

module.exports = aggregateLists
