const create = (serviceLocator) => {
  async function purgeCacheByUrl(url, zoneId, done) {
    if (!url) {
      serviceLocator.logger.info('(purgeCacheByUrl) No URL provided!')
      return
    }

    if (!zoneId) {
      serviceLocator.logger.info('(purgeCacheByUrl) No Zone ID provided!')
      return
    }

    try {
      const response = await fetch(
        `https://api.cloudflare.com./client/v4/zones/${zoneId}/purge_cache`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Auth-Email': serviceLocator.secrets.cloudflare.authEmail,
            Authorization: `Bearer ${serviceLocator.secrets.cloudflare.apiToken}`
          },
          body: JSON.stringify({ files: [url.toString()] })
        }
      )

      if (response.ok) {
        const data = await response.json()
        serviceLocator.logger.info('(purgeCacheByUrl) data', data)
        serviceLocator.logger.info(
          '(purgeCacheByUrl) success: ',
          url.toString()
        )
        done(data)
      } else {
        const data = await response.json()
        serviceLocator.logger.info('(purgeCacheByUrl) data (not ok)', data)
      }
      return
    } catch (error) {
      serviceLocator.logger.error('error: ', error)
      serviceLocator.logger.info('(purgeCacheByUrl) An error has occured')
    }
  }

  return purgeCacheByUrl
}

module.exports = create
