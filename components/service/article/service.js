import { algoliasearch } from 'algoliasearch'

const isVisible = require('cf-visibility-check')
const async = require('async')
const filter = require('./filter')
const createSchema = require('./schema')
const createCrudService = require('../trash/lib/soft-delete-crud-service')
const cachedCrudService = require('../lib/cached-crud-service')
const persistTags = require('../tag/persist')
const createSearch = require('@clocklimited/cf-text-search')
const extend = require('lodash.assign')
const pick = require('lodash.pick')
const castProperty = require('@clocklimited/schemata').castProperty
const crypto = require('crypto')
const hat = require('hat')

const createViewCountUpdater = require('./lib/view-count-updater')

module.exports = (serviceLocator, options) => {
  options = options || {}
  const {
    logger,
    sectionService,
    authorService,
    instanceService,
    trashService
  } = serviceLocator

  const updateViewCounts = createViewCountUpdater(serviceLocator)

  const save = serviceLocator.persistence('article')
  const schema = createSchema(serviceLocator, save.findOne, sectionService)
  const properties = schema.getProperties()
  const crudService = createCrudService(
    'Article',
    save,
    schema,
    {},
    trashService
  )
  const defaultFilter = (q) => q
  const applyQueryFilter = options.filter || defaultFilter
  const sectionFilter = options.sectionFilter || {}
  const inDevMode = serviceLocator.env && serviceLocator.env === 'development'

  let algoliaClient
  if (serviceLocator?.secrets?.algolia?.searchApiKey) {
    algoliaClient = algoliasearch(
      serviceLocator.secrets.algolia.applicationId,
      serviceLocator.secrets.algolia.searchApiKey
    )
  } else {
    serviceLocator.logger.info(
      'No Algolia search API key provided. Either something went wrong or this is a test'
    )
  }

  function ArticleService() {}

  ArticleService.prototype = Object.create(crudService)

  // TODO: remove the validationOptions as they only invite trouble and have no use cases
  const getValidationOptions = (validationOptions, object) => {
    validationOptions = extend(validationOptions, {})

    if (typeof object.state !== 'undefined' && object.state !== '') {
      logger.info('Using ' + object.state + ' validation set for Article.')
      validationOptions.set = object.state.toLowerCase()

      return validationOptions
    } else {
      logger.info('Article with no state submitted')
      validationOptions.set = 'draft'

      return validationOptions
    }
  }

  /**
   * We've opted to override create instead of making a publish method in order
   * to guarantee that the publish validation is adhered to.
   */
  ArticleService.prototype.create = (object, validationOptions, callback) => {
    if (typeof validationOptions === 'function') {
      callback = validationOptions
      validationOptions = {}
    }

    const hash = crypto
      .createHash('sha256')
      .update(
        JSON.stringify({
          headline: object.headline,
          sell: object.sell,
          slug: object.slug,
          sections: object.sections,
          contentType: object.contentType,
          subContentType: object.subContentType,
          category: object.category,
          body: object.body,
          tags: object.tags,
          author: object.author,
          instance: object.instance,
          account: object.account
        })
      )
      .digest('hex')
    object.hash = hash || hat()

    validationOptions = getValidationOptions(validationOptions, object)
    return crudService.create(object, validationOptions, (err, object) => {
      if (err) return callback(err)
      addArticleProperties([object], first(callback))
    })
  }

  ArticleService.prototype.update = (object, validationOptions, callback) => {
    if (typeof validationOptions === 'function') {
      callback = validationOptions
      validationOptions = {}
    }

    // add last modified date
    object.modifiedDate = new Date()

    validationOptions = getValidationOptions(validationOptions, object)
    crudService.update(object, validationOptions, (err, object) => {
      if (err) return callback(err)
      if (!object) return callback(null, undefined)
      addArticleProperties([object], first(callback))
    })
  }

  ArticleService.prototype.partialUpdate = (
    object,
    validationOptions,
    callback
  ) => {
    if (typeof validationOptions === 'function') {
      callback = validationOptions
      validationOptions = {}
    }

    object.modifiedDate = new Date()

    return crudService.partialUpdate(
      object,
      validationOptions,
      (err, object) => {
        if (err) return callback(err)
        if (!object) return callback(null, undefined)
        addArticleProperties([object], first(callback))
      }
    )
  }

  ArticleService.prototype.read = (id, callback) => {
    return save.read(
      castProperty(properties[save.idProperty].type, id),
      (error, object) => {
        if (error) return callback(error)
        if (!object) return callback(null, undefined)
        addArticleProperties([object], first(callback))
      }
    )
  }

  const sectionsLookup = async (ids) => {
    // const startTime = Date.now()
    const sectionMap = {}
    let sections = []
    if (serviceLocator.cachedSections) {
      sections = serviceLocator.cachedSections
    } else {
      const query = {}

      if (ids && Array.isArray(ids)) query._id = { $in: ids }

      sections = await sectionService.cachedFind(query, {
        _id: 1,
        name: 1,
        instance: 1,
        fullUrlPath: 1,
        parent: 1
      })
    }
    sections.forEach((section) => {
      sectionMap[section._id] = section
    })
    // console.log('###### Find PUB - section lookup', Date.now() - startTime)
    return sectionMap
  }

  const authorsLookup = async () => {
    const authorMap = {}
    const authors = await authorService.cachedFind({}, { _id: 1, name: 1 })
    authors.forEach((author) => {
      authorMap[author._id] = author
    })
    return authorMap
  }

  const instancesLookup = async () => {
    // const startTime = Date.now()
    const instanceMap = {}
    const instances = await instanceService.cachedFind({})
    instances.forEach((instance) => {
      const protocol = inDevMode ? 'http://' : 'https://'
      const subdomain = instance.subdomain
      const port = inDevMode ? `:${serviceLocator.config.port}` : ''

      const fullUrl = protocol + subdomain + port

      instance.__fullUrl = fullUrl

      instanceMap[instance._id] = instance
    })
    // console.log('###### Find PUB - instance lookup', Date.now() - startTime)
    return instanceMap
  }

  const getBreadcrumb = (section, sectionMap) => {
    const crumb = []
    let mappedSection

    if (section && sectionMap[section._id]) {
      mappedSection = sectionMap[section._id]
      crumb.push(
        extend(pick(mappedSection, 'name', 'fullUrlPath'), {
          publicVisible: isVisible(mappedSection)
        })
      )

      return getBreadcrumb(mappedSection.parent, sectionMap).concat(crumb)
    }
    return crumb
  }

  const getEventFullUrlPath = (article, callback) => {
    // NOT EFFICIENT
    serviceLocator.eventService.read(article.eventId, (error, event) => {
      if (error) return callback(error)
      if (!event)
        return callback(
          new Error('Event not found', !!article.eventId, article.eventId)
        )
      serviceLocator.eventUmbrellaService.read(
        event.eventUmbrellaId,
        (error, eventUmbrella) => {
          if (error) return callback(error)
          const eventArticleCategory = event.articleCategories.find(
            (category) => category.key === article.eventArticleCategoryKey
          )
          const path =
            'events' +
            '/' +
            eventUmbrella.slug +
            '/' +
            event.slug +
            '/' +
            eventArticleCategory.articleBaseSlug +
            '/' +
            article.slug
          callback(null, path)
        }
      )
    })
  }

  const addArticleProperties = (articles, callback) => {
    const sectionIds = articles.reduce((ids, article) => {
      if (
        Array.isArray(article.sections) &&
        article.sections[0] !== undefined
      ) {
        ids.push(article.sections[0])
      }
      return ids
    }, [])

    const tasks = {
      sectionMap: sectionsLookup.bind(null, sectionIds),
      instanceMap: instancesLookup,
      authorMap: authorsLookup
    }
    async.parallel(tasks, (error, lookups) => {
      if (error) return callback(error)

      const { sectionMap, instanceMap, authorMap } = lookups

      const results = []
      async
        .eachSeries(articles, (article, cb) => {
          const sectionId = Array.isArray(article.sections)
            ? article.sections[0]
            : undefined
          const authorId = article.author
          const section = sectionId ? sectionMap[sectionId] : null
          const instance = section ? instanceMap[section.instance] : null
          const author = authorId ? authorMap[authorId] : null

          if (sectionId && !section) {
            logger.warn(
              'Article [%s] linked to a section [%s] that does not exist',
              article._id,
              sectionId
            )
          }

          // If the article has a section then embellish the object with useful section related info
          if (section) {
            // Set the fullUrlPath from the section
            article.__fullUrlPath = section.fullUrlPath + '/' + article.slug

            // Remove double slashes in url path
            article.__fullUrlPath = article.__fullUrlPath.replace('//', '/')

            // Create the bread crumb
            article.__breadcrumb = getBreadcrumb(section, sectionMap)

            article.__instance = instance

            article.__author = author

            // HACKY
            article.section = sectionId
          }
          if (article.eventId && article.eventArticleCategoryKey) {
            // Runs asyncronously to set the event fullUrlPath
            getEventFullUrlPath(article, (error, path) => {
              if (error) serviceLocator.logger.error(error)
              if (path) {
                article.__instance = article.instance
                  ? instanceMap[article.instance]
                  : null
                article.__author = author
                article.__fullUrlPath = '/' + path
              }
              results.push(article)
              cb()
            })
          } else {
            results.push(article)
            cb()
          }
        })
        .then(() => {
          // Insert results into file
          callback(undefined, results)
        })
        .catch((err) => console.error(err))
    })
  }
  ArticleService.prototype.count = (query, callback) => {
    save.count(filter.extendQuery(query, applyQueryFilter(query)), callback)
  }

  ArticleService.prototype.find = (query, options, callback) => {
    if (typeof options === 'function') {
      callback = options
      options = {}
    }

    if (typeof callback === 'undefined') {
      // Stream implementation
      return save.find(
        filter.extendQuery(query, applyQueryFilter(query)),
        options
      )
    }

    // const startTime = Date.now()
    save.find(
      filter.extendQuery(query, applyQueryFilter(query)),
      options,
      (error, objects) => {
        // console.log(
        //   '###### Find PUB - save.find',
        //   Date.now() - startTime,
        //   JSON.stringify(filter.extendQuery(query, applyQueryFilter(query))),
        //   JSON.stringify(options)
        // )
        if (error) return callback(error)
        if (!objects.length) return callback(null, objects)
        addArticleProperties(objects, callback)
      }
    )
  }

  // Convert an array callback response to the first item in the list
  const first = (callback) => {
    return (error, items) => {
      if (error) return callback(error)
      callback(null, items[0])
    }
  }

  // Find the articles that are available to the public
  ArticleService.prototype.findPublic = (query, options, callback) => {
    if (typeof options === 'function') {
      callback = options
      options = {}
    }

    const publicQuery = filter.publicQuery(query, options)
    logger.debug('Got public article query', JSON.stringify(publicQuery))

    // Clone options so they don't get stripped for possible subsequent
    // finds with signpost
    options = extend({}, options)

    // Remove the date option so that mongo returns other properties
    if (options.date) {
      delete options.date
    }

    ArticleService.prototype.find(publicQuery, options, callback)
  }
  ArticleService.prototype.findByUrl = (findFn, urlPath, options, callback) => {
    // Get just the section part of the URL
    const trimmed = urlPath.replace(/\/+$/, '')
    const lastSlashPos = trimmed.lastIndexOf('/')
    const sectionPath = urlPath.substring(0, lastSlashPos)
    const slug = trimmed.substring(lastSlashPos + 1)
    const sectionQuery = extend({}, { fullUrlPath: sectionPath }, sectionFilter)

    logger.debug('Finding section with fullUrlPath', sectionPath)

    // First find the sections
    sectionService.find(sectionQuery, (error, sections) => {
      if (error) return callback(error)
      if (sections.length === 0) return callback(null, undefined)
      // then the matching articles
      findFn(
        { sections: sections[0]._id, slug: slug },
        options,
        first(callback)
      )
    })
  }

  // Find the first public Article with a given URL
  ArticleService.prototype.findPublicByUrl = ArticleService.prototype.findByUrl.bind(
    null,
    ArticleService.prototype.findPublic
  )

  ArticleService.prototype.countPublic = (query, options, callback) => {
    if (typeof options === 'function') {
      callback = options
      options = {}
    }

    const publicQuery = filter.publicQuery(query, options)
    const finalQuery = filter.extendQuery(
      publicQuery,
      applyQueryFilter(publicQuery)
    )
    logger.debug('Got public article count query', JSON.stringify(finalQuery))

    save.count(finalQuery, callback)
  }

  const service = new ArticleService()

  service.findOne = save.findOne
  persistTags(serviceLocator, service)
  service.search = createSearch(service)

  service.algoliaSearch = async (query, options, callback) => {
    if (!algoliaClient) {
      serviceLocator.logger.info(
        '[service.algoliaSearch] No Algolia client provided. Either something went wrong or this is a test'
      )
      return callback(null, null)
    }
    if (typeof options === 'function') {
      callback = options
      options = {}
    }
    options.facetFilters = options.facetFilters || []
    options.page = options.page || 1
    options.hitsPerPage = options.hitsPerPage || 5
    options.facets = options.facets || []

    const searchParams = {
      query,
      facetFilters: options.facetFilters,
      page: Math.max(options.page - 1, 0),
      hitsPerPage: options.hitsPerPage
    }

    // Add facets parameter if provided
    if (options.facets && options.facets.length > 0) {
      searchParams.facets = options.facets
    }

    try {
      const results = await algoliaClient.searchSingleIndex({
        indexName: 'article',
        searchParams
      })
      if (!results) return callback(null, [])
      callback(null, results)
    } catch (error) {
      callback(error)
    }
  }

  service.addArticleProperties = addArticleProperties

  service.on('view', updateViewCounts)

  if (options.uncached) return service

  return cachedCrudService(serviceLocator, service, {
    functions: ['findPublic', 'count', 'countPublic']
  })
}
