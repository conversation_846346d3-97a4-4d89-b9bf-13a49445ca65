const schemata = require('@clocklimited/schemata')
const moment = require('moment')
const unique = require('lodash.uniq')
const required = require('@clocklimited/validity-required')
const requireOne = require('validity-require-one')
const oneOf = require('validity-required-options')
const validateLength = require('validity-length')
const validateIfPropertyEquals = require('@clocklimited/validity-validate-if-property-equals')
const dateBeforeExpiryValidator = require('validity-date-before-property')(
  'expiryDate'
)
const dateBeforeEndDateValidator = require('validity-date-before-property')(
  'endDate'
)
const { promisify } = require('util')
const validateUrl = require('@clocklimited/validity-url')
const validateIfSet = require('@clocklimited/validity-validate-if-set')
const createContextValidator = require('validity-cf-image-context-selection')
const createUniqueValidator = require('validity-unique-property')
const createCropValidator = require('../../../lib/validators/crop-integrity-validator')
const validateIf = require('@clocklimited/validity-validate-if')
const createImageCaptionValidator = require('../../../lib/validators/image-caption-validator')
const hasWidgets = require('../../../lib/has-widgets')
const imageConfig = require('./image-config.json')
const tagSchema = require('../tag/schema')
const customValidityMessage = require('../lib/custom-validity-message')
const createWidgetAreaResolveType = require('../widget/lib/widget-area-resolve-type')
const categoryTypeValidator = require('./lib/category-type-validator')
const createCompaniesSchema = require('./lib/companies-schema')
const createPartnershipsSchema = require('./lib/partnerships-schema')
const createExecutiveSchema = require('./lib/executive-schema')

const contentTypes = require('./content-types.json')

const defaultSubType = 'Standard'

module.exports = (serviceLocator, find, sectionService) => {
  const requiredContexts = ['Thumbnail', 'Hero', 'Share Image']
  const requiredCrops = imageConfig.crops.map((crop) => crop.name)

  const fullUrlUniqueValidator = customValidityMessage(
    createUniqueValidator(find, { keys: ['sections'] }),
    'Slug within this section is already in use'
  )

  const eventSlugUniqueValidator2 = async (key, keyDisplayName, object, cb) => {
    const slug = object[key]
    const event = await promisify(serviceLocator.eventService.read)(
      object.eventId
    )

    if (!event) return cb(null, undefined)

    const articlesWithSameSlug = await promisify(
      serviceLocator.articleService.find
    )({
      slug,
      contentType: 'BizClik Live Event',
      instance: object.instance
    })

    if (articlesWithSameSlug.length === 0) {
      return cb(null, undefined)
    }

    const thisArticleBaseSlug = event.articleCategories.find(
      (c) => c.key === object.eventArticleCategoryKey
    )?.articleBaseSlug
    const thisIdentifier = `${thisArticleBaseSlug}/${slug}`

    const embellishedArticles = articlesWithSameSlug.map((article) => {
      const articleBaseSlug = event.articleCategories.find(
        (c) => c.key === article.eventArticleCategoryKey
      )?.articleBaseSlug
      return {
        ...article,
        identifier: `${articleBaseSlug}/${article.slug}`
      }
    })

    const result = embellishedArticles.reduce((acc, article) => {
      if (!acc[article.identifier]) {
        acc[article.identifier] = [article]
      } else {
        acc[article.identifier].push(article)
      }
      return acc
    }, {})

    if (result[thisIdentifier] && result[thisIdentifier].length > 1) {
      return cb(null, 'Slug within this section is already in use')
    }

    return cb(null, undefined)
  }

  const eventIdValidator = async (key, msg, object, callback) => {
    const value = object[key]

    if (object.contentType !== 'BizClik Live Event')
      return callback(null, undefined)

    if (object.contentType === 'BizClik Live Event' && !value)
      callback(null, 'Event ID is required')

    const event = await promisify(serviceLocator.eventService.read)(value)
    if (!event) return callback(null, 'Event not found')
    const eventUmbrella = await promisify(
      serviceLocator.eventUmbrellaService.read
    )(event.eventUmbrellaId)
    if (!eventUmbrella) return callback(null, 'Event Umbrella not found')
    const validInstances = [
      eventUmbrella.primaryInstance,
      ...eventUmbrella.secondaryInstances
    ]
    if (!validInstances.includes(object.instance))
      return callback(null, `Instance not part of the chosen Event's Portfolio`)
    return callback(null, undefined)
  }

  const regexSlugValidator = (key, keyDisplayName, object, cb) => {
    const value = object[key]
    if (!value) return cb(null, undefined)

    // ChatGPT o1
    if (!/^[a-z0-9-]+$/.test(value)) {
      return cb(
        null,
        'Slug can only contain lowercase letters, numbers, and hyphens (no spaces).'
      )
    }

    return cb(null, undefined)
  }

  const createSlugUniqueValidator = () => {
    const validator = [required, regexSlugValidator].concat(
      contentTypes
        .map((contentType) => {
          const c = contentType.type
          switch (c) {
            case 'BizClik Live Event':
              return [
                validateIfPropertyEquals(
                  'contentType',
                  c,
                  eventSlugUniqueValidator2
                )
              ]
            default:
              return [
                validateIfPropertyEquals(
                  'contentType',
                  c,
                  fullUrlUniqueValidator
                )
              ]
          }
        })
        .flat()
    )

    return {
      draft: validator,
      published: validator,
      archived: []
    }
  }

  const createSectionsValidator = () => {
    const contentTypeKeys = contentTypes
      .map((c) => c.type)
      .filter((key) => key !== 'BizClik Live Event')
    const validator = contentTypeKeys
      .map((contentType) => [
        validateIfPropertyEquals('contentType', contentType, validateLength(1)),
        validateIfPropertyEquals(
          'contentType',
          contentType,
          uniqueInstanceValidator
        )
      ])
      .flat()
    return {
      draft: [],
      published: validator,
      archived: []
    }
  }

  const uniqueInstanceValidator = (key, keyDisplayName, object, cb) => {
    // If no section service, assume valid
    if (!sectionService) return cb(null, undefined)

    sectionService.find({ _id: { $in: object[key] } }, (err, sections) => {
      if (err) return cb(err)

      const sectionCount = sections.length
      const uniqueInstanceCount = unique(
        sections.map((section) => section.instance)
      ).length

      if (sectionCount !== uniqueInstanceCount) {
        return cb(
          null,
          keyDisplayName +
            ' must not contain more than one section from any instance'
        )
      }

      return cb(null, undefined)
    })
  }

  const mappedContentTypes = contentTypes.map((content) => content.type)

  return schemata({
    name: 'Article',
    properties: {
      _id: {
        type: String
      },

      hash: {
        type: String
      },

      state: {
        type: String,
        options: ['Draft', 'Published', 'Archived', 'Trashed'],
        defaultValue: 'Draft',
        validators: { all: [] }
      },

      hasBeenPublished: {
        type: Boolean,
        defaultValue: false
      },

      headline: {
        type: String,
        validators: {
          draft: [required],
          published: [required],
          archived: []
        }
      },

      sell: {
        type: String,
        name: 'Standfirst',
        validators: {
          draft: [],
          published: [required],
          archived: []
        }
      },

      slug: {
        type: String,
        validators: createSlugUniqueValidator()
      },

      sections: {
        type: Array,
        validators: createSectionsValidator()
      },

      contentType: {
        type: String,
        validators: {
          draft: [],
          published: [required, oneOf(mappedContentTypes)],
          archived: []
        }
      },

      subContentType: {
        type: String,
        defaultValue: defaultSubType
      },

      region: {
        type: Array,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      category: {
        type: String,
        validators: {
          draft: [],
          published: [categoryTypeValidator],
          archived: []
        }
      },

      body: {
        type: Object,
        resolveType: createWidgetAreaResolveType(serviceLocator),
        defaultValue: () => ({ widgets: [] }),
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      images: {
        type: Object,
        defaultValue: () => ({ widgets: [] }),
        validators: {
          draft: [
            validateIf(hasWidgets, createContextValidator(requiredContexts)),
            validateIf(hasWidgets, createCropValidator(requiredCrops)),
            validateIf(hasWidgets, createImageCaptionValidator())
          ],
          published: [
            createContextValidator(requiredContexts),
            createCropValidator(requiredCrops),
            createImageCaptionValidator()
          ],
          archived: [
            createContextValidator(requiredContexts),
            createCropValidator(requiredCrops),
            createImageCaptionValidator()
          ]
        }
      },

      displayDate: {
        type: Date,
        defaultValue: () => moment().startOf('minute').toDate(),
        validators: {
          all: [required]
        }
      },

      liveDate: {
        type: Date,
        validators: {
          draft: [dateBeforeExpiryValidator],
          published: [dateBeforeExpiryValidator],
          archived: []
        }
      },

      publishDate: {
        type: Date
      },

      expiryDate: {
        type: Date,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      dateCreated: {
        type: Date,
        defaultValue: () => new Date(),
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      modifiedDate: {
        type: Date,
        defaultValue: () => new Date(),
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      tags: {
        type: schemata.Array(tagSchema())
      },

      metaTitle: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      metaDescription: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      shareTitle: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      shareDescription: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      signUpRequired: {
        type: Boolean,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      featured: {
        type: Boolean,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      // ID used by admin to preview
      previewId: {
        type: String,
        defaultValue: () =>
          Math.round(Math.random() * ************).toString(36)
      },

      account: {
        type: String,
        validators: {
          draft: [required],
          published: [required],
          archived: [required]
        }
      },

      instance: {
        type: String,
        validators: {
          draft: [required],
          published: [required],
          archived: [required]
        }
      },

      layout: {
        type: String,
        defaultValue: null
      },

      downloadUrl: {
        type: String,
        validators: {
          all: [[validateIfSet(validateUrl)]]
        }
      },

      issue: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      author: {
        type: String,
        validators: {
          draft: [],
          published: [requireOne(['author', 'legacyAuthorName'])],
          archived: []
        }
      },

      legacyAuthorName: {
        type: String,
        name: 'Custom Author Name',
        validators: {
          draft: [],
          published: [requireOne(['author', 'legacyAuthorName'])],
          archived: []
        }
      },

      publisherName: {
        type: String
      },

      pageNumber: {
        type: Number
      },

      viewCounts: {
        type: Array,
        defaultValue: () => []
      },

      last30DaysNumViews: {
        type: Number,
        defaultValue: 0
      },

      companies: {
        type: schemata.Array(createCompaniesSchema()),
        defaultValue: () => []
      },

      partnerships: {
        type: schemata.Array(createPartnershipsSchema()),
        defaultValue: () => []
      },

      partners: {
        type: schemata.Array(createCompaniesSchema()),
        defaultValue: () => []
      },

      executives: {
        type: schemata.Array(createExecutiveSchema()),
        defaultValue: () => []
      },

      eventRegistrationLink: {
        type: String,
        validators: {
          draft: [],
          published: [
            validateIfPropertyEquals('contentType', 'Event', required)
          ],
          archived: []
        }
      },

      onDemandLink: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      address: {
        type: String,
        validators: {
          draft: [],
          published: [
            validateIfPropertyEquals('contentType', 'Event', required)
          ],
          archived: []
        }
      },

      price: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      startDate: {
        type: Date,
        validators: {
          draft: [dateBeforeEndDateValidator],
          published: [
            dateBeforeEndDateValidator,
            validateIfPropertyEquals('contentType', 'Event', required)
          ],
          archived: []
        }
      },

      endDate: {
        type: Date,
        validators: {
          draft: [],
          published: [
            validateIfPropertyEquals('contentType', 'Event', required)
          ],
          archived: []
        }
      },

      country: {
        type: String,
        validators: {
          draft: [],
          published: [
            validateIfPropertyEquals('contentType', 'Event', required)
          ],
          archived: []
        }
      },

      timezone: {
        type: String,
        validators: {
          draft: [],
          published: [
            validateIfPropertyEquals('contentType', 'Event', required)
          ],
          archived: []
        }
      },

      startDateISOWithoutTZ: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      endDateISOWithoutTZ: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      videoProvider: {
        type: String,
        validators: {
          draft: [],
          published: [
            validateIfPropertyEquals('contentType', 'Video', required)
          ],
          archived: []
        }
      },

      videoId: {
        type: String,
        validators: {
          draft: [],
          published: [
            validateIfPropertyEquals('contentType', 'Video', required)
          ],
          archived: []
        }
      },

      quote: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      attribution: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      subAttribution: {
        type: String,
        validators: {
          draft: [],
          published: [],
          archived: []
        }
      },

      migratedFromId: {
        type: String
      },

      magazineOrigin: {
        type: String,
        defaultValue: 'issu'
      },

      issuPublicationId: {
        type: String
      },

      issuIssueId: {
        type: String
      },

      canonicalUrl: {
        type: String
      },

      sourceId: {
        type: String
      },

      eventId: {
        type: String,
        validators: {
          draft: [eventIdValidator],
          published: [eventIdValidator],
          archived: []
        }
      },

      eventArticleCategoryKey: {
        type: String,
        validators: {
          draft: [
            validateIfPropertyEquals(
              'contentType',
              'BizClik Live Event',
              required
            )
          ],
          published: [
            validateIfPropertyEquals(
              'contentType',
              'BizClik Live Event',
              required
            )
          ],
          archived: []
        }
      },

      syndicationOuts: {
        type: Array,
        defaultValue: () => []
      },

      cacheLastClearedDate: {
        type: Date
      },

      algoliaLastCrawlDate: {
        type: Date
      }
    }
  })
}
