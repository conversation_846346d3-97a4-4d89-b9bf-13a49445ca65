const { promisify } = require('util')
const schemata = require('@clocklimited/schemata')
const extendSchemata = require('../../../api/lib/schemata-extender')
const createGraphqlImageBuilderProperty = require('../asset/lib/graphql-image-builder-property')
const createSchema = require('./schema')
const createSectionSchema = require('../section/schema')
const createUrlInstancifier = require('../../site/next/lib/url-instancifier')
const createCompaniesSchema = require('../company/graphql-schema')
const createExecutivesSchema = require('../executive/graphql-schema')
const createAuthorSchema = require('../author/graphql-schema')
const { default: resolveCompanies } = require('./lib/companies-resolver')
const createDarkroomUrlBuilder = require('@clocklimited/darkroom-url-builder')
const moment = require('moment-timezone')
const slugg = require('slugg')

const createGraphqlSchema = (serviceLocator) => {
  let urlBuilder
  if (serviceLocator) {
    urlBuilder = createDarkroomUrlBuilder(
      serviceLocator.config.darkroom.url,
      serviceLocator.config.darkroom.salt
    )
  }
  const schema = createSchema(serviceLocator)
  const sectionSchema = createSectionSchema(serviceLocator)
  const landscapeImageSizes = [138, 206, 290, 330, 412, 580, 900]
  const portraitImageSizes = [104, 144, 208, 286, 290, 576, 580, 720, 900]
  const widescreenImageSizes = [322, 553, 644, 1106, 290]
  const landscapeImages = landscapeImageSizes.map((size) => {
    return {
      name: `thumbnail_landscape_${size}`,
      crop: 'Landscape',
      context: 'Thumbnail',
      size: { width: size }
    }
  })
  const portraitImages = portraitImageSizes.map((size) => {
    return {
      name: `thumbnail_portrait_${size}`,
      crop: 'Portrait',
      context: 'Thumbnail',
      size: { width: size }
    }
  })
  const widescreenImages = widescreenImageSizes.map((size) => {
    return {
      name: `thumbnail_widescreen_${size}`,
      crop: 'Widescreen',
      context: 'Thumbnail',
      size: { width: size }
    }
  })
  const imageVariations = [
    ...landscapeImages,
    {
      name: 'thumbnail_landscape_322',
      crop: 'Landscape',
      context: 'Thumbnail',
      size: { width: 322 }
    },
    {
      name: 'thumbnail_landscape_307',
      crop: 'Landscape',
      context: 'Thumbnail',
      size: { width: 307 }
    },
    {
      name: 'thumbnail_landscape_644',
      crop: 'Landscape',
      context: 'Thumbnail',
      size: { width: 644 }
    },
    {
      name: 'thumbnail_landscape_900',
      crop: 'Landscape',
      context: 'Thumbnail',
      size: { width: 900 }
    },
    ...portraitImages,
    {
      name: 'thumbnail_portrait_286',
      crop: 'Portrait',
      context: 'Thumbnail',
      size: { width: 286 }
    },
    {
      name: 'thumbnail_portrait_147',
      crop: 'Portrait',
      context: 'Thumbnail',
      size: { width: 147 }
    },
    ...widescreenImages,
    {
      name: 'thumbnail_widescreen_307',
      crop: 'Widescreen',
      context: 'Thumbnail',
      size: { width: 307 }
    },
    {
      name: 'hero_landscape_320',
      crop: 'Landscape',
      context: 'Hero',
      size: { width: 320 }
    },
    {
      name: 'hero_landscape_668',
      crop: 'Landscape',
      context: 'Hero',
      size: { width: 668 }
    },
    {
      name: 'hero_landscape_900',
      crop: 'Landscape',
      context: 'Hero',
      size: { width: 900 }
    },
    {
      name: 'hero_landscape_1336',
      crop: 'Landscape',
      context: 'Hero',
      size: { width: 1336 }
    },
    {
      name: 'hero_landscape_1800',
      crop: 'Landscape',
      context: 'Hero',
      size: { width: 1800 }
    },
    {
      name: 'hero_portrait_144',
      crop: 'Portrait',
      context: 'Hero',
      size: { width: 144 }
    },
    {
      name: 'hero_widescreen_320',
      crop: 'Landscape',
      context: 'Hero',
      size: { width: 320 }
    },
    {
      name: 'hero_widescreen_668',
      crop: 'Landscape',
      context: 'Hero',
      size: { width: 668 }
    },
    {
      name: 'hero_widescreen_900',
      crop: 'Landscape',
      context: 'Hero',
      size: { width: 900 }
    },
    {
      name: 'hero_widescreen_1336',
      crop: 'Landscape',
      context: 'Hero',
      size: { width: 1336 }
    },
    {
      name: 'hero_widescreen_1800',
      crop: 'Landscape',
      context: 'Hero',
      size: { width: 1800 }
    },
    {
      name: `share_widescreen_1200`,
      crop: 'OpenGraphWidescreen',
      context: 'Share Image',
      size: { width: 1200 }
    }
  ]
  const typeName = 'Article'
  const graphqlSchema = schemata({
    name: typeName,
    properties: {
      images: createGraphqlImageBuilderProperty(
        serviceLocator,
        imageVariations,
        typeName
      ),
      fullUrlPath: {
        type: String,
        resolve: (parent) => {
          const __url = createUrlInstancifier(parent.__instance)
          return __url(parent.__fullUrlPath)
        }
      },
      fullUrlPathWithSubdomain: {
        type: String,
        resolve: (parent) => {
          const subdomain = parent.__instance.subdomain
          const url = new URL(parent.__fullUrlPath, `https://${subdomain}`)
          return url.toString()
        }
      },
      logoUrl: {
        type: String,
        resolve: (parent, args, context) => {
          if (!parent.__instance.lightLogo) return null
          if (!urlBuilder) return null
          // if __instance._id is context.instance._id is the same
          if (parent.__instance._id === context.instance._id) return null
          return urlBuilder()
            .resource(parent.__instance.lightLogo)
            .filename(slugg(parent.__instance.name) + '-light-logo.png')
            ?.url()
        }
      },
      siblingInstance: {
        type: schemata({
          name: 'SiblingInstance',
          properties: {
            logoUrl: {
              type: String
            },
            subdomain: {
              type: String
            }
          }
        }),
        resolve: (parent, _, context) => {
          if (!parent.__instance.lightLogo) return null
          if (!urlBuilder) return null
          if (!parent.__instance.subdomain) return null
          if (parent.__instance._id === context.instance._id) {
            return null
          }
          const siblingInstance = {}
          siblingInstance.logoUrl = urlBuilder()
            .resource(parent.__instance.lightLogo)
            .filename(slugg(parent.__instance.name) + '-light-logo.png')
            ?.url()
          siblingInstance.subdomain = parent.__instance.subdomain
          return siblingInstance
        }
      },
      section: {
        type: sectionSchema,
        resolve: async (parent, args, context) => {
          context.dedupe(parent._id)
          context.article = parent
          const section = await promisify(serviceLocator.sectionService.read)(
            parent.sections[0]
          )
          return section
        }
      },
      author: {
        type: createAuthorSchema(serviceLocator),
        resolve: async (parent) => {
          if (!parent.author && !parent.legacyAuthorName) return null
          if (parent.author) {
            return promisify(serviceLocator.authorService.read)(parent.author)
          } else {
            return {
              name: parent.legacyAuthorName,
              instances: []
            }
          }
        }
      },
      companies: {
        type: schemata.Array(createCompaniesSchema(serviceLocator)),
        resolve: resolveCompanies('companies', serviceLocator)
      },
      partners: {
        type: schemata.Array(createCompaniesSchema(serviceLocator)),
        resolve: resolveCompanies('partners', serviceLocator)
      },
      executives: {
        type: schemata.Array(createExecutivesSchema(serviceLocator)),
        resolve: async (parent) => {
          const rawExecs = parent.executives || []
          const execIds = rawExecs.map((c) => c.executive)
          if (!execIds.length) return []
          const executives = await promisify(
            serviceLocator.executiveService.find
          )({ _id: { $in: execIds } })

          if (!executives || !executives.length) return []

          return executives.sort(
            (a, b) => execIds.indexOf(a._id) - execIds.indexOf(b._id)
          )
        }
      },
      issueSlug: {
        type: String,
        resolve: async (parent) => {
          const issueId = parent.issue
          if (!issueId) return null
          const issue = await promisify(
            serviceLocator.magazineIssueService.findOne
          )(
            {
              _id: issueId
            },
            { projection: { _id: 1, slug: 1 } }
          )
          if (!issue) return null

          return issue.slug
        }
      },
      advertSlotName: {
        type: String,
        resolve: (parent) => {
          const mapping = {
            'Company Report': 'company',
            Blog: 'blogs',
            Event: 'events',
            Interview: 'interviews',
            List: 'lists',
            Podcast: 'podcasts',
            'Press Release': 'PRs',
            'BizClik Live Event': 'bizclik_live_events',
            Video: 'videos',
            Whitepaper: 'whitepapers'
          }
          const suffix = mapping[parent.contentType] || 'article-page'
          return parent.__instance
            ? parent.__instance.advertSiteId + suffix
            : null
        }
      },
      eventBaseSlug: {
        type: String,
        resolve: async (parent) => {
          if (!parent.eventId) return null
          const eventArticleCategory = await promisify(
            serviceLocator.eventService.findSubCollectionItem
          )(parent.eventId, 'articleCategories', parent.eventArticleCategoryKey)
          if (!eventArticleCategory) return null
          return eventArticleCategory.articleBaseSlug
        }
      },
      _eventArticleCategoryName: {
        type: String,
        resolve: async (parent) => {
          if (!parent.eventId) return null
          const eventArticleCategory = await promisify(
            serviceLocator.eventService.findSubCollectionItem
          )(parent.eventId, 'articleCategories', parent.eventArticleCategoryKey)
          if (!eventArticleCategory) return null
          return eventArticleCategory.name
        }
      },
      localeSafeStartTime: {
        type: String,
        resolve: (parent) => {
          if (!parent.startDate) return 'unset'
          if (!parent.startDateISOWithoutTZ) return 'N/A'
          return moment.utc(parent.startDateISOWithoutTZ).format('HH:mm')
        }
      },
      localeSafeEndTime: {
        type: String,
        resolve: (parent) => {
          if (!parent.endDate) return 'unset'
          if (!parent.endDateISOWithoutTZ) return 'N/A'
          return moment.utc(parent.endDateISOWithoutTZ).format('HH:mm')
        }
      }
    }
  })
  const extendedSchema = extendSchemata(schema, graphqlSchema)
  extendedSchema.isTypeOf = (item) => item.headline
  return extendedSchema
}

module.exports = createGraphqlSchema
