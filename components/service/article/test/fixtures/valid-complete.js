const now = new Date()
const isoCreated = now.toISOString()
const isoLive = new Date(now.getFullYear() - 1, 1, 1).toISOString()
const isoExpired = new Date(now.getFullYear() + 2, 1, 1).toISOString()
const isoPublished = new Date(now.getFullYear() - 1, 1, 1).toISOString()
const isoAlgoliaLastCrawlDate = new Date().toISOString()
const images = {
  widgets: [
    {
      crops: [
        { name: 'Square', src: 'Square' },
        { name: 'Portrait', src: 'Portrait' },
        { name: 'Landscape', src: 'Landscape' },
        { name: 'Widescreen', src: 'Widescreen' },
        { name: 'OpenGraphWidescreen', src: 'Widescreen' }
      ],
      caption: 'Caption',
      selectedContexts: ['Hero', 'Thumbnail', 'Share Image']
    }
  ]
}

module.exports = {
  state: 'Draft',
  headline: 'Headline',
  sell: 'sell',
  slug: 'slug',
  hasBeenPublished: false,
  sections: ['1'],
  body: { widgets: [{ type: 'html', html: '<p>Hello World!</p>' }] },
  images: images,
  displayDate: isoPublished,
  liveDate: isoLive,
  publishDate: isoPublished,
  expiryDate: isoExpired,
  dateCreated: isoCreated,
  algoliaLastCrawlDate: isoAlgoliaLastCrawlDate,
  tags: [{ type: 'System', tag: 'Featured' }],
  metaTitle: 'Meta Title',
  metaDescription: 'Meta Description',
  shareTitle: 'Share Title',
  shareDescription: 'Share Description',
  previewId: '1',
  category: 'News',
  account: '123abc',
  layout: '',
  instance: 'abc',
  address: 'abc',
  attribution: 'abc',
  author: 'abc',
  contentType: 'abc',
  downloadUrl: 'abc',
  startDate: new Date(2001, 1, 1),
  endDate: new Date(2002, 1, 1),
  startDateISOWithoutTZ: new Date(2001, 1, 1).toISOString(),
  endDateISOWithoutTZ: new Date(2002, 1, 1).toISOString(),
  timezone: 'Europe/London',
  country: 'GB',
  eventRegistrationLink: 'abc',
  onDemandLink: 'abc',
  migratedFromId: 'abc',
  legacyAuthorName: 'abc',
  publisherName: 'abc',
  issue: 'abc',
  last30DaysNumViews: 123,
  pageNumber: 'abc',
  price: 'abc',
  quote: 'abc',
  region: 'abc',
  subAttribution: 'abc',
  subContentType: 'abc',
  issuPublicationId: 'abc',
  issuIssueId: 'cba',
  magazineOrigin: 'abc',
  videoId: 'abc',
  videoProvider: 'abc',
  viewCounts: {},
  featured: false,
  signUpRequired: false,
  companies: [],
  partners: [],
  executives: [],
  modifiedDate: new Date(),
  canonicalUrl: '',
  sourceId: '',
  eventId: 'id',
  eventArticleCategoryKey: 'event-category-key',
  syndicationOuts: [],
  cacheLastClearedDate: new Date(),
  partnerships: []
}
