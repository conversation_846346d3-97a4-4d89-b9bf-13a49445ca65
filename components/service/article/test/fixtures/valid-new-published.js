const now = new Date()
const isoCreated = now.toISOString()
const isoLive = new Date(now.getFullYear() - 1, 1, 1).toISOString()
const isoExpired = new Date(now.getFullYear() + 2, 1, 1).toISOString()
const isoPublished = new Date(now.getFullYear() - 1, 1, 1).toISOString()
const isoAlgoliaLastCrawlDate = new Date().toISOString()
const images = {
  widgets: [
    {
      crops: [
        { name: 'Square', src: 'Square' },
        { name: 'Portrait', src: 'Portrait' },
        { name: 'Landscape', src: 'Landscape' },
        { name: 'Widescreen', src: 'Widescreen' },
        { name: 'OpenGraphWidescreen', src: 'Widescreen' }
      ],
      caption: 'Caption',
      selectedContexts: ['Hero', 'Thumbnail', 'Share Image']
    }
  ]
}

module.exports = {
  state: 'Published',
  headline: 'Headline',
  sell: 'Sell',
  slug: 'slug',
  hasBeenPublished: true,
  sections: ['1'],
  body: { widgets: [{ type: 'html', html: '<p>Hello World!</p>' }] },
  images: images,
  displayDate: isoPublished,
  liveDate: isoLive,
  publishDate: isoPublished,
  expiryDate: isoExpired,
  dateCreated: isoCreated,
  algoliaLastCrawlDate: isoAlgoliaLastCrawlDate,
  tags: [{ type: 'System', tag: 'Featured' }],
  previewId: '1',
  account: '123abc',
  instance: '123abc',
  category: 'News',
  contentType: 'Article',
  region: ['abc'],
  author: 'abc',
  canonicalUrl: '',
  sourceId: '',
  syndicationOuts: [],
  cacheLastClearedDate: new Date(),
  partnerships: []
}
