const extend = require('lodash.assign')
const now = new Date()
const isoCreated = now.toISOString()

module.exports = extend({}, require('./valid-new'), {
  _id: '1',
  hash: '1',
  state: 'Draft',
  headline: 'Headline is required',
  sell: null,
  slug: 'required-slug',
  hasBeenPublished: false,
  sections: [],
  body: { widgets: [] },
  images: { widgets: [] },
  displayDate: isoCreated,
  liveDate: null,
  publishDate: null,
  expiryDate: null,
  dateCreated: isoCreated,
  algoliaLastCrawlDate: null,
  tags: [],
  metaTitle: null,
  metaDescription: null,
  shareTitle: null,
  shareDescription: null,
  previewId: '14d06h7n',
  account: '123abc',
  layout: '',
  address: null,
  attribution: null,
  author: null,
  contentType: null,
  downloadUrl: null,
  startDate: null,
  endDate: null,
  startDateISOWithoutTZ: null,
  endDateISOWithoutTZ: null,
  timezone: null,
  country: null,
  eventRegistrationLink: null,
  onDemandLink: null,
  migratedFromId: null,
  legacyAuthorName: null,
  publisherName: null,
  issue: null,
  last30DaysNumViews: null,
  pageNumber: null,
  price: null,
  quote: null,
  region: null,
  subAttribution: null,
  subContentType: null,
  videoId: null,
  videoProvider: null,
  issuPublicationId: null,
  issuIssueId: null,
  magazineOrigin: null,
  viewCounts: {},
  featured: false,
  signUpRequired: false,
  companies: [],
  partners: [],
  executives: [],
  modifiedDate: new Date(),
  canonicalUrl: '',
  sourceId: '',
  eventId: null,
  eventArticleCategoryKey: null,
  syndicationOuts: [],
  cacheLastClearedDate: null,
  partnerships: []
})
