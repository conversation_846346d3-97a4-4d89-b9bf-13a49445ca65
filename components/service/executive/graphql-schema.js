const { promisify } = require('util')
const schemata = require('@clocklimited/schemata')
const extendSchemata = require('../../../api/lib/schemata-extender')
const createGraphqlImageBuilderProperty = require('../asset/lib/graphql-image-builder-property')
const createSchema = require('./schema')
const createCompaniesSchema = require('../company/graphql-schema')
const createHistorySchema = require('./history/schema')
const createHistoryGraphQlSchema = require('./history/graphql-schema')
const { createPlatformsResolver } = require('../company/graphql-schema')

const createGraphqlSchema = (serviceLocator) => {
  const sectionSchema =
    serviceLocator &&
    serviceLocator.sectionService &&
    serviceLocator.sectionService.schema
  const schema = createSchema()
  const imageVariations = [
    {
      name: 'headshot_220x347_220',
      crop: '220:347',
      context: 'Headshot',
      size: { width: 220 }
    },
    {
      name: 'headshot_220x347_576',
      crop: '220:347',
      context: 'Headshot',
      size: { width: 576 }
    },
    {
      name: 'headshot_220x347_720',
      crop: '220:347',
      context: 'Headshot',
      size: { width: 720 }
    },
    {
      name: 'headshot_220x347_900',
      crop: '220:347',
      context: 'Headshot',
      size: { width: 900 }
    },
    {
      name: 'headshotv2_1x1_220',
      crop: '1:1',
      context: 'HeadshotV2',
      size: { width: 220 }
    },
    {
      name: 'headshotv2_1x1_576',
      crop: '1:1',
      context: 'HeadshotV2',
      size: { width: 576 }
    },
    {
      name: 'headshotv2_1x1_720',
      crop: '1:1',
      context: 'HeadshotV2',
      size: { width: 720 }
    },
    {
      name: 'headshotv2_1x1_900',
      crop: '1:1',
      context: 'HeadshotV2',
      size: { width: 900 }
    }
  ]

  const platformsResolver = createPlatformsResolver(serviceLocator)

  const typeName = 'Executive'
  const graphqlSchema = schemata({
    name: typeName,
    properties: {
      images: createGraphqlImageBuilderProperty(
        serviceLocator,
        imageVariations,
        typeName
      ),
      history: {
        type: schemata.Array(
          extendSchemata(
            createHistorySchema(serviceLocator),
            createHistoryGraphQlSchema(serviceLocator)
          )
        )
      },
      company: {
        type: createCompaniesSchema(serviceLocator),
        resolve: async (parent) => {
          try {
            const company = await promisify(serviceLocator.companyService.read)(
              parent.companyId
            )
            if (!company) return null
            company.platforms = await platformsResolver(parent.companyId)
            return company
          } catch (e) {
            serviceLocator.logger.error(e)
            return null
          }
        }
      },
      entitySection: {
        type: sectionSchema,
        resolve: async (parent, args, context) => {
          context.executiveId = parent._id
          const section = await serviceLocator.sectionService.cachedFindOne({
            slug: 'executive',
            instance: context.instance._id
          })
          return section
        }
      },

      isVerified: {
        type: Boolean,
        resolve: async (parent, args, context) => {
          const { verifiedInstances = [] } = parent
          const { instance } = context

          return verifiedInstances.includes(instance._id)
        }
      },

      galleryImages: createGraphqlImageBuilderProperty(
        serviceLocator,
        [
          {
            name: 'image_768',
            crop: '16:9',
            context: 'Image',
            size: { width: 768 }
          }
        ],
        `${typeName}GalleryImages`
      )
    }
  })
  return extendSchemata(schema, graphqlSchema)
}

module.exports = createGraphqlSchema
