const schemata = require('@clocklimited/schemata')
const required = require('@clocklimited/validity-required')

const imageConfig = require('./image-config.json')
const createUniqueValidator = require('validity-unique-property')
const customValidityMessage = require('../lib/custom-validity-message')
const createContextValidator = require('validity-cf-image-context-selection')
const createCropValidator = require('../../../lib/validators/crop-integrity-validator')
const createImageCaptionValidator = require('../../../lib/validators/image-caption-validator')
const createMileStonesSchema = require('./milestones/schema')
const createHistorySchema = require('./history/schema')

module.exports = (findOne) => {
  const requiredCrops = imageConfig.crops.map((crop) => crop.name)
  const requiredContexts = imageConfig.contexts.map((context) => context.name)
  const uniqueSlugValidator = customValidityMessage(
    createUniqueValidator(findOne),
    'This slug is already in use'
  )

  return schemata({
    name: 'Executive',
    properties: {
      _id: {
        type: String,
        tag: ['import']
      },
      name: {
        type: String,
        validators: [required],
        tag: ['import']
      },
      verifiedInstances: {
        type: Array,
        defaultValue: () => []
      },
      slug: {
        type: String,
        validators: [required, uniqueSlugValidator],
        tag: ['import']
      },
      account: {
        type: String,
        validators: [required],
        tag: ['import']
      },
      jobTitle: {
        type: String,
        tag: ['import']
      },
      companyId: {
        type: String,
        validators: [required],
        tag: ['import']
      },
      bio: {
        type: String
      },
      twitterId: {
        type: String,
        tag: ['import']
      },
      // linkedinId: {
      //   type: String,
      //   tag: ['import']
      // },
      migratedFromId: {
        type: String,
        tag: ['import']
      },
      dateCreated: {
        type: Date,
        defaultValue: function () {
          return new Date()
        },
        tag: ['import']
      },
      images: {
        type: Object,
        defaultValue: () => ({ widgets: [] }),
        validators: [
          createContextValidator(requiredContexts),
          createCropValidator(requiredCrops),
          createImageCaptionValidator()
        ],
        tag: ['import']
      },
      migratedFromCompanyId: {
        type: String
      },
      cacheLastClearedDate: {
        type: Date
      },
      milestones: {
        type: schemata.Array(createMileStonesSchema()),
        defaultValue: () => []
      },
      history: {
        type: schemata.Array(createHistorySchema()),
        defaultValue: () => []
      },
      modifiedDate: {
        type: Date
      },
      linkedinProfileUrlV2: {
        type: String,
        tag: ['manual']
      },
      apolloId: {
        type: String
      },
      cachedScrapinIoData: {
        type: Object
      },
      city: {
        type: String,
        tag: ['scrapin.io']
      },
      state: {
        type: String,
        tag: ['scrapin.io']
      },
      country: {
        type: String,
        tag: ['scrapin.io']
      },
      // This field is going to be fetched either from the cachedData or an API request
      linkedinPrivateIdentifier: { type: String },
      linkedinPosts: {
        type: schemata.Array(linkedinPostSchema),
        defaultValue: () => []
      },
      algoliaLastCrawlDate: {
        type: Date
      },
      galleryImages: {
        type: Object,
        defaultValue: () => ({ widgets: [] })
      }
    }
  })
}

const linkedinPostSchema = schemata({
  name: 'LinkedinPosts',
  properties: {
    activityId: { type: String },
    text: { type: String },
    activityDate: { type: Date }
  }
})
