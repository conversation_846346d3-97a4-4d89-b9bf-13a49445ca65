import { algoliasearch } from 'algoliasearch'
import crudService from 'crud-service'
import cachedCrudService from '../lib/cached-crud-service'
import createSchema from './schema'
import textSearch from '@clocklimited/cf-text-search'
import { promisify } from 'util'

export default (serviceLocator) => {
  const save = serviceLocator.persistence('executive')
  const schema = createSchema(save.findOne)
  const service = crudService('Executive', save, schema, {})

  let algoliaClient
  if (serviceLocator?.secrets?.algolia?.searchApiKey) {
    algoliaClient = algoliasearch(
      serviceLocator.secrets.algolia.applicationId,
      serviceLocator.secrets.algolia.searchApiKey
    )
  } else {
    serviceLocator.logger.info(
      'No Algolia search API key provided. Either something went wrong or this is a test'
    )
  }

  service.findOne = save.findOne
  service.search = textSearch(service)

  service.algoliaSearch = async (query, callback) => {
    if (!algoliaClient) {
      serviceLocator.logger.info(
        '[service.algoliaSearch] No Algolia client provided. Either something went wrong or this is a test'
      )
      return callback(null, null)
    }
    try {
      const results = await algoliaClient.searchSingleIndex({
        indexName: 'executive',
        searchParams: { query }
      })
      if (!results) return callback(null, [])
      if (!results.hits?.length) return callback(null, [])
      const ids = results.hits.map((hit) => hit._id)
      // console.log('(service: executive) ids: ', ids)
      const executives = await promisify(serviceLocator.executiveService.find)({
        _id: { $in: ids }
      })
      // console.log('(service: executive) executives: ', executives)
      if (!executives?.length) return callback(null, [])
      results.hits = executives
      // sort in the same order as ids
      results.hits = results.hits.sort(
        (a, b) => ids.indexOf(a._id) - ids.indexOf(b._id)
      )
      callback(null, results)
    } catch (error) {
      callback(error)
    }
  }

  service.pre('update', (data, cb) =>
    cb(null, { ...data, modifiedDate: new Date() })
  )
  return cachedCrudService(serviceLocator, service)
}
