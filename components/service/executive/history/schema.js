const schemata = require('@clocklimited/schemata')
const required = require('@clocklimited/validity-required')

module.exports = () =>
  schemata({
    name: 'Executive_History',
    properties: {
      apollo_id: {
        type: String
      },
      isCurrent: {
        type: Boolean
      },
      startYear: {
        type: String,
        validators: [required]
      },
      endYear: {
        type: String
      },
      startMonth: {
        type: Number,
        validators: [required]
      },
      endMonth: {
        type: Number
      },
      companyId: {
        type: String
        // validators: [required
      },
      fallbackCompanyName: {
        type: String
      },
      jobTitle: {
        type: String,
        validators: [required]
      }
    }
  })
