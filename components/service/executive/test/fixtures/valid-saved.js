const extend = require('lodash.assign')
const validNew = require('./valid-new')

module.exports = function () {
  return extend({}, validNew(), {
    _id: 1,
    jobTitle: null,
    twitterId: null,
    linkedinProfileUrlV2: null,
    migratedFromId: null,
    migratedFromCompanyId: null,
    dateCreated: new Date().toISOString(),
    modifiedDate: new Date().toISOString(),
    cacheLastClearedDate: null,
    milestones: [],
    history: [],
    verifiedInstances: [],
    apolloId: null,
    cachedScrapinIoData: {},
    city: null,
    state: null,
    country: null,
    algoliaLastCrawlDate: null,
    galleryImages: {
      widgets: []
    },
    linkedinPosts: [],
    linkedinPrivateIdentifier: null
  })
}
