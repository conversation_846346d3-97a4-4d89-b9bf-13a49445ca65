const extend = require('lodash.assign')
const validNew = require('./valid-new')

module.exports = function (suffix) {
  return extend({}, validNew(suffix), {
    _id: 1,
    jobTitle: 'a',
    twitterId: 'a',
    linkedinProfileUrlV2: 'a',
    migratedFromId: 'a',
    migratedFromCompanyId: 'a',
    dateCreated: new Date().toISOString(),
    modifiedDate: new Date().toISOString(),
    cacheLastClearedDate: null,
    milestones: [],
    history: [],
    verifiedInstances: [],
    apolloId: 'a',
    cachedScrapinIoData: {},
    city: 'a',
    state: 'a',
    country: 'a',
    algoliaLastCrawlDate: new Date().toISOString(),
    galleryImages: {
      widgets: []
    },
    linkedinPosts: [],
    linkedinPrivateIdentifier: 'a'
  })
}
