const compileJade = require('browjadify-compile')
const sortBy = require('lodash.sortby')
const join = require('path').join
const mapFormToObject = require('cf-map-form-to-object')
const createLayoutSchema = require('../../../service/event/sub-content-types/layout/layout-schema')
const template = compileJade(
  join(__dirname, '/../templates/layout-editor.jade')
)
const errorTemplate = compileJade(
  join(__dirname, '/../../../../admin/source/js/lib/form-errors.jade')
)
const WidgetListView = require('../../widget/views/widget-list')
const modal = require('modal')
const widgetExpander = require('../../widget/lib/widget-expander-toggle')(
  () => {}
)
const fixedSidebar = require('../../../../admin/source/js/lib/fixed-sidebar')
const View = require('ventnor')
const LayoutModel = require('../../widget/models/layout')
const ToolbarView = require('./toolbar')
const WidgetRowView = require('./widget-row')
const getPreviewUrlComponents = require('../../section/lib/get-preview-url-component')
const createDarkroomUrlBuilder = require('@clocklimited/darkroom-url-builder')
const manageWidgetMeta = require('../lib/widget-meta-manager')
const dateTimePickers = require('../../../../admin/source/js/lib/init-date-time-pickers')

/**
 * Validates that value is the correct form for a URL
 *
 * Uses the regex from Jquery Validation plugin
 *
 * @param {String} value URL to validate
 * @return {Boolean} True if value is a valid for a URL
 */
function urlMatch(value) {
  /* eslint-disable no-useless-escape */
  return /^(https?|ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)*(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(
    value
  )
}

class LayoutEditorView extends View {
  constructor(serviceLocator, saveService, layoutDescriptor) {
    super(...arguments)
    if (!serviceLocator.widgetFactories.get(layoutDescriptor.type)) {
      throw new Error(
        'Unsupported layout type "' + layoutDescriptor.get('type') + '"'
      )
    }
    this.widgetFactory = serviceLocator.widgetFactories.get(
      layoutDescriptor.type
    )

    this.saveService = saveService
    this.layoutDescriptor = layoutDescriptor
    this.layout = new LayoutModel(layoutDescriptor.layout, {
      serviceLocator: this.serviceLocator,
      abstractWidgetFactory: this.widgetFactory
    })

    const splitPathname = window.location.pathname.split('/')
    const indexOfParentId = splitPathname.indexOf(
      this.layoutDescriptor.parent._id
    )
    this.type = splitPathname[indexOfParentId - 1]

    this.initialModel = this.layout.toJSON()
    this.initialWidgets = this.getWidgets()

    this.setupToolbar()

    this.section = this.layoutDescriptor.parent
    this.instance = this.section.__instance

    this.$el.attr('id', this.cid)

    this.$el.on('click', '.js-add-row', this.handleAddRow.bind(this))
    this.$el.on(
      'keyup',
      '.js-widget-filter',
      this.handleWidgetFilter.bind(this)
    )

    this.dateTimePickers = []
    this.initDateTimePickers = dateTimePickers(
      window.config.locale.longDateFormat.LLLL
    )

    this.on('remove', () => {
      this.dateTimePickers.forEach((picker) => picker.destroy())
    })

    this.widgetItemViews = []

    this.rowCount = 0

    this.listenTo(this.layout, 'addRow', this.renderRow.bind(this))

    this.listenTo(this.layout, 'removeRow', (rowIndex) => {
      this.$el.find('[data-index=' + rowIndex + ']').remove()
      this.$el.find('.js-rows .js-widget-row').each((i, row) => {
        this.$el.find(row).attr('data-index', i)
      })
    })

    const drUrl = this.serviceLocator.config.darkroom.url
    const drSalt = this.serviceLocator.config.darkroom.salt

    if (this.section.__instance && this.section.__instance.logo) {
      const logoUrlBuilder = createDarkroomUrlBuilder(drUrl, drSalt)
      const url = logoUrlBuilder()
        .resource(this.section.__instance.logo)
        .filename('preview.jpg')
        .url()
      this.section.instanceLogoUrl = url
    }

    this.on('afterAppend', () => {
      fixedSidebar(this, this.$el.find('.js-fixed-sidebar'))
    })

    this.$ = this.$el.find.bind(this.$el)
  }

  /*
   * View.prototype implements this but because it's listening to
   * backbone views, it needs to be compatible with both those too
   */
  stopListening() {
    this._listeners.forEach((listener) => {
      if (typeof listener.obj.removeListener === 'function') {
        listener.obj.removeListener(listener.event, listener.fn)
      } else {
        listener.obj.off(listener.event, listener.fn)
      }
    })
  }

  setupToolbar() {
    this.toolbar = new ToolbarView(
      this.serviceLocator,
      this.layoutDescriptor,
      this
    )

    // Back to list
    this.toolbar.on('back', () => {
      if (this.type === 'events' || this.type === 'event-umbrellas') {
        return this.backToEventList()
      }
      return this.remove()
    })

    // Save
    this.toolbar.on(
      'save',
      this.save.bind(this, (err) => {
        if (err) {
          console.error(JSON.stringify(err))
          return modal({
            title: 'Error Saving Layout',
            content: err.message,
            buttons: [{ text: 'Dismiss', className: 'btn' }]
          })
        }
        this.toolbar.showSaveNotification()
      })
    )

    // Save and close
    this.toolbar.on(
      'saveAndClose',
      this.save.bind(this, (err) => {
        if (err) {
          return modal({
            title: 'Error Saving Layout',
            content: err.message,
            buttons: [{ text: 'Dismiss', className: 'btn' }]
          })
        }
        if (this.type === 'events' || this.type === 'event-umbrellas')
          return this.backToEventList()

        return this.remove()
      })
    )

    // Save and preview
    this.toolbar.on(
      'saveAndPreview',
      this.save.bind(this, (err) => {
        if (err) {
          return modal({
            title: 'Error Saving Layout',
            content: err.message,
            buttons: [{ text: 'Dismiss', className: 'btn' }]
          })
        }
        const w = window.open('')
        const serviceLocator = this.serviceLocator

        let previewInstance
        switch (this.type) {
          case 'events':
            previewInstance = this.section.__eventUmbrella.primaryInstance
            break
          case 'event-umbrellas':
            previewInstance = this.section.primaryInstance
            break
          default:
            previewInstance = this.section.instance
        }

        getPreviewUrlComponents(serviceLocator, previewInstance, (err, obj) => {
          if (err) {
            w.close()
            return serviceLocator.logger.error(err, 'Could not preview section')
          }
          if (this.type === 'event-umbrellas') {
            w.location =
              serviceLocator.instanceService.createUrl(
                obj.instance,
                obj.account
              ) +
              '/events/' +
              this.section.slug +
              '?previewId=' +
              this.section.previewId
          } else if (this.type === 'events') {
            w.location =
              serviceLocator.instanceService.createUrl(
                obj.instance,
                obj.account
              ) +
              '/events/' +
              this.section.__eventUmbrella.slug +
              '/' +
              this.section.slug +
              '?previewId=' +
              this.section.previewId
          } else {
            w.location =
              serviceLocator.instanceService.createUrl(
                obj.instance,
                obj.account
              ) +
              this.section.fullUrlPath +
              '?previewId=' +
              this.section.previewId
          }
        })
      })
    )

    this.toolbar.on('expandAll', widgetExpander.handleExpandAll.bind(this))
    this.toolbar.on('collapseAll', widgetExpander.handleCollapseAll.bind(this))
  }

  getWidgets() {
    return this.layout.layout.reduce((acc, row) => {
      return acc.concat(
        row.cols.reduce((acc, column) => {
          return acc.concat(column.widgetArea.widgets)
        }, [])
      )
    }, [])
  }

  renderRow(row) {
    const widgetRow = new WidgetRowView(
      this.serviceLocator,
      row,
      this.layout,
      this.widgetFactory,
      this.widgetItemViews,
      { isOnlyEditable: this.isOnlyEditable() }
    )
    this.attachView(widgetRow)
    this.$el.find('.js-rows').append(widgetRow.render().$el)
  }

  isOnlyEditable() {
    switch (this.type) {
      case 'events':
        return !this.serviceLocator.allowed('event', 'manageLayouts')
      case 'event-umbrellas':
        return !this.serviceLocator.allowed('eventUmbrella', 'manageLayouts')
      default:
        return false
    }
  }

  handleAddRow(e) {
    this.layout.addRow($(e.currentTarget).data('row-layout'))

    this.backgroundHandler()
  }

  handleRemoveRow(index) {
    modal({
      content: 'Do you want to delete this row and containing widgets?',
      buttons: [
        { text: 'Cancel', event: 'cancel', className: 'btn', keyCodes: [27] },
        { text: 'Delete', event: 'confirm', className: 'btn btn--error' }
      ]
    }).on('confirm', () => {
      this.layout.removeRow(index)
    })
  }

  backToEventList() {
    const eventLayoutList = window.location.pathname
      .split('/')
      .slice(0, -1)
      .join('/')
    this.serviceLocator.router.navigate(eventLayoutList, { trigger: true })
  }

  remove() {
    this.layout.stopListening()
    View.prototype.remove.call(this)
  }

  save(cb) {
    if (!this.validate()) return
    const widgets = this.getWidgets()

    if (this.type === 'events' || this.type === 'event-umbrellas') {
      const schema = createLayoutSchema(
        this.serviceLocator,
        'Event Layout Descriptor'
      )
      const formData = mapFormToObject(this.$el.find('form'), schema)
      this.layoutDescriptor = { ...this.layoutDescriptor, ...formData }

      const commit = (method, cb) => {
        this.saveService[method](
          this.layoutDescriptor.parent._id,
          {
            ...this.layoutDescriptor,
            layout: this.layout.toJSON(),
            parent: null
          },
          (err, data) => {
            if (err) return cb(err)
            this.initialModel = this.layout.toJSON()
            this.emit('save')
            return cb(null, data)
          }
        )
      }

      if (this.layoutDescriptor._id) {
        return commit('updateLayout', cb)
      } else {
        return commit('createLayout', cb)
      }
    }

    // currently assumes is inBuilt. Check if
    this.serviceLocator.layoutService.update(
      this.saveService,
      this.layoutDescriptor,
      this.layout.toJSON(),
      (err, data) => {
        if (err) return cb(err)
        this.initialModel = this.layout.toJSON()
        this.emit('save')

        manageWidgetMeta(
          this.serviceLocator,
          widgets,
          this.initialWidgets,
          (err, updatedWidgets) => {
            if (err) {
              console.warn('Error managing widget meta:', err)
              return cb(null, data)
            }
            this.initialWidgets = updatedWidgets
            return cb(null, data)
          }
        )
      }
    )
  }

  handleWidgetFilter() {
    var filterText = this.$widgetFilter.val().toUpperCase()
    this.$widgetList.find('li').each((index, widget) => {
      const widgetName = widget.innerHTML.toUpperCase()
      const offset = widgetName.search(filterText)
      if (offset > -1) {
        this.$el.find(widget).show()
      } else {
        this.$el.find(widget).hide()
      }
    })
  }

  disableSlugsIfPublishedEvent() {
    if (this.layoutDescriptor.type === 'event') {
      this.serviceLocator.eventService.read(
        this.layoutDescriptor.parent._id,
        (err, event) => {
          if (err) return this.serviceLocator.logger.error(err)
          if (event.state === 'Published') {
            this.$el.find('[name=slug]').prop('disabled', true)
          }
        }
      )
    } else if (this.layoutDescriptor.type === 'eventUmbrella') {
      this.serviceLocator.eventUmbrellaService.read(
        this.layoutDescriptor.parent._id,
        (err, eventUmbrella) => {
          if (err) return this.serviceLocator.logger.error(err)
          if (eventUmbrella.state === 'Published') {
            this.$el.find('[name=slug]').prop('disabled', true)
          }
        }
      )
    }
  }

  render() {
    this.$el.empty().append(
      template({
        title: this.layoutDescriptor.isBuiltIn
          ? this.layoutDescriptor.name + ':'
          : 'Custom Layout' + ':',
        layout: this.layout.toJSON(),
        layoutDescriptor: this.layoutDescriptor,
        section: this.section,
        type: this.type,
        isOnlyEditable: this.isOnlyEditable()
      })
    )

    this.disableSlugsIfPublishedEvent()

    this.toolbar.render().$el.appendTo(this.$el.find('.js-toolbar-root'))

    this.dateTimePickers = this.$('.js-date-time-picker')
      .toArray()
      .map(this.initDateTimePickers)

    const widgetListView = new WidgetListView({
      collection: new window.Backbone.Collection(
        sortBy(this.widgetFactory.toArray(), 'name')
      ),
      widgetDropArea: '#' + this.cid + ' .js-widgets'
    })

    this.$widgetList = this.$el.find('.js-widget-list')
    this.$widgetFilter = this.$el.find('.js-widget-filter')
    this.$widgetList.append(widgetListView.$el)

    Object.keys(this.layout.layout).forEach((type) => {
      this.renderRow(this.layout.layout[type])
    })

    this.setupSortable()
    this.backgroundHandler()

    return this
  }

  backgroundHandler() {
    this.$el.find('.js-row-background-select').on('change', (e) => {
      const $el = $(e.currentTarget)
      const $row = $el.closest('.js-widget-row')
      const index = $row.data('index')
      this.layout.layout[index].background = $el.val()
    })
  }

  setupSortable() {
    this.$el.find('.js-rows').sortable({
      axis: 'y',
      helper: 'clone',
      cursor: 'move',
      handle: '.js-row-drag-handle',
      update: () => {
        const layout = []

        // this is needed to fix a bug on certain layouts where the minId is NOT 0. It should always be 0
        const minId = this.layout.layout.reduce(
          (minId, row) => Math.min(row.id, minId),
          9999
        )
        this.$el.find('.js-widget-row').each((i, el) => {
          const $el = $(el)
          const index = $el.data('index') - minId
          layout[i] = this.layout.layout[index]
          layout[i].id = i
          $el.data('index', i)
          el.setAttribute('data-index', i)
        })

        const sortedLayout = layout.sort((a, b) => {
          if (a.id > b.id) return 1
          if (a.id < b.id) return -1
          return 0
        })

        this.layout.layout = sortedLayout
      }
    })
  }

  validate() {
    /* jshint maxcomplexity:7 */

    // Only the article layout type can be invalid
    const validatedTypes = ['article', 'event', 'eventUmbrella']
    if (!validatedTypes.includes(this.layoutDescriptor.type)) return true

    let errors = {}
    const $errors = this.$el.find('.js-error-summary').empty()

    if (this.layoutDescriptor.type === 'article') {
      if (!this.layoutDescriptor.isBuiltIn) {
        if (!this.layoutDescriptor.name) {
          errors.layoutName = 'A name must be provided for this layout'
        }
        if (!this.layoutDescriptor.slug) {
          errors.layoutSlug = 'A slug must be provided for this layout'
        }
      }
    } else if (
      this.layoutDescriptor.type === 'event' ||
      this.layoutDescriptor.type === 'eventUmbrella'
    ) {
      const eventLayoutSchema = createLayoutSchema(
        this.serviceLocator,
        'Event Layout Descriptor'
      )
      const formData = mapFormToObject(this.$el.find('form'), eventLayoutSchema)
      this.layoutDescriptor = { ...this.layoutDescriptor, ...formData }
      if (!this.layoutDescriptor.isBuiltIn) {
        if (!this.layoutDescriptor.name) {
          errors.layoutName = 'A name must be provided for this layout'
        }
        if (!this.layoutDescriptor.slug) {
          errors.layoutSlug = 'A slug must be provided for this layout'
        }
      }
      if (this.layoutDescriptor.type === 'event') {
        const redirectFields = [
          'upcomingEventRedirectUrl',
          'liveEventRedirectUrl',
          'pastEventRedirectUrl'
        ]
        errors = redirectFields.reduce((prev, curr) => {
          const value = this.layoutDescriptor[curr]
          if (value && !urlMatch(value) && value[0] !== '/')
            prev[curr] = 'Invalid URL: ' + curr
          return prev
        }, errors)
      }
    }

    // if (this.layout.getType('articlePlaceholder').length !== 1) {
    //   errors.articlePlaceholder = 'Article layout must have exactly one ‘Article Placeholder’ on the layout'
    // }

    if (Object.keys(errors).length === 0) return true

    $errors.html(errorTemplate({ errors: errors }))
    return false
  }
}

module.exports = LayoutEditorView
