const async = require('async')

const manageWidgetMeta = (
  serviceLocator,
  widgets,
  initialWidgets,
  callback
) => {
  // Finding all changed widgets
  const changedWidgets = widgets.filter(
    (widget) => Object.keys(widget.changed).length && widget
  )

  // Finding all new widgets within changed widgets
  const changedAndNewWidgets = changedWidgets.map((widget) => {
    const foundInitialWidget = initialWidgets.find(
      (initialWidget) => initialWidget.id === widget.id
    )
    widget.isNew = !foundInitialWidget
    return widget
  })

  // Find me all deleted widgets
  const deletedWidgets = initialWidgets
    .filter(
      (initialWidget) =>
        !widgets.find((widget) => widget.id === initialWidget.id)
    )
    .map((widget) => {
      widget.wasDeleted = true
      return widget
    })

  // Perform create, update and delete operations
  const widgetsToProcess = [...changedAndNewWidgets, ...deletedWidgets]
  async.each(
    widgetsToProcess,
    (widget, cb) => {
      const data = widget.toJSON()
      data._id = data.id.slice(0, 24)

      // CREATE
      if (widget.isNew === true) {
        data.adminUrl = window.location.href.split('#')[0]
        serviceLocator.widgetMetaService.create(data, (err, result) => {
          if (err) return cb(err)
          widget.isNew = false
          initialWidgets.push(widget)
          return cb(null, result)
        })

        // DELETE
      } else if (widget.wasDeleted) {
        serviceLocator.widgetMetaService.delete(data._id, (err, result) => {
          if (err) {
            return cb(err)
          }
          initialWidgets = initialWidgets.filter(
            (initialWidget) => initialWidget.id !== widget.id
          )
          return cb(null, result)
        })

        // UPDATE
      } else {
        serviceLocator.widgetMetaService.update(
          data._id,
          data,
          (err, result) => {
            if (err) return cb(err)
            initialWidgets = initialWidgets.map((initialWidget) => {
              if (initialWidget.id === widget.id) {
                return widget
              }
              return initialWidget
            })
            return cb(null, result)
          }
        )
      }
    },
    (err) => {
      if (err) return callback(err)
      // Return updated widgets
      return callback(null, initialWidgets)
    }
  )
}

module.exports = manageWidgetMeta
