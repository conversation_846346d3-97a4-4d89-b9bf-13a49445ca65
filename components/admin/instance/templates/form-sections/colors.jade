.panel.panel-styled
  .panel-header
    h2 Colours
  .panel-content
    .form-row(id='field--primaryColorOverride', data-field='primaryColorOverride')
      label
        span.form-label-text Primary Color Override
        .form-field
          enableAnchorColor
          input.control.control--text(type='text', name='primaryColorOverride', value=data.primaryColorOverride)
      .js-error
      .form-row-description.form-copy
        p Overwrite the primary color with a hexadecimal color value, e.g
          strong #12a583.
          | This color is used for the background of the main site header.
    
    hr
    
    .form-row(id='field--secondaryColorOverride', data-field='secondaryColorOverride')
      label
        span.form-label-text Secondary Color Override
        .form-field
          input.control.control--text(type='text', name='secondaryColorOverride', value=data.secondaryColorOverride)
      .js-error
      .form-row-description.form-copy
        p Overwrite the secondary color with a hexadecimal color value, e.g
          strong #12a583.
          | This color is used for the buttons, bordering and hover states in the main site header.
    
    hr
    
    .form-row(id='field--anchorColor', data-field='anchorColor')
      span
        span.form-label-text Anchor colors
        .form-field
          div(style="margin-block: 3px 10px; padding: 5px 10px; background: #f0f0ff;")
            input.control.control--boolean(type='checkbox', name='enableAnchorColor', checked=data.enableAnchorColor, data-action-for="anchorColor")
            span Enable anchor colour?
          div(data-show-hide="anchorColor", style=!data.enableAnchorColor ? 'opacity: 0.5; pointer-events: none;' : false)
            input.control.control--text(type='text', name='anchorColor', value=data.anchorColor)
      .js-error
      .form-row-description.form-copy
        p Set a colour to be used for links in content
    
    hr
    
    .form-row(id='field--buttonForegroundColor', data-field='buttonForegroundColor')
      span
        span.form-label-text Button foreground colour
        .form-field
          div(style="margin-block: 3px 10px; padding: 5px 10px; background: #f0f0ff;")
            input.control.control--boolean(type='checkbox', name='enableButtonForegroundColor', checked=data.enableButtonForegroundColor, data-action-for="buttonForegroundColor")
            span Enable button foreground colour?
          div(data-show-hide="buttonForegroundColor", style=!data.enableButtonForegroundColor ? 'opacity: 0.5; pointer-events: none;' : false)
            input.control.control--text(type='text', name='buttonForegroundColor', value=data.buttonForegroundColor)
      .js-error
      .form-row-description.form-copy
        p Colour used for the foreground of buttons
    
    hr
    
    .form-row(id='field--buttonBackgroundColor', data-field='buttonBackgroundColor')
      span
        span.form-label-text Button background colour
        .form-field
          div(style="margin-block: 3px 10px; padding: 5px 10px; background: #f0f0ff;")
            input.control.control--boolean(type='checkbox', name='enableButtonBackgroundColor', checked=data.enableButtonBackgroundColor, data-action-for="buttonBackgroundColor")
            span Enable button background colour?
          div(data-show-hide="buttonBackgroundColor", style=!data.enableButtonBackgroundColor ? 'opacity: 0.5; pointer-events: none;' : false)
            input.control.control--text(type='text', name='buttonBackgroundColor', value=data.buttonBackgroundColor)
      .js-error
      .form-row-description.form-copy
        p Colour used for the background of buttons
