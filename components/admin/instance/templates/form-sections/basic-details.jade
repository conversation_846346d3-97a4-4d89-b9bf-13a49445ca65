.panel.panel-styled
  .panel-header
    h2 Basic Details
  .panel-content

    .form-row(id='field--name', data-field='name')
      label
        span.form-label-text Name
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='name', value=data.name, autofocus)
      .js-error

    .form-row.hidden(id='field--account', data-field='account')
      label
        span.form-label-text Account
          abbr(title='This field is required') *
        .js-accounts.form-field
      .js-error
      .form-row-description.form-copy
        p This is the account that the instance is assigned to.

    .form-row(id='field--subdomain', data-field='subdomain')
      label
        span.form-label-text Subdomain or Full Domain
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='subdomain', value=data.subdomain)
      .js-error
      .form-row-description.form-copy
        p This will form the URL for this instance. http://{SUBDOMAIN}.domain.com
        p If this instance is linked to an account without a domain, it will be the full domain. http://{DOMAIN}

    .form-row(id='field--strapline', data-field='strapline')
      label
        span.form-label-text Strapline
        textarea.control.control--text.control--multiline.form-field(name='strapline', rows='3')= data.strapline
      .js-error

    .form-row.form-row-boolean(id='field--enabled', data-field='enabled')
      label
        span.form-label-text Enabled
        .form-field
          input.control.control--boolean(type='checkbox', name='enabled', checked=data.enabled)
          span Should this instance be publicly available?
      .js-error

    .form-row(id='field--brandType', data-field='brandType')
      label
        span.form-label-text Brand Type
          abbr(title='This field is required') *
        .form-field
          select.control.control--choice(type='text', name='brandType')
            option(selected= data.brandType === 'B2B' value=brandType) B2B
            option(selected= data.brandType === 'Lifestyle', value=brandType) Lifestyle
      .js-error

    .form-row(id='field--categories', data-field='categories')
      label
        span.form-label-text Categories
          abbr(title='This field is required') *
        .form-field
          select.control.control--choice.js-selectize(type='text', name='categories', multiple)
            if data.categories
              each category in data.categories
                option(selected, value=category)=category
      .js-error

    .form-row(id='field--deprecatedCategories', data-field='deprecatedCategories')
      label
        span.form-label-text Deprecated Categories
        .form-field
          select.control.control--choice.js-selectize(type='text', name='deprecatedCategories', multiple)
            if data.deprecatedCategories
              each category in data.deprecatedCategories
                option(selected, value=category)=category
      .js-error
      .form-row-description.form-copy
        p Categories that should be marked as deprecated in the article form. These will still be available for selection but will show a warning.

    .form-row(id='field--oldBrandId', data-field='oldBrandId')
      label
        span.form-label-text Old Brand ID
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='oldBrandId', value=data.oldBrandId)
      .js-error
      .form-row-description.form-copy
        p The ID used to previously identify this brand site e.g "cso_magazine". This is only used for the content API.
        P If this is a new brand, set this to whatever ID needs to be shown in the content API

    .form-row(id='field--theme', data-field='theme')
      label
        span.form-label-text Theme
          abbr(title='This field is required') *
        select.control.control--choice.form-field(name='theme')
          option(selected=data.theme==='ai', value='ai') AI
          option(selected=data.theme==='bc', value='bc') Business Chief
          option(selected=data.theme==='bcorp', value='bcorp') BCorp
          option(selected=data.theme==='construction', value='construction') Construction
          option(selected=data.theme==='climatetech', value='climatetech') ClimateTech
          option(selected=data.theme==='crypto', value='crypto') Crypto
          option(selected=data.theme==='cyber', value='cyber') Cyber
          option(selected=data.theme==='dc', value='dc') Data Centre
          option(selected=data.theme==='energy', value='energy') Energy
          option(selected=data.theme==='ev', value='ev') EV
          option(selected=data.theme==='fintech', value='fintech') FinTech
          option(selected=data.theme==='food', value='food') Food
          option(selected=data.theme==='healthcare', value='healthcare') Healthcare
          option(selected=data.theme==='insurtech', value='insurtech') InsurTech
          option(selected=data.theme==='manufacturing', value='manufacturing') Manufacturing
          option(selected=data.theme==='march8', value='march8') March8
          option(selected=data.theme==='mobile', value='mobile') Mobile
          option(selected=data.theme==='mining', value='mining') Mining
          option(selected=data.theme==='procurement', value='procurement') Procurement
          option(selected=data.theme==='sc', value='sc') Supply Chain
          option(selected=data.theme==='scope3', value='scope3') Scope 3
          option(selected=data.theme==='sustainability', value='sustainability') Sustainability
          option(selected=data.theme==='technology', value='technology') Technology
          option(selected=data.theme==='vc', value='vc') VC
      .js-error
      .form-row-description.form-copy
        p Used for brand flourishes and textures across this brand site. Theme color values are stored in the codebase and can be overwritten using the fields below.
        
    .form-row(id='field--headerType', data-field='headerType')
      label
        span.form-label-text Header Type
          abbr(title='This field is required') *
        .form-field
          select.control.control--choice(type='text', name='headerType')
            option(selected= data.headerType === 'Default' value=headerType) Default
            option(selected= data.headerType === 'Inverse', value=headerType) Inverse
      .js-error

    .form-row(id='field--mailChimpNewsletterAudienceId', data-field='mailChimpNewsletterAudienceId')
      label
        span.form-label-text MailChimp Newsletter Audience ID
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='mailChimpNewsletterAudienceId', value=data.mailChimpNewsletterAudienceId)
      .js-error
      .form-row-description.form-copy
        p The ID of a desired MailChimp audience (list). This is where the newsletter subscribers data will be stored.

    .form-row(id='field--googleTagManagerId', data-field='googleTagManagerId')
      label
        span.form-label-text Google Tag Manager ID
        input.control.control--text.form-field(type='text', name='googleTagManagerId', value=data.googleTagManagerId)
      .js-error
      .form-row-description.form-copy
        p The Google Tag Manager ID for this instance e.g GTM-KC6MRRD

    .form-row(id='field--googleOptimizeContainerId', data-field='googleOptimizeContainerId')
      label
        span.form-label-text Google Optimize Container ID
        input.control.control--text.form-field(type='text', name='googleOptimizeContainerId', value=data.googleOptimizeContainerId)
      .js-error
      .form-row-description.form-copy
        p The Google Optimize Container ID for this instance e.g OPT-TPXTC8T

    .form-row.form-row-boolean(id='field--googleOptimizeAsynchronous', data-field='googleOptimizeAsynchronous')
      label
        span.form-label-text Google Optimize Asynchronous Loading
        .form-field
          input.control.control--boolean(type='checkbox', name='googleOptimizeAsynchronous', checked=data.googleOptimizeAsynchronous)
          span Should Google Optimize load Asynchronously?
        .js-error

    .form-row(id='field--editorialContactFormToEmailAddress', data-field='editorialContactFormToEmailAddress')
      label
        span.form-label-text Editorial Contact Form To Email Address
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='editorialContactFormToEmailAddress', value=data.editorialContactFormToEmailAddress)
      .js-error

    .form-row(id='field--salesforceId', data-field='salesforceId')
      label
        span.form-label-text Salesforce ID
        input.control.control--text.form-field(type='text', name='salesforceId', value=data.salesforceId)
      .js-error
