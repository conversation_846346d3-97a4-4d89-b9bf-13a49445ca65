const BaseFormView = require('../../base/views/form')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const AccountSelect = require('../../account/views/account-select')
const modal = require('modal')
const AssetCollection = require('../../asset/collections/asset')
const AssetPickerView = require('../../asset/views/picker')
const createDarkroomUrlBuilder = require('@clocklimited/darkroom-url-builder')
const config = window.config
const InstanceSelect = require('../../instance/views/instance-select')
const ManageNavigationView = require('./manage-navigation')
const GlobeNewswireMetadataView = require('./globenewswire-metadata')
const KeyValueTargetingView = require('../../../admin/widgets/general/advert/views/key-value-targeting')
const imageConfig = require('../../../service/instance/image-config.json')
const getImageFactory = require('../../asset/lib/image-factory')()
const WidgetAreaView = require('../../widget/views/widget-area')
const BaseSelect = require('../../base/views/select')
const ColorPicker = require('../../widgets/lib/color-picker')

class FormView extends BaseFormView {
  constructor(...args) {
    super(...args)

    this.footerNavigationInstanceSelect = new BaseSelect(
      this.serviceLocator,
      'instanceService',
      this.model.get('footerNavigationInstances'),
      false,
      'Instance',
      'Instances',
      'name',
      'name'
    )

    this.footerNavigationInstanceSelect.$el.attr(
      'name',
      'footerNavigationInstances'
    )

    this.accountSelectView = new AccountSelect(
      this.serviceLocator,
      this.model.get('account')
    )

    this.globenewswireMetadataView = new GlobeNewswireMetadataView(
      this.serviceLocator,
      this.model
    )

    this.listenTo(
      this.model,
      'change:logo',
      this.updateDarkLogoPreview.bind(this)
    )
    this.$el.on(
      'click',
      '.js-btn-choose-logo',
      this.handleChooseDarkLogo.bind(this)
    )

    this.listenTo(
      this.model,
      'change:lightLogo',
      this.updateLightLogoPreview.bind(this)
    )
    this.$el.on(
      'click',
      '.js-btn-choose-light-logo',
      this.handleChooseLightLogo.bind(this)
    )

    this.$el.on('click', '.js-images-add', this.handleAddImages.bind(this))

    this.instanceSelectView = new InstanceSelect(
      this.serviceLocator,
      this.model.get('duplicatedInstance')
    )

    this.$el.on(
      'click',
      '.js-btn-manage-header-navigation',
      this.handleManageHeaderNavigation.bind(this)
    )
    this.$el.on(
      'click',
      '.js-btn-manage-footer-navigation',
      this.handleManageFooterNavigation.bind(this)
    )

    this.selectedFeatureFlags = this.model.get('featureFlags') || []
  }

  renderFeatureFlags() {
    this.$el.find('.js-feature-flags').empty()

    const avaliableFeatureFlags = this.model.getFeatureFlags()
    const keys = Object.keys(avaliableFeatureFlags)

    // We want to create a new checkbox for each one

    const checkboxes = []
    keys.forEach((flag) => {
      const el = $(`
        <div style="display:flex; gap: 8px;">
          <input type="checkbox" id="${flag}" name="${flag}" value="${flag}" ${
        this.selectedFeatureFlags.includes(flag) ? 'checked' : ''
      }/>
          <label for="${flag}">
            <span style="font-weight: bold;">${
              avaliableFeatureFlags[flag].label
            }</span>
            <span> - ${avaliableFeatureFlags[flag].description}</span>
          </label>
        </div>
      `)
      this.$el.find('.js-feature-flags').append(el)

      el.on('change', (e) => {
        // Update the model.
        if (e.target.checked) {
          this.selectedFeatureFlags.push(e.target.value)
        } else {
          this.selectedFeatureFlags = this.selectedFeatureFlags.filter(
            (f) => f !== e.target.value
          )
        }
        this.model.set('featureFlags', this.selectedFeatureFlags)
      })

      this.attachView(el)

      checkboxes.push(el)
    })
  }

  render() {
    // Render the template
    this.$el.append(
      template({
        title: this.isNew ? 'New Instance' : 'Edit Instance',
        isNew: this.isNew,
        data: this.model.toJSON()
      })
    )

    // Render the toolbar
    this.toolbar.render().$el.appendTo(this.$el.find('.js-toolbar-root'))

    this.accountSelectView
      .render()
      .populate()
      .$el.appendTo(this.$el.find('.js-accounts'))

    this.$el
      .find('.js-footer-navigation-instances')
      .append(this.footerNavigationInstanceSelect.render().$el)
    this.footerNavigationInstanceSelect.on('change', (instances) => {
      this.model.set('footerNavigationInstances', instances)
    })

    const editor = window.ace.edit(this.$('.js-html-editor')[0])
    editor.getSession().setMode('ace/mode/html')
    editor.setOptions({ theme: 'ace/theme/chrome', tabSize: 2 })
    editor.resize()

    editor.getSession().on('change', () => {
      this.model.set('footerHtml', editor.getSession().getValue())
    })

    if (!this.serviceLocator.session.account) {
      this.$el.find('#field--account').removeClass('hidden')
    }

    this.$el.find('.js-selectize').each((i, el) => {
      $(el).selectize({
        delimiter: ',',
        createOnBlur: true,
        create: true,
        onInitialize: () =>
          el.selectize.on('change', () => {
            this.model.set(el.name, el.selectize.getValue())
          })
      })
    })

    // Listen for changes to the GlobeNewswire RSS feed URL
    this.$el.on('input', 'input[name="globenewswireRssFeedUrl"]', (e) => {
      this.model.set('globenewswireRssFeedUrl', e.target.value)
    })

    this.$el.find('.js-instances').append(this.instanceSelectView.render().$el)
    this.instanceSelectView.on('change', (instance) =>
      this.model.set('duplicatedInstance', instance)
    )
    this.attachView(this.instanceSelectView)

    this.updateDarkLogoPreview()
    this.updateLightLogoPreview()
    this.renderImageWidgetArea()

    const keyValueTargeting = new KeyValueTargetingView(
      this.serviceLocator,
      this.model
    )

    keyValueTargeting
      .render()
      .$el.appendTo(this.$el.find('.js-key-value-targeting'))

    this.renderColorPickers()

    this.renderFeatureFlags()

    // Render GlobeNewswire metadata view
    this.globenewswireMetadataView.initialize()
    this.globenewswireMetadataView
      .render()
      .$el.appendTo(this.$el.find('.js-globenewswire-metadata-container'))
    this.attachView(this.globenewswireMetadataView)

    return this
  }

  handleManageHeaderNavigation() {
    this.handleManageNavigation({ property: 'navigation', type: 'Header' })
  }

  handleManageFooterNavigation() {
    this.handleManageNavigation({
      property: 'footerNavigation',
      type: 'Footer'
    })
  }

  renderImageWidgetArea() {
    this.imageArea = new WidgetAreaView({
      model: this.model.images,
      receiveDrops: false,
      serviceLocator: this.serviceLocator,
      widgetItemOptions: imageConfig
    })
    this.$el.find('.js-image-area').append(this.imageArea.$el)
  }

  handleAddImages(e) {
    e.preventDefault()

    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      type: 'image',
      serviceLocator: this.serviceLocator
    })

    collection.getByType('image')
    modal({
      title: 'Instance Image',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        { text: 'Cancel', event: 'Cancel', keyCodes: [27], className: 'btn' },
        {
          text: 'Add selected images',
          event: 'add',
          className: 'btn btn--action',
          keyCodes: [13]
        }
      ]
    })
      .on('add', this.addImages.bind(this, assetListView.selectedCollection))
      .on('close', assetListView.remove.bind(assetListView))
  }

  addImages(images) {
    const ImageModel = getImageFactory('image').model
    images.map((model) => {
      const image = new ImageModel(model.attributes)
      this.model.images.add(image)
      image.setDefaultCrops(imageConfig.crops)
      return image
    })
  }

  handleManageNavigation(options) {
    const manageNavigationView = new ManageNavigationView({
      navigation: this.model.get(options.property),
      instance: this.model.toJSON(),
      serviceLocator: this.serviceLocator
    }).render()
    const navigationModal = modal({
      title: `Manage ${options.type} Navigation`,
      className: 'modal-view wide',
      content: manageNavigationView.$el,
      buttons: []
    })

    manageNavigationView
      .on('save', () => {
        navigationModal.close()
        this.model.set(options.property, manageNavigationView.getNavigation())
      })
      .on('close', () => {
        navigationModal.close()
        manageNavigationView.remove.bind(manageNavigationView)
      })
  }

  handleChooseDarkLogo() {
    this.handleChooseLogo(this.addDarkLogo)
  }

  handleChooseLightLogo() {
    this.handleChooseLogo(this.addLightLogo)
  }

  handleChooseLogo(addLogoFn) {
    const assets = new AssetCollection()
    const assetPicker = new AssetPickerView({
      collection: assets,
      type: 'image',
      serviceLocator: this.serviceLocator
    })

    assets.getByType('image')
    modal({
      title: 'Logo',
      className: 'modal-asset-view wide',
      content: assetPicker.$el,
      buttons: [
        { text: 'Cancel', event: 'Cancel', keyCodes: [27], className: 'btn' },
        {
          text: 'Add Selected Logo',
          event: 'add',
          className: 'btn btn--action',
          keyCodes: [13]
        }
      ]
    })
      .on('add', addLogoFn.bind(this, assetPicker.selectedCollection))
      .on('close', assetPicker.remove.bind(assetPicker))
  }

  addDarkLogo(collection) {
    this.addLogo(collection, 'logo')
  }

  addLightLogo(collection) {
    this.addLogo(collection, 'lightLogo')
  }

  addLogo(collection, propertyName) {
    const data = {}
    data[propertyName] = null
    if (collection.length) {
      data[propertyName] = collection.models[0].get('binaryUri')
    }
    this.model.set(data)
  }

  updateDarkLogoPreview() {
    this.updateLogoPreview('.js-logo-preview', 'logo')
  }

  updateLightLogoPreview() {
    this.updateLogoPreview('.js-light-logo-preview', 'lightLogo')
  }

  renderColorPickers() {
    const hexFields = [
      'primaryColorOverride',
      'secondaryColorOverride',
      'anchorColor',
      'buttonForegroundColor',
      'buttonBackgroundColor'
    ]

    for (const hexField of hexFields) {
      const colorPicker = new ColorPicker(
        this.serviceLocator,
        this.model.get(hexField)
      )
      this.$el
        .find(`[name="${hexField}"]`)
        .replaceWith(colorPicker.render().$el)
      colorPicker.on('change', (value) => this.model.set(hexField, value))
    }

    this.$el.on('change', '[data-action-for]', ({ currentTarget }) => {
      const { actionFor } = currentTarget.dataset
      const $elsToUpdate = this.$el.find(`[data-show-hide="${actionFor}"]`)

      if (currentTarget.checked) {
        $elsToUpdate.attr('style', '')
      } else {
        $elsToUpdate.css({
          opacity: '.5',
          pointerEvents: 'none'
        })
      }
    })
  }

  updateLogoPreview(selector, property) {
    this.$el.find(selector).empty()
    const uri = this.model.get(property)
    if (!uri) return

    const urlBuilder = createDarkroomUrlBuilder(
      config.darkroom.url,
      config.darkroom.salt
    )
    const img = new Image()

    img.src = urlBuilder()
      .resource(uri)
      .width(400)
      .filename('preview.jpg')
      .url()
    this.$el.find(selector).append(img)
  }
}

module.exports = FormView
