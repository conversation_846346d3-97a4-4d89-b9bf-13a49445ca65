const iro = require('@jaames/iro')
const View = require('ventnor')

class ColorPicker extends View {
  constructor(serviceLocator, value) {
    super(serviceLocator)
    this.value = value || '#ea5a1c'
    this.$el = $('<div>')
    this.$colorPicker = $('<div>')
    this.$input = $(
      '<input type="text" class="control control--text" pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$" />'
    )
    this.$input.val(this.value)
    this.$input.css('margin-top', '16px')
  }

  updateValue() {
    this.value = this.iro.color.hexString
    this.$input.val(this.value)
    this.emit('change', this.value)
  }

  updateColorPicker({ currentTarget }) {
    const { value } = currentTarget
    this.value = value
    this.iro.color.hexString = value
    this.emit('change', this.value)
  }

  render() {
    setTimeout(() => {
      this.$input.on('change', this.updateColorPicker.bind(this))

      this.iro = new iro.ColorPicker(this.$colorPicker[0], {
        color: this.value,
        width: 200
      })
      this.iro.on(['color:init', 'color:change'], this.updateValue.bind(this))

      this.$el.append(this.$colorPicker, this.$input)
    }, 0)

    return this
  }
}

module.exports = ColorPicker
