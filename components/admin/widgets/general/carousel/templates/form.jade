extends ../../../../widget/templates/form/base.jade

block custom-form

  .form-row.js-list-select-form-row(id='lists', data-field='lists')
    span.form-label-text Lists
    .js-list-select.form-field
    .js-error
    .form-row-description.form-copy
      button.btn.js-new-list Create New List
    .form-row-description.form-copy
      p Choose the lists that will filter content.

  .form-row(id='limit', data-field='limit')
    label
      span.form-label-text Limit
      input.control.control--text.form-field(type='text', name='limit', value=data.limit)
    .js-error

  .form-row.form-row-boolean(id='dedupe', data-field='dedupe')
    label
      span.form-label-text Dedupe
      .form-field
        input.control.control--boolean(type='checkbox', name='dedupe', checked=data.dedupe)
        span Only show Articles that haven’t already been shown on the page.
    .js-error

  .form-row(id='field--itemsPerRow', data-field='itemsPerRow')
    label
      span.form-label-text Number of items per row
        abbr(title='This field is required') *
      select.control.control--choice.form-field(name='itemsPerRow')
        option(value='3', selected=data.itemsPerRow === '3') 3 items
        option(value='4', selected=data.itemsPerRow === '4') 4 items
        option(value='5', selected=data.itemsPerRow === '5') 5 items
    .js-error
    .form-row-description.form-copy
      p Choose whether 3, 4 or 5 items should be displayed in a single row at the same time.

  .form-row(id='fallbackInstances', data-field='fallbackInstances')
    span.form-label-text Fallback Instances
    .js-fallback-instances.form-field
    .js-error
    .form-row-description.form-copy
      p If the selected lists do not generate enough articles, the widget will also fetch articles from these instances to fill the carousel.
