const BaseModel = require('cf-base-model')
const schemata = require('../../../../../service/widgets/general/executive-gallery/schema')()
const validateDelegate = require('../../../../../../admin/source/js/lib/validate-delegate')()

const type = 'executiveGallery'
module.exports = BaseModel.extend({
  schemata,
  type,
  defaults() {
    return schemata.makeDefault({ type })
  },
  validate: validateDelegate
})
