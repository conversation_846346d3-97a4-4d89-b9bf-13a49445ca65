const init = (serviceLocator, done) => {
  const widget = {
    name: 'Executive Gallery',
    model: require('./models/model'),
    editView: require('./views/form'),
    itemView: require('./views/item'),
    description: 'Displays gallery'
  }

  serviceLocator.widgetFactories
    .get('executive')
    .register('executiveGallery', widget)

  done()
}

module.exports = () => ({
  executiveGalleryWidget: ['widget', 'executiveAdmin', init]
})
