const init = (serviceLocator, done) => {
  const widget = {
    editView: require('./views/form'),
    model: require('./models/model'),
    name: 'FAQs',
    itemView: require('./views/item'),
    description: ''
  }

  for (const widgetType of [
    'section',
    'event',
    'eventUmbrella',
    'eventSponsorBody'
  ]) {
    serviceLocator.widgetFactories.get(widgetType).register('faqs', widget)
  }

  done()
}

module.exports = () => ({ faqsWidget: ['widget', 'sectionAdmin', init] })
