const BaseModel = require('cf-base-model')
const schema = require('../../../../../../service/widgets/general/faqs/faq-item-schema')()

module.exports = BaseModel.extend({
  schema,

  defaults() {
    return schema.makeDefault()
  },

  // initialize() {
  //   this.images = new ImageAreaModel(this.get('images'))
  // },

  validate(cb) {
    this.schema.validate(this.attributes, (ignoreErr, errors) =>
      cb(Object.keys(errors).length > 0 ? errors : undefined)
    )
  }
})
