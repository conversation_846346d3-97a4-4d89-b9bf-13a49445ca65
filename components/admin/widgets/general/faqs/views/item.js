const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/item.jade'))
const EditBaseItemView = require('../../../../widget/views/item/edit-base')

module.exports = EditBaseItemView.extend({
  template,
  render() {
    const faqsLength = this.model.get('faqs').length

    this.$el.empty().append(
      this.template(
        Object.assign(
          {
            name: this.factory.name,
            description: this.factory.description,
            options: this.factory.options,
            model: this.model,
            faqsLength
          },
          this.extraProperties
        )
      )
    )

    this.$('.js-tooltip-trigger').tooltip({ html: true })

    return this
  }
})
