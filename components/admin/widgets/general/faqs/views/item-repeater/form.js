const compileJade = require('browjadify-compile')
const join = require('path')
const BaseItemRepeaterFormView = require('../../../../../widget/views/form/base-item-repeater')
const template = compileJade(
  join(__dirname, '../../templates/item-repeater/form.jade')
)

class ItemRepeaterFormView extends BaseItemRepeaterFormView {
  constructor(...args) {
    super(...args)
    this.template = template
  }

  handleSave() {
    this.model.set('faqs', this.model.toJSON())
    super.handleSave()
  }

  render() {
    this.$el.append(this.template({ data: this.model.toJSON() }))

    this.$('.js-text-editor').each((index, value) =>
      this.richTextEditorManager.create(value, {
        height: 300,
        startupFocus: true
      })
    )

    return this
  }
}

module.exports = ItemRepeaterFormView
