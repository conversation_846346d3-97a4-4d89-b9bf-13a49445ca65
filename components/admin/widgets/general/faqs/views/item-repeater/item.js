const compileJade = require('browjadify-compile')
const join = require('path')
const BaseItemRepeaterItemView = require('../../../../../widget/views/item/base-item-repeater')
const template = compileJade(
  join(__dirname, '../../templates/item-repeater/item.jade')
)

class ItemRepeaterItemView extends BaseItemRepeaterItemView {
  constructor(serviceLocator, model, ...args) {
    super(serviceLocator, model, ...args)
    this.template = template
  }

  render() {
    const data = this.model.toJSON()
    this.$el.append(this.template({ data: data, id: this.model.id }))
    return this
  }
}

module.exports = ItemRepeaterItemView
