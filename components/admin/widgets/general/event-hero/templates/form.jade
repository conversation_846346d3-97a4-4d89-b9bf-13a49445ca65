extends ../../../../widget/templates/form/base

block custom-form

  .form-row.form-row--ckeditor(id='field--title', data-field='title')
    label
      span.form-label-text Title
      textarea.control.control--text.control--multiline.js-text-editor.form-field(name='title', rows='5')= data.title
    .js-error
    .form-row-description.form-copy

  .form-row.form-row-boolean(id='field--isTitleVisuallyHidden', data-field='isTitleVisuallyHidden')
    label
      span.form-label-text Is title visually hidden?
      .form-field
        input.control.control--boolean(type='checkbox', name='isTitleVisuallyHidden', checked=data.isTitleVisuallyHidden)
        span Hides the title visually while keeping as a H1 tag 
      .js-error
      
  .form-row.form-row--ckeditor(id='field--subtitle', data-field='subtitle')
    label
      span.form-label-text Subtitle
      textarea.control.control--text.control--multiline.js-text-editor.form-field(name='subtitle', rows='3')= data.subtitle
    .js-error
    .form-row-description.form-copy

  .form-row.form-row--ckeditor(id='field--description', data-field='description')
    label
      span.form-label-text Description
      textarea.control.control--text.control--multiline.form-field.js-text-editor(type='text', name='description', rows="3")=data.description
      .js-error
      .form-row-description.form-copy




  //- Theme color override is now derived from the proxy event

  .form-row(id='field--backgroundVideoYoutubeId', data-field='backgroundVideoYoutubeId')
    label
      span.form-label-text Background Video Youtube ID
      input.control--text.form-field(type='text', name='backgroundVideoYoutubeId', value=data.backgroundVideoYoutubeId)
      .js-error

  .form-row(id='field--align', data-field='align')
    label
      span.form-label-text Align
      select.control.control--choice.form-field(name='align')
        option(value='center', selected=data.align === 'center') Center
        option(value='left', selected=data.align === 'left') Left

  .form-row.form-row-boolean(id='field--parallax', data-field='parallax')
    label
      span.form-label-text Parallax Effect Background
      .form-field
        input.control.control--boolean(type='checkbox', name='parallax', checked=data.parallax)
        span Should the background image (if set) have it's position fixed when scrolling?
      .js-error

  .form-row.form-row-boolean(id='field--hasBigButton', data-field='hasBigButton')
    label
      span.form-label-text Big Button
      .form-field
        input.control.control--boolean(type='checkbox', name='hasBigButton', checked=data.hasBigButton)
        span Makes the first button of the button group wide and on it's own line
      .js-error

  .form-row.form-row-boolean(id='field--useAsHeading', data-field='useAsHeading')
    label
      span.form-label-text Force narrow mode
      .form-field
        input.control.control--boolean(type='checkbox', name='useAsHeading', checked=data.useAsHeading)
        span Simply removes the padding and margin from the top and bottom of the widget.
      .js-error

  .form-row.form-row-boolean(id='field--hideEventLogo', data-field='hideEventLogo')
    label
      span.form-label-text Hide the event logo
      .form-field
        input.control.control--boolean(type='checkbox', name='hideEventLogo', checked=data.hideEventLogo)
        span Should the event logo be hidden?
      .js-error

  .form-row.form-row-boolean(id='field--useMaxHeight', data-field='useMaxHeight')
    label
      span.form-label-text Use max height
      .form-field
        input.control.control--boolean(type='checkbox', name='useMaxHeight', checked=data.useMaxHeight)
        span Should the widget use the max height?
      .js-error

  .form-row(id='field--proxyEventId', data-field='proxyEventId')
    label
      span.form-label-text Proxy Event
      .js-proxyEventId-select.form-field
    .js-error
    .form-row-description.form-copy
      p If set, the logo and brand color will be taken from the selected event.

  .form-row(id='field--sponsorId', data-field='sponsorId')
    label
      span.form-label-text Sponsor's Logo
      .js-sponsor-select.form-field
    .js-error
    .form-row-description.form-copy
      p The logo which appears to the left of the title (most likely used in a sponsor booth). If set will force align content to the left.

  .js-button-item-repeater

  .panel.panel-styled
    .panel-header
      h2 Image
    .panel-content
      .js-image-area
      .form-row.form-row-actions
        input.btn.btn-success.js-image-add(type='submit', value='Choose Image')
