const join = require('path').join
const compileJade = require('browjadify-compile')
const BaseWidgetView = require('../../../../widget/views/form/base')
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const debug = require('../../../../../../admin/source/js/lib/debug')(
  'Event Content Grid widget view'
)
const ButtonItemRepeaterInit = require('../../../../event/views/components/button-item-repeater')
const retrieveEventIdByPath = require('../../../../event/lib/event-id-by-path-retriever')
const ItemRepeater = require('../../../../widget/views/item-repeater')
const EventSelect = require('../../../../event/views/components/event-select')
const FormFieldDescriptions = require('../../../../widget/views/form/form-field-descriptions')
const {
  config: validationConfig
} = require('../../../../../service/event/validation-config')
const repeaterConfig = {
  formView: require('./item-repeater/form'),
  itemView: require('./item-repeater/item'),
  itemModel: require('../models/item-repeater/item'),
  itemNames: {
    singular: 'Content',
    plural: 'List'
  }
}

module.exports = BaseWidgetView.extend({
  template,
  debug,
  initialize() {
    BaseWidgetView.prototype.initialize.apply(this, arguments)
    this.$el
      .find('.js-listType-select')
      .on('change', this.handleTypeChange.bind(this))

    this.showElement(this.model.get('listType'))

    this.eventId = retrieveEventIdByPath(window.location.pathname)
    this.defaultEventId = this.eventId

    this.setupSelect()

    const appendItemRepeater = (event) => {
      const itemRepeater = new ItemRepeater(
        this.options.serviceLocator,
        repeaterConfig,
        this.model.get('list'),
        'Add Content',
        { event }
      )
      this.itemRepeater = itemRepeater
      this.$el.find('.js-item-repeater').append(itemRepeater.render().$el)
    }

    if (this.eventId) {
      this.options.serviceLocator.eventService.read(
        this.eventId,
        (err, event) => {
          if (err) return console.error(err)
          appendItemRepeater(event)
        }
      )
    } else {
      appendItemRepeater()
    }

    const buttonItemRepeater = new ButtonItemRepeaterInit(
      this.options.serviceLocator,
      this.model.get('buttonGroup'),
      { buttonActions: ['action 1', 'action 2'] }
    ).itemRepeater
    this.buttonItemRepeater = buttonItemRepeater
    this.$el
      .find('.js-button-item-repeater')
      .append(buttonItemRepeater.render().$el)
  },
  setupSelect() {
    this.eventSelect = new EventSelect(
      this.options.serviceLocator,
      this.model.get('eventId')
    )
    this.eventSelect.on('change', (value) => {
      this.model.set('eventId', value)
    })
    this.$el
      .find('.js-eventIdOverride-select')
      .empty()
      .append(this.eventSelect.render().$el)
  },
  showElement(listType) {
    this.model.set('listType', listType)
    if (listType === 'auto') {
      this.$el.find('.js-auto').show()
      this.$el.find('.js-manual').hide()
    } else if (listType === 'manual') {
      this.$el.find('.js-manual').show()
      this.$el.find('.js-auto').hide()
    } else {
      this.$el.find('.js-auto').hide()
      this.$el.find('.js-manual').hide()
    }
  },
  handleTypeChange() {
    const listType = this.$el.find('.js-listType-select').val()
    this.showElement(listType)
  },
  addCustomFormData(formData) {
    formData.buttonGroup = this.buttonItemRepeater.getItems()
    return BaseWidgetView.prototype.addCustomFormData.call(
      this,
      Object.assign({}, formData, {
        list: this.itemRepeater.getItems()
      })
    )
  },
  renderRichTextEditor() {
    this.$('.js-text-editor').each((index, value) =>
      this.richTextEditorManager.create(value, {
        height: 100,
        startupFocus: true
      })
    )
  },
  retrieveEvents(cb) {
    this.options.serviceLocator.eventService.find(
      '',
      { eventUmbrellaId: { $ne: null } },
      [],
      { page: 1, pageSize: 100000 },
      (err, res) => {
        if (err) return cb(err)
        const events = res && res.results
        cb(null, events)
      }
    )
  },
  renderSelects() {
    this.retrieveEvents((err, events) => {
      if (err) return this.serviceLocator.logger.error(err)
      this.$el.find('.js-selectize').each((i, el) => {
        $(el).selectize({
          delimiter: ',',
          createOnBlur: false,
          create: false,
          options: events.map((e) => ({
            text: e.name,
            value: e._id
          })),
          onInitialize: () => {
            if (el.name === 'eventIds') {
              if (
                !this.model.get('eventIds') ||
                this.model.get('eventIds').length === 0
              ) {
                this.model.set('eventIds', [this.defaultEventId])
              }
              el.selectize.setValue(this.model.get(el.name))
            }
            el.selectize.on('change', () => {
              this.model.set(el.name, el.selectize.getValue())
            })
          }
        })
      })
    })
  },
  renderFormDescriptions() {
    const { EventContentGridWidget } = validationConfig
    const properties = Object.keys(EventContentGridWidget).map((key) => ({
      name: key,
      maxLength: EventContentGridWidget[key],
      initialValue: this.model.get(key)
    }))

    new FormFieldDescriptions(
      this.serviceLocator,
      this.$el,
      properties
    ).render()
  },
  render() {
    BaseWidgetView.prototype.render.call(this)
    this.renderRichTextEditor()
    this.renderSelects()
    this.renderFormDescriptions()
  }
})
