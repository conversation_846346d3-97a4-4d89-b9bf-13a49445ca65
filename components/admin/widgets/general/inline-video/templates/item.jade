extend ../../../../widget/templates/item/edit-base

block content
  .list-item-content.expander-content
    - var platform = platforms[model.get('provider')]
    if !platform
      p Video 
    else
      .grid
        .grid__item.one-third
          iframe(width='100%', src=platform.embedUrl(model.get('videoId')))

        .grid__item.two-thirds
          dl
            dt Video ID:
            dd= model.get('videoId')
            dt Link to Video:
            - var link = platform.link(model.get('videoId'))
            dd
              a(href=link, target="_blank")= link
            dt How to link to this video:
            dd To link to this video on a page, simply add #<strong>#{model.get('videoId')}</strong> after the page url
            dt Video caption:
            dd
              if model.get('videoCaption')
                - var text = model.get('videoCaption').replace(/\n/g, "<br />")
                span= text  
              else
                span None
