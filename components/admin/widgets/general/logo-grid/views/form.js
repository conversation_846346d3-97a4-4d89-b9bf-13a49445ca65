const modal = require('modal')
const join = require('path').join
const compileJade = require('browjadify-compile')
const BaseWidgetView = require('../../../../widget/views/form/base')
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const debug = require('../../../../../../admin/source/js/lib/debug')(
  'Logo Grid widget view'
)
const AssetPickerView = require('../../../../asset/views/picker')
const WidgetAreaView = require('../../../../widget/views/widget-area')
const AssetCollection = require('../../../../asset/collections/asset')
const getImageFactory = require('../../../../asset/lib/image-factory')()
const imageConfig = require('../image-config.json')

module.exports = BaseWidgetView.extend({
  events: Object.assign({}, BaseWidgetView.prototype.events, {
    'click .js-image-add': 'handleAddImage'
  }),

  template,
  debug,
  imageConfig,
  initialize() {
    BaseWidgetView.prototype.initialize.apply(this, arguments)
  },
  addCustomFormData(formData) {
    formData.images = this.model.images.toJSON()

    return formData
  },
  handleAddImage(e) {
    e.preventDefault()
    debug('adding image', arguments)

    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      serviceLocator: this.options.serviceLocator,
      type: 'image',
      multiple: true
    })

    collection.getByType('image')

    modal({
      title: 'Add Image',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        { text: 'Add Image', event: 'add', className: 'btn', keyCodes: [13] }
      ]
    })
      .on('add', this.addImage.bind(this, assetListView.selectedCollection))
      .on('close', assetListView.remove.bind(assetListView))
  },
  addImage(images) {
    debug('images', images)
    const factory = getImageFactory('image')
    const Model = factory.model
    images.map(function (model) {
      const image = new Model(model.attributes)
      this.model.images.add(image)
      image.setDefaultCrops(this.galleryImageConfig.crops)
      debug('Adding new image', image)
      return image
    }, this)
  },
  renderImageSelector() {
    const imageView = new WidgetAreaView({
      model: this.model.images,
      receiveDrops: false,
      serviceLocator: this.options.serviceLocator,
      widgetItemOptions: this.galleryImageConfig,
      multiple: true
    })

    this.$('.js-image-area').append(imageView.$el)
  },
  render() {
    BaseWidgetView.prototype.render.call(this)
    this.renderImageSelector()
  }
})
