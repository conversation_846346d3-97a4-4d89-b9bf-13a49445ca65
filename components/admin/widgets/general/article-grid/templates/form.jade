extends ../../../../widget/templates/form/base.jade

block custom-form

  .form-row(id='displayType', data-field='displayType')
    span.form-label-text Display Type
      abbr(title='This field is required') *
    .form-row-description.form-copy
        p 
          | Choose the type of grid to display. Coloured blocks represent image crops.
        p
          span(style="width: 10px; height:10px; background-color: #7847d7; display: inline-block; margin-right: 10px")
          | Landscape (Daily stories, Lists etc.)
        p
          span(style="width: 10px; height:10px; background-color: #D77847; display: inline-block; margin-right: 10px")
          | Portrait (Interviews, Profiles, Whitepapers)
        p
          span(style="width: 10px; height:10px; background-color: #47D778; display: inline-block; margin-right: 10px")
          | Widescreen (Videos)
    .form-field.form-field--thumbnails
      each item in ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']
        input.control.control--thumbnail(type='radio', value=item, name='displayType', checked=data.displayType === item, data-label=item)
    .js-error
    

  .form-row.js-list-select-form-row(id='lists', data-field='lists')
    span.form-label-text Lists
    .js-list-select.form-field
    .js-error
    .form-row-description.form-copy
      button.btn.js-new-list Create New List
    .form-row-description.form-copy
      p Choose the lists that will filter content.

  .form-row(id='limit', data-field='limit')
    label
      span.form-label-text Limit
      input.control.control--text.form-field(type='text', name='limit', value=data.limit)
    .js-error
    .form-row-description.form-copy
      p This may be overridden / ignored depending on your chosen display type

  .form-row.form-row-boolean(id='dedupe', data-field='dedupe')
    label
      span.form-label-text Dedupe
      .form-field
        input.control.control--boolean(type='checkbox', name='dedupe', checked=data.dedupe)
        span Only show Articles that haven’t already been shown on the page.
    .js-error

  .form-row.form-row-boolean(id='displayCategory', data-field='displayCategory')
    label
      span.form-label-text Display Category
      .form-field
        input.control.control--boolean(type='checkbox', name='displayCategory', checked=data.displayCategory)
        span Show the Category the article belongs to if it has one
    .js-error
    .form-row-description.form-copy
      p This may not be applicable depending on your selected display type

  .form-row(id='fallbackInstances', data-field='fallbackInstances')
    span.form-label-text Fallback Instances
    .js-fallback-instances.form-field
    .js-error
    .form-row-description.form-copy
      p If the selected lists do not generate enough articles, the widget will also fetch articles from these instances to fill the grid.
