const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/item.jade'))
const EditBaseItemView = require('../../../../widget/views/item/edit-base')

module.exports = EditBaseItemView.extend({
  template,
  render() {
    const numImages = this.model.get('images').filter((img) => {
      if (img.images.widgets && img.images.widgets.length) return img
    }).length
    this.$el.empty().append(
      this.template(
        Object.assign(
          {
            name: this.factory.name,
            description: this.factory.description,
            options: this.factory.options,
            model: this.model,
            numImages
          },
          this.extraProperties
        )
      )
    )

    this.$('.js-tooltip-trigger').tooltip({ html: true })

    return this
  }
})
