const join = require('path').join
const compileJade = require('browjadify-compile')
const BaseWidgetView = require('../../../../widget/views/form/base')
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const debug = require('../../../../../../admin/source/js/lib/debug')(
  'Event Testimonial Grid widget view'
)
const retrieveEventIdByPath = require('../../../../event/lib/event-id-by-path-retriever')
const ItemRepeater = require('../../../../widget/views/item-repeater')
const repeaterConfig = {
  formView: require('./item-repeater/form'),
  itemView: require('./item-repeater/item'),
  itemModel: require('../models/item-repeater/item'),
  itemNames: {
    singular: 'Testimonial',
    plural: 'List'
  }
}

module.exports = BaseWidgetView.extend({
  template,
  debug,
  initialize() {
    BaseWidgetView.prototype.initialize.apply(this, arguments)
    this.eventId = retrieveEventIdByPath(window.location.pathname)
    if (!this.eventId) return this.createItemRepeater()
    this.options.serviceLocator.eventService.read(
      this.eventId,
      (err, event) => {
        if (err) return console.error(err)
        this.createItemRepeater({ event })
      }
    )
  },
  createItemRepeater(additionalVars) {
    if (!additionalVars) additionalVars = {}
    const itemRepeater = new ItemRepeater(
      this.options.serviceLocator,
      repeaterConfig,
      this.model.get('list'),
      'Add Testimonial',
      additionalVars
    )
    this.itemRepeater = itemRepeater
    this.$el.find('.js-item-repeater').append(itemRepeater.render().$el)
  },
  addCustomFormData(formData) {
    return BaseWidgetView.prototype.addCustomFormData.call(
      this,
      Object.assign({}, formData, {
        list: this.itemRepeater.getItems(),
        eventId: this.eventId
      })
    )
  },
  renderRichTextEditor() {
    this.$('.js-text-editor').each((index, value) =>
      this.richTextEditorManager.create(value, {
        height: 100,
        startupFocus: true
      })
    )
  },
  render() {
    BaseWidgetView.prototype.render.call(this)
    this.renderRichTextEditor()
  }
})
