const init = (serviceLocator, done) => {
  const widget = {
    name: 'Company Featured Articles',
    model: require('./models/model'),
    editView: require('./views/form'),
    itemView: require('./views/item'),
    description: 'Displays featured articles from the company'
  }

  serviceLocator.widgetFactories
    .get('company')
    .register('companyFeaturedArticles', widget)

  done()
}

module.exports = () => ({
  companyFeaturedArticlesWidget: ['widget', 'companyAdmin', init]
})
