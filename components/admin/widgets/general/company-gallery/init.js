const init = (serviceLocator, done) => {
  const widget = {
    name: 'Company Gallery',
    model: require('./models/model'),
    editView: require('./views/form'),
    itemView: require('./views/item'),
    description: 'Displays gallery'
  }

  serviceLocator.widgetFactories
    .get('company')
    .register('companyGallery', widget)

  done()
}

module.exports = () => ({
  companyGalleryWidget: ['widget', 'companyAdmin', init]
})
