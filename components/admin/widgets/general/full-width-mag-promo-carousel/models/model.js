const BaseModel = require('cf-base-model')
const createImageUrlBuilder = require('cf-image-url-builder')
const schemata = require('../../../../../service/widgets/general/full-width-mag-promo-carousel/schema')()
const validateDelegate = require('../../../../../../admin/source/js/lib/validate-delegate')()
const config = window.config

module.exports = BaseModel.extend({
  schemata,
  defaults() {
    return schemata.makeDefault({ type: 'fullWidthMagPromoCarousel' })
  },
  validate: validateDelegate,
  getPreviewImageUrls() {
    const slidesArray = this.get('slides') || []

    return slidesArray.map((slide) => {
      const widgetImage = slide.images.widgets

      const drUrl = config.darkroom.url
      const drKey = config.darkroom.salt
      const urlBuilder = createImageUrlBuilder(drUrl, drKey, widgetImage)
      const inlineImage = urlBuilder.getImage('Background')

      if (inlineImage) {
        return inlineImage.crop('Landscape').constrain(300).url()
      }

      return null
    })
  }
})
