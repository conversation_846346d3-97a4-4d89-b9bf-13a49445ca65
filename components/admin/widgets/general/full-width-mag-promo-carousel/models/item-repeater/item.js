const BaseModel = require('cf-base-model')
const ImageAreaModel = require('../../../../../asset/models/image-area')
const config = window.config
const createImageUrlBuilder = require('cf-image-url-builder')
const schema = require('../../../../../../service/widgets/general/full-width-mag-promo-carousel/slide-schema')()

module.exports = BaseModel.extend({
  schema,

  defaults() {
    return schema.makeDefault()
  },

  initialize() {
    this.images = new ImageAreaModel(this.get('images'))
  },

  getPreviewImageUrl() {
    const images = this.images.widgets.map((widget) => widget.toJSON())
    const drUrl = config.darkroom.url
    const drKey = config.darkroom.salt
    const urlBuilder = createImageUrlBuilder(drUrl, dr<PERSON><PERSON>, images)
    const image = urlBuilder.getImage('Background')

    if (image) {
      return image.crop('Landscape').constrain(300).url()
    }

    return null
  },

  validate(cb) {
    this.schema.validate(this.attributes, (ignoreErr, errors) => {
      if (!errors) {
        return null
      }

      cb(Object.keys(errors).length > 0 ? errors : undefined)
    })
  }
})
