const init = (serviceLocator, done) => {
  const widget = {
    name: 'Full width magazine promo carousel',
    model: require('./models/model'),
    editView: require('./views/form'),
    itemView: require('./views/item'),
    description: 'Displays the full width magazine promo carousel.'
  }

  serviceLocator.widgetFactories
    .get('section')
    .register('fullWidthMagPromoCarousel', widget)

  done()
}

module.exports = () => ({
  fullWidthMagPromoCarouselWidget: ['widget', 'sectionAdmin', init]
})
