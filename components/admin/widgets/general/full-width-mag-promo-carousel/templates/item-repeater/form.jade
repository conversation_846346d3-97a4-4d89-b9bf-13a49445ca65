extends ../../../../../widget/templates/form/base-item-repeater

block custom-form
  .form-row(data-field='title')
    label
      span.form-label-text Title
      input.control--text.form-field(type='text', name='title', value=data.title)
      .js-error
      .form-row-description.form-copy
        p Add [ and ] around text that you want to be coloured. For example, Your Opinion [Matters].

  .form-row(data-field='content')
    label
      span.form-label-text Content
      textarea.control--text.form-field(name='content', rows="3")=data.content
      .js-error
        
  .form-row(id='field--linkText', data-field='linkText')
    label
      span.form-label-text Link text
      input.control.control--text.form-field(type='text', name='linkText', value=data.linkText)
    .js-error
      
  .form-row(id='field--destination', data-field='destination')
    label
      span.form-label-text Destination
      input.control.control--text.form-field(type='text', name='destination', value=data.destination)
    .js-error
    .form-row-description.form-copy
      p An optional field where you can add an external or internal link to make your image an anchor 

  .form-row.form-row-boolean(id='field--opensInNewTab', data-field='opensInNewTab')
    label
      span.form-label-text Open in a new tab?
      .form-field
        input.control.control--boolean(type='checkbox', name='opensInNewTab', checked=data.opensInNewTab)
    .js-error
      
  hr
      
  .form-row.form-row-boolean(id='field--hasBorder', data-field='hasBorder')
    label
      span.form-label-text Does slide have a border?
      .form-field
        input.control.control--boolean(type='checkbox', name='hasBorder', checked=data.hasBorder)
    .js-error
      
  hr

  .form-row(id='field--primaryColor', data-field='primaryColor')
    label
      span.form-label-text Primary colour
      .form-field(data-color-picker)
    .js-error

  .panel.panel-styled
    .panel-header
      h2 Image
    .panel-content
      .js-image-area
      .form-row.form-row-actions
        input.btn.btn-success.js-image-add(type='submit', value='Choose Image')
