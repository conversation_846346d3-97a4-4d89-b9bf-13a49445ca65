const join = require('path').join
const compileJade = require('browjadify-compile')
const BaseWidgetView = require('../../../../widget/views/form/base')
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const debug = require('../../../../../../admin/source/js/lib/debug')(
  'Full width magazine promo carousel widget view'
)

const { crops } = require('../image-config.json')
const ItemRepeater = require('../../../../widget/views/item-repeater')
const repeaterConfig = {
  formView: require('./item-repeater/form'),
  itemView: require('./item-repeater/item'),
  itemModel: require('../models/item-repeater/item'),
  itemNames: {
    singular: 'Slide',
    plural: 'Slides'
  }
}

module.exports = BaseWidgetView.extend({
  template,
  debug,
  initialize() {
    BaseWidgetView.prototype.initialize.apply(this, arguments)

    const images = this.model
      .get('slides')
      .filter((slide) => slide.images.widgets && slide.images.widgets.length)

    const itemRepeater = new ItemRepeater(
      this.options.serviceLocator,
      repeaterConfig,
      images
    )

    this.itemRepeater = itemRepeater
    this.$el.find('.js-item-repeater').append(itemRepeater.render().$el)
  },

  addCustomFormData(formData) {
    return BaseWidgetView.prototype.addCustomFormData.call(
      this,
      Object.assign({}, formData, {
        slides: this.itemRepeater.getItems(),
        crops
      })
    )
  }
})
