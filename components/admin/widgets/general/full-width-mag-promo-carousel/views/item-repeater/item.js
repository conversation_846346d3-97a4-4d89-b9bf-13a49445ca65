const compileJade = require('browjadify-compile')
const join = require('path')
const BaseItemRepeaterItemView = require('../../../../../widget/views/item/base-item-repeater')
const template = compileJade(
  join(__dirname, '../../templates/item-repeater/item.jade')
)

class ItemRepeaterItemView extends BaseItemRepeaterItemView {
  constructor(serviceLocator, model, ...args) {
    super(serviceLocator, model, ...args)
    this.template = template
  }

  render() {
    const data = this.model.toJSON()
    data.previewImageUrl = this.model.getPreviewImageUrl()
    const images = data && data.images && data.images.widgets
    const image = images.filter(
      (image) => (image.selectedContexts[0] = 'Main')
    )[0]
    const caption =
      image && image.caption ? image.caption : 'No caption for image'
    this.$el.append(this.template({ data: data, id: this.model.id, caption }))

    return this
  }
}

module.exports = ItemRepeaterItemView
