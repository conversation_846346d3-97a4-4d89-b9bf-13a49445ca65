const compileJade = require('browjadify-compile')
const join = require('path')
const BaseItemRepeaterFormView = require('../../../../../widget/views/form/base-item-repeater')
const template = compileJade(
  join(__dirname, '../../templates/item-repeater/form.jade')
)
const AssetCollection = require('../../../../../asset/collections/asset')
const getImageFactory = require('../../../../../asset/lib/image-factory')()
const AssetPickerView = require('../../../../../asset/views/picker')
const WidgetAreaView = require('../../../../../widget/views/widget-area')
const modal = require('modal')
const { crops, contexts } = require('../../image-config.json')
const ColorPicker = require('../../../../lib/color-picker')

class ItemRepeaterFormView extends BaseItemRepeaterFormView {
  constructor(...args) {
    super(...args)
    this.template = template

    this.$el.on('click', '.js-image-add', this.handleAddImage.bind(this))
  }

  renderImageSelector() {
    const imageView = new WidgetAreaView({
      model: this.model.images,
      receiveDrops: false,
      serviceLocator: this.serviceLocator,
      widgetItemOptions: {
        crops,
        contexts
      }
    })

    this.$('.js-image-area').append(imageView.$el)
  }

  handleAddImage(e) {
    e.preventDefault()

    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      serviceLocator: this.serviceLocator,
      type: 'image',
      multiple: true
    })

    collection.getByType('image')

    modal({
      title: 'Add Image',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        { text: 'Add Image', event: 'add', className: 'btn', keyCodes: [13] }
      ]
    })
      .on('add', this.addImage.bind(this, assetListView.selectedCollection))
      .on('close', assetListView.remove.bind(assetListView))
  }

  addImage(images) {
    const factory = getImageFactory('image')
    const Model = factory.model
    images.map((model) => {
      const image = new Model(model.attributes)
      this.model.images.add(image)
      image.setDefaultCrops(crops)

      return image
    })
  }

  handleSave() {
    this.model.set('images', this.model.images.toJSON())
    super.handleSave()
  }

  renderColorPicker() {
    const colorPicker = new ColorPicker(
      this.serviceLocator,
      this.model.get('primaryColor')
    )
    this.$el.find('[data-color-picker]').append(colorPicker.render().$el)
    colorPicker.on('change', (value) => this.model.set('primaryColor', value))
  }

  render() {
    this.$el.append(this.template({ data: this.model.toJSON() }))
    this.renderImageSelector()
    this.renderColorPicker()
    return this
  }
}

module.exports = ItemRepeaterFormView
