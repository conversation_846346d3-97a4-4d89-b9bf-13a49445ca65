const { join } = require('path')
const compileJade = require('browjadify-compile')
const template = compileJade(join(__dirname, '/../templates/item.jade'))
const EditBaseItemView = require('../../../../widget/views/item/edit-base')
const retrieveEventIdByPath = require('../../../../event/lib/event-id-by-path-retriever')

module.exports = EditBaseItemView.extend({
  template,

  render() {
    EditBaseItemView.prototype.render.call(this)
    this.eventId = retrieveEventIdByPath(window.location.pathname)
    this.hydrateEntityData()
    return this
  },

  hydrateEntityData() {
    const list = this.model.get('list') || []

    if (list.length === 0) {
      return
    }

    // Filter for agenda items only for now
    const agendaItems = list.filter(
      (item) => item.type === 'agendaItem' && item.agendaItemId
    )

    if (agendaItems.length === 0) {
      return
    }

    // Fetch agenda item data
    let completed = 0
    const enrichedItems = []

    agendaItems.forEach((item, index) => {
      this.fetchAgendaItem(item.agendaItemId, (err, agendaItem) => {
        if (!err && agendaItem) {
          enrichedItems[index] = {
            ...item,
            entityName: agendaItem.name,
            entityType: 'Agenda Item'
          }
        } else {
          enrichedItems[index] = {
            ...item,
            entityName: 'Unknown Agenda Item',
            entityType: 'Agenda Item'
          }
        }

        completed++
        if (completed === agendaItems.length) {
          // All agenda items fetched, re-render with enriched data
          this.extraProperties = {
            enrichedItems: enrichedItems
          }
          this.renderWithEnrichedData()
        }
      })
    })
  },

  fetchAgendaItem(agendaItemId, callback) {
    // We need to get the eventId from somewhere - let's check if it's available in the model or options
    if (!this.eventId) {
      return callback(new Error('No event ID available'))
    }
    this.options.serviceLocator.eventService.findAgendaItem(
      this.eventId,
      agendaItemId,
      callback
    )
  },

  renderWithEnrichedData() {
    // Re-render the template with enriched data
    this.$el.empty().append(
      this.template(
        Object.assign(
          {
            name: this.factory.name,
            description: this.factory.description,
            options: this.factory.options,
            model: this.model,
            id: this.model.id
          },
          this.extraProperties
        )
      )
    )

    this.$('.js-tooltip-trigger').tooltip({ html: true })
  }
})
