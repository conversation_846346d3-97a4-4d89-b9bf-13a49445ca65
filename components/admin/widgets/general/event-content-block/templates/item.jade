extend ../../../../widget/templates/item/edit-base

block content
  .grid
    .grid__item(style="padding: 0 0.75rem 0 1.5rem;")
      if enrichedItems && enrichedItems.length > 0
        .content-preview-list
          each item in enrichedItems
            .content-preview-item(style="margin-bottom: 8px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;")
              .content-preview-header
                if model.get('inverse')
                  span.label.label--small.label--inverse Inverse Layout
                span.label.label--small.label--standard #{item.entityType}
                if item.entityName
                  strong(style="margin-left: 8px;")= item.entityName
                else if item.title
                  strong(style="margin-left: 8px;")= item.title
                else
                  em(style="margin-left: 8px; color: #999;") No title set
              if item.subtitle
                p(style="margin: 4px 0; font-size: 0.9em; color: #666;")= item.subtitle
              if item.description
                p(style="margin: 4px 0; font-size: 0.85em; color: #888;")= item.description.length > 100 ? item.description.substring(0, 100) + '...' : item.description
      else if model.get('list') && model.get('list').length > 0
        .content-preview-list
          each item, index in model.get('list')
            .content-preview-item(style="margin-bottom: 8px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;")
              .content-preview-header
                span.label.label--small.label--standard #{item.type || 'Unknown'}
                if item.title
                  strong(style="margin-left: 8px;")= item.title
                else
                  em(style="margin-left: 8px; color: #999;") No title set
              if item.subtitle
                p(style="margin: 4px 0; font-size: 0.9em; color: #666;")= item.subtitle
              if item.description
                p(style="margin: 4px 0; font-size: 0.85em; color: #888;")= item.description.length > 100 ? item.description.substring(0, 100) + '...' : item.description
      else
        p(style="color: #999; font-style: italic;") No content items configured yet
