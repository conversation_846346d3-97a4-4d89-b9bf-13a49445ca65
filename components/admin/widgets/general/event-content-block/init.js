const init = (serviceLocator, done) => {
  const widget = {
    name: 'Event Content Block',
    model: require('./models/model'),
    editView: require('./views/form'),
    itemView: require('./views/item'),
    description:
      'Displays a customizable list of event content items (videos, articles, speakers, etc.)'
  }

  serviceLocator.widgetFactories
    .get('event')
    .register('eventContentBlock', widget)

  serviceLocator.widgetFactories
    .get('eventUmbrella')
    .register('eventContentBlock', widget)

  serviceLocator.widgetFactories
    .get('eventSponsorBody')
    .register('eventContentBlock', widget)

  done()
}

module.exports = () => ({
  eventContentBlockWidget: ['widget', 'eventAdmin', init]
})
