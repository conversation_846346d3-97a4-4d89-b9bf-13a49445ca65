const init = (serviceLocator, done) => {
  const widget = {
    name: 'Company Article Section',
    model: require('./models/model'),
    editView: require('./views/form'),
    itemView: require('./views/item'),
    description: 'Displays article section'
  }

  serviceLocator.widgetFactories
    .get('company')
    .register('companyArticleSection', widget)

  done()
}

module.exports = () => ({
  companyArticleSectionWidget: ['widget', 'companyAdmin', init]
})
