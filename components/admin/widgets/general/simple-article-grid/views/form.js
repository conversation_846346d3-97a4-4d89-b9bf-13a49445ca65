const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '../templates/form.jade'))
const listWidgetView = require('../../../../widget/views/form/list')
const InstanceSelect = require('../../../../instance/views/instance-select')
const debug = require('../../../../../../admin/source/js/lib/debug')(
  'Article Grid Widget View'
)

module.exports = listWidgetView.extend({
  template: template,
  debug: debug,
  initialize(options) {
    this.setupFallbackInstancesControl(options)
    listWidgetView.prototype.initialize.apply(this, arguments)
  },
  setupFallbackInstancesControl(options) {
    const selectedInstances = this.model.get('fallbackInstances') || []
    // InstanceSelect expects selectedInstances to be wrapped in an array when isMultiple is true
    // But the constructor converts it to an array, so we pass null and set them after initialization
    this.fallbackInstancesControl = new InstanceSelect(
      options.serviceLocator,
      null,
      { isMultiple: true }
    )

    // Set the selected instances after initialization
    if (selectedInstances.length > 0) {
      this.fallbackInstancesControl.selectedInstances = selectedInstances
    }

    this.fallbackInstancesControl.on('change', (value) => {
      this.model.set('fallbackInstances', value)
    })
  },
  render() {
    listWidgetView.prototype.render.call(this)
    this.$el
      .find('.js-fallback-instances')
      .append(this.fallbackInstancesControl.render().$el)
    return this
  }
})
