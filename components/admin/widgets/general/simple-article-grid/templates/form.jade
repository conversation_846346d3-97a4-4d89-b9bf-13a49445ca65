extends ../../../../widget/templates/form/base.jade

block custom-form

  .form-row(id='numPerRow', data-field='numPerRow')
    label
      span.form-label-text Articles Per Row
      input.control.control--text.form-field(type='text', name='numPerRow', value=data.numPerRow || 4)
    .js-error
    .form-row-description.form-copy
      p How many articles should shown per row

  .form-row.js-list-select-form-row(id='lists', data-field='lists')
    span.form-label-text Lists
    .js-list-select.form-field
    .js-error
    .form-row-description.form-copy
      button.btn.js-new-list Create New List
    .form-row-description.form-copy
      p Choose the lists that will filter content.

  .form-row(id='limit', data-field='limit')
    label
      span.form-label-text Limit
      input.control.control--text.form-field(type='text', name='limit', value=data.limit)
    .js-error
    .form-row-description.form-copy
      p This may be overridden / ignored depending on your chosen display type

  .form-row.form-row-boolean(id='dedupe', data-field='dedupe')
    label
      span.form-label-text Dedupe
      .form-field
        input.control.control--boolean(type='checkbox', name='dedupe', checked=data.dedupe)
        span Only show Articles that haven’t already been shown on the page.
    .js-error

  hr

  .form-row.form-row-boolean(id='loadMoreEnabled', data-field='loadMoreEnabled')
    label
      span.form-label-text Enable Load More
      .form-field
        input.control.control--boolean(type='checkbox', name='loadMoreEnabled', checked=data.loadMoreEnabled)
        span A "Show More" button will be visible allowing extra content to be added after page load
    .js-error

  .form-row(id='numPerPage', data-field='numPerPage')
    label
      span.form-label-text Articles Per Page
      input.control.control--text.form-field(type='text', name='numPerPage', value=data.numPerPage)
    .js-error
    .form-row-description.form-copy
      p How many articles should be loaded per page load

  .form-row(id='fallbackInstances', data-field='fallbackInstances')
    span.form-label-text Fallback Instances
    .js-fallback-instances.form-field
    .js-error
    .form-row-description.form-copy
      p If the selected lists do not generate enough articles, the widget will also fetch articles from these instances to fill the carousel.

