extends ../../../../widget/templates/form/base

block custom-form
  .form-row.form-row-boolean(id='showTitle', data-field='showTitle')
    label
      span.form-label-text Show Title
      .form-field
        input.control.control--boolean(type='checkbox', name='showTitle', checked=data.showTitle)
    .js-error
    .form-row-description.form-copy
      p Should the title show?


  .form-row(id='field--skip', data-field='skip')
    label
      span.form-label-text Skip
      input.control.control--text.form-field(type='number', name='skip', value=data.skip, min='0')
    .js-error

  .form-row(id='field--display', data-field='display')
    label
      span.form-label-text Display
      input.control.control--text.form-field(type='number', name='display', value=data.display, min='1')
    .js-error
