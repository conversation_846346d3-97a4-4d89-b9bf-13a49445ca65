const MenuBarView = require('../views/menu-bar')
const menu = [
  {
    title: 'Content',
    items: [
      { name: 'Articles', route: 'articles', resource: 'article' },
      { name: 'Sections', route: 'sections', resource: 'section' },
      { name: 'Lists', route: 'lists', resource: 'list' },
      {
        name: 'Magazine Issues',
        route: 'magazine-issues',
        resource: 'magazineIssue'
      },
      {
        name: 'Authors',
        route: 'authors',
        resource: 'author'
      },
      { name: 'Assets', route: 'asset-manager', resource: 'asset' },
      { name: 'Tags', route: 'tags', resource: 'tag' }
      // { name: 'Testimonials', route: 'testimonials', resource: 'testimonial' }
    ]
  },
  {
    title: 'Company Portals',
    items: [
      {
        name: 'Companies',
        route: 'companies',
        resource: 'company'
      },
      {
        name: 'Executives',
        route: 'executives',
        resource: 'executive'
      },
      { name: 'Partnerships', route: 'partnerships', resource: 'partnership' }
    ]
  },
  {
    title: 'Events',
    items: [
      {
        name: 'Presets',
        route: 'event-presets',
        resource: 'eventPresets'
      },
      {
        name: 'Portfolios',
        route: 'event-umbrellas',
        resource: 'eventUmbrella'
      },
      {
        name: 'Videos',
        route: 'event-videos',
        resource: 'eventVideo'
      }
    ]
  },
  {
    title: 'Site',
    items: [{ name: 'Redirects', route: 'redirects', resource: 'redirect' }]
  },
  {
    title: 'Settings',
    items: [
      { name: 'Roles', route: 'roles', resource: 'role' },
      {
        name: 'Administrators',
        route: 'administrators',
        resource: 'administrator'
      },
      { name: 'Accounts', route: 'accounts', resource: 'account' },
      { name: 'Instances', route: 'instances', resource: 'instance' },
      { name: 'Changelog', route: 'changelog', resource: 'changelog' }
    ]
  },
  {
    title: '3rd Party',
    items: [
      {
        name: 'Globenewswire',
        route: 'globenewswire'
      },
      {
        name: 'Algolia Analytics',
        route: 'algolia'
      }
    ]
  }
]

const createController = (serviceLocator) => {
  serviceLocator.hub.once('acl:load', () => {
    const menuBarView = new MenuBarView({
      el: $('.js-menu-bar'),
      menu: filterMenu(menu, serviceLocator),
      serviceLocator: serviceLocator
    })

    menuBarView.render()

    menuBarView.on('route', (route) => {
      serviceLocator.router.navigate(route, { trigger: true })
    })
  })
}

const filterMenu = (menu, serviceLocator) =>
  menu.filter((section) => {
    section.items = section.items.filter(
      (menuItem) =>
        (menuItem && serviceLocator.allowed(menuItem.resource, 'discover')) ||
        !menuItem.resource
    )
    return section.items.length > 0
  })

module.exports = createController
