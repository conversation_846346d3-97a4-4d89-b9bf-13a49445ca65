include state-map
include issue-map

.list-item.list-item-detail

  .image-wrapper
    i.image.image-detail(style='background-image: url("' + data.previewImageUrlSmall + '");')

  .list-item-header

    .list-item-actions

      mixin stateMap(data.schedule, data.visibility)

      if data.instanceLogoUrl
        span.instance-logo.js-tooltip-trigger(data-toggle='tooltip', title=data.__instance.name)
          img(src=data.instanceLogoUrl)

      if allowed('article', 'delete')
        label.list-item-select
          input.js-select(type='checkbox', name='name')
      .btn-group
        if data.hasSection
          button.btn.btn--small.js-preview(type='button') Preview
        else
          .tooltip__disabled-wrapper.js-tooltip-trigger(
            data-container='body'
            data-toggle='tooltip'
            title='Can’t be previewed without a section assigned')
            button.btn.btn--small.is-disabled(type='button') Preview

        if dropdownActions.length > 0
          a.btn.btn--small.dropdown-toggle(data-toggle='dropdown', href='#')
            span.caret
          ul.dropdown-menu.pull-right
            if dropdownActions.indexOf('duplicate') > -1
              li
                a.js-duplicate Duplicate
            if dropdownActions.indexOf('syndicate') > -1
              li
                a.js-syndicate Syndicate
            li
              a.js-show-revisions Show Revisions

    if allowed('article', 'update')
      h2: a.js-edit(href='/articles/#{data._id}/form')= data.headline
    else
      h2= data.headline

  .list-item-content
    .list-item-actions.list-item-actions--left
      dl
        dt Created:
        dd= format(data.dateCreated, 'calendar')
        dt Live:
        dd= data.liveDate ? format(data.liveDate, 'calendar') : 'Always'
        dt Expires:
        dd= data.expiryDate ? format(data.expiryDate, 'calendar') : 'Never'
        dt Content Type:
        dd= data.contentType ? data.contentType : 'Not set'
        dt Author:
        dd= data && data.__author && data.__author.name || data.legacyAuthorName || 'Not set'

      if hasSection
        div
          a(href=data.publicUrlPath, target='_blank')= data.__fullUrlPath
    .list-item-actions.list-item-actions--right
      each issue in data.__issues
        mixin stateMap(issue)
