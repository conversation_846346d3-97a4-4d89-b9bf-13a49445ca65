//-
//- LIST FILTERS
//- ============
//- Appears to the right of the list grid, and scrolls with the page.
//- Contains form to filter list items.
//-

.panel.panel-styled
  .panel-header
    h2 Filter
    if allowed('article', 'export')
      .panel-actions
        button.btn.js-export(type='button') Export

  .panel-content

    form(action='#', method='get')
      .form-row.form-row-full-width
        label
          span.form-label-text Search 
            .form-label-text__right
              span.js-tooltip-trigger.label(
                data-toggle='tooltip'
                title='Use double quotes to search for a phrase and minus to negate a word, e.g.<br />[ "Fashion Week" ] or [ Fashion -Week ]'
                )
                i.icon.icon--question Help

          input.control.control--text.form-field(type='text', name='keywords', placeholder='Keywords')

      .form-row.form-row-full-width
        label
          span.form-label-text Order
          select.control.control--choice.form-field(name='sort')
            option(value='-dateCreated') Newest First
            option(value='dateCreated') Oldest First
            option(value='-score') Relevance

      .form-row.form-row-full-width
        label
          span.form-label-text From Date
          span.datepicker
            input.control.control--text.form-field.js-date-time-picker(type='text', name='fromDate')

      .form-row.form-row-full-width
        label
          span.form-label-text To Date
          span.datepicker
            input.control.control--text.form-field.js-date-time-picker(type='text', name='toDate')

      .form-row.form-row-full-width
        label
          span.form-label-text Expiring Within
          select.control.control--choice.form-field(name='expiring')
            option(value='') Any Time
            option(value='1') 1 Day
            option(value='3') 3 Days
            option(value='5') 5 Days
            option(value='7') 7 Days

      .form-row.form-row-full-width
        label
          span.form-label-text State
          select.control.control--choice.form-field(name='state')
            option(value='') All States
            option(value='Draft') Draft
            option(value='Published') Published

      .form-row.form-row-full-width
        label
          span.form-label-text Section
          .form-field.js-section-select

      if instances && instances.length > 1
        .form-row.form-row-full-width
          label
            span.form-label-text Instance
            select.control.control--choice.form-field(name='instance')
              option(value='') All Instances
              each instance in instances
                option(value=instance._id)= instance.name

      if authors && authors.length > 1
        .form-row.form-row-full-width
          label
            span.form-label-text Author
            select.control.control--choice.form-field(name='author')
              option(value='') All authors
              each author in authors
                option(value=author._id)= author.name

      if contentTypes && contentTypes.length > 1
        .form-row.form-row-full-width
          label
            span.form-label-text Content Type
            select.control.control--choice.form-field(name='contentType')
              option(value='') All Content Types
              each contentType in contentTypes
                option(value=contentType.name)= contentType.name

      .form-row.form-row-full-width
        div
          span.form-label-text
            span.js-company-filter-link-text(style="font-weight: bold; color: red;") Unlinked
            span.js-company-filter-label=" Company Filter"
            .form-label-text__right
              .toggle
                input(type='checkbox', name='companyFilterToggle', id='companyFilterToggle')
                label.js-tooltip-trigger.label(
                  for='companyFilterToggle'
                  data-toggle='tooltip'
                  title="Finds articles with the selected company's naem within Headline or Sell. Default toggle option only shows articles that have no companies linked to them. Adjust the toggle to show all articles."
                  )
                  i.icon.icon--question Help
          .form-field.js-company-select

      .form-row.form-row-full-width
        div
          span.form-label-text
            span.js-executive-filter-link-text(style="font-weight: bold; color: red;") Unlinked
            span.js-executive-filter-label=" Executive Filter"
            .form-label-text__right
              .toggle
                input(type='checkbox', name='executiveFilterToggle', id='executiveFilterToggle')
                label.js-tooltip-trigger.label(
                  for='executiveFilterToggle'
                  data-toggle='tooltip'
                  title="Finds articles with the selected executive's name within Headline or Sell. Default toggle option only shows articles that have no executives linked to them. Adjust the toggle to show all articles."
                  )
                  i.icon.icon--question Help
          .form-field.js-executive-select

      .form-row.form-row-full-width
        div
          span.form-label-text
            span Featured (Company Portal)
            .form-label-text__right
              .toggle
                input(type='checkbox', name='featuredCompanyPortalFilterToggle', id='featuredCompanyPortalFilterToggle')
                label.js-tooltip-trigger.label(
                  for='featuredCompanyPortalFilterToggle'
                  data-toggle='tooltip'
                  title="Filters articles that have been marked as featured within the companies list"
                )
                  i.icon.icon--question Help
            
      .form-row.form-row-actions
        input.btn(type='reset', value='Clear')
        input.btn.btn--action(type='submit', value='Filter')
