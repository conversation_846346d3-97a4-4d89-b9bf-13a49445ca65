.panel.panel-styled(id='section-basic-details')
  .panel-header
    h2 Basic Details

  .panel-content

    .form-row(id='field--headline', data-field='headline')
      label
        span.form-label-text Headline
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='headline', value=article.headline, autofocus, maxLength='60')
      .js-error
      .form-row-description.form-copy
        p A full headline, limited to 60 characters.
      .form-row-description.form-copy
        p 
          span.js-headline-char-count(style="font-weight: bold;") #{article.headline ? article.headline.length : 0}/60
          |  characters

    .form-row(id='field--sell', data-field='sell')
      label
        span.form-label-text Standfirst
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='sell', value=article.sell, maxlength='155')
      .js-error
      .form-row-description.form-copy
        p Summary to use in search results and listings to entice the reader to view the full article. Limited to 155 characters.
      .form-row-description.form-copy
        p 
          span.js-sell-char-count(style="font-weight: bold;") #{article.sell ? article.sell.length : 0}/155
          |  characters

    hr

    .form-row(id='field--slug', data-field='slug')
      label
        span.form-label-text Slug
          abbr(title='This field is required') *
        if articleState === 'Published' || hasBeenPublished || isDuplicate
          input.control.control--text.form-field(type='text', name='slug', disabled, value=article.slug)
        else
          input.control.control--text.form-field(type='text', name='slug', value=article.slug)
      .js-error
      .form-row-description.form-copy
        p Makes up the URL for this article. WARNING: Changing this impacts linking and SEO!

    if isDuplicate
      .form-row(id='field--canonicalUrl', data-field='canonicalUrl')
        label
          span.form-label-text Canonical URL
          input.control.control--text.form-field(type='text', name='canonicalUrl', value=article.canonicalUrl)
        .js-error
