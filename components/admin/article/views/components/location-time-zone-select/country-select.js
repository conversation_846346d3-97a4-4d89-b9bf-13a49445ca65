const View = require('ventnor')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(
  join(__dirname, '/./templates/country-select.jade')
)

class CountrySelect extends View {
  constructor(serviceLocator, country) {
    super(serviceLocator)
    this.serviceLocator = serviceLocator
    this.country = country

    this.render()
    this.select = this.$el.find('.js-country-select')

    this.setSelected(country)
    this.setListeners()
  }

  setSelected(country) {
    this.$el.find('option[value=' + country + ']').attr('selected', true)
  }

  // NOTE: For some reason previous implementation stopped working but can still be found within Events working
  setListeners() {
    this.select.on('change', (e) => {
      this.emit('change', e.target.value)
    })
  }

  render() {
    this.$el.append(template({}))
    return this
  }
}

module.exports = CountrySelect
