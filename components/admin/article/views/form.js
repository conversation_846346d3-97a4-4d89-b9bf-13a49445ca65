const modal = require('modal')
const slug = require('slugg')
const join = require('path').join
const compileJade = require('browjadify-compile')
const BaseFormView = require('../../base/views/form')
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const ToolbarView = require('./form/toolbar')
const Suggestion = require('./form/suggestion')
const availableTagTypes = require('../../asset/views/tag-config.json')
const TagCheckbox = require('../../tag/views/tag-checkbox')
const TagSelect = require('../../tag/views/tag-select')
const InstanceSectionSelect = require('../../section/views/instance-section-select')
const WidgetAreaView = require('../../widget/views/widget-area')
const WidgetReferenceArea = require('../../event/views/components/widget-reference-area/form')
const AssetCollection = require('../../asset/collections/asset')
const AssetPickerView = require('../../asset/views/picker')
const getImageFactory = require('../../asset/lib/image-factory')()
const imageConfig = require('../../../service/article/image-config.json')
const dateTimePickers = require('../../../../admin/source/js/lib/init-date-time-pickers')
const CategorySelectView = require('../../instance/views/category-select')
const AuthorSelectView = require('../../author/views/select')
const InstanceSelect = require('../../instance/views/instance-select')
const RegionSelect = require('./region-select')
const ItemRepeater = require('../../widget/views/item-repeater')
const generateWidgetMetaFilter = require('../../event/views/components/widget-reference-area/lib/widget-meta-filter-generator')
const BaseSelect = require('../../base/views/select')
const contentTypes = require('../../../service/article/content-types.json')
const openRouterConfig = require('../../../service/open-router/config')
const DateTimePicker = require('anytime')
const CountrySelect = require('./components/location-time-zone-select/country-select')
const TimezoneSelect = require('./components/location-time-zone-select/timezone-select')
const DEFAULT_WRITER_MODEL = openRouterConfig.supportedModels[0].id

const contentTypeViews = {
  Interview: require('../content-types/interview/form'),
  'Company Report': require('../content-types/company-report/form'),
  Whitepaper: require('../content-types/whitepaper/form'),
  Video: require('../content-types/video/form'),
  Event: require('../content-types/event/form'),
  'BizClik Live Event': require('../content-types/bizclik-live-event/form')
}

const companyRepeaterConfig = {
  itemModel: require('../models/company'),
  itemView: require('./repeater/company/item'),
  formView: require('./repeater/company/form'),
  itemNames: {
    singular: 'Company',
    plural: 'Companies'
  }
}

const partnershipRepeaterConfig = {
  itemModel: require('../models/partnership'),
  itemView: require('./repeater/partnership/item'),
  formView: require('./repeater/partnership/form'),
  itemNames: {
    singular: 'Partnership',
    plural: 'Partnerships'
  }
}

const partnerRepeaterConfig = {
  itemModel: require('../models/company'),
  itemView: require('./repeater/company/item'),
  formView: require('./repeater/company/form'),
  itemNames: {
    singular: 'Partner',
    plural: 'Partners'
  }
}

const executiveRepeaterConfig = {
  itemModel: require('../models/executive'),
  itemView: require('./repeater/executive/item'),
  formView: require('./repeater/executive/form'),
  itemNames: {
    singular: 'Executive',
    plural: 'Executives'
  }
}

class FormView extends BaseFormView {
  constructor({ serviceLocator, model, account, isNew, saveOptions }) {
    super(serviceLocator, model, isNew)
    this.account = account
    this.saveOptions = saveOptions
    this.contentSubTypes = []
    this.dateTimePickers = []
    this.suggestionState = {
      type: 'back',
      // internalHasBeenTriggered: false,
      backHasBeenTriggered: false,
      manualHasBeenTriggered: false,
      manualSearchValue: null,
      selectedInstances: [],
      selectedContentTypes: [],
      selectedLimit: null,
      selectedDisplayDate: null
    }
    this.model.bodyOptions = this.model.bodyOptions || {}
    this.initDateTimePickers = dateTimePickers(
      window.config.locale.longDateFormat.LLLL,
      model
    )

    // Auto-save properties
    this.autoSaveInterval = null
    this.autoSaveEnabled = true
    this.autoSaveIntervalMs = 30000 // 30 seconds
    this.lastAutoSaveTime = null
    this.isAutoSaving = false
    this.typingTimer = null
    this.typingDelay = 2000 // 2 seconds after stopping typing
    this.minSaveGapMs = 10000 // 10 seconds minimum gap between saves
    this.lastSaveSnapshot = null // Snapshot of model at last save

    this.on('remove', () => {
      this.dateTimePickers.forEach((picker) => picker.destroy())
      this.cleanupAutoSave()
    })

    serviceLocator.logger.info('model', model)

    this.setUpTagControls()
    this.setupRegionControls()
    this.setupInstanceControls()

    this.$el.on(
      'change',
      '.js-content-sub-type-switcher',
      this.handleContentSubTypeChange.bind(this)
    )

    this.$el.on(
      'change',
      '.js-content-type-switcher',
      this.handleContentTypeChange.bind(this)
    )

    this.$el.on('click', '.js-images-add', this.handleAddImages.bind(this))
    this.$el.on(
      'click',
      '.js-syndicate-body',
      this.handleSyndicateBody.bind(this)
    )
    this.$el.on('click', '.js-clear-body', this.handleClearBody.bind(this))

    this.$el.on('click', '.js-shuffle-body', this.shuffleBody.bind(this))

    this.$el.on(
      'click',
      '.js-syndicate-body-restore-original',
      this.restoreOriginalBody.bind(this)
    )

    this.$el.on(
      'change',
      '[name=headline]',
      this.generateHeadlineBasedProperties.bind(this)
    )
    this.on('afterAppend', () =>
      this.$el.find('input[type=text]:first').focus()
    )

    this.partnerRepeater = new ItemRepeater(
      serviceLocator,
      partnerRepeaterConfig,
      this.model.get('partners') || [],
      'Add Partner'
    )

    this.partnerRepeater.on('itemsUpdated', () => {
      this.model.set('partners', this.partnerRepeater.getItems())
    })

    this.companyRepeater = new ItemRepeater(
      serviceLocator,
      companyRepeaterConfig,
      this.model.get('companies'),
      'Add Company'
    )

    this.companyRepeater.on('itemsUpdated', () => {
      this.model.set('companies', this.companyRepeater.getItems())
    })

    this.partnershipRepeater = new ItemRepeater(
      serviceLocator,
      partnershipRepeaterConfig,
      this.model.get('partnerships') || [],
      'Add Partnership'
    )

    this.partnershipRepeater.on('itemsUpdated', () => {
      this.model.set('partnerships', this.partnershipRepeater.getItems())
    })

    this.executiveRepeater = new ItemRepeater(
      serviceLocator,
      executiveRepeaterConfig,
      this.model.get('executives'),
      'Add Executive'
    )

    this.executiveRepeater.on('itemsUpdated', () => {
      this.model.set('executives', this.executiveRepeater.getItems())
    })

    this.$el.on(
      'input',
      '[name=headline]',
      this.handleCharCountChange.bind(this, 'headline', 60)
    )
    this.$el.on(
      'input',
      '[name=sell]',
      this.handleCharCountChange.bind(this, 'sell', 155)
    )
    // TODO
    this.$el.on('click', '.js-btn-backup', this.handleBackup.bind(this))
    this.$el.on(
      'click',
      '.js-btn-open-revisions',
      this.handleOpenRevisions.bind(this)
    )
    // TODO
    this.$el.on(
      'click',
      '.js-btn-restore-backup',
      this.handleRestoreBackup.bind(this)
    )
    this.$el.on(
      'click',
      '.js-type--back',
      this.handleSuggestionTypeChange.bind(this, 'back')
    )
    this.$el.on(
      'click',
      '.js-type--manual',
      this.handleSuggestionTypeChange.bind(this, 'manual')
    )
    this.$el.on(
      'click',
      '.js-search-button',
      this.triggerSuggestionSearch.bind(this)
    )

    // Setup auto-save after all event handlers are bound
    this.setupAutoSave()

    // Add debug method to window for testing (development only)
    if (window.location.hostname.includes('localhost')) {
      window.testAutoSave = () => {
        this.serviceLocator.logger.info('Manual auto-save test triggered')
        this.performAutoSave()
      }
      window.autoSaveStatus = () => {
        return {
          enabled: this.autoSaveEnabled,
          hasId: !!this.model.get('_id'),
          state: this.model.get('state'),
          lastSaveTime: this.lastAutoSaveTime,
          isAutoSaving: this.isAutoSaving,
          hasUnsavedChanges: this.hasUnsavedChanges(),
          shouldAutoSave: this.shouldAutoSave()
        }
      }
    }
  }

  preRenderSetup() {
    this.partnerRepeater = new ItemRepeater(
      this.serviceLocator,
      partnerRepeaterConfig,
      this.model.get('partners') || [],
      'Add Partner'
    )

    this.partnerRepeater.on('itemsUpdated', () => {
      this.model.set('partners', this.partnerRepeater.getItems())
    })

    this.companyRepeater = new ItemRepeater(
      this.serviceLocator,
      companyRepeaterConfig,
      this.model.get('companies'),
      'Add Company'
    )

    this.companyRepeater.on('itemsUpdated', () => {
      this.model.set('companies', this.companyRepeater.getItems())
    })

    this.partnershipRepeater = new ItemRepeater(
      this.serviceLocator,
      partnershipRepeaterConfig,
      this.model.get('partnerships') || [],
      'Add Partnetship'
    )

    this.partnershipRepeater.on('itemsUpdated', () => {
      this.model.set('partnerships', this.partnershipRepeater.getItems())
    })

    this.executiveRepeater = new ItemRepeater(
      this.serviceLocator,
      executiveRepeaterConfig,
      this.model.get('executives'),
      'Add Executive'
    )

    this.executiveRepeater.on('itemsUpdated', () => {
      this.model.set('executives', this.executiveRepeater.getItems())
    })

    // remove event handlers
    this.$el.off('change', '.js-content-sub-type-switcher')
    this.$el.off('change', '.js-content-type-switcher')
    this.$el.off('click', '.js-images-add')
    this.$el.off('click', '.js-syndicate-body')
    this.$el.off('click', '.js-clear-body')
    this.$el.off('click', '.js-shuffle-body')
    this.$el.off('click', '.js-syndicate-body-restore-original')
    this.$el.off('change', '[name=headline]')
    this.$el.off('input', '[name=headline]')
    this.$el.off('input', '[name=sell]')
    this.$el.off('click', '.js-btn-backup')
    this.$el.off('click', '.js-btn-open-revisions')
    this.$el.off('click', '.js-btn-restore-backup')
    this.$el.off('click', '.js-type--back')
    this.$el.off('click', '.js-type--manual')
    this.$el.off('click', '.js-search-button')

    // call setupToolbar bound to ToolbarView view
    this.setupToolbar()
  }

  // all initial setup which will need to be redone after render
  postRenderSetup() {
    this.setUpTagControls()
    this.setupRegionControls()
    this.setupInstanceControls()

    this.$el.on(
      'change',
      '.js-content-sub-type-switcher',
      this.handleContentSubTypeChange.bind(this)
    )

    this.$el.on(
      'change',
      '.js-content-type-switcher',
      this.handleContentTypeChange.bind(this)
    )

    this.$el.on('click', '.js-images-add', this.handleAddImages.bind(this))
    this.$el.on(
      'click',
      '.js-syndicate-body',
      this.handleSyndicateBody.bind(this)
    )
    this.$el.on('click', '.js-clear-body', this.handleClearBody.bind(this))

    this.$el.on('click', '.js-shuffle-body', this.shuffleBody.bind(this))

    this.$el.on(
      'click',
      '.js-syndicate-body-restore-original',
      this.restoreOriginalBody.bind(this)
    )

    this.$el.on(
      'change',
      '[name=headline]',
      this.generateHeadlineBasedProperties.bind(this)
    )
    this.on('afterAppend', () =>
      this.$el.find('input[type=text]:first').focus()
    )

    this.$el.on(
      'input',
      '[name=headline]',
      this.handleCharCountChange.bind(this, 'headline', 60)
    )
    this.$el.on(
      'input',
      '[name=sell]',
      this.handleCharCountChange.bind(this, 'sell', 155)
    )
    this.$el.on('click', '.js-btn-backup', this.handleBackup.bind(this))
    this.$el.on(
      'click',
      '.js-btn-open-revisions',
      this.handleOpenRevisions.bind(this)
    )
    this.$el.on(
      'click',
      '.js-btn-restore-backup',
      this.handleRestoreBackup.bind(this)
    )
    this.$el.on(
      'click',
      '.js-type--back',
      this.handleSuggestionTypeChange.bind(this, 'back')
    )
    this.$el.on(
      'click',
      '.js-type--manual',
      this.handleSuggestionTypeChange.bind(this, 'manual')
    )
    this.$el.on(
      'click',
      '.js-search-button',
      this.triggerSuggestionSearch.bind(this)
    )

    // Setup auto-save after all event handlers are bound
    this.setupAutoSave()

    // Add debug method to window for testing (development only)
    if (window.location.hostname.includes('localhost')) {
      window.testAutoSave = () => {
        this.serviceLocator.logger.info('Manual auto-save test triggered')
        this.performAutoSave()
      }
      window.autoSaveStatus = () => {
        return {
          enabled: this.autoSaveEnabled,
          hasId: !!this.model.get('_id'),
          state: this.model.get('state'),
          lastSaveTime: this.lastAutoSaveTime,
          isAutoSaving: this.isAutoSaving,
          hasUnsavedChanges: this.hasUnsavedChanges(),
          shouldAutoSave: this.shouldAutoSave()
        }
      }
    }
  }

  /*
   * Auto-save functionality
   */
  setupAutoSave() {
    if (!this.shouldEnableAutoSave()) {
      this.serviceLocator.logger.info('Auto-save not enabled:', {
        hasId: !!this.model.get('_id'),
        state: this.model.get('state'),
        isNew: this.isNew
      })
      return
    }

    this.serviceLocator.logger.info(
      'Setting up auto-save for article:',
      this.model.get('_id')
    )

    // Initialize snapshot if not already set
    if (!this.lastSaveSnapshot) {
      this.lastSaveSnapshot = JSON.stringify(this.model.toJSON())
      this.lastAutoSaveTime = new Date()
    }

    // Start auto-save timer
    this.autoSaveInterval = setInterval(() => {
      this.performAutoSave()
    }, this.autoSaveIntervalMs)

    // Setup typing detection to delay auto-save during active typing
    this.$el.on('input', 'input, textarea', this.handleTyping.bind(this))
    this.$el.on('change', 'select', this.handleFormChange.bind(this))

    // Add auto-save status indicator to the page
    this.addAutoSaveStatusIndicator()

    // Update status display every 5 seconds
    this.statusUpdateInterval = setInterval(() => {
      this.updateAutoSaveStatus()
      this.updateLastModifiedIndicator() // Also update last modified time
    }, 5000)
  }

  shouldEnableAutoSave() {
    // Only enable auto-save for existing draft articles
    return (
      this.model.get('_id') &&
      this.model.get('state') === 'Draft' &&
      this.serviceLocator.allow('article', 'experimental')
    )
  }

  // TODO: Remove at some point
  handleSyndicationNewsPopup() {
    const now = new Date()
    const cutoff = new Date(now.getFullYear(), 10, 1) // 1st Nov, 00:00
    if (now > cutoff) return

    const hasSeen = window.localStorage.getItem('hasSeenSyndicationPopup')
    if (hasSeen === 'true') return null

    this.showSyndicationModal()
    window.localStorage.setItem('hasSeenSyndicationPopup', true)
  }

  showSyndicationModal() {
    // Create modal overlay
    const modalOverlay = document.createElement('div')
    modalOverlay.className = 'syndication-modal-overlay'
    modalOverlay.innerHTML = `
      <div class="syndication-modal-container">
        <div class="syndication-modal-content">
          <div class="syndication-modal-header">
            <img src="/assets/img/content/librechat.png" alt="Rita Syndication Agent" class="syndication-hero-image" />
          </div>
          <div class="syndication-modal-body">
            <h2>Article Syndication Update</h2>
            <p><strong>The article syndication button has been removed.</strong></p>
            <p>Instead, you can now visit <strong>bizclik.ai</strong> and use the most advanced <span class="rita-highlight">Rita Syndication Agent</span> for all your content syndication needs.</p>
            <p>If you have any questions, get in touch with Libby or India and they'd be happy to assist 😊.</p>
          </div>
          <div class="syndication-modal-footer">
            <button class="syndication-got-it-btn" type="button">Got it!</button>
            <a href="https://ai.bizclikmedia.net/c/new" target="_blank" class="syndication-visit-btn">
              Visit Rita Syndication Agent
            </a>
          </div>
        </div>
      </div>
    `

    // Add to DOM
    document.body.appendChild(modalOverlay)

    // Add event listeners
    const gotItBtn = modalOverlay.querySelector('.syndication-got-it-btn')
    const closeModal = () => {
      modalOverlay.remove()
    }

    gotItBtn.addEventListener('click', closeModal)

    // Close on overlay click
    modalOverlay.addEventListener('click', (e) => {
      if (e.target === modalOverlay) {
        closeModal()
      }
    })

    // Close on escape key
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        closeModal()
        document.removeEventListener('keydown', handleEscape)
      }
    }
    document.addEventListener('keydown', handleEscape)
  }

  handleTyping() {
    // Clear existing typing timer
    if (this.typingTimer) {
      clearTimeout(this.typingTimer)
    }

    // Set new timer to trigger auto-save after user stops typing
    this.typingTimer = setTimeout(() => {
      this.performAutoSave()
    }, this.typingDelay)
  }

  handleFormChange() {
    // Trigger auto-save after form changes (dropdowns, checkboxes, etc.)
    setTimeout(() => {
      this.performAutoSave()
    }, 500) // Small delay to allow change to propagate
  }

  shouldAutoSave() {
    // Check all conditions for auto-save
    if (!this.autoSaveEnabled) return false
    if (this.isAutoSaving) return false
    if (!this.model.get('_id')) return false
    if (this.model.get('state') !== 'Draft') return false
    if (!this.hasUnsavedChanges()) return false

    // Check if enough time has passed since last save (10 second minimum gap)
    if (this.lastAutoSaveTime) {
      const timeSinceLastSave = Date.now() - this.lastAutoSaveTime.getTime()
      if (timeSinceLastSave < this.minSaveGapMs) {
        return false
      }
    }

    // Check if there are actual changes since last auto-save
    if (this.lastSaveSnapshot) {
      const currentSnapshot = JSON.stringify(this.model.toJSON())
      if (currentSnapshot === this.lastSaveSnapshot) {
        return false // No changes since last save
      }
    }

    return true
  }

  performAutoSave() {
    if (!this.shouldAutoSave()) {
      // Log why auto-save was skipped for debugging
      const reasons = []
      if (!this.autoSaveEnabled) reasons.push('disabled')
      if (this.isAutoSaving) reasons.push('already saving')
      if (!this.model.get('_id')) reasons.push('no _id')
      if (this.model.get('state') !== 'Draft') reasons.push('not draft')
      if (!this.hasUnsavedChanges()) reasons.push('no unsaved changes')

      if (this.lastAutoSaveTime) {
        const timeSinceLastSave = Date.now() - this.lastAutoSaveTime.getTime()
        if (timeSinceLastSave < this.minSaveGapMs) {
          reasons.push(
            `too soon (${Math.round(timeSinceLastSave / 1000)}s < 10s)`
          )
        }
      }

      if (this.lastSaveSnapshot) {
        const currentSnapshot = JSON.stringify(this.model.toJSON())
        if (currentSnapshot === this.lastSaveSnapshot) {
          reasons.push('no changes since last save')
        }
      }

      if (reasons.length > 0) {
        this.serviceLocator.logger.info(
          'Auto-save skipped:',
          reasons.join(', ')
        )
      }
      return
    }

    this.serviceLocator.logger.info('Performing auto-save')
    this.isAutoSaving = true
    this.showAutoSaveIndicator()

    // Emit auto-save event to be handled by the controller
    this.emit('autoSave')
  }

  showAutoSaveIndicator() {
    // Show a subtle indicator that auto-save is happening
    let indicator = this.$el.find('.js-auto-save-indicator')
    if (indicator.length === 0) {
      $('body').append(
        '<div class="js-auto-save-indicator auto-save-indicator auto-save-indicator--saving">Auto-saving</div>'
      )
      indicator = $('.js-auto-save-indicator')
    } else {
      indicator
        .removeClass('auto-save-indicator--error')
        .addClass('auto-save-indicator--saving')
        .text('Auto-saving')
        .show()
    }

    // Update status indicator
    const statusText = this.$el.find('.js-auto-save-status-text')
    if (statusText.length > 0) {
      statusText.text('Saving...')
    }
  }

  hideAutoSaveIndicator(success = true) {
    const indicator = $('.js-auto-save-indicator')
    if (indicator.length > 0) {
      if (success) {
        indicator
          .removeClass('auto-save-indicator--saving auto-save-indicator--error')
          .text('Auto-saved')
      } else {
        indicator
          .removeClass('auto-save-indicator--saving')
          .addClass('auto-save-indicator--error')
          .text('Auto-save failed')
      }

      setTimeout(
        () => {
          indicator.fadeOut()
        },
        success ? 2000 : 4000
      ) // Show errors longer
    }

    this.isAutoSaving = false

    if (success) {
      this.lastAutoSaveTime = new Date()
      // Capture snapshot of current model state for change detection
      this.lastSaveSnapshot = JSON.stringify(this.model.toJSON())

      // Update status indicator immediately
      this.updateAutoSaveStatus()

      // Update last modified indicator
      this.updateLastModifiedIndicator()
    }
  }

  addAutoSaveStatusIndicator() {
    // Add a persistent status indicator showing auto-save status
    if (this.$el.find('.js-auto-save-status').length === 0) {
      const statusHtml = `
        <div class="js-auto-save-status auto-save-status">
          <div class="auto-save-status__label">Auto-save:</div>
          <div class="js-auto-save-status-text auto-save-status__text">Enabled</div>
          <div class="js-auto-save-last-saved auto-save-status__time"></div>
        </div>
      `
      this.$el.find('.js-toolbar-root').append(statusHtml)
      this.updateAutoSaveStatus()
    }
  }

  updateAutoSaveStatus() {
    const statusText = this.$el.find('.js-auto-save-status-text')
    const lastSavedText = this.$el.find('.js-auto-save-last-saved')

    if (this.lastAutoSaveTime) {
      const now = new Date()
      const timeDiff = now - this.lastAutoSaveTime
      const minutes = Math.floor(timeDiff / 60000)
      const seconds = Math.floor((timeDiff % 60000) / 1000)

      let timeText = ''
      if (minutes > 0) {
        timeText = `${minutes}m ${seconds}s ago`
      } else {
        timeText = `${seconds}s ago`
      }

      statusText.text('Ready')
      lastSavedText.text(`Last saved: ${timeText}`)
    } else {
      statusText.text('Ready')
      lastSavedText.text('Not saved yet')
    }
  }

  cleanupAutoSave() {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
      this.autoSaveInterval = null
    }
    if (this.typingTimer) {
      clearTimeout(this.typingTimer)
      this.typingTimer = null
    }
    if (this.statusUpdateInterval) {
      clearInterval(this.statusUpdateInterval)
      this.statusUpdateInterval = null
    }
  }

  enableAutoSaveAfterFirstSave() {
    // Called after the first manual save to enable auto-save for new articles
    if (!this.autoSaveInterval && this.shouldEnableAutoSave()) {
      // Initialize snapshot and timing
      this.lastAutoSaveTime = new Date()
      this.lastSaveSnapshot = JSON.stringify(this.model.toJSON())
      this.setupAutoSave()
    }
  }

  addLastModifiedIndicator() {
    // Add last modified text under the page header
    const modifiedDate = this.model.get('modifiedDate')
    if (modifiedDate && !this.isNew) {
      const formattedDate = this.formatLastModified(new Date(modifiedDate))
      const lastModifiedHtml = `
        <div class="js-last-modified last-modified">
          <small class="last-modified__text">Last modified: <span class="js-last-modified-time">${formattedDate}</span></small>
        </div>
      `
      this.$el.find('.page-header h1').after(lastModifiedHtml)
    }
  }

  updateLastModifiedIndicator() {
    // Update the last modified time display
    const modifiedDate = this.model.get('modifiedDate')
    if (modifiedDate) {
      const formattedDate = this.formatLastModified(new Date(modifiedDate))
      const timeElement = this.$el.find('.js-last-modified-time')
      if (timeElement.length > 0) {
        timeElement.text(formattedDate)
      } else {
        // Add it if it doesn't exist yet
        this.addLastModifiedIndicator()
      }
    }
  }

  formatLastModified(date) {
    // Format the date in a user-friendly way
    const now = new Date()
    const diffMs = now - date
    const diffMinutes = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMs / 3600000)
    const diffDays = Math.floor(diffMs / 86400000)

    if (diffMinutes < 1) {
      return 'just now'
    } else if (diffMinutes < 60) {
      return `${diffMinutes} minute${diffMinutes === 1 ? '' : 's'} ago`
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`
    } else {
      // For older dates, show the actual date
      return (
        date.toLocaleDateString() +
        ' at ' +
        date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      )
    }
  }

  // TODO: Works but causes article not to save after backup - likely due to event listeners not being correctly added/removed
  handleBackup() {
    // Store article data in local storage with a timestamp
    const articleData = this.model.toJSON()
    const timestamp = new Date().toISOString()
    const backupKey = `article_backup_${this.model.get('_id')}_${timestamp}`
    localStorage.setItem(backupKey, JSON.stringify(articleData))
    this.serviceLocator.logger.info('Article backup created:', backupKey)
    // Download article data as a json file
    const filename = `${backupKey}.json`
    this.downloadArticleData(articleData, filename)
  }

  downloadArticleData(data, filename) {
    const dataStr =
      'data:text/json;charset=utf-8,' + encodeURIComponent(JSON.stringify(data))
    const downloadAnchorNode = document.createElement('a')
    downloadAnchorNode.setAttribute('href', dataStr)
    downloadAnchorNode.setAttribute('download', filename)
    document.body.appendChild(downloadAnchorNode) // required for firefox
    downloadAnchorNode.click()
    downloadAnchorNode.remove()
  }

  handleOpenRevisions() {
    // open /revisions in a new tab
    const w = window.open('')
    w.location = `/articles/${this.model.get('_id')}/revisions`
  }

  handleRestoreBackup() {
    // Must get list of backups for this article, by checking local storage for keys that start with `article_backup_${this.model.get('_id')}_`
    // Then display a modal with a list of backups, and a button to restore each one
    // When a backup is selected, restore the article data from local storage
    // Then confirm with a modal if the user wants to restore the backup
    // If yes, replace the current article data with the backup data
    // If no, do nothing

    const backupKeys = Object.keys(localStorage).filter((key) =>
      key.startsWith(`article_backup_${this.model.get('_id')}_`)
    )
    if (backupKeys.length === 0) {
      return this.serviceLocator.logger.info(
        'No backups found for this article'
      )
    }

    const backupData = backupKeys.map((key) => {
      const data = JSON.parse(localStorage.getItem(key))
      const timestamp = key.split('_').pop()
      const date = new Date(timestamp)
      return {
        key,
        data,
        timestamp,
        date
      }
    })

    // should automatically restore the latest backup
    const latestBackup = backupData.sort((a, b) => b.date - a.date)[0]
    const latestBackupData = latestBackup.data
    const latestBackupDate = latestBackup.date
    const latestBackupDateString =
      latestBackupDate.toLocaleDateString() +
      ' at ' +
      latestBackupDate.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
      })

    modal({
      title: 'Restore Backup',
      content: `Would you like to restore the latest backup? ${latestBackupDateString}`,
      buttons: [
        { text: 'Cancel', event: 'cancel', className: 'btn', keyCodes: [27] },
        { text: 'Restore', event: 'confirm', className: 'btn btn--action' }
      ]
    }).on('confirm', () => {
      this.model.set(latestBackupData)
      this.clearUnsavedChanges()
      this.clearErrors()
      this.preRenderSetup()
      this.render()
      this.postRenderSetup()
    })
  }

  handleCharCountChange(field, maxLength, e) {
    const value = $(e.currentTarget).val()
    this.$el
      .find('.js-' + field + '-char-count')
      .text(value.length + '/' + maxLength)
  }

  addContentSubTypeOptions(subTypes) {
    const selectedSubType = this.model.get('subContentType')

    const field = this.$el.find('.js-content-sub-type-switcher').empty()

    subTypes &&
      subTypes.forEach((type) => {
        const selected = selectedSubType === type
        field.append(
          $('<option></option>')
            .attr('value', type)
            .attr('selected', selected)
            .text(type)
        )
      })
  }

  setupRegionControls() {
    this.regionControls = []

    const regionControl = new RegionSelect(this.model.get('region'))
    this.regionControls.push(regionControl)

    this.regionControls.forEach((regionControl) => {
      regionControl.on('change', (value) => {
        this.model.set('region', value)
      })
    })
  }

  setupCategoryControls() {
    this.categoryControls = []

    const categoryControl = new CategorySelectView(
      this.serviceLocator,
      this.model.get('category'),
      this.model.get('instance')
    )
    this.categoryControls.push(categoryControl)

    this.categoryControls.forEach((categoryControl) => {
      categoryControl.on('change', (value) => {
        this.model.set('category', value)
      })
    })
  }

  resetCategoryControls() {
    $('.js-category-select').empty()

    this.setupCategoryControls()
    this.categoryControls &&
      this.categoryControls.forEach((categoryControl) => {
        this.$el
          .find('.js-category-select')
          .append(categoryControl.render().$el)
      })
  }

  resetSectionControls() {
    $('.js-instance-sections').empty()
    this.model.set('sections', [])
    this.renderSectionSelect()
  }

  showInstanceChangeModal(value) {
    const currentInstance = this.model.get('instance')
    modal({
      title: 'Change instance?',
      content:
        'Are you sure you want to change this? By doing so, any instance specific settings will be lost.',
      buttons: [
        { text: 'Confirm', event: 'confirm', className: 'btn btn--action' },
        { text: 'Cancel', event: 'cancel', className: 'btn' }
      ]
    })
      .on('confirm', this.handleInstanceChange.bind(this, value))
      .on('cancel', this.revertChange.bind(this, currentInstance))
  }

  revertChange(currentInstance) {
    this.instanceControls.forEach((instanceControl) => {
      instanceControl.el.selectize.addItem(currentInstance, true)
    })
  }

  handleInstanceChange(value) {
    this.model.set('instance', value)
    this.model.set('category', null)
    this.resetSectionControls()
    this.resetCategoryControls()
    if (value) {
      this.showOptions()
      this.renderMagazineIssuesLink(true)
    }
  }

  setupInstanceControls() {
    this.instanceControls = []

    const instanceControl = new InstanceSelect(
      this.serviceLocator,
      this.model.get('instance')
    )

    this.instanceControls.push(instanceControl)

    this.instanceControls.forEach((instanceControl) => {
      instanceControl.on('change', (value) => {
        if (this.model.get('instance')) {
          this.showInstanceChangeModal(value)
        } else {
          this.handleInstanceChange(value)
        }
      })
    })
  }

  showOptions() {
    if (!this.model.get('instance')) return
    this.$el.find('#field--contentType').show()
  }

  renderAuthorSelect() {
    this.authorSelect = new AuthorSelectView(
      this.serviceLocator,
      this.model.get('author')
    )

    this.authorSelect.on('change', (value) => {
      this.model.set('author', value)
    })

    this.$el
      .find('.js-author-select')
      .empty()
      .append(this.authorSelect.render().$el)
  }

  setContentSubTypes(type) {
    this.contentSubTypes =
      contentTypes && contentTypes.find((content) => content.type === type)

    const foundType = this.contentSubTypes
    const { subTypes, categorySelect } = foundType || {}

    if (subTypes) {
      this.addContentSubTypeOptions(subTypes)
      this.$el.find('#field--subContentType').show()
    } else {
      this.$el.find('#field--subContentType').hide()
      this.model.set('subContentType', 'Standard')
    }

    if (categorySelect) {
      this.$el.find('#field--category').show()
      this.resetCategoryControls()
    } else {
      this.$el.find('#field--category').hide()
    }
  }

  handleContentTypeChange(e) {
    e.preventDefault()
    const selectedType = $(e.currentTarget).val()
    this.model.set('contentType', selectedType)
    this.model.set('category', null)
    this.model.set('eventId', null)
    this.model.set('eventArticleCategoryKey', null)
    this.model.set('sections', null)
    this.resetSectionControls()
    this.setContentSubTypes(selectedType)
    this.renderContentTypeFields()
  }

  handleContentSubTypeChange(e) {
    e.preventDefault()
    const selectedType = e && e.target && e.target.value
    this.model.set('subContentType', selectedType)
  }

  /*
   * When a headline is created, guess some sensible starting
   * points for some other properties, eg. slug
   */
  generateHeadlineBasedProperties() {
    const headline = this.$el.find('[name=headline]').val()
    const $slug = this.$el.find('[name=slug]')
    if (!$slug.val()) $slug.val(slug(headline)).change()
  }

  /*
   * Create a toolbar view and bind to its events
   */
  setupToolbar() {
    // remove all event listeners from this.toolbar if it exists
    if (this.toolbar) {
      this.toolbar.remove()
    }
    this.toolbar = new ToolbarView(this.serviceLocator, this.model, this)
    this.toolbar.on('back', this.emit.bind(this, 'back'))
    this.toolbar.on('openEvent', this.emit.bind(this, 'openEvent', this.model))
    this.toolbar.on('save', this.handleSave.bind(this))
    this.toolbar.on('saveAndClose', this.emit.bind(this, 'saveAndClose'))
    this.toolbar.on('saveAndPreview', this.emit.bind(this, 'saveAndPreview'))
    this.toolbar.on('publish', this.emit.bind(this, 'publish'))
    this.toolbar.on('draft', this.emit.bind(this, 'draft'))
    this.toolbar.on('addCompany', this.addCompanyFromSmartActions.bind(this))
    this.toolbar.on(
      'addExecutive',
      this.addExecutiveFromSmartActions.bind(this)
    )
  }

  addCompanyFromSmartActions(company) {
    const currentCompanies = this.model.get('companies') || []
    const existingCompany = currentCompanies.find(
      (c) => c.company === company._id
    )
    if (existingCompany)
      return modal({
        title: 'Company already added',
        content: 'This company is already added to the article',
        buttons: [{ text: 'Dismiss', className: 'btn', keyCodes: [27] }]
      })
    currentCompanies.push({ company: company._id })
    this.model.set('companies', currentCompanies)
    this.companyRepeater = new ItemRepeater(
      this.serviceLocator,
      companyRepeaterConfig,
      currentCompanies,
      'Add Company'
    )
    this.companyRepeater.on('itemsUpdated', () => {
      this.model.set('companies', this.companyRepeater.getItems())
    })

    this.$el
      .find('.js-companies')
      .empty()
      .append(this.companyRepeater.render().$el)
  }

  addExecutiveFromSmartActions(executive) {
    const currentExecutives = this.model.get('executives') || []
    const existingExecutive = currentExecutives.find(
      (e) => e.executive === executive._id
    )
    if (existingExecutive)
      return modal({
        title: 'Executive already added',
        content: 'This executive is already added to the article',
        buttons: [{ text: 'Dismiss', className: 'btn', keyCodes: [27] }]
      })
    currentExecutives.push({ executive: executive._id })
    this.model.set('executives', currentExecutives)
    this.executiveRepeater = new ItemRepeater(
      this.serviceLocator,
      executiveRepeaterConfig,
      currentExecutives,
      'Add Executive'
    )
    this.executiveRepeater.on('itemsUpdated', () => {
      this.model.set('executives', this.executiveRepeater.getItems())
    })

    this.$el
      .find('.js-executives')
      .empty()
      .append(this.executiveRepeater.render().$el)
  }

  setUpTagControls() {
    this.tagControls = []

    availableTagTypes.forEach((tagType) => {
      let tagControl
      let readOnly
      switch (tagType.inputStyle) {
        case 'checkbox':
          tagControl = new TagCheckbox(
            this.serviceLocator,
            this.model.get('tags'),
            tagType.type
          )
          tagControl.loadTags()
          break
        case 'textbox':
          readOnly =
            tagType.readOnly || !this.serviceLocator.allowed('tag', 'create')
          tagControl = new TagSelect(
            this.serviceLocator,
            this.model.get('tags'),
            tagType.type,
            readOnly
          )
          tagControl.loadTags()
          break
      }
      tagControl.on('change', this.updateTags.bind(this))
      this.tagControls.push(tagControl)
      this.attachView(tagControl)
    })
  }

  /*
   * Any time a tag input changes, update the model
   */
  updateTags() {
    let tags = []
    this.tagControls.forEach((tagControl) => {
      tags = tags.concat(tagControl.tags.toJSON())
    })
    return this.model.set('tags', tags)
  }

  showMissingSectionError() {
    modal({
      title: 'Section is required',
      content: 'An article must have a section in order to be previewed',
      buttons: [{ text: 'Dismiss', className: 'btn' }]
    })
  }

  handleManageEvent(e) {
    e.preventDefault()
    this.emit('manageEvent', this.model.get('eventId'))
  }

  handleClearBody(e) {
    e.preventDefault()
    modal({
      title: 'Clear all widgets?',
      content:
        'Are you sure you want to clear all widgets from the body? This will also remove the selected author.',
      buttons: [
        { text: 'Cancel', event: 'cancel', className: 'btn', keyCodes: [27] },
        { text: 'Clear', event: 'confirm', className: 'btn btn--action' }
      ]
    }).on('confirm', () => {
      this.updateWidgetArea([])
      this.model.set('author', null)
      this.renderAuthorSelect()
    })
  }

  shuffleBody(e) {
    e.preventDefault()
    modal({
      title: 'Shuffle all widgets?',
      content:
        'This will push all the text widgets to the top and merge them into one widget',
      buttons: [
        { text: 'Cancel', event: 'cancel', className: 'btn', keyCodes: [27] },
        { text: 'Shuffle', event: 'confirm', className: 'btn btn--action' }
      ]
    }).on('confirm', () => {
      const widgets = this.model.body.widgets

      const textWidgetModels = widgets.filter(
        (widget) => widget.get('type') === 'text'
      )

      if (textWidgetModels.length < 2) {
        return modal({
          title: 'No need to shuffle',
          content:
            'This requires at least two text widgets to combine all text widgets into one',
          buttons: [{ text: 'Dismiss', className: 'btn' }]
        })
      }

      const shuffedWidgets = this.model.body.widgets
        .sort((a, b) => {
          if (a.get('type') === 'text' && b.get('type') !== 'text') return -1
          if (a.get('type') !== 'text' && b.get('type') === 'text') return 1
          return 0
        })
        .map((widget) => widget.toJSON())

      const textWidgets = shuffedWidgets.filter(
        (widget) => widget.type === 'text'
      )
      const combinedTextWidget = textWidgets.reduce(
        (acc, widget) => {
          acc.html += widget.html
          return acc
        },
        { ...textWidgets[0], html: '' }
      )

      const finalWidgets = [
        combinedTextWidget,
        ...shuffedWidgets.filter((widget) => widget.type !== 'text')
      ]

      this.updateWidgetArea(finalWidgets)
    })
  }

  restoreOriginalBody() {
    // Create a modal which asks to confirm
    modal({
      title: 'Restore original body?',
      content: 'Are you sure you want to restore the original body?',
      buttons: [
        { text: 'Cancel', event: 'cancel', className: 'btn', keyCodes: [27] },
        { text: 'Restore', event: 'confirm', className: 'btn btn--action' }
      ]
    }).on('confirm', () => {
      if (!this.restorableBodyWidgets) return
      this.bodyWidgetArea.remove()
      this.model.set('body', { widgets: this.restorableBodyWidgets })
      this.model.remakeBody()
      this.bodyWidgetArea = new WidgetAreaView({
        model: this.model.body,
        serviceLocator: this.serviceLocator,
        collection: new window.Backbone.Collection(
          this.serviceLocator.widgetFactories.get('articleBody').toArray()
        )
      })

      this.$el.find('.js-body').empty().append(this.bodyWidgetArea.$el)
      this.on('remove', () => {
        this.bodyWidgetArea.remove()
      })
      this.restorableBodyWidgets = null
      this.$el.find('.js-syndicate-body-restore-original').hide()
    })
  }

  setSyndicationControls(state) {
    switch (state) {
      case 'processing':
        this.$el.find('.js-syndicate-body').prop('disabled', true)
        this.$el.find('.js-syndicate-body').val('Syndicating...')
        this.$el
          .find('.js-syndicate-body-restore-original')
          .prop('disabled', true)
        this.$el.find('.js-body').addClass('disabled-div')
        this.$el.find('.js-body').addClass('loading')
        this.$el.find('.js-syndicate-instance-select').addClass('disabled-div')
        break
      case 'idle':
        this.$el.find('.js-syndicate-body').val('Syndicate Article Body')
        this.$el.find('.js-syndicate-body').prop('disabled', false)
        this.$el
          .find('.js-syndicate-body-restore-original')
          .prop('disabled', false)
        this.$el.find('.js-body').removeClass('disabled-div')
        this.$el.find('.js-body').removeClass('loading')
        this.$el
          .find('.js-syndicate-instance-select')
          .removeClass('disabled-div')
        break
      default:
        break
    }
  }

  getWriterModelId() {
    const writerModelObj = openRouterConfig.supportedModels.find((model) =>
      model.id.includes(this.model.get('instance'))
    )
    return writerModelObj ? writerModelObj.id : DEFAULT_WRITER_MODEL
  }

  handleSyndicateBody(e) {
    e.preventDefault()
    const instance = this.model.get('instance')
    this.restorableBodyWidgets = this.model.body.toJSON().widgets

    if (!instance) {
      modal({
        title: 'Instance is required',
        content:
          'An article must have an instance in order to syndicate the body',
        buttons: [{ text: 'Dismiss', className: 'btn' }]
      })
    } else {
      if (!this.model.bodyOptions.writerModel) {
        this.model.bodyOptions.writerModel = this.getWriterModelId()
        if (this.aiModelWriterSelect) {
          this.aiModelWriterSelect.setValue(this.model.bodyOptions.writerModel)
        }
      }

      this.setSyndicationControls('processing')
      const body = this.model.get('body')
      try {
        this.serviceLocator.articleService.syndicateArticleBody(
          {
            instance,
            widgets: body.widgets,
            article: {
              headline: this.model.get('headline'),
              standfirst: this.model.get('sell')
            },
            options: this.model.bodyOptions
          },
          (error, response) => {
            if (error) {
              modal({
                title: '😢 Something went wrong',
                content:
                  'Please try again, if the problem persists, contact support. Error message: ' +
                  JSON.stringify(error),
                buttons: [{ text: 'Dismiss', className: 'btn' }]
              })
              this.setSyndicationControls('idle')
              return this.serviceLocator.logger.error(error, response)
            }
            this.serviceLocator.logger.info('response.info', response.info)
            this.serviceLocator.logger.info(
              'response.widgets: ',
              response.widgets
            )
            this.updateWidgetArea(response.widgets)
            this.$el.find('.js-syndicate-body-restore-original').show()
            this.model.set('author', null)
            this.model.set('canonicalUrl', null)
            this.$el.find('input[name=canonicalUrl]').val('')

            const syndicationOuts = this.model.get('syndicationOuts') || []
            this.model.set(
              'syndicationOuts',
              syndicationOuts.concat({
                widgets: response.widgets,
                aiInstanceSelected: this.model.bodyOptions.instance,
                dateCreated: new Date().toDateString(),
                user: {
                  adminId: localStorage.getItem('apiId'),
                  firstName: localStorage.getItem('firstName')
                },
                info: response.info
              })
            )

            this.renderAuthorSelect()

            this.serviceLocator.logger.info(
              'syndication complete: ',
              this.model
            )

            this.$el.find('.js-syndicate-body').val('Syndication complete')
            modal({
              title: '✅ Syndication complete',
              content:
                'NOTE: Please review the body to ensure that the syndication was successful, ai generated content may not be perfect! \n 🆕 You can now restore your original article by clicking the restore button.',
              buttons: [{ text: 'Dismiss', className: 'btn' }]
            })
            setTimeout(() => {
              this.setSyndicationControls('idle')
            }, 3000)
          }
        )
      } catch (error) {
        this.setSyndicationControls('idle')
        return this.serviceLocator.logger.error(error)
      }
    }
  }

  handleSave() {
    // check path for /duplicate and if found set duplicatedFromArticle equal to id (which is before the /duplicate)
    if (window.location.pathname.includes('/duplicate')) {
      const splitPath = window.location.pathname.split('/')
      const duplicateStringIndex = splitPath.indexOf('duplicate')
      this.model.id = splitPath[duplicateStringIndex - 1]
    }

    // Handle timezone conversion for Event articles
    if (this.model.get('contentType') === 'Event') {
      const startDate = this.model.get('startDate')
      const endDate = this.model.get('endDate')

      const startTimezoneOffset = startDate.getTimezoneOffset()
      const endTimezoneOffset = endDate.getTimezoneOffset()

      this.model.set(
        'startDateISOWithoutTZ',
        new Date(
          startDate.getTime() - startTimezoneOffset * (60 * 1000)
        ).toISOString()
      )

      this.model.set(
        'endDateISOWithoutTZ',
        new Date(
          endDate.getTime() - endTimezoneOffset * (60 * 1000)
        ).toISOString()
      )
    }

    // Clear any pending auto-save when manually saving
    if (this.typingTimer) {
      clearTimeout(this.typingTimer)
      this.typingTimer = null
    }

    // Update save timing and snapshot for manual saves
    this.lastAutoSaveTime = new Date()
    this.lastSaveSnapshot = JSON.stringify(this.model.toJSON())

    // Update last modified indicator for manual saves too
    setTimeout(() => {
      this.updateLastModifiedIndicator()
    }, 100) // Small delay to ensure model is updated

    this.emit('save')
    this.$el.find('.js-btn-save').attr('disabled', true)
    setTimeout(
      () => this.$el.find('.js-btn-save').attr('disabled', false),
      this.saveOptions.timeout
    )
  }

  handleAddImages(e) {
    e.preventDefault()

    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      type: 'image',
      serviceLocator: this.serviceLocator
    })

    collection.getByType('image')

    modal({
      title: 'Article Images',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        { text: 'Cancel', event: 'Cancel', keyCodes: [27], className: 'btn' },
        {
          text: 'Add selected images',
          event: 'add',
          className: 'btn btn--action',
          keyCodes: [13]
        }
      ]
    })
      .on('add', this.addImages.bind(this, assetListView.selectedCollection))
      .on('close', assetListView.remove.bind(assetListView))
  }

  addImages(images) {
    const ImageModel = getImageFactory('image').model
    images.map((model) => {
      const image = new ImageModel(model.attributes)
      this.model.images.add(image)
      image.setDefaultCrops(imageConfig.crops)
      return image
    })
  }

  renderInstanceControls() {
    this.instanceControls &&
      this.instanceControls.forEach((instanceControl) => {
        this.$el.find('.js-instances').append(instanceControl.render().$el)
      })
  }

  renderWidgetReferences() {
    const widgetReferenceArea = new WidgetReferenceArea(
      this.serviceLocator,
      this.model,
      {
        imageConfig,
        filter: generateWidgetMetaFilter(this.model, 'article')
      }
    )
    this.$el
      .find('.js-widget-reference-area')
      .append(widgetReferenceArea.render().$el)
  }

  getBodyText(cb) {
    this.serviceLocator.articleService.getPlainTextBody(
      { articleBody: this.model.body.toJSON() },
      (err, res) => {
        if (err) return this.serviceLocator.error(err)
        cb(null, res)
      }
    )
  }

  renderSyndicationInstanceSelect() {
    const initialArticleInstance = this.model.get('instance')
    this.model.bodyOptions = this.model.bodyOptions || {
      instance: initialArticleInstance
    }
    const select = new InstanceSelect(
      this.serviceLocator,
      initialArticleInstance
    )
    this.$el.find('.js-syndicate-instance-select').append(select.render().$el)
    select.on('change', (instance) => {
      this.model.bodyOptions.instance = instance
    })
  }

  triggerSuggestionSearch() {
    this.serviceLocator.logger.info(
      'this.suggestionState: ',
      this.suggestionState
    )
    switch (this.suggestionState.type) {
      // case 'internal':
      //   this.renderInternalSuggestions()
      // break
      case 'back':
        this.renderBackSuggestions()
        break
      case 'manual':
        this.renderManualSuggestions()
        break
    }
  }

  renderSuggestionTitle(title, tip) {
    this.$el.find('.suggestions-title').html(title)
    this.$el.find('.suggestions-tip').html(tip)
  }

  determineIfBackLinkSuggestionsCanBeTriggered() {
    if (this.model.get('canonicalUrl')) {
      this.$el
        .find('.js-list--back')
        .html(
          'Cannot perform this task on a duplicate article (becuase it contains a Canonical URL), please find the original'
        )
      this.$el.find('.js-search-button--back').addClass('hidden')
    }
  }

  renderSuggestionView(type) {
    this.$el
      .find('.js-type')
      .toArray()
      .forEach((element) => {
        if (element.classList.contains(`js-type--${type}`)) {
          $(element).addClass('btn--success')
          $(element).attr('disabled', true)
          return
        }
        $(element).removeClass('btn--success')
        $(element).attr('disabled', false)
      })

    this.$el
      .find('.js-list, .js-search, .js-search-button')
      .toArray()
      .forEach((element) => {
        if (element.className.includes(`--${type}`)) {
          return $(element).removeClass('hidden')
        }
        return $(element).addClass('hidden')
      })
  }

  renderBackView() {
    this.renderSuggestionView('back')
    this.renderSuggestionTitle(
      '🤖 External Back Link Suggestions',
      `This will help boost the article's SEO by sending links from other domains Google deems as trustworthy. Backlinks therefore shouldn't ideally come from within the same site (this would be an internal link)`
    )
    this.determineIfBackLinkSuggestionsCanBeTriggered()
  }

  setupManualSearchInput() {
    this.$el.on('input', '.js-search-input--manual', (e) => {
      e.preventDefault()
      const value = e.target.value
      this.suggestionState.manualSearchValue = value
      if (!value.length)
        this.$el.find('.js-search-button--manual').prop('disabled', true)
      else this.$el.find('.js-search-button--manual').prop('disabled', false)
    })

    this.$el.on('keydown', '.js-search-input--manual', (e) => {
      if (e.which === 13) {
        e.preventDefault()
        e.stopPropagation()
        if (this.suggestionState.manualSearchValue) {
          this.triggerSuggestionSearch()
        }
      }
    })
  }

  renderManualView() {
    this.renderSuggestionView('manual')
    this.renderSuggestionTitle(
      '🚀 Internal Search',
      `This is an advanced search feature which hopes to return hyper relevant articles based on the content of the article you're currently writing`
    )
    this.setupManualSearchInput()
  }

  renderSearchFields() {
    // Render input multi select
    this.instanceSelect = new BaseSelect(
      this.serviceLocator,
      'instanceService',
      [],
      false,
      'Instance',
      'Instances',
      'name',
      'name'
    )
    this.$el
      .find('.js-ai-search--instances')
      .empty()
      .append(this.instanceSelect.render().$el)
    this.instanceSelect.on('change', (instances) => {
      this.suggestionState.selectedInstances = instances
    })

    // Render content type multi select
    this.$el.find('.js-ai-search--contentTypes').each((_, el) => {
      $(el).selectize({
        delimiter: ',',
        createOnBlur: false,
        placeholder: 'Choose one or more content types',
        create: false,
        options: contentTypes.map((t) => ({ text: t.type, value: t.type })),
        onInitialize: () =>
          el.selectize.on('change', () => {
            this.suggestionState.selectedContentTypes = el.selectize
              .getValue()
              .split(',')
              .filter(Boolean)
          })
      })
    })

    // Render limit field
    this.limitField = this.$el.find('.js-ai-search--limit')
    this.limitField.on('input', (e) => {
      e.preventDefault()
      const value = e.target.value
      if (value === '') {
        this.suggestionState.selectedLimit = null
        return
      } else if (value < 1 || value > 20) {
        this.limitField.val(this.suggestionState.selectedLimit)
        return
      }
      this.suggestionState.selectedLimit = e.target.value
    })

    this.dateFieldPicker = new DateTimePicker({
      input: this.$el.find('.js-ai-search--displayDate')[0],
      format: window.config.locale.longDateFormat.LLLL,
      initialDate: new Date()
    })
    this.dateFieldPicker.render()
    this.dateFieldPicker.on('change', (date) => {
      this.suggestionState.selectedDisplayDate = date
    })
  }

  handleSuggestionTypeChange(newType) {
    // this.$el.find('.js-trigger-suggestions').addClass('hidden')
    this.suggestionState.type = newType
    switch (newType) {
      // case 'internal':
      //   this.$el.find('.js-type-internal').addClass('btn--success')
      //   this.$el.find('.js-list-internal').removeClass('hidden')

      //   this.$el.find('.js-type--back').removeClass('btn--success')
      //   this.$el.find('.js-type--manual').removeClass('btn--success')

      //   this.$el.find('.js-list--back').addClass('hidden')
      //   this.$el.find('.js-list--manual').addClass('hidden')

      //   // Implement disabling

      //   this.$el.find('.js-search').addClass('hidden')

      //   this.$el.find('.suggestions-title').html('🤖 Internal Link Suggestions')
      //   this.$el
      //     .find('.suggestions-tip')
      //     .html(
      //       `This will help boost the SEO of the magazine site this article is being written on by helping the search engine crawl the site easier and have a better understanding of the domain's content`
      //     )
      //   break
      case 'back':
        this.renderBackView()
        break
      case 'manual':
        this.renderManualView()
        break
    }
  }

  renderSuggestionLoadingView(type) {
    this.$el.find(`.js-list--${type}`).empty()
    this.$el.find(`.js-list--${type}`).addClass('disabled-div loading')
    this.$el.find(`.js-search-button--${type}`).prop('disabled', true)
    switch (type) {
      case 'manual':
        this.$el.find('.js-search-input--manual').prop('disabled', true)
        break
    }
  }

  quitSuggestionsLoading(type) {
    switch (type) {
      case 'back':
        this.suggestionState.backHasBeenTriggered = false
        this.$el.find('.js-list--back').removeClass('disabled-div loading')
        this.$el.find('.js-search-button--back').prop('disabled', false)
        break
      case 'manual':
        this.suggestionState.manualHasBeenTriggered = false
        this.$el.find('.js-list--manual').removeClass('disabled-div loading')
        this.$el.find('.js-search-button--manual').prop('disabled', false)
        this.$el.find('.js-search-input--manual').prop('disabled', false)
        break
    }
  }

  // renderInternalSuggestions() {
  //   if (!this.suggestionState.internalHasBeenTriggered) {
  //     this.loadInternalSuggestions((err, res) => {
  //       if (err) return this.serviceLocator.logger.error(err)
  //       this.$el.find('.js-list-internal').removeClass('disabled-div loading')
  //       const articles = res.suggestions
  //       if (!articles.length)
  //         this.$el.find('.js-list-internal').html('No articles found')
  //       articles.forEach((a) => {
  //         const suggestionJson = JSON.parse(a.__suggestion)
  //         this.serviceLocator.logger.info(
  //           'suggestionJson (internal): ',
  //           suggestionJson
  //         )
  //         const suggestion = new Suggestion({
  //           article: { ...a, __suggestion: suggestionJson },
  //           type: 'internal',
  //           serviceLocator: this.serviceLocator
  //         })
  //         this.$el.find('.js-list-internal').append(suggestion.$el)
  //       })
  //     })
  //   }
  // }

  renderSuggestions(type, articles) {
    if (!articles.length) {
      this.$el.find(`.js-list--${type}`).html('No articles found')
      return
    }
    articles.forEach((a) => {
      const suggestionJson = JSON.parse(a.__suggestion)
      const suggestion = new Suggestion({
        article: { ...a, __suggestion: suggestionJson },
        type: type,
        serviceLocator: this.serviceLocator
      })
      this.$el.find(`.js-list--${type}`).append(suggestion.$el)
    })
  }

  renderBackSuggestions() {
    if (!this.suggestionState.backHasBeenTriggered) {
      this.loadBackSuggestions((err, res) => {
        if (err) {
          this.$el.find('.js-list--back').html('Error occured')
          this.quitSuggestionsLoading('back')
          return this.serviceLocator.logger.error(err)
        }
        this.quitSuggestionsLoading('back')
        this.renderSuggestions('back', res.suggestions)
      })
    }
  }

  renderManualSuggestions() {
    if (!this.suggestionState.manualHasBeenTriggered) {
      this.loadManualSuggestions((err, res) => {
        if (err) {
          this.$el.find('.js-list--manual').html('Error occured')
          this.quitSuggestionsLoading('manual')
          return this.serviceLocator.logger.error(err)
        }
        this.quitSuggestionsLoading('manual')
        this.renderSuggestions('manual', res.suggestions)
      })
    }
  }

  // loadInternalSuggestions(cb) {
  //   this.suggestionState.internalHasBeenTriggered = true
  //   this.getBodyText((err, plainTextBody) => {
  //     if (err) {
  //       this.suggestionState.internalHasBeenTriggered = false
  //       return this.serviceLocator.logger.error(err)
  //     }
  //     this.serviceLocator.articleService.findILS(
  //       {
  //         article: { ...this.model.toJSON(), textBody: plainTextBody }
  //       },
  //       (err, res) => {
  //         if (err) {
  //           this.suggestionState.internalHasBeenTriggered = false
  //           return this.serviceLocator.logger.error(err)
  //         }
  //         cb(null, res)
  //       }
  //     )
  //   })
  // }

  loadBackSuggestions(cb) {
    this.suggestionState.backHasBeenTriggered = true
    this.renderSuggestionLoadingView('back')
    this.getBodyText((err, plainTextBody) => {
      if (err) return cb(err)

      this.serviceLocator.articleService.findBLS(
        {
          article: { ...this.model.toJSON(), textBody: plainTextBody },
          instances: this.suggestionState.selectedInstances,
          contentTypes: this.suggestionState.selectedContentTypes,
          limit: this.suggestionState.selectedLimit,
          displayDate: this.suggestionState.selectedDisplayDate
        },
        (err, res) => {
          if (err) return cb(err)
          cb(null, res)
        }
      )
    })
  }

  loadManualSuggestions(cb) {
    this.suggestionState.manualHasBeenTriggered = true
    this.renderSuggestionLoadingView('manual')
    this.$el.find('.js-search--manual-input').prop('disabled', true)

    this.serviceLocator.articleService.findMS(
      {
        queryText: this.suggestionState.manualSearchValue,
        instances: this.suggestionState.selectedInstances,
        contentTypes: this.suggestionState.selectedContentTypes,
        limit: this.suggestionState.selectedLimit,
        displayDate: this.suggestionState.selectedDisplayDate
      },
      (err, res) => {
        if (err) return cb(err)
        cb(null, res)
      }
    )
  }

  // renderActionListView() method removed - now handled by smart actions toolbar

  render() {
    const { format } = this.serviceLocator
    const mappedContentTypes = contentTypes.map((content) => content.type)
    const article = this.model.toJSON()
    let siteUrl = null
    if (article.__instance && article.__fullUrlPath)
      siteUrl = `${article.__instance.__fullUrl}${article.__fullUrlPath}`
    this.$el.empty().append(
      template({
        title: this.isNew ? 'New Article' : 'Edit Article',
        article: this.model.toJSON(),
        account: this.account,
        availableTagTypes,
        contentTypes: mappedContentTypes,
        contentSubTypes: this.contentSubTypes,
        platforms: {},
        format,
        isDuplicate: this.model.isDuplicate,
        articleState: this.model.get('state'),
        hasBeenPublished: this.model.get('hasBeenPublished'),
        allowed: this.serviceLocator.allowed,
        siteUrl
      })
    )

    // Add last modified indicator after the title
    this.addLastModifiedIndicator()

    // Render the toolbar
    this.toolbar.render().$el.appendTo(this.$el.find('.js-toolbar-root'))

    this.dateTimePickers = this.$el
      .find('.js-date-time-picker')
      .toArray()
      .map(this.initDateTimePickers)

    this.renderInstanceControls()
    // this.renderSyndicationInstanceSelect()
    this.renderContentTypeFields()
    this.renderTagControls()
    this.renderWidgetArea()
    this.renderImageWidgetArea()
    this.renderSectionSelect()
    this.renderAuthorSelect()
    this.renderWidgetReferences()
    this.renderSearchFields()
    this.determineIfBackLinkSuggestionsCanBeTriggered()
    // this.renderAiModelSelects()

    this.setContentSubTypes(this.model.get('contentType'))

    this.handleSyndicationNewsPopup()

    if (this.model.get('instance')) {
      this.renderMagazineIssuesLink()
    }

    this.$el.find('.js-companies').append(this.companyRepeater.render().$el)
    this.$el
      .find('.js-partnerships')
      .append(this.partnershipRepeater.render().$el)
    this.$el.find('.js-partners').append(this.partnerRepeater.render().$el)
    this.$el.find('.js-executives').append(this.executiveRepeater.render().$el)

    // Render timezone controls for Event articles
    this.renderTimezoneControls()

    this.showOptions()

    if (!this.model.get('_id')) {
      this.$el.find('#suggestions').css('pointer-events', 'none')
      this.$el.find('#suggestions').css('cursor', 'not-allowed')
      this.$el.find('#suggestions').css('opacity', 0.25)
      this.$el.find('.suggestions-label').css('display', 'inline-block')
    }

    return this
  }

  renderContentTypeFields() {
    const contentType = this.model.get('contentType')
    const ContentTypeForm = contentTypeViews[contentType]
    this.$el.find('.js-content-type-fields').empty()
    if (contentType && contentType !== 'BizClik Live Event')
      this.$el.find('#field--sections').show()
    else this.$el.find('#field--sections').hide()

    // Show/hide timezone fields for Event content type
    if (contentType === 'Event') {
      this.$el.find('.js-panel[data-panel="location-and-time"]').show()
      this.renderTimezoneControls()
    } else {
      this.$el.find('.js-panel[data-panel="location-and-time"]').hide()
    }

    if (this.contentTypeView) this.contentTypeView.remove()
    if (!ContentTypeForm) return
    const contentTypeForm = new ContentTypeForm(
      this.serviceLocator,
      this.model,
      this.addImages
    )
    this.attachView(contentTypeForm)
    this.contentTypeView = contentTypeForm
    this.$el
      .find('.js-content-type-fields')
      .append(contentTypeForm.render().$el)
  }

  renderTimezoneControls() {
    // Runs after current stack is empty, a little hack to deal with .show not displaying dom elements instantanously
    setTimeout(() => {
      this.renderCountrySelect()
      this.renderTimezoneSelect(
        this.model.get('country'),
        this.model.get('timezone')
      )
    }, 0)
  }

  renderCountrySelect() {
    this.countrySelect = new CountrySelect(
      this.serviceLocator,
      this.model.get('country')
    )
    this.$el.find('.js-country-select').empty().append(this.countrySelect.$el)
    this.countrySelect.on('change', (country) => {
      this.model.set('country', country)
      this.renderTimezoneSelect(country, null)
    })
  }

  renderTimezoneSelect(country, timezone) {
    this.timezoneSelect = new TimezoneSelect(
      this.serviceLocator,
      country,
      timezone
    )
    this.$el.find('.js-timezone-select').empty().append(this.timezoneSelect.$el)
    this.timezoneSelect.on('change', (tz) => {
      this.model.set('timezone', tz)
    })
  }

  renderMagazineIssuesLink(reset) {
    const MagazineIssuesForm = require('../magazine-issues/form')
    this.$el.find('.js-magazine-issues').empty()
    if (this.magazineIssues) this.magazineIssues.remove()
    if (!MagazineIssuesForm) return
    if (reset) {
      this.model.set('issue', null)
      this.model.set('pageNumber', null)
    }
    const magazineIssuesForm = new MagazineIssuesForm(
      this.serviceLocator,
      this.model
    )
    this.attachView(magazineIssuesForm)
    this.magazineIssuesForm = magazineIssuesForm
    this.$el.find('.js-magazine-issues').append(magazineIssuesForm.render().$el)
  }

  updateSectionSelectView() {
    this.$el.find('.js-section--instance').show()
  }

  renderSectionSelect() {
    const instanceSectionSelect = new InstanceSectionSelect(
      this.serviceLocator,
      this.model.get('sections'),
      this.model.get('instance')
    )

    this.$el
      .find('.js-instance-sections')
      .append(instanceSectionSelect.render().$el)
    instanceSectionSelect.on('change', (sections) => {
      this.model.set('sections', sections)
      this.updateLayoutOptions()
    })
    this.attachView(instanceSectionSelect)

    if (this.model.get('sections').length) {
      const query = { _id: { $in: [this.model.get('sections')[0]] } }
      this.serviceLocator.sectionService.find(
        '',
        query,
        [],
        { page: 1, pageSize: 1 },
        (err, res) => {
          if (err)
            return this.serviceLocator.logger.error(
              err,
              'Cannot get current sections'
            )
          if (!res.results.length) return
          this.updateSectionSelectView()
        }
      )
    }
    this.updateLayoutOptions()

    this.on('platformChange', (platform) => {
      const { platforms } = this.serviceLocator.config
      const { defaultSection } = platforms[platform]

      if (!defaultSection) return

      this.serviceLocator.sectionService.find(
        '',
        { fullUrlPath: defaultSection },
        [],
        { page: 1, pageSize: 1 },
        (err, res) => {
          if (err)
            return this.serviceLocator.logger.error(
              err,
              'Cannot get default section for platform',
              platform
            )
          if (!res.results.length) return
          const [section] = res.results
          instanceSectionSelect.setSelected(section)
          this.updateLayoutOptions()
        }
      )
    })
  }

  renderTagControls() {
    this.tagControls.forEach((tagControl) => {
      this.$el
        .find('.js-tags-' + tagControl.tagType.toLowerCase())
        .append(tagControl.render().$el)
    })
  }

  renderWidgetArea() {
    this.bodyWidgetArea = new WidgetAreaView({
      model: this.model.body,
      serviceLocator: this.serviceLocator,
      collection: new window.Backbone.Collection(
        this.serviceLocator.widgetFactories.get('articleBody').toArray()
      )
    })

    this.$el.find('.js-body').append(this.bodyWidgetArea.$el)
    this.on('remove', () => {
      this.bodyWidgetArea.remove()
    })
  }

  updateWidgetArea(widgets) {
    const oldBody = this.model.get('body')
    this.model.set('body', { ...oldBody, widgets })
    this.model.remakeBody()
    this.bodyWidgetArea = new WidgetAreaView({
      model: this.model.body,
      serviceLocator: this.serviceLocator,
      collection: new window.Backbone.Collection(
        this.serviceLocator.widgetFactories.get('articleBody').toArray()
      )
    })
    this.$el.find('.js-body').empty().append(this.bodyWidgetArea.$el)
    this.on('remove', () => {
      this.bodyWidgetArea.remove()
    })
  }

  renderImageWidgetArea() {
    this.imageArea = new WidgetAreaView({
      model: this.model.images,
      receiveDrops: false,
      serviceLocator: this.serviceLocator,
      widgetItemOptions: imageConfig
    })
    this.$el.find('.js-image-area').append(this.imageArea.$el)
  }

  renderAiModelSelects() {
    this.$el.find('.js-ai-model-select--writer').empty()

    this.aiModelWriterSelect = this.$el
      .find('.js-ai-model-select--writer')
      .selectize({
        maxItems: 1,
        valueField: 'id',
        labelField: 'name',
        searchField: 'name',
        allowEmptyOption: false,
        options: openRouterConfig.supportedModels
          .map((model) => {
            if (model.traits.reasoning) return null
            return { id: model.id, name: model.name }
          })
          .filter(Boolean),
        create: false
      })[0].selectize
    this.aiModelWriterSelect.on('change', (value) => {
      this.model.bodyOptions.writerModel = value
    })

    this.aiModelWriterSelect.setValue(this.getWriterModelId())
    this.$el.find('.js-ai-model-select--editor').empty()

    this.aiModelEditorSelect = this.$el
      .find('.js-ai-model-select--editor')
      .selectize({
        maxItems: 1,
        valueField: 'id',
        labelField: 'name',
        searchField: 'name',
        options: openRouterConfig.supportedModels
          .map((model) => {
            // if (!model.traits.reasoning) return null
            return { id: model.id, name: model.name }
          })
          .filter(Boolean),
        create: false
      })[0].selectize
    this.aiModelEditorSelect.on('change', (value) => {
      this.model.bodyOptions.editorModel = value
    })
    // this.aiModelEditorSelect.setValue(DEFAULT_EDITOR_MODEL)
  }

  updateLayoutOptions() {
    const $layoutSelect = this.$el.find('.js-layout-select')
    const currentSectionId = this.model.get('sections')[0]

    $layoutSelect.empty()
    $('<option/>', { value: '' })
      .text('Default article layout')
      .appendTo($layoutSelect)

    if (currentSectionId) {
      this.serviceLocator.sectionService.read(
        currentSectionId,
        (err, section) => {
          if (err)
            return this.serviceLocator.logger.error(
              err,
              'Cannot find existing section'
            )

          const layouts = section.layouts
          if (layouts.length === 1) {
            this.$el.find('.js-layout-select-panel').addClass('hidden')
          } else {
            this.$el.find('.js-layout-select-panel').removeClass('hidden')
          }
          Object.keys(layouts).forEach((key) => {
            if (key === 'article' || key === 'section') return

            const option = $('<option/>')
              .attr('value', layouts[key].key)
              .text(
                layouts[key].name +
                  (layouts[key].description
                    ? ' - ' + layouts[key].description
                    : '')
              )
              .appendTo($layoutSelect)

            if (layouts[key].key === this.model.get('layout'))
              option.attr('selected', true)
          })
        }
      )
    }
  }
}

module.exports = FormView

/**
 * Backup / Save Locally
 *
 * 1. Click button save arrow, Save Locally (Backup)
 * 2. Exports a JSON object and also saves to local storage
 * 3. On the left next to Jump To they'll be a Actions dropdown. Under there is a Restore
 * 4. If an article is entered that is stored within local storage, popup should come up asking if you would like to restore
 * 5. Upload JSON file, populates the form
 * *
 *
 *
 *
 */

/**

 */
