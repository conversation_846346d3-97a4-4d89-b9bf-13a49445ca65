const qs = require('querystring')
const BaseListItemView = require('../../base/views/list')
const modal = require('modal')
const FilterView = require('./list-filter')
const ListItemView = require('./list-item')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/list.jade'))
const bulkAddEntityTemplate = compileJade(
  join(__dirname, '/../templates/list-entity-modal.jade')
)
const CompanySelect = require('../../company/views/company-select')
const ExecutiveSelectMulti = require('../../executives/views/executive-select-multi')
const tagTypes = require('../../asset/views/tag-config.json')
const createListTagDelegate = require('../../asset/lib/list-tag-delegate')
const createSignedApiUrl = require('../../../../admin/source/js/lib/signed-api-url-creator')
const widgetDeleteDelegate = require('../../event/views/components/widget-reference-area/lib/delete-delegate')

class ListView extends BaseListItemView {
  constructor(
    collection,
    paginationModel,
    serviceLocator,
    instances,
    authors,
    contentTypes
  ) {
    super(...arguments)
    this.instances = instances
    this.authors = authors
    this.contentTypes = contentTypes
    this.$el.on('click', '.js-tag', this.handleTag.bind(this))
    if (this.serviceLocator.allowed('article', 'experimental')) {
      this.$el.on(
        'click',
        '.js-attach-entities',
        this.handleAttachEntities.bind(this)
      )
    }
  }

  get name() {
    return { singular: 'Article', plural: 'Articles', resource: 'article' }
  }

  get ListItemView() {
    return ListItemView
  }

  get template() {
    return template
  }

  get FilterView() {
    return FilterView
  }

  createFilterView() {
    super.createFilterView()
    this.filters.on('export', this.handleExport.bind(this))
  }

  addListItem(model) {
    var listItem = new this.ListItemView(this.serviceLocator, model)
    this.listenTo(listItem, 'edit', this.emit.bind(this, 'edit', model.id))
    this.listenTo(listItem, 'preview', this.emit.bind(this, 'preview', model))
    this.listenTo(
      listItem,
      'datePreview',
      this.emit.bind(this, 'datePreview', model)
    )
    this.listenTo(
      listItem,
      'duplicate',
      this.emit.bind(this, 'duplicate', model)
    )
    this.listenTo(
      listItem,
      'syndicate',
      this.emit.bind(this, 'syndicate', model)
    )
    this.listenTo(
      listItem,
      'showRevisions',
      this.emit.bind(this, 'showRevisions', model)
    )
    this.attachView(listItem)
    this.$el.find('.js-items').append(listItem.render().$el)
  }

  handleDelete() {
    widgetDeleteDelegate(
      this.serviceLocator,
      this.selectedCollection.models,
      'article',
      () => {
        const length = this.selectedCollection.models.length
        if (!length) return
        modal({
          title: 'Delete',
          content:
            'Are you sure you want to delete ' +
            length +
            ' ' +
            (length === 1 ? 'item' : ' items') +
            '?',
          buttons: [
            {
              text: 'Cancel',
              event: 'cancel',
              className: 'btn',
              keyCodes: [27]
            },
            { text: 'Delete', event: 'confirm', className: 'btn btn--action' }
          ]
        }).on('confirm', () => {
          this.emit(
            'delete',
            this.selectedCollection.models.map((model) => model.id)
          )
        })
      }
    )
  }

  handleTag() {
    const addTags = createListTagDelegate(this.serviceLocator, tagTypes)
    const saveArticle = (model) => {
      this.serviceLocator.articleService.update(
        model.id,
        model.toJSON(),
        (err) => {
          if (err) this.serviceLocator.logger.error(err)
        }
      )
    }
    addTags(this.selectedCollection, saveArticle, this.selectNone.bind(this))
  }

  handleAttachEntities() {
    if (this.selectedCollection.length === 0) return
    const $el = $(bulkAddEntityTemplate({}))
    this.selectedBulkCompanies = []
    this.selectedBulkExecutives = []

    // Setup company select
    const companySelect = new CompanySelect(this.serviceLocator, null, {
      filter: { $and: [{ slug: { $ne: null } }] },
      isMultiple: true
    })
    $el.find('.js-company-select').append(companySelect.render().$el)
    companySelect.on('change', (value) => {
      if (!value || value.length === 0) return
      // Handle multiple selections - value is an array of IDs
      const newCompanyIds = Array.isArray(value) ? value : [value]
      newCompanyIds.forEach((companyId) => {
        if (!this.selectedBulkCompanies.includes(companyId)) {
          this.selectedBulkCompanies.push(companyId)
          this.serviceLocator.companyService.read(companyId, (err, company) => {
            if (err) return this.serviceLocator.logger.error(err)
            const item = `<li>${company.name}</li>`
            $el.find('.js-company-list').append(item)
          })
        }
      })
    })

    // Setup executive select
    const executiveSelect = new ExecutiveSelectMulti(
      this.serviceLocator,
      null,
      {
        filter: { $and: [{ slug: { $ne: null } }] }
      }
    )
    $el.find('.js-executive-select').append(executiveSelect.render().$el)
    executiveSelect.on('change', (value) => {
      if (!value || value.length === 0) return
      // Handle multiple selections - value is an array of IDs
      const newExecutiveIds = Array.isArray(value) ? value : [value]
      newExecutiveIds.forEach((executiveId) => {
        if (!this.selectedBulkExecutives.includes(executiveId)) {
          this.selectedBulkExecutives.push(executiveId)
          this.serviceLocator.executiveService.read(
            executiveId,
            (err, executive) => {
              if (err) return this.serviceLocator.logger.error(err)
              const item = `<li>${executive.name}</li>`
              $el.find('.js-executive-list').append(item)
            }
          )
        }
      })
    })
    modal({
      title: 'Linking Entities',
      content: $el,
      buttons: [
        { text: 'Cancel', className: 'btn', keyCodes: [27] },
        { text: 'Link', className: 'btn btn--action', event: 'confirm' }
      ]
    }).on('confirm', () => {
      // Get the featured article checkbox value
      const isFeaturedArticle = $el
        .find('[name="isFeaturedArticle"]')
        .is(':checked')

      this.selectedCollection.forEach((model) => {
        // Handle companies
        let newCompanies = model.get('companies') || []
        newCompanies = newCompanies.concat(
          this.selectedBulkCompanies.map((company) => ({
            company,
            isFeaturedArticle: isFeaturedArticle
          }))
        )

        const deduplicateCompanies = (companies) => {
          const dedupedCompanies = []
          const seenCompanies = new Set()
          for (const companyObj of companies) {
            const { company } = companyObj
            if (!seenCompanies.has(company)) {
              dedupedCompanies.push(companyObj)
              seenCompanies.add(company)
            }
          }
          return dedupedCompanies
        }

        const dedupedCompanies = deduplicateCompanies(newCompanies)

        // Handle executives
        let newExecutives = model.get('executives') || []
        newExecutives = newExecutives.concat(
          this.selectedBulkExecutives.map((executive) => ({ executive }))
        )

        const deduplicateExecutives = (executives) => {
          const dedupedExecutives = []
          const seenExecutives = new Set()
          for (const { executive } of executives) {
            if (!seenExecutives.has(executive)) {
              dedupedExecutives.push(executive)
              seenExecutives.add(executive)
            }
          }
          return dedupedExecutives.map((executive) => ({ executive }))
        }

        const dedupedExecutives = deduplicateExecutives(newExecutives)

        model.set('companies', dedupedCompanies)
        model.set('executives', dedupedExecutives)

        this.serviceLocator.articleService.partialUpdate(
          model.id,
          {
            companies: model.get('companies'),
            executives: model.get('executives')
          },
          (err, savedArticle) => {
            if (err) this.serviceLocator.logger.error(err)
            model.reset(savedArticle)
          }
        )
      })
    })
    $('.js-modal').css({ width: '1000px', maxWidth: '90vw' })
  }

  handleExport(params) {
    const filter = JSON.stringify(params.filter)
    const sort = JSON.stringify(params.sort)
    const querystring = qs.stringify({
      keywords: params.keywords,
      filter,
      sort
    })

    window.location = createSignedApiUrl(
      `/api/article/export?${querystring}`,
      window.localStorage.apiKey,
      window.localStorage.apiId,
      window.config.apiUrl
    )
  }
}

module.exports = ListView
