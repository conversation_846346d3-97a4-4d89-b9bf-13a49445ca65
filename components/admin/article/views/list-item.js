const BaseListItemView = require('../../base/views/list-item')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/list-item.jade'))
const stateMap = require('../state-map')
const { issueMap, getIssueState } = require('../issue-map')
const createImageUrlBuilder = require('cf-image-url-builder')
const config = window.config
const createDarkroomUrlBuilder = require('@clocklimited/darkroom-url-builder')

class ListItemView extends BaseListItemView {
  constructor(...args) {
    super(...args)
    this.$el.on('click', '.js-preview', this.emit.bind(this, 'preview'))
    this.$el.on(
      'click',
      '.js-date-preview',
      this.emit.bind(this, 'datePreview', this.model)
    )
    this.$el.on('click', '.js-duplicate', this.emit.bind(this, 'duplicate'))
    this.$el.on('click', '.js-syndicate', this.emit.bind(this, 'syndicate'))
    this.$el.on(
      'click',
      '.js-show-revisions',
      this.emit.bind(this, 'showRevisions')
    )
  }

  get template() {
    return template
  }

  serialize() {
    const images = this.model.get('images').widgets
    const data = this.model.toJSON()
    const drUrl = config.darkroom.url
    const drKey = config.darkroom.salt
    const urlBuilder = createImageUrlBuilder(
      drUrl,
      drKey,
      this.model.get('images').widgets
    )
    const getVisibilityState = stateMap.visibility.get(
      this.model.getVisibilityState()
    )
    const getScheduleState = stateMap.schedule.get(
      this.model.getScheduleState()
    )

    const issueState = getIssueState(data)
    data.__issues = issueState.map((issue) => issueMap.get(issue)())

    if (
      images &&
      images.length &&
      urlBuilder.getImage('Thumbnail') &&
      urlBuilder.getImage('Thumbnail').crop('Square')
    ) {
      data.previewImageUrlSmall = urlBuilder
        .getImage('Thumbnail')
        .crop('Square')
        .constrain(150)
        .url()
      data.previewImageUrlLarge = urlBuilder
        .getImage('Thumbnail')
        .crop('Square')
        .constrain(400)
        .url()
    } else {
      data.previewImageUrlSmall = '/assets/img/content/no-photo-medium.png'
      data.previewImageUrlLarge = '/assets/img/content/no-photo-large.png'
    }

    data.schedule = getScheduleState(
      this.model.get('liveDate'),
      this.model.get('expiryDate')
    )
    data.visibility = getVisibilityState()

    data.hasSection = typeof data.__fullUrlPath !== 'undefined'

    // Add include path
    data.publicUrlPath = config.url + data.__fullUrlPath

    if (data.__instance && data.__instance.logo) {
      const logoUrlBuilder = createDarkroomUrlBuilder(
        config.darkroom.url,
        config.darkroom.salt
      )
      const url = logoUrlBuilder()
        .resource(data.__instance.logo)
        .filename('preview.jpg')
        .url()
      data.instanceLogoUrl = url
    }

    return data
  }

  render() {
    const dropdownActions = []
    const data = this.serialize()

    if (this.serviceLocator.allowed('article', 'duplicate')) {
      dropdownActions.push('duplicate')
      dropdownActions.push('syndicate')
    }

    if (data.hasSection) {
      dropdownActions.push('datePreview')
    }

    this.$el.empty().append(
      template({
        data: data,
        allowed: this.serviceLocator.allowed,
        dropdownActions: dropdownActions,
        format: this.serviceLocator.format
      })
    )

    this.$el.find('.js-tooltip-trigger').tooltip({ html: true })
    return this
  }
}

module.exports = ListItemView
