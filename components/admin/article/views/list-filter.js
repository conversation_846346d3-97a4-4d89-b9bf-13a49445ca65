const compileJade = require('browjadify-compile')
const View = require('ventnor')
const join = require('path').join
const templates = {
  fixed: compileJade(join(__dirname, '/../templates/list-filter.jade')),
  condensed: compileJade(
    join(__dirname, '/../templates/list-filter-condensed.jade')
  )
}
const dateTimePickers = require('../../../../admin/source/js/lib/init-date-time-pickers')
const InstanceSectionSelect = require('../../section/views/instance-section-select')
const CompanySelectView = require('../../company/views/company-select')
const ExecutiveSelectView = require('../../executives/views/executive-select')
const moment = require('moment')

class ListFilterView extends View {
  constructor(serviceLocator, listView, layout) {
    super(...arguments)

    this.layout = layout || 'fixed'
    this.$el.addClass('list-filters')
    this.instances = listView.instances
    this.authors = listView.authors
    this.contentTypes = listView.contentTypes
    this.companies = []
    this.executives = []
    this.$el.find('[name=companyFilterToggle]').prop('checked', true)
    this.$el.find('[name=executiveFilterToggle]').prop('checked', true)
    this.$el.on('submit', 'form', this.handleSubmit.bind(this))
    this.$el.on('click', 'input[type=reset]', this.handleClear.bind(this))
    this.$el.on(
      'click',
      '.dropdown-menu a',
      this.handleDropDownAction.bind(this)
    )
    this.$el.on('click', '.js-export', this.handleExport.bind(this))
    this.$el.on(
      'change',
      '[name=companyFilterToggle]',
      this.handleCompanyFilterToggle.bind(this)
    )
    this.$el.on(
      'change',
      '[name=executiveFilterToggle]',
      this.handleExecutiveFilterToggle.bind(this)
    )
    this.dateTimePickers = []
    this.initDateTimePickers = dateTimePickers(
      window.config.locale.longDateFormat.LLL +
        window.config.locale.longDateFormat.LT
    )
    // Compatibility with backbone admin
    this.trigger = super.emit
    this.filteredSections = []
    this.filteredCompany = null
    this.companyFilterChecked = false
  }

  updateDisplay({ keywords, sort, filter }) {
    this.$el.find('form [name=keywords]').val(keywords)

    if (filter.state) {
      this.$el
        .find('[name=state] option[value=' + filter.state + ']')
        .attr('selected', true)
    }

    const backMap = {
      'dateCreated,asc': 'dateCreated',
      'dateCreated,desc': '-dateCreated',
      'score,desc': '-score'
    }
    let dayDiff

    if (filter.expiryDate) {
      dayDiff = Math.round(
        (new Date(filter.expiryDate.$lte) - new Date()) / 1000 / 60 / 60 / 24
      )
      this.$el
        .find('[name=expiring] option[value=' + dayDiff + ']')
        .attr('selected', true)
    }

    if (Array.isArray(sort)) {
      this.$el
        .find('[name=sort] option[value=' + backMap[sort.join(',')] + ']')
        .attr('selected', true)
    }

    if (filter.instance) {
      this.$el
        .find('[name=instance] option[value=' + filter.instance + ']')
        .attr('selected', true)
    }
  }

  handleDropDownAction(e) {
    e.preventDefault()
    const $target = $(e.target)
    const $field = $target.closest('.dropdown-menu')
    const field = $field.data('field')
    $field.find('.js-current').text('Current: ' + $target.text())
    this.$el.find(`form [name=${field}]`).val($target.attr('data-value'))
    this.handleSubmit()
  }

  consolidateFilters(e) {
    const params = { filter: {}, sort: [] }
    const map = {
      dateCreated: ['dateCreated', 'asc'],
      '-dateCreated': ['dateCreated', 'desc'],
      '-score': ['score', 'desc']
    }

    let toDate = this.$el.find('form [name=toDate]').val() || new Date()
    let fromDate = this.$el.find('form [name=fromDate]').val()
    const expiring = this.$el.find('form [name=expiring]').val()
    const keywords = this.$el.find('form [name=keywords]').val()
    const instance = this.$el.find('form [name=instance]').val()
    const contentType = this.$el.find('form [name=contentType]').val()
    const author = this.$el.find('form [name=author]').val()
    const sections = this.filteredSections
    const company = this.filteredCompany
    const executive = this.filteredExecutive
    const featuredCompanyPortalFilterToggle = this.$el
      .find('form [name=featuredCompanyPortalFilterToggle]')
      .is(':checked')
    let expiryDate
    let now

    params.sort = map[this.$el.find('form [name=sort]').val()]

    if (expiring) {
      now = new Date()
      expiryDate = new Date()
      expiryDate.setDate(expiryDate.getDate() + parseInt(expiring, 10))
      params.filter.expiryDate = { $gte: now, $lte: expiryDate }
    }

    if (toDate && fromDate) {
      toDate = moment(toDate).format(window.config.formats.iso)
      fromDate = moment(fromDate).format(window.config.formats.iso)
      params.filter.dateCreated = {
        $gte: fromDate,
        $lt: toDate
      }
    }

    this.getFilterState(params.filter)

    if (keywords.length) params.keywords = keywords
    if (sections && sections.length) params.filter.sections = { $in: sections }
    if (instance) params.filter.instance = instance
    if (author) params.filter.author = author
    if (contentType) params.filter.contentType = contentType
    if (company && this.companies.length) {
      // Find the company object to get the name
      const companyObj = this.companies.find((c) => c._id === company)
      if (companyObj) {
        params.filter = {
          ...params.filter,
          $and: [
            {
              $or: [
                { headline: { $regex: companyObj.name, $options: 'i' } },
                { sell: { $regex: companyObj.name, $options: 'i' } },
                {
                  $and: [
                    { tags: { $elemMatch: { tag: companyObj.name } } },
                    { companies: { $size: 0 } }
                  ]
                }
              ]
            },
            ...(!this.companyFilterChecked
              ? [{ $or: [{ companies: { company: companyObj._id } }] }]
              : [])
          ],
          dateCreated: { $gt: new Date('2024-01-31T23:59:59.999Z') },
          state: 'Published'
        }
      }
    }
    if (executive && this.executives.length) {
      // Find the executive object to get the name
      const executiveObj = this.executives.find((e) => e._id === executive)
      if (executiveObj) {
        params.filter = {
          ...params.filter,

          ...(!this.executiveFilterChecked && {
            $or: [{ executives: { $size: 0 } }]
          }),

          $text: { $search: `"${executiveObj.name}"` },
          dateCreated: { $gt: new Date('2024-01-31T23:59:59.999Z') },
          state: 'Published'
        }
      }
    }

    if (featuredCompanyPortalFilterToggle) {
      params.filter['companies.isFeaturedArticle'] = true
    }

    return params
  }

  getFilterState(filter) {
    var state = this.$el.find('form [name=state]').val()
    if (state) filter.state = state
  }

  handleClear(e) {
    e.preventDefault()
    this.$el.find('form [name=keywords]').val('')
    this.$el.find('form [name=sort]').val('-dateCreated')
    this.$el.find('form [name=expiring]').val('')
    this.$el.find('form [name=state]').val('')
    this.$el.find('form [name=toDate]').val('')
    this.$el.find('form [name=fromDate]').val('')
    this.$el.find('form [name=instance]').val('')
    this.$el.find('form [name=author]').val('')
    this.$el.find('form [name=contentType]').val('')
    this.filteredSections = []
    this.filteredCompany = null
    this.filteredExecutive = null
    this.companyFilterChecked = false
    this.executiveFilterChecked = false
    this.handleCompanyFilterToggle(null, false)
    this.handleExecutiveFilterToggle(null, false)
    this.sectionSelect.clear()
    if (this.companySelect) {
      this.companySelect.el.selectize.setValue(null)
    }
    if (this.executiveSelect) {
      this.executiveSelect.el.selectize.setValue(null)
    }
    this.$el
      .find('form [name=featuredCompanyPortalFilterToggle]')
      .prop('checked', null)
    this.handleSubmit()
  }

  handleSubmit(e) {
    if (e) e.preventDefault()
    const params = this.consolidateFilters()
    this.emit('filter', params)
  }

  render() {
    this.$el.empty().append(
      templates[this.layout]({
        instances: this.instances,
        authors: this.authors,
        contentTypes: this.contentTypes,
        allowed: this.serviceLocator.allowed
      })
    )

    this.$el.find('.js-tooltip-trigger').tooltip({ html: true })
    this.dateTimePickers = this.$el
      .find('.js-date-time-picker')
      .toArray()
      .map(this.initDateTimePickers)

    this.renderSectionSelect()
    this.renderCompanySelect()
    this.renderExecutiveSelect()
    this.loadCompanies()
    this.loadExecutives()

    return this
  }

  renderSectionSelect() {
    const instanceSectionSelect = new InstanceSectionSelect(this.serviceLocator)

    this.sectionSelect = instanceSectionSelect

    this.$el
      .find('.js-section-select')
      .append(instanceSectionSelect.render().$el)
    instanceSectionSelect.on('change', (section) => {
      this.filteredSections = section ? [section] : null
    })
    this.attachView(instanceSectionSelect)
  }

  renderCompanySelect() {
    this.companySelect = new CompanySelectView(this.serviceLocator, null, {
      filter: { $and: [{ slug: { $ne: null } }] }
    })
    this.$el.find('.js-company-select').append(this.companySelect.render().$el)
    this.companySelect.on('change', (value) => {
      this.filteredCompany = value
    })
    this.attachView(this.companySelect)
  }

  renderExecutiveSelect() {
    this.executiveSelect = new ExecutiveSelectView(
      this.serviceLocator,
      null,
      null,
      {
        filter: { $and: [{ slug: { $ne: null } }] }
      }
    )
    this.$el
      .find('.js-executive-select')
      .append(this.executiveSelect.render().$el)
    this.executiveSelect.on('change', (value) => {
      this.filteredExecutive = value
    })
    this.attachView(this.executiveSelect)
  }

  handleExport(e) {
    if (e) e.preventDefault()
    const params = this.consolidateFilters()
    this.emit('export', params)
  }

  handleCompanyFilterToggle(e, checked) {
    let isChecked = checked
    if (e) isChecked = e.target.checked
    this.companyFilterChecked = checked

    if (!isChecked) {
      this.$el.find('[name=companyFilterToggle]').prop('checked', false)
      this.$el.find('.js-company-filter-link-text').text('Unlinked' + ' ')
      this.$el.find('.js-company-filter-link-text').css('color', 'red')
      this.$el.find('.js-company-filter-label').text('Company Filter')
    } else {
      this.$el.find('[name=companyFilterToggle]').prop('checked', true)
      this.$el.find('.js-company-filter-link-text').text('Full' + ' ')
      this.$el.find('.js-company-filter-link-text').css('color', 'green')
      this.$el.find('.js-company-filter-label').text('Company Filter')
    }

    this.handleSubmit()
  }

  handleExecutiveFilterToggle(e, checked) {
    let isChecked = checked
    if (e) isChecked = e.target.checked
    this.executiveFilterChecked = checked

    if (!isChecked) {
      this.$el.find('[name=executiveFilterToggle]').prop('checked', false)
      this.$el.find('.js-executive-filter-link-text').text('Unlinked' + ' ')
      this.$el.find('.js-executive-filter-link-text').css('color', 'red')
      this.$el.find('.js-executive-filter-label').text('Executive Filter')
    } else {
      this.$el.find('[name=executiveFilterToggle]').prop('checked', true)
      this.$el.find('.js-executive-filter-link-text').text('Full' + ' ')
      this.$el.find('.js-executive-filter-link-text').css('color', 'green')
      this.$el.find('.js-executive-filter-label').text('Executive Filter')
    }

    this.handleSubmit()
  }

  loadCompanies() {
    this.serviceLocator.companyService.find(
      '',
      {},
      [],
      { pageSize: 1000000 },
      (err, res) => {
        if (err) return this.serviceLocator.logger.error(err)
        this.companies = res.results
      }
    )
  }

  loadExecutives() {
    this.serviceLocator.executiveService.find(
      '',
      {},
      [],
      { pageSize: 1000000 },
      (err, res) => {
        if (err) return this.serviceLocator.logger.error(err)
        this.executives = res.results
      }
    )
  }
}

module.exports = ListFilterView
