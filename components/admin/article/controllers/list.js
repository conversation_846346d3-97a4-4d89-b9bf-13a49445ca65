const ListView = require('../views/list')
const Collection = require('chale')
const ArticleModel = require('../models/article')
const Model = require('merstone')
const async = require('async')
const pageSize = 50
const moment = require('moment')
const crypto = require('crypto')
const contentTypes = require('../../../service/article/content-types.json')

const createController = (serviceLocator) => {
  const collection = new Collection(serviceLocator, [], ['select', 'deSelect'])
  const paginationModel = new Model(serviceLocator, {
    totalItems: 0,
    showing: 0
  })
  const projection = {
    _id: 1,
    slug: 1,
    headline: 1,
    dateCreated: 1,
    liveDate: 1,
    expiryDate: 1,
    contentType: 1,
    legacyAuthorName: 1,
    images: 1,
    state: 1,
    sections: 1,
    previewId: 1,
    eventId: 1,
    eventArticleCategoryKey: 1,
    instance: 1,
    companies: 1,
    executives: 1
  }
  let currentParams = {
    keywords: '',
    filter: {},
    sort: ['dateCreated', 'desc']
  }
  let currentPage = 1

  const getInstances = (cb) => {
    serviceLocator.instanceService.find(
      '',
      {},
      ['name'],
      { pageSize: 1000 },
      (err, res) => {
        if (err) return cb(err)
        return cb(null, res.results)
      }
    )
  }

  const getAuthors = (cb) => {
    serviceLocator.authorService.find(
      '',
      {},
      ['name'],
      { pageSize: 1000 },
      (err, res) => {
        if (err) return cb(err)
        return cb(null, res.results)
      }
    )
  }

  const getInstance = (id, cb) => {
    serviceLocator.instanceService.read(id, (err, instance) => {
      if (err) return cb(err)
      return cb(null, instance)
    })
  }

  const getAccount = (cb) => {
    serviceLocator.accountService.find(
      '',
      {},
      ['name'],
      { pageSize: 1000 },
      (err, res) => {
        if (err) return cb(err)
        return cb(null, res.results[0])
      }
    )
  }

  const getEventUmbrella = (id, cb) => {
    serviceLocator.eventUmbrellaService.read(id, (err, eventUmbrella) => {
      if (err) return cb(err)
      return cb(null, eventUmbrella)
    })
  }

  const getEvent = (id, cb) => {
    serviceLocator.eventService.read(id, (err, event) => {
      if (err) return cb(err)
      return cb(null, event)
    })
  }

  // Reload the first page of the current filters when a new item is created in case it should appear there
  serviceLocator.articleService.on('create', () => {
    currentPage = 1
    var pagination = { page: currentPage, pageSize: pageSize }
    getArticles(
      currentParams.keywords,
      currentParams.filter,
      currentParams.sort,
      pagination,
      projection
    )
  })

  // Whenever an article is updated, reset the model with its new attributes
  serviceLocator.articleService.on('update', (id, attrs) => {
    var model = collection.get(id)
    if (model) model.reset(attrs)
  })

  serviceLocator.router.route('articles(/)', 'listArticles', () => {
    if (!serviceLocator.allow('article', 'discover')) return false

    async.parallel(
      {
        instances: getInstances,
        authors: getAuthors
      },
      (err, res) => {
        if (err)
          return this.serviceLocator.logger.error(
            err,
            'Could not load instances'
          )

        // Pre-populate the list's collection with a set of articles
        const pagination = { page: currentPage, pageSize: pageSize }

        const list = new ListView(
          serviceLocator,
          collection,
          paginationModel,
          res.instances || [],
          res.authors || [],
          contentTypes.map((type) => ({ name: type.type, _id: type.type })) ||
            []
        ).render()

        const queryString = window.location.search
        const searchParams = new URLSearchParams(queryString)
        const isHospital = searchParams.get('hospital') === 'true'
        if (isHospital) {
          currentParams.filter = { headline: { $regex: 'Sam Altman' } }
        }

        getArticles(
          currentParams.keywords,
          currentParams.filter,
          currentParams.sort,
          pagination,
          projection
        )

        list.displayFilterParams(currentParams)

        list.on('createNew', () => {
          if (!serviceLocator.allow('article', 'create')) return false
          serviceLocator.router.navigate('articles/form', { trigger: true })
        })

        list.on('edit', (id) => {
          if (!serviceLocator.allow('article', 'update')) return false
          serviceLocator.router.navigate('articles/' + id + '/form', {
            trigger: true
          })
        })

        const getPreviewUrlComponents = (article, cb) => {
          async.waterfall(
            [
              (cb) => {
                serviceLocator.sectionService.read(
                  article.get('sections')[0],
                  (err, section) => {
                    if (err) return cb(err)
                    return cb(null, { section: section })
                  }
                )
              },
              (obj, cb) => {
                if (obj.section.instance) {
                  serviceLocator.instanceService.read(
                    obj.section.instance,
                    (err, instance) => {
                      if (err) return cb(err)
                      obj.instance = instance
                      return cb(null, obj)
                    }
                  )
                } else {
                  serviceLocator.instanceService.find(
                    '',
                    {},
                    [],
                    { pageSize: 1, page: 1 },
                    (err, res) => {
                      if (err) return cb(err)
                      obj.instance = res.results[0]
                      return cb(null, obj)
                    }
                  )
                }
              },
              (obj, cb) => {
                serviceLocator.accountService.read(
                  obj.instance.account,
                  (err, account) => {
                    if (err) return cb(err)
                    obj.account = account
                    return cb(null, obj)
                  }
                )
              }
            ],
            cb
          )
        }

        list.on('preview', (article) => {
          var w = window.open('')
          const isEventArticle = !!article.get('eventId')
          if (!isEventArticle) {
            getPreviewUrlComponents(article, (err, obj) => {
              if (err) {
                w.close()
                return serviceLocator.logger.error(
                  err,
                  'Could not preview article'
                )
              }

              w.location =
                serviceLocator.instanceService.createUrl(
                  obj.instance,
                  obj.account
                ) +
                article.get('__fullUrlPath') +
                '?previewId=' +
                article.get('previewId')
            })
          } else {
            getAccount((err, account) => {
              if (err) return err
              getInstance(article.get('instance'), (err, instance) => {
                if (err) return err
                getEvent(article.get('eventId'), (err, event) => {
                  if (err) return err
                  getEventUmbrella(
                    event.eventUmbrellaId,
                    (err, eventUmbrella) => {
                      if (err) return err
                      const eventArticleCategory = event.articleCategories.find(
                        (c) => c.key === article.get('eventArticleCategoryKey')
                      )
                      let url
                      if (eventArticleCategory) {
                        url =
                          serviceLocator.instanceService.createUrl(
                            instance,
                            account
                          ) +
                          '/events/' +
                          eventUmbrella.slug +
                          '/' +
                          event.slug +
                          '/' +
                          eventArticleCategory.articleBaseSlug +
                          '/' +
                          article.get('slug') +
                          '?previewId=' +
                          event.previewId
                      }
                      w.location = url
                    }
                  )
                })
              })
            })
          }
        })

        list.on('datePreview', (article) => {
          const w = window.open('')
          const now = moment().format(serviceLocator.config.formats.isoZ)

          async.parallel(
            { url: getPreviewUrlComponents.bind(null, article) },
            (err, obj) => {
              if (err) {
                w.close()
                return serviceLocator.logger.error(
                  err,
                  'Could not preview article'
                )
              }
              let url =
                '//' + obj.url.account.domain + '/' + obj.url.instance.slug
              const hash = crypto.createHash('sha1').update(now).digest('hex')

              if (
                ['development', 'testing'].indexOf(
                  serviceLocator.config.env
                ) !== -1
              ) {
                url += ':' + serviceLocator.config.port
              }
              url +=
                '/date-preview' +
                article.get('__fullUrlPath') +
                '?date=' +
                now +
                '&dateHash=' +
                hash
              w.location = url
            }
          )
        })

        list.on('duplicate', (model) => {
          if (!serviceLocator.allow('article', 'duplicate')) return false
          serviceLocator.router.navigate(
            'articles/' + model.id + '/duplicate',
            { trigger: true }
          )
        })

        list.on('syndicate', (model) => {
          if (!serviceLocator.allow('article', 'duplicate')) return false
          serviceLocator.router.navigate(
            'articles/' + model.id + '/syndicate',
            { trigger: true }
          )
        })

        list.on('delete', (ids) => {
          if (!serviceLocator.allow('article', 'delete')) return false
          const deleteOne = (id, cb) => {
            serviceLocator.articleService.delete(id, (err) => {
              if (err) return cb(err)
              collection.remove(id)
            })
          }

          async.each(ids, deleteOne, (err) => {
            if (err) return alert(err.message)
          })
        })

        list.on('filter', (params) => {
          currentParams = params
          var pagination = { page: currentPage, pageSize: pageSize }
          currentPage = 1
          getArticles(
            params.keywords,
            params.filter,
            params.sort,
            pagination,
            projection
          )
        })

        list.on('loadMore', () => {
          currentPage += 1
          var pagination = { page: currentPage, pageSize: pageSize }
          appendArticles(
            currentParams.keywords,
            currentParams.filter,
            currentParams.sort,
            pagination
          )
        })

        list.on('showRevisions', (model) => {
          serviceLocator.router.navigate(
            'articles/' + model.id + '/revisions',
            {
              trigger: true
            }
          )
        })

        serviceLocator.router.render(list, 'Articles')
      }
    )
  })

  const getArticles = (keywords, filter, sort, pagination, projection) => {
    serviceLocator.articleService.find(
      keywords,
      filter,
      sort,
      pagination,
      projection,
      (err, res) => {
        if (err)
          return serviceLocator.logger.error(err, 'Could not load articles')
        collection.reset(
          res.results.map(
            (article) => new ArticleModel(serviceLocator, article)
          )
        )
        paginationModel.set('totalItems', res.totalItems)
        paginationModel.set('showing', collection.models.length)
      }
    )
  }

  const appendArticles = (keywords, filter, sort, pagination) => {
    serviceLocator.articleService.find(
      keywords,
      filter,
      sort,
      pagination,
      (err, res) => {
        if (err) return alert(err.message)
        res.results.forEach((article) => {
          collection.add(new ArticleModel(serviceLocator, article))
        })
        paginationModel.set('totalItems', res.totalItems)
        paginationModel.set('showing', collection.models.length)
      }
    )
  }
}

module.exports = createController
