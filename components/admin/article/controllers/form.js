const Model = require('../models/article')
const createArticleFormViewController = require('./form-view-controller')
const async = require('async')

const createController = (serviceLocator) => {
  const initializeArticleFormView = createArticleFormViewController(
    serviceLocator
  )

  // Create
  serviceLocator.router.route('articles/form', 'createArticle', () => {
    if (!serviceLocator.allow('article', 'create')) return false

    const accountId = serviceLocator.session.account
    const model = new Model(serviceLocator)

    if (!accountId)
      return alert(
        'Session must be associated with an account id to create/edit articles'
      )

    model.set(model.schema.makeDefault())
    getData(null, accountId, (err, { account }) => {
      if (err) return serviceLocator.router.trigger('notFound', err.message)
      setupNavigation(
        initializeArticleFormView({
          model,
          account,
          isNew: true
        })
      )
    })
  })

  // Edit
  serviceLocator.router.route('articles/:id/form', 'editArticle', (id) => {
    if (!serviceLocator.allow('article', 'update')) return false

    const accountId = serviceLocator.session.account
    if (!accountId)
      return alert(
        'Session must be associated with an account id to create/edit articles'
      )

    getData(id, accountId, (err, { article, account }) => {
      if (err) return serviceLocator.router.trigger('notFound', err.message)
      const model = new Model(serviceLocator, article)
      setupNavigation(
        initializeArticleFormView({
          model,
          account,
          isNew: false
        })
      )
    })
  })

  // Duplicate
  serviceLocator.router.route(
    'articles/:id/duplicate',
    'duplicateArticle',
    (id) => {
      if (!serviceLocator.allow('article', 'update')) return false

      const accountId = serviceLocator.session.account
      if (!accountId)
        return alert(
          'Session must be associated with an account id to create/edit articles'
        )

      getData(id, accountId, (err, { article, account }) => {
        if (err) return serviceLocator.router.trigger('notFound', err.message)

        const articleData = article

        articleData.sourceId = article._id
        articleData.canonicalUrl = `${article.__instance.__fullUrl}${article.__fullUrlPath}`
        articleData.hasBeenPublished = false

        // Clear ID so it's new
        delete articleData._id
        // Clear previewId so a new one is generated
        delete articleData.previewId
        // Delete dateCreated so ordering is logical
        delete articleData.dateCreated
        delete articleData.viewCounts
        delete articleData.headline
        delete articleData.last30DaysNumViews
        delete articleData.sections
        delete articleData.instance
        delete articleData.displayDate
        delete articleData.modifiedDate
        delete articleData.publishDate
        delete articleData.publisherName
        delete articleData.author

        articleData.state = 'Draft'

        const model = new Model(serviceLocator, articleData)
        model.isDuplicate = true
        model.set(model.schema.makeDefault(articleData))
        setupNavigation(
          initializeArticleFormView({
            model,
            account,
            isNew: true
          })
        )
      })
    }
  )

  // Syndicate
  serviceLocator.router.route(
    'articles/:id/syndicate',
    'syndicateArticle',
    (id) => {
      if (!serviceLocator.allow('article', 'update')) return false

      const accountId = serviceLocator.session.account
      if (!accountId)
        return alert(
          'Session must be associated with an account id to create/edit articles'
        )

      getData(id, accountId, (err, { article, account }) => {
        if (err) return serviceLocator.router.trigger('notFound', err.message)

        const articleData = article

        articleData.sourceId = article._id
        articleData.canonicalUrl = null
        articleData.hasBeenPublished = false

        // Clear ID so it's new
        delete articleData._id
        // Clear previewId so a new one is generated
        delete articleData.previewId
        // Delete dateCreated so ordering is logical
        delete articleData.dateCreated
        delete articleData.viewCounts
        delete articleData.headline
        delete articleData.last30DaysNumViews
        delete articleData.sections
        delete articleData.instance
        delete articleData.displayDate
        delete articleData.modifiedDate
        delete articleData.publishDate
        delete articleData.publisherName
        delete articleData.author

        articleData.state = 'Draft'

        const model = new Model(serviceLocator, articleData)
        model.set(model.schema.makeDefault(articleData))
        setupNavigation(
          initializeArticleFormView({
            model,
            account,
            isNew: true
          })
        )
      })
    }
  )

  const getData = (id, accountId, cb) => {
    const tasks = {
      account: (cb) => serviceLocator.accountService.read(accountId, cb)
    }

    if (id) tasks.article = (cb) => serviceLocator.articleService.read(id, cb)

    async.parallel(tasks, cb)
  }

  const setupNavigation = (view) => {
    view.on('close', () => {
      serviceLocator.router.navigate('articles', { trigger: true })
    })

    view.on('saveComplete', (isNew) => {
      if (isNew)
        serviceLocator.router.navigate('articles/' + view.model.id + '/form', {
          trigger: true
        })
    })

    view.on('back', () => {
      serviceLocator.router.navigate('articles', { trigger: true })
    })
  }
}

module.exports = createController
