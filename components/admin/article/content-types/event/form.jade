.panel.panel-styled(id='section-configuration')
  .panel-header
    h2 Event

  .panel-content

    .form-row(id='field--eventRegistrationLink', data-field='eventRegistrationLink')
      label
        span.form-label-text Event Registration Link
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='eventRegistrationLink', value=article.eventRegistrationLink)
      .js-error

    .form-row(id='field--onDemandLink', data-field='onDemandLink')
      label
        span.form-label-text On Demand Link
        input.control.control--text.form-field(type='text', name='onDemandLink', value=article.onDemandLink)
      .form-row-description.form-copy
        p If provided, past events will show "Watch on Demand 🎬" instead of "Find out more"
      .js-error

    .form-row(id='field--startDate', data-field='startDate')
      label
        span.form-label-text Start Date
          abbr(title='This field is required') *
        .form-field
          span.datepicker
            input.control.control--text.form-field.js-date-time-picker(type='text', name='startDate', value=article.startDate, readonly)
      .js-error

    .form-row(id='field--endDate', data-field='endDate')
      label
        span.form-label-text End Date
          abbr(title='This field is required') *
        .form-field
          span.datepicker
            input.control.control--text.form-field.js-date-time-picker(type='text', name='endDate', value=article.endDate, readonly)
      .js-error

    .form-row(id='field--country', data-field='country')
      label
        span.form-label-text Country
          abbr(title='This field is required') *
        .js-country-select
      .js-error

    .form-row(id='field--timezone', data-field='timezone')
      label
        span.form-label-text Timezone
          abbr(title='This field is required') *
        .js-timezone-select
      .js-error

    .form-row(id='field--address', data-field='address')
      label
        span.form-label-text Address
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='address', value=article.address)
      .js-error

    .form-row(id='field--price', data-field='price')
      label
        span.form-label-text Price
        input.control.control--text.form-field(type='numer', name='price', value=article.price)
      .js-error
