.list-item

  .list-item-header

    .list-item-actions
      if allowed('partnership', 'delete')
        label.list-item-select
          input.js-select(type='checkbox', name='name')
      .btn-group
        
    if allowed('partnership', 'update')
      h2
        a.js-edit(href='partnerships/#{data._id}/form')= data.name
    else
      h2= data.name

  .list-item-content
    dl
      dt Created:
      dd= format(data.createdDate, 'calendar')
      dt Slug:
      dd= data.slug
