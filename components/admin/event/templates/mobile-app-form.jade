if !allowed('event', 'manageMobileApp')
  .page-content
    .centering
      header.page-header
        h1 Mobile App Configuration
      .panel.panel-styled
        .panel-content
          p You don't have access to this mobile app configuration.
else
  .page-content
    .toolbar
      .centering.js-toolbar
        .toolbar__left
          button.btn.js-back(type='button') Back to Event List
          button.btn.btn--action.js-open-event(type='button') Open Event
        .control-group
          button.btn.js-save(type='button') Save

    .centering

      header.page-header.page-header--with-shortcuts-nav
        h1 Mobile App Configuration
        .js-shortcut-nav

      form
        .js-errors-summary

        //- Allow submission of form using enter button
        input.hidden(type='submit')

        .panel.panel-styled
          .panel-header.js-toggle-content(style="display:flex", data-content="basic-details")
            h2 Basic Details

            a.btn(style="margin-left:auto;")
              span.caret

          .panel-content.js-content.basic-details-content(style="display:block")

            .form-row
              label
                span.form-label-text
                  .code homepageSplashVideoUrl
                input.control.control--text.form-field(type='text', name='homepageSplashVideoUrl', value=data.homepageSplashVideoUrl, autofocus)
              .js-error


            video.js-homepageSplashVideoUrl--preview(controls, muted, autoplay, loop, style='width: 250px; background: #ddd;')
              source(src=data.homepageSplashVideoUrl, type='video/mp4')

              //- http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4
              //- http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4

            .form-row
              label
                span.form-label-text
                  .code whiteUmbrellaLogoUrl
                input.control.control--text.form-field(type='text', name='whiteUmbrellaLogoUrl', value=data.whiteUmbrellaLogoUrl)
              .js-error

            img.js-whiteUmbrellaLogoUrl--preview(src=data.whiteUmbrellaLogoUrl, style="width: 250px; background: #ddd;")

            .form-row
              label
                span.form-label-text
                  .code whiteBrandLogoUrl
                input.control.control--text.form-field(type='text', name='whiteBrandLogoUrl', value=data.whiteBrandLogoUrl)
              .js-error

            img.js-whiteBrandLogoUrl--preview(src=data.whiteBrandLogoUrl, style="width: 250px; background: #ddd;")

            .form-row
              label
                span.form-label-text
                  .code darkCombinedLogoUrl
                input.control.control--text.form-field(type='text', name='darkCombinedLogoUrl', value=data.darkCombinedLogoUrl)
              .js-error

            img.js-darkCombinedLogoUrl--preview(src=data.darkCombinedLogoUrl, style="width: 250px; background: #ddd;")

            .form-row
              label
                span.form-label-text
                  .code broughtToYouByTitle
                input.control.control--text.form-field(type='text', name='broughtToYouByTitle', value=data.broughtToYouByTitle)
              .js-error

            .form-row.form-row--ckeditor.form-row-full-width(id='field--broughtToYouByDescription', data-field='broughtToYouByDescription')
              label
                span.form-label-text
                  .code broughtToYouByDescription
                textarea.control.control--text.control--multiline.js-text-editor.form-field(id='broughtToYouByDescription', name='broughtToYouByDescription', rows='5')= data.broughtToYouByDescription
              .js-error

