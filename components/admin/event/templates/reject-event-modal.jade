p This event will be rejected and returned to draft status. Please provide a reason for rejection.

br

.control-group
  label(for='reject-note') Rejection Reason 
    span.required *
  textarea#reject-note.js-reject-note(placeholder='Please explain why this event is being rejected...', rows='3', required)
  .js-error-message(style='color: red; margin-top: 5px; display: none;') Please provide a reason for rejection.
  
.modal-controls
  button.btn.js-cancel Cancel
  button.btn.btn--warning.js-confirm Reject to Draft
