.page-content

  .toolbar
    .centering.js-toolbar
      .toolbar__left
        button.btn.js-back(type='button') Back to Portfolios
        if allowed('event', 'delete')
          button.btn.btn--delete.js-delete(type='button') Delete Selected

      if allowed('event', 'create')
        .btn-group
          button.btn.btn--success.js-sync(type='button') Sync Widgets
        .btn-group
          a.btn.dropdown-toggle.btn--action(data-toggle='dropdown', href='#') New #{displayName.singular}
            | 
            span.caret
          ul.dropdown-menu
            li: a(href='#').js-btn-restore-backup.js-new Blank
            li: a(href='#').js-btn-open-revisions.js-copy Copy Previous Event

  .centering

    header.page-header
      h1= displayName.plural
      .js-filters
      table.js-items.table--vertical-align-middle.table--selectable-rows.table--striped
      .pagination
        p
          | Showing 
          b.js-item-count
          |  of 
          b.js-total-item-count
          |  items
        button.btn.js-more(type='button') Load more
