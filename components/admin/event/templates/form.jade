.page-content(id="content")
  .toolbar
    .centering.js-toolbar
      .toolbar__left
        .control-group
          button.btn.js-cancel(type='button') Cancel
      .control-group
        button.btn.js-save(type='button') Save
        if data.state === 'Published'
          button.btn.btn--warning.js-btn-draft(type='button') Revert to Draft
        else if data.state === 'Review'
          button.btn.btn--success.js-btn-accept(type='button') Accept & Publish
          button.btn.btn--warning.js-btn-reject(type='button') Reject to Draft
        else if data.state === 'Draft'
          button.btn.btn--action.js-btn-review(type='button', disabled=!data._id) Submit for Review

  .centering

    header.page-header.page-header--with-shortcuts-nav
      h1= title
      .js-shortcut-nav

    form
      .js-errors-summary

      //- Allow submission of form using enter button
      input.hidden(type='submit')

      if showEventPreset || allowed('event', 'managePreset')
        include form-sections/event-preset

      if !showEventPreset || allowed('event', 'managePreset')
        include form-sections/basic-details
        include form-sections/location-and-time
        include form-sections/logos
        include form-sections/seo-sharing
        include form-sections/sponsor-information
        include form-sections/agenda-information
        include form-sections/collections
        include form-sections/speaker-information
        include form-sections/navigation-information
        include form-sections/article-information
        include form-sections/piano-information
        include form-sections/event-preset-information
        include form-sections/schema

  //- .panel.panel-styled(id='section-widgets')
        //-   .panel-header
        //-     h2 Widgets
        //-   .panel-content
        //-     .panel.js-widget-reference-area
