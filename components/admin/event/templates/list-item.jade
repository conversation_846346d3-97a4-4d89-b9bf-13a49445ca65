include state-map

//- .list-item.list-item-detail

//-   .image-wrapper
//-       i.image.image-detail(style='background-image: url("' + data.previewImageUrlSmall + '");')

//-   .list-item-header

//-     .list-item-actions
//-       mixin stateMap(data.status, data.visibility)

//-       .btn-group
//-         if allowed('eventArticle', 'discover')
//-           a.btn.btn--small.js-manage-articles Articles
//-         if allowed('eventSponsor', 'discover')
//-           a.btn.btn--small.js-manage-sponsors Sponsors
//-         if allowed('eventSpeaker', 'discover')
//-           a.btn.btn--small.js-manage-speakers Speakers
//-         if allowed('eventVideo', 'discover')  
//-           a.btn.btn--small.js-manage-videos Videos
//-         if allowed('eventAgendaItem', 'discover')  
//-           a.btn.btn--small.js-manage-agenda Agenda
//-         if allowed('event', 'editLayouts')
//-           a.btn.btn--small.js-manage-layouts Pages
//-           a.btn.btn--small.js-generate-layouts(disabled=true) 🎉
      
//-       if allowed('event', 'delete')
//-         label.list-item-select
//-           input.js-select(type='checkbox')
      
//-       button.btn.btn--small.js-preview(type='button') Preview

//-     if allowed('event', 'update')
//-       h2
//-         a.js-edit= data.name
//-     else
//-       h2= data.name

//-   .list-item-content
//-     dl
//-       dt Created:
//-       dd= format(data.createdDate, 'calendar')      




tr
  td
    .list-item-actions.list-item-actions--left
      mixin stateMap(data.status, data.visibility)

  td=data.__formattedStartDate

  td
    if allowed('event', 'update')
      a.js-edit= data.name
    else
      span=data.name

  td
    .list-item-actions
      .btn-group
        if allowed('eventArticle', 'discover')
          a.btn.btn--small.js-manage-articles Articles
        if allowed('eventSponsor', 'discover')
          a.btn.btn--small.js-manage-sponsors Sponsors
        if allowed('eventSpeaker', 'discover')
          a.btn.btn--small.js-manage-speakers Speakers
        if allowed('eventVideo', 'discover')  
          a.btn.btn--small.js-manage-videos Videos
        if allowed('eventAgendaItem', 'discover')  
          a.btn.btn--small.js-manage-agenda Agenda
        if allowed('event', 'editLayouts')
          a.btn.btn--small.js-manage-layouts Pages
        if allowed('event', 'manageMobileApp')
          a.btn.btn--small.js-manage-mobile-app 📱
        //- if allowed('event', '*')
          //- a.btn.btn--small.js-generate-layouts(disabled=true) 🎉
      
      if allowed('event', 'delete')
        label.list-item-select
          input.js-select(type='checkbox')
      
      button.btn.btn--small.js-preview.btn--preview(type='button', style="min-width: 80px;") Preview