ul.grid-n
  if allowed('eventVideo', 'discover')
    .panel.panel-styled
      .panel-header
        h2 Videos
        .panel-actions
          button.btn.btn--success.js-manage-videos(type='button') Manage

  if allowed('event', 'editLayouts')
    .panel.panel-styled
      .panel-header
        h2 Pages
        .panel-actions
          button.btn.btn--success.js-manage-layouts(type='button') Manage
          
  //- if allowed('event', 'manageNavigation')
  //-   .panel.panel-styled
  //-     .panel-header
  //-       h2 Event Navigation
  //-       .panel-actions
  //-         button.btn.btn--success.js-manage-navigation(type='button') Manage