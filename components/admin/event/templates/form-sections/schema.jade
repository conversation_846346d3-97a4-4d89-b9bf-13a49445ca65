.panel.panel-styled
  .panel-header.js-toggle-content(style="display:flex;flex-direction:column;")
    h2 Schema
    .form-copy
      p Within this section you can manage the event schema

  .panel-content
    .form-row(id='field--locationName', data-field='locationName')
      label
        span.form-label-text Location name
        input.control.control--text.form-field(type='text', name='locationName', value=data.locationName)
      .js-error
        
    .form-row(id='field--locationStreetAddress', data-field='locationStreetAddress')
      label
        span.form-label-text Location street address
        input.control.control--text.form-field(type='text', name='locationStreetAddress', value=data.locationStreetAddress)
      .js-error

    .form-row(id='field--locationAddressLocality', data-field='locationAddressLocality')
      label
        span.form-label-text Location city
        input.control.control--text.form-field(type='text', name='locationAddressLocality', value=data.locationAddressLocality)
      .js-error

    .form-row(id='field--locationPostalCode', data-field='locationPostalCode')
      label
        span.form-label-text Location postcode
        input.control.control--text.form-field(type='text', name='locationPostalCode', value=data.locationPostalCode)
      .js-error
        
    .form-row(id='field--locationAddressRegion', data-field='locationAddressRegion')
      label
        span.form-label-text Location county/region
        input.control.control--text.form-field(type='text', name='locationAddressRegion', value=data.locationAddressRegion)
      .js-error
    
    .form-row(id='field--locationAddressCountry', data-field='locationAddressCountry')
      label
        span.form-label-text Location country
        input.control.control--text.form-field(type='text', name='locationAddressCountry', value=data.locationAddressCountry)
      .js-error

    .form-row(id='field--offersUrl', data-field='offersUrl')
      label
        span.form-label-text URL to booking page
        input.control.control--text.form-field(type='text', name='offersUrl', value=data.offersUrl)
      .js-error    
