.panel.panel-styled
  .panel-header.js-toggle-content(style="display:flex", data-content="basic-details")
    h2 Basic Details

    a.btn(style="margin-left:auto;")
      span.caret

  .panel-content.js-content.basic-details-content(style="display:block")

    .form-row(id='field--name', data-field='name')
      label
        span.form-label-text Name
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='name', value=data.name, autofocus)
      .js-error

    .form-row(id='field--slug', data-field='slug')
      label
        span.form-label-text Slug
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='slug', value=data.slug, autofocus, disabled=hasBeenPublished)
        if hasBeenPublished
          .form-row-description.form-copy
            p This event has been published and the slug can no longer be changed. If you need to change it, please contact the tech team.
      .js-error

    .form-row(id='field--city', data-field='city')
      label
        span.form-label-text City
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='city', value=data.city)
      .js-error

    .form-row(id='field--brandColor', data-field='brandColor')
      label
        span.form-label-text Brand Colour
        input.control.control--text.form-field(type='text', name='brandColor', value=data.brandColor)
      .js-error

    .form-row(id='field--sidebarColorOverride', data-field='sidebarColorOverride')
      label
        span.form-label-text Sidebar Override Colour
        input.control.control--text.form-field(type='text', name='sidebarColorOverride', value=data.sidebarColorOverride)
      .js-error

    .form-row(id='field--shortDescription', data-field='shortDescription')
      label
        span.form-label-text Short Description
        input.control.control--text.form-field(type='text', name='shortDescription', value=data.shortDescription)
      .js-error
      .form-row-description.form-copy
        p A short description of the event, used on widgets and other places where space is limited.

    .form-row.form-row--ckeditor(id='field--description', data-field='description')
      label
        span.form-label-text Description
        textarea.control.control--text.control--multiline.js-text-editor.form-field(name='description', rows='5')= data.description
      .js-error
      .form-row-description.form-copy
