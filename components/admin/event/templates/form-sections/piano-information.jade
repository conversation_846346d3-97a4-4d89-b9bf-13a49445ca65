.panel.panel-styled
  .panel-header.js-toggle-content(style="display:flex;flex-direction:column;", data-content="piano-information")
    h2 Button Actions
    .form-copy
      p Within this section you can manage everything Button related. There is currently 1 special key. Future plan would be to introduce many more to allow for much more finegrained control over various components across the site without the need for code changes.
      ul
        li <strong>register</strong> - Used for agenda item popup CTA buttons.
        li <strong>live</strong> - Used for navigation live CTA button.
        li <strong>navigation_cta</strong> - Used for navigation CTA button (black background, white text).
        li <strong>navigation_awards_cta</strong> - Used for navigation Awards CTA button (gold background, black text).
        li <strong>navigation_logo</strong> - Used for navigation logo.

    .panel-actions(style="margin-left:auto;")
      a.btn
        span.caret

  .panel-content.js-content.piano-information-content
    .form-row(id='field--pianoResourceIds', data-field='pianoResourceIds')
      label
        span.form-label-text Piano Resource IDs
          abbr(title='This field is required') *
        .form-field
          select.control.control--choice.js-selectize(type='pianoResourceIds', name='pianoResourceIds', multiple)
            if data.pianoResourceIds
              each id in data.pianoResourceIds
                option(selected, value=id)=id
      .js-error
      .form-row-description.form-copy
        p Which Piano Resource IDs a user can access the event content with.

  .js-piano-actions.no-panel-styled
    