const moment = require('moment')
const status = require('regg')()
const visibility = require('regg')()

status

  .register('Upcoming', (startDate, endDate) => {
    var description = 'Goes live ' + moment(startDate).fromNow()
    return {
      labelClass: 'label-scheduled label--notice',
      iconClass: 'icon--clock',
      title: 'Scheduled – Future',
      description: description
    }
  })

  .register('Live', (startDate, endDate) => {
    var description = 'Event is LIVE'
    return {
      labelClass: 'label-scheduled label--live',
      iconClass: 'icon--clock',
      title: 'Scheduled – Future',
      description: description
    }
  })

  .register('Past', (startDate, endDate) => {
    var description = 'Finished ' + moment(endDate).fromNow()
    return {
      labelClass: 'label-scheduled label--inverse',
      iconClass: 'icon--clock',
      title: 'Expired',
      description: description
    }
  })

visibility

  .register('Draft', () => ({
    labelClass: 'label-status',
    iconClass: 'icon--draft',
    title: 'Draft',
    description:
      'This content is not published.<br>It is not visible on the site'
  }))

  .register('Review', () => ({
    labelClass: 'label-status label--warning',
    iconClass: 'icon--draft',
    title: 'Review',
    description:
      'This content is pending review.<br>It is not visible on the site'
  }))

  .register('Published', () => ({
    labelClass: 'label-status label--standard',
    iconClass: 'icon--published',
    title: 'Published',
    description: 'This content is visible on the site.'
  }))

module.exports = { visibility, status }
