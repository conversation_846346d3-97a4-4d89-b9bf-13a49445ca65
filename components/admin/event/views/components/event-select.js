const View = require('ventnor')

class EventSelect extends View {
  constructor(serviceLocator, selectedEvent, options) {
    super(...arguments)
    this.$el = $('<select id="event" />')
    this.$el.addClass('control control--choice')
    this.el = this.$el[0]
    this.$el.attr('placeholder', 'Select a Event')
    this.serviceLocator = serviceLocator
    this.selectedEvent = selectedEvent || null
    this.options = options || { filter: {} }
  }

  retrieveEvents(cb) {
    const query = { ...this.options.filter, eventUmbrellaId: { $ne: null } }
    this.serviceLocator.eventService.find(
      '',
      query,
      [],
      { page: 1, pageSize: 100000 },
      (err, res) => {
        if (err) return cb(err)
        const events = res && res.results
        cb(null, events)
      }
    )
  }

  initializeSelectize() {
    this.retrieveEvents((err, events) => {
      if (err) return this.serviceLocator.logger.error(err)
      events.forEach((event) => {
        this.el.selectize.addOption({
          value: event._id,
          text: event.name
        })

        this.el.selectize.addItem(this.selectedEvent)
      })
      this.el.selectize.on('change', this.updateSelection.bind(this))
    })
  }

  updateSelection() {
    this.x = this.el.selectize.getValue()
    this.emit('change', this.x)
  }

  load(keywords, cb) {
    this.retrieveEvents((err, events) => {
      if (err) return this.serviceLocator.logger.error(err)
      cb(
        events &&
          events.map((event) => {
            return {
              value: event._id,
              text: event.name
            }
          })
      )
    })
  }

  render() {
    setTimeout(() => {
      this.$el.selectize({
        delimiter: ',',
        persist: false,
        create: false,
        onInitialize: this.initializeSelectize.bind(this),
        load: this.load.bind(this),
        preload: true,
        maxItems: 1,
        maxOptions: 99999999 // Unlikely high number to show all options
      })
    }, 0)
    return this
  }
}

module.exports = EventSelect
