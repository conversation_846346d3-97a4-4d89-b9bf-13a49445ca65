extends ../../../../../widget/templates/form/base-item-repeater
.button-block
  block custom-form
    .form-row(id="field--label", data-field='label')
      label
        span.form-label-text CTA Label
          abbr(title='This field is required') *
        input.control--text.form-field(type='text', name='label', value=data.label)
        .js-error
        .form-row-description.form-copy

    .form-row(id='field--variant', data-field='variant')
      label
        span.form-label-text Variant
          abbr *
        select.control.control--choice.form-field.js-variant-select(name='variant')
          option(value='primary', selected=data.variant === 'primary') Primary
          option(value='primary--black-text', selected=data.variant === 'primary--black-text') Primary (Black Text)
          option(value='primary--awards', selected=data.variant === 'primary--awards') Primary (Awards)
          option(value='secondary', selected=data.variant === 'secondary') Secondary
          option(value='tertiary', selected=data.variant === 'tertiary') Tertiary

    .form-row(id='field--type', data-field='type')
      label
        span.form-label-text Type
          abbr *
        select.control.control--choice.form-field.js-type-select(name='type')
          option(value='') -- Please select --
          option(value='link', selected=data.type === 'link') Link
          option(value='action', selected=data.type === 'action') Action
          option(value='eventLayout', selected=data.type === 'eventLayout') Event Page
          option(value='piano', selected=data.type === 'piano') Piano

    .js-content-inputs.js-link(style="display: none;")
      include form-sections/link

    .js-content-inputs.js-action(style="display: none;")
      include form-sections/action

    .js-content-inputs.js-piano(style="display: none;")
      include form-sections/piano

    .js-content-inputs.js-eventLayout(style="display: none;")
      include form-sections/event-layout


