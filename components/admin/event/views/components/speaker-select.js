const View = require('ventnor')
const async = require('async')

class SpeakerSelect extends View {
  constructor(serviceLocator, eventId, selectedSpeaker) {
    super(...arguments)
    this.$el = $('<select id="speaker" />')
    this.$el.addClass('control control--choice')
    this.el = this.$el[0]
    this.$el.attr('placeholder', 'Select a Speaker')
    this.serviceLocator = serviceLocator
    this.selectedSpeaker = selectedSpeaker || null
    this.eventId = eventId
  }

  retrieveEventSpeakers(cb) {
    this.serviceLocator.eventService.findSpeakers(this.eventId, (err, res) => {
      if (err) return cb(err)
      const speakers = res.results
      cb(null, speakers)
    })
  }

  embellishSpeakers(speakers, cb) {
    // Get unique event IDs
    const uniqueEventIds = [
      ...new Set(speakers.map((speaker) => speaker.eventId))
    ]

    // Fetch all events in parallel
    async.map(
      uniqueEventIds,
      (eventId, callback) => {
        this.serviceLocator.eventService.read(eventId, callback)
      },
      (err, events) => {
        if (err) return cb(err)

        // Create a map of eventId to event for quick lookup
        const eventMap = {}
        events.forEach((event) => {
          eventMap[event._id] = event
        })

        // Embellish all speakers with their corresponding events
        const embellishedSpeakers = speakers.map((speaker) => ({
          ...speaker,
          event: eventMap[speaker.eventId]
        }))

        cb(null, embellishedSpeakers)
      }
    )
  }

  initializeSelectize() {
    this.retrieveEventSpeakers((err, speakers) => {
      if (err) return this.serviceLocator.logger.error(err)

      this.embellishSpeakers(speakers, (err, embellishedSpeakers) => {
        if (err) return this.serviceLocator.logger.error(err)

        embellishedSpeakers.forEach((speaker) => {
          this.el.selectize.addOption({
            value: speaker._id,
            text: speaker.event.name + ' - ' + speaker.name
          })
        })

        this.el.selectize.addItem(this.selectedSpeaker)
        this.el.selectize.on('change', this.updateSelection.bind(this))
      })
    })
  }

  updateSelection() {
    this.x = this.el.selectize.getValue()
    this.emit('change', this.x)
  }

  load(keywords, cb) {
    this.retrieveEventSpeakers((err, speakers) => {
      if (err) return this.serviceLocator.logger.error(err)

      this.embellishSpeakers(speakers, (err, embellishedSpeakers) => {
        if (err) return this.serviceLocator.logger.error(err)

        const out = embellishedSpeakers.map((speaker) => ({
          value: speaker._id,
          text: speaker.event.name + ' - ' + speaker.name
        }))

        cb(out)
      })
    })
  }

  render() {
    setTimeout(() => {
      this.$el.selectize({
        delimiter: ',',
        persist: false,
        create: false,
        onInitialize: this.initializeSelectize.bind(this),
        load: this.load.bind(this),
        preload: true,
        maxItems: 1,
        maxOptions: 99999999 // Unlikely high number to show all options
      })
    }, 0)
    return this
  }
}

module.exports = SpeakerSelect
