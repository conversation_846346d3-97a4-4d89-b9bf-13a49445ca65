const View = require('ventnor')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/./templates/list.jade'))

class ShortcutNav extends View {
  constructor(options) {
    super()
    this.serviceLocator = options.serviceLocator
    this.hooks = options.hooks || {}
  }

  setActiveLink() {
    const pathname = window.location.pathname
    const lastPathSegment = pathname.substring(pathname.lastIndexOf('/') + 1)
    this.$el.find(`.js-${lastPathSegment}`).addClass('active')
  }

  disable() {
    this.$el.find('a').attr('href', '#')
    this.$el.css('opacity', 0.5)
    this.$el.off('click', 'a')
  }

  setupLinks() {
    this.$el.on('click', 'a', (e) => {
      e.preventDefault()
      const currentPath = window.location.pathname
      const pathSegments = currentPath.split('/').filter((segment) => segment)
      const linkClass = e.currentTarget.className
      const newSegment = linkClass.split('-').slice(1).join('-')

      if (pathSegments.length > 0) {
        pathSegments[pathSegments.length - 1] = newSegment
      } else {
        pathSegments.push(newSegment)
      }

      const newPath = pathSegments.join('/')

      if (this.hooks.pre) {
        this.hooks.pre(() =>
          this.serviceLocator.router.navigate(newPath, { trigger: true })
        )
      } else {
        this.serviceLocator.router.navigate(newPath, { trigger: true })
      }
    })
  }

  render() {
    this.$el.append(template({ allowed: this.serviceLocator.allowed }))
    this.setActiveLink()
    this.setupLinks()
    return this
  }
}

module.exports = ShortcutNav
