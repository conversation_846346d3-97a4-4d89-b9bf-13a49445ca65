const compileJade = require('browjadify-compile')
const join = require('path').join
const isEqual = require('lodash.isequal')
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const debug = require('../../../../admin/source/js/lib/debug')(
  'event form view'
)
const decamelize = require('decamelize')
const capitalize = require('capitalize')
const createDarkroomUrlBuilder = require('@clocklimited/darkroom-url-builder')
const RichTextEditorInstanceManager = require('../../../../admin/source/js/lib/rich-text-editor-instance-manager')
const slugg = require('slugg')
const BaseView = require('cf-base-view')
const modal = require('modal')
const formSaveDelegate = require('../../../../admin/source/js/lib/form-save-delegate')(
  debug
)
const dateTimePickers = require('../../../../admin/source/js/lib/init-date-time-pickers')
const formCancelDelegate = require('cf-form-cancel-delegate')(debug)
const formErrorsDelegate = require('../../../../admin/source/js/lib/form-errors-delegate')(
  debug
)
const formTitleDelegate = require('../../../../admin/source/js/lib/form-title-delegate')
const AssetCollection = require('../../asset/collections/asset')
const AssetPickerView = require('../../asset/views/picker')
const getImageFactory = require('../../asset/lib/image-factory')()
const WidgetAreaView = require('../../widget/views/widget-area')
const WidgetReferenceArea = require('../views/components/widget-reference-area/form')
const ShortcutNav = require('../views/components/shortcut-nav/list')
const imageConfig = require('../../../service/event/image-config.json')
const NavigationListView = require('./sub-content-types/navigation/list')
const ItemRepeater = require('../../widget/views/item-repeater')
const createDefaultArticleLayout = require('../../../service/event/lib/default-article-layout')
const generateWidgetMetaFilter = require('./components/widget-reference-area/lib/widget-meta-filter-generator')
const CountrySelect = require('./components/location-time-zone-select/country-select')
const TimezoneSelect = require('./components/location-time-zone-select/timezone-select')
const CopyLayoutsSuccessView = require('./copy-layouts-success')
const EventPresetSelect = require('../../event-preset/views/select')
const FormFieldDescriptions = require('../../widget/views/form/form-field-descriptions')
const {
  config: validationConfig
} = require('../../../service/event/validation-config')
const EventSelect = require('./components/event-select')
const AcceptEventModal = require('./accept-event-modal')
const RejectEventModal = require('./reject-event-modal')
const config = window.config

const pianoActionRepeaterConfig = {
  itemModel: require('../models/piano-action'),
  itemView: require('./item-repeater/piano-action/item'),
  formView: require('./item-repeater/piano-action/form'),
  itemNames: {
    singular: 'Action',
    plural: 'Actions'
  }
}

const locationRepeaterConfig = {
  itemModel: require('../models/location'),
  itemView: require('./item-repeater/location/item'),
  formView: require('./item-repeater/location/form'),
  itemNames: {
    singular: 'Location',
    plural: 'Locations'
  }
}

const trackRepeaterConfig = {
  itemModel: require('../models/track'),
  itemView: require('./item-repeater/track/item'),
  formView: require('./item-repeater/track/form'),
  itemNames: {
    singular: 'Track',
    plural: 'Tracks'
  }
}

const speakerRoleRepeaterConfig = {
  itemModel: require('../models/speaker-role'),
  itemView: require('./item-repeater/speaker-role/item'),
  formView: require('./item-repeater/speaker-role/form'),
  itemNames: {
    singular: 'Role',
    plural: 'Speaker Roles'
  }
}

const sessionTypeRepeaterConfig = {
  itemModel: require('../models/session-type'),
  itemView: require('./item-repeater/session-type/item'),
  formView: require('./item-repeater/session-type/form'),
  itemNames: {
    singular: 'Session Type',
    plural: 'Session Types'
  }
}

const tierRepeaterConfig = {
  itemModel: require('../models/tier'),
  itemView: require('./item-repeater/tier/item'),
  formView: require('./item-repeater/tier/form'),
  itemNames: {
    singular: 'Tier',
    plural: 'Tiers'
  }
}

const articleCategoryRepeaterConfig = {
  itemModel: require('../models/article-category'),
  itemView: require('./item-repeater/article-category/item'),
  formView: require('./item-repeater/article-category/form'),
  itemNames: {
    singular: 'Category',
    plural: 'Categories'
  }
}

const navigationRepeaterConfig = {
  itemModel: require('../models/navigation-cta'),
  itemView: require('./item-repeater/navigation/item'),
  formView: require('./item-repeater/navigation/form'),
  itemNames: {
    singular: 'Navigation CTA',
    plural: 'Navigation CTAs'
  }
}

module.exports = BaseView.extend({
  events: {
    'click .js-save': 'handleSave',
    'click .js-cancel': 'handleCancel',
    'click .js-manage-articles': 'listEventArticles',
    'click .js-manage-speakers': 'listEventSpeakers',
    'click .js-manage-sponsors': 'listEventSponsors',
    'click .js-manage-layouts': 'listEventLayouts',
    'click .js-manage-navigation': 'listEventNavigation',
    'click .js-manage-agenda': 'listEventAgendaItems',
    'click .js-manage-videos': 'listEventVideos',
    'click .js-toggle-content': 'toggleContent',
    'click .tabs__tab': 'handleTabClick',
    'click .js-btn-review': 'handleReview',
    'click .js-btn-accept': 'handleAccept',
    'click .js-btn-reject': 'handleReject',
    submit: (e) => e.preventDefault()
  },

  initialize({ serviceLocator, title, eventUmbrellaId }) {
    this.initialModel = this.model.toJSON()
    this.formTitle = formTitleDelegate(title)
    this.eventUmbrellaId = eventUmbrellaId
    this.serviceLocator = serviceLocator
    this.id = this.model.get('_id')

    // Compatability with formErrorsDelegate
    this.showErrors = formErrorsDelegate.showErrors
    this.clearErrors = formErrorsDelegate.clearErrors

    this.initDateTimePickers = dateTimePickers(
      window.config.locale.longDateFormat.LLLL,
      this.model
    )

    this.$el.on('click', '.js-btn-publish', () => this.handlePublish())
    this.$el.on('click', '.js-btn-draft', this.confirmDraft.bind(this))
    this.$el.on('change', '[name=name]', this.generateSlug.bind(this))
    this.$el.on('click', '.js-image-add', this.handleAddImages.bind(this))
    this.$el.on(
      'change',
      '.js-navigation-action-select',
      this.handleNavigationActionChange.bind(this)
    )

    this.$el.on(
      'click',
      '.js-btn-choose-light-logo',
      this.handleChooseLightLogo.bind(this)
    )

    this.listenTo(
      this.model,
      'change:lightLogo',
      this.updateLightLogoPreview.bind(this)
    )

    this.$el.on(
      'click',
      '.js-btn-choose-dark-logo',
      this.handleChooseDarkLogo.bind(this)
    )

    this.listenTo(
      this.model,
      'change:darkLogo',
      this.updateDarkLogoPreview.bind(this)
    )

    this.$el.on(
      'click',
      '.js-btn-choose-portfolio-logo',
      this.handleChoosePortfolioLogo.bind(this)
    )

    this.listenTo(
      this.model,
      'change:portfolioLogoOverride',
      this.updatePortfolioLogoPreview.bind(this)
    )

    this.on('remove', this.destroy)

    this.richTextEditorInstanceManager = new RichTextEditorInstanceManager()

    this.on('remove', () => {
      this.richTextEditorInstanceManager.destroy()
    })

    this.renderEventPresetSelect()

    this.render()

    this.listenTo(this, 'afterAppend', this.focusForm)
  },

  confirmDraft() {
    modal({
      title: 'Revert to Draft',
      content: 'This event will be removed from public view. Are you sure?',
      buttons: [
        {
          text: 'Remain Published',
          event: 'cancel',
          className: 'btn',
          keyCodes: [27]
        },
        {
          text: 'Revert to Draft',
          event: 'confirm',
          className: 'btn btn--warning'
        }
      ]
    }).on('confirm', () => this.trigger('draft', this.model))
  },

  generateSlug() {
    const name = this.$el.find('[name=name]').val()
    const $slug = this.$el.find('[name=slug]')
    if (!$slug.val()) $slug.val(slugg(name)).change()
  },

  toggleContent(e) {
    const tabClass = e.currentTarget.attributes['data-content'].value
    this.$el.find(`.${tabClass}-content`).toggle()
  },

  handleTabClick(e) {
    e.preventDefault()
    const tabClass = e.currentTarget.attributes[`data-panel`].value
    this.$el.find('.tabs__tab.is-active').removeClass('is-active')
    this.$el.find(`.tabs__tab[data-panel=${tabClass}]`).addClass('is-active')
    this.$el
      .find(`.tabs__content.visible`)
      .removeClass(`visible`)
      .addClass(`hidden`)
    this.$el
      .find(`.tabs__content.${tabClass}`)
      .removeClass(`hidden`)
      .addClass(`visible`)
  },

  handleAddImages(e) {
    e.preventDefault()

    this.unsavedImageModels = this.unsavedImageModels || []
    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      type: 'image',
      serviceLocator: this.serviceLocator,
      unsavedModels: this.unsavedImageModels
    })

    collection.getByType('image')

    modal({
      title: 'Images',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        { text: 'Cancel', event: 'Cancel', keyCodes: [27], className: 'btn' },
        {
          text: 'Add selected images',
          event: 'add',
          className: 'btn btn--action',
          keyCodes: [13]
        }
      ]
    })
      // .on('add', this.addImages.bind(this, assetListView.selectedCollection))
      .on('add', () => {
        const images = assetListView.selectedCollection
        images.forEach((image) => {
          this.unsavedImageModels.push(image)
        })
        this.addImages(images)
      })
      .on('close', assetListView.remove.bind(assetListView))
  },

  addImages(images) {
    const ImageModel = getImageFactory('image').model
    images.map((model) => {
      const image = new ImageModel(model.attributes)
      this.model.images.add(image)
      image.setDefaultCrops(imageConfig.crops)
      return image
    })
  },

  handleChooseLightLogo() {
    this.handleChooseLogo(this.addLightLogo)
  },

  handleChooseDarkLogo() {
    this.handleChooseLogo(this.addDarkLogo)
  },

  handleChoosePortfolioLogo() {
    this.handleChooseLogo(this.addPortfolioLogo)
  },

  handleChooseLogo(addLogoFn) {
    const assets = new AssetCollection()
    const assetPicker = new AssetPickerView({
      collection: assets,
      type: 'image',
      serviceLocator: this.serviceLocator
    })

    assets.getByType('image')
    modal({
      title: 'Logo',
      className: 'modal-asset-view wide',
      content: assetPicker.$el,
      buttons: [
        { text: 'Cancel', event: 'Cancel', keyCodes: [27], className: 'btn' },
        {
          text: 'Add Selected Logo',
          event: 'add',
          className: 'btn btn--action',
          keyCodes: [13]
        }
      ]
    })
      .on('add', addLogoFn.bind(this, assetPicker.selectedCollection))
      .on('close', assetPicker.remove.bind(assetPicker))
  },

  addLightLogo(collection) {
    this.addLogo(collection, 'lightLogo')
  },

  addDarkLogo(collection) {
    this.addLogo(collection, 'darkLogo')
  },

  addPortfolioLogo(collection) {
    this.addLogo(collection, 'portfolioLogoOverride')
  },

  addLogo(collection, propertyName) {
    const data = {}
    data[propertyName] = null
    if (collection.length) {
      data[propertyName] = collection.models[0].get('binaryUri')
    }
    this.model.set(data)
  },

  updateLightLogoPreview() {
    this.updateLogoPreview('.js-light-logo-preview', 'lightLogo')
  },

  updateDarkLogoPreview() {
    this.updateLogoPreview('.js-dark-logo-preview', 'darkLogo')
  },

  updatePortfolioLogoPreview() {
    this.updateLogoPreview(
      '.js-portfolio-logo-preview',
      'portfolioLogoOverride'
    )
  },

  updateLogoPreview(selector, property) {
    this.$el.find(selector).empty()
    const uri = this.model.get(property)
    if (!uri) return

    const urlBuilder = createDarkroomUrlBuilder(
      config.darkroom.url,
      config.darkroom.salt
    )
    const img = new Image()

    img.src = urlBuilder()
      .resource(uri)
      .width(400)
      .filename('preview.jpg')
      .url()
    this.$el.find(selector).append(img)
  },

  focusForm() {
    this.$(':input[name=title]').focus()
  },

  handleNavigationActionChange(e) {
    e.preventDefault()
    const key = $(e.currentTarget).val()
    this.model.set('navigationCtaActionKey', key)
  },

  listEventNavigation() {
    this.handleUnsavedEvent('Event Navigation', () => {
      const listNavigationView = new NavigationListView({
        serviceLocator: this.serviceLocator,
        navigation: this.model.get('navigation'),
        ids: {
          event: this.id
        }
      })
      const navigationModal = modal({
        title: `Manage Event Navigation`,
        className: 'modal-view wide',
        content: listNavigationView.render().$el,
        buttons: []
      })
      listNavigationView
        .on('save', (navigation) => {
          this.model.set('navigation', navigation)
        })
        .on('close', () => {
          navigationModal.close()
          listNavigationView.remove.bind(listNavigationView)
        })
    })
  },

  listEventArticles() {
    this.handleUnsavedEvent(
      'Event Articles',
      formCancelDelegate.call(this, (err, success) => {
        if (err) this.serviceLocator.logger.error(err)
        if (success) return this.trigger('listEventArticles', this.model)
      })
    )
  },

  listEventSpeakers() {
    this.handleUnsavedEvent(
      'Event Speakers',
      formCancelDelegate.call(this, (err, success) => {
        if (err) this.serviceLocator.logger.error(err)
        if (success) return this.trigger('listEventSpeakers', this.model)
      })
    )
  },

  listEventSponsors() {
    this.handleUnsavedEvent(
      'Event Sponsors',
      formCancelDelegate.call(this, (err, success) => {
        if (err) this.serviceLocator.logger.error(err)
        if (success) return this.trigger('listEventSponsors', this.model)
      })
    )
  },

  listEventLayouts() {
    this.handleUnsavedEvent(
      'Event Layouts',
      formCancelDelegate.call(this, (err, success) => {
        if (err) this.serviceLocator.logger.error(err)
        if (success) return this.trigger('listEventLayouts', this.model)
      })
    )
  },

  listEventAgendaItems() {
    this.handleUnsavedEvent(
      'Event Agenda',
      formCancelDelegate.call(this, (err, success) => {
        if (err) this.serviceLocator.logger.error(err)
        if (success) return this.trigger('listEventAgendaItems', this.model)
      })
    )
  },

  listEventVideos() {
    this.handleUnsavedEvent(
      'Event Videos',
      formCancelDelegate.call(this, (err, success) => {
        if (err) this.serviceLocator.logger.error(err)
        if (success) return this.trigger('listEventVideos', this.model)
      })
    )
  },

  handleUnsavedEvent(title, cb) {
    if (!this.id) {
      modal({
        title,
        content:
          'You must save this event before you can update this collection',
        buttons: [{ text: 'Exit', event: 'cancel', className: 'btn' }]
      })
    } else {
      cb()
    }
  },

  handleCancel() {
    formCancelDelegate.call(this)
  },

  createNewArticleLayout(model, slug) {
    const name =
      slug.charAt(0).toUpperCase() + slug.slice(1) + ' Article Layout'
    return {
      key: `article-${slug}`,
      isBuiltIn: true,
      type: 'eventArticle',
      name,
      description: `The layout for every ${slug} article`,
      slug,
      layout: createDefaultArticleLayout().layout,
      visible: true
    }
  },

  handleEventBaseSlugChange() {
    const newSlugs = this.model.get('articleBaseSlugs')
    const oldSlugs = this.initialModel.articleBaseSlugs
    const slugsToAdd = newSlugs.filter((slug) => !oldSlugs.includes(slug))
    const slugsToRemove = oldSlugs.filter((slug) => !newSlugs.includes(slug))
    const currentLayouts = { ...this.model.get('layouts') }
    slugsToAdd.forEach((slug) =>
      this.trigger(
        'updateLayout',
        this.model,
        this.createNewArticleLayout(this.model, slug)
      )
    )
    slugsToRemove.forEach((slug) => {
      this.trigger(
        'deleteLayout',
        this.model,
        currentLayouts[`article-${slug}`]
      )
    })
  },

  // handleTierChange(value) {
  //   const tiers = value.map((tier, index) => {
  //     return { name: tier, rank: index + 1 }
  //   })
  //   this.model.set('tiers', tiers)
  // },

  handleSave() {
    if (
      !isEqual(
        this.model.get('articleBaseSlugs'),
        this.initialModel.articleBaseSlugs
      )
    ) {
      this.handleEventBaseSlugChange()
    }

    // Handle timezone conversion
    const startDate = this.model.get('startDate')
    const endDate = this.model.get('endDate')

    if (startDate && endDate) {
      const startTimezoneOffset = startDate.getTimezoneOffset()
      const endTimezoneOffset = endDate.getTimezoneOffset()

      this.model.set(
        'startDateISOWithoutTZ',
        new Date(
          startDate.getTime() - startTimezoneOffset * (60 * 1000)
        ).toISOString()
      )

      this.model.set(
        'endDateISOWithoutTZ',
        new Date(
          endDate.getTime() - endTimezoneOffset * (60 * 1000)
        ).toISOString()
      )
    }

    this.model.set('eventUmbrellaId', this.eventUmbrellaId)

    // update original assets with new fingerprint
    formSaveDelegate.call(this, debug, (err, savedModel) => {
      if (err) {
        this.showErrors(err.errors)
        return this.serviceLocator.logger.error(err)
      }
      if (!savedModel) return
      // eslint-disable-next-line no-console
      console.log('savedModel (intial)', savedModel)

      // standard bits
      if (this.unsavedChanges) this.unsavedChanges = false
      debug('saving model success', savedModel.toJSON())

      // handle if no changes were made

      // probably abstract fingerprinting out into lib

      // action unique stuff
      if (!savedModel.get('eventPresetId'))
        return this.trigger('save', savedModel)

      if (!this.serviceLoctor.allow('event', 'manageLayouts'))
        return this.trigger('save', savedModel)

      this.serviceLocator.eventService.findLayouts(
        savedModel.get('_id'),
        { filter: { eventId: savedModel._id, isBuiltIn: false } },
        (err, layouts) => {
          if (err) {
            this.showErrors(err.errors)
            return this.serviceLocator.logger.error(err)
          }
          if (layouts.length !== 0) return this.trigger('save', savedModel)

          // Create modal which asks if they want to copy the layouts from the eventPreset.useEventId (which would the the layouts which the event (eventPreset.useUserId) have eventId set to)
          return modal({
            title: 'Save Success ✅',
            content:
              'Would you like to copy the layouts from the event preset?',
            buttons: [
              {
                text: 'Close',
                event: 'no',
                className: 'btn',
                keyCodes: [27]
              },
              {
                text: 'Copy Layouts',
                event: 'yes',
                className: 'btn btn--action',
                keyCodes: [13]
              }
            ]
          })
            .on('yes', () => {
              this.serviceLocator.eventPresetService.copyLayouts(
                savedModel.get('eventPresetId'),
                savedModel.get('_id'),
                (err, allLayouts) => {
                  const successModalView = new CopyLayoutsSuccessView()
                  if (err) {
                    this.showErrors(err.errors)
                    return this.serviceLocator.logger.error(err)
                  }
                  modal({
                    title: 'Copied Layouts',
                    content: successModalView.render(allLayouts).$el,
                    buttons: []
                  })
                  successModalView.on('close', () => {
                    this.trigger('save', savedModel)
                  })
                  successModalView.on('open', () => {
                    window.location.href = window.location.pathname.replace(
                      'form',
                      'layouts'
                    )
                  })
                }
              )
            })
            .on('no', () => {
              this.trigger('save', savedModel)
            })
        }
      )
    })
  },

  handlePublish() {
    modal({
      title: 'Publish Event',
      content:
        'This event will be publically accessible. Are you sure? Note this task does not save the event. Please click save before publishing.',
      buttons: [
        {
          text: 'Remain in Draft',
          event: 'cancel',
          className: 'btn',
          keyCodes: [27]
        },
        {
          text: 'Confirm Publish',
          event: 'confirm',
          className: 'btn btn--action'
        }
      ]
    }).on('confirm', () => this.trigger('publish', this.model))
  },

  handleReview() {
    modal({
      title: 'Submit for Review',
      content:
        'This event will be submitted for review. You will not be able to publish it directly until it is approved.',
      buttons: [
        {
          text: 'Cancel',
          event: 'cancel',
          className: 'btn',
          keyCodes: [27]
        },
        {
          text: 'Submit for Review',
          event: 'confirm',
          className: 'btn btn--action'
        }
      ]
    }).on('confirm', () =>
      this.updateStateWithLog('Review', 'Event submitted for review')
    )
  },

  handleAccept() {
    const acceptModal = new AcceptEventModal({
      serviceLocator: this.serviceLocator
    })

    const modalInstance = modal({
      title: 'Accept & Publish Event',
      content: acceptModal.render().$el,
      buttons: [],
      className: 'modal--center modal--medium',
      clickOutsideToClose: false
    })

    acceptModal.on('cancel', () => {
      modalInstance.close()
    })

    acceptModal.on('confirm', (data) => {
      modalInstance.close()
      this.updateStateWithLog('Published', data.note)
    })
  },

  handleReject() {
    const rejectModal = new RejectEventModal({
      serviceLocator: this.serviceLocator
    })

    const modalInstance = modal({
      title: 'Reject Event',
      content: rejectModal.render().$el,
      buttons: [],
      className: 'modal--center modal--medium',
      clickOutsideToClose: false
    })

    rejectModal.on('cancel', () => {
      modalInstance.close()
    })

    rejectModal.on('confirm', (data) => {
      modalInstance.close()
      this.updateStateWithLog('Draft', data.note)
    })
  },

  updateStateWithLog(newState, note) {
    const currentStateLog = this.model.get('stateLog') || []
    const newLogEntry = {
      state: newState,
      timestamp: new Date(),
      note: note
    }

    const updatedStateLog = [...currentStateLog, newLogEntry]

    this.model.set({
      state: newState,
      stateLog: updatedStateLog
    })

    this.handleSave(() => {
      // Refresh the form to show updated buttons
      this.render()
    })
  },

  renderShortcutNav() {
    const shortcutNav = new ShortcutNav({
      serviceLocator: this.serviceLocator,
      hooks: {
        pre: (cb) => {
          formCancelDelegate.call(this, (err, success) => {
            if (err) this.serviceLocator.logger.error(err)
            if (success) return cb()
          })
        }
      }
    })
    this.$el.find('.js-shortcut-nav').append(shortcutNav.render().$el)
    if (!this.model.get('_id')) {
      shortcutNav.disable()
    }
  },

  renderWidgetReferences() {
    const widgetReferenceArea = new WidgetReferenceArea(
      this.serviceLocator,
      this.model,
      {
        imageConfig,
        filter: generateWidgetMetaFilter(this.model, 'event')
      }
    )
    this.$el
      .find('.js-widget-reference-area')
      .append(widgetReferenceArea.render().$el)
  },

  renderDateTimePicker() {
    this.dateTimePickers = this.$el
      .find('.js-date-time-picker')
      .toArray()
      .map(this.initDateTimePickers)
  },

  renderImageWidgetArea() {
    this.imageArea = new WidgetAreaView({
      model: this.model.images,
      receiveDrops: false,
      serviceLocator: this.serviceLocator,
      widgetItemOptions: imageConfig
    })
    this.$el.find('.js-image-area').append(this.imageArea.$el)
  },

  renderItemRepeater() {
    this.locationRepeater = new ItemRepeater(
      this.serviceLocator,
      locationRepeaterConfig,
      this.model.get('locations'),
      'Add Location'
    )

    this.trackRepeater = new ItemRepeater(
      this.serviceLocator,
      trackRepeaterConfig,
      this.model.get('tracks'),
      'Add Track'
    )

    this.speakerRoleRepeater = new ItemRepeater(
      this.serviceLocator,
      speakerRoleRepeaterConfig,
      this.model.get('speakerRoles'),
      'Add Role'
    )

    this.sessionTypeRepeater = new ItemRepeater(
      this.serviceLocator,
      sessionTypeRepeaterConfig,
      this.model.get('sessionTypes'),
      'Add Session Type'
    )

    this.tierRepeater = new ItemRepeater(
      this.serviceLocator,
      tierRepeaterConfig,
      this.model.get('tiers'),
      'Add Tier'
    )

    this.articleCategoryRepeater = new ItemRepeater(
      this.serviceLocator,
      articleCategoryRepeaterConfig,
      this.model.get('articleCategories'),
      'Add Category'
    )

    this.pianoActionRepeater = new ItemRepeater(
      this.serviceLocator,
      pianoActionRepeaterConfig,
      this.model.get('pianoActions'),
      'Add Action'
    )

    this.locationRepeater.on('itemsUpdated', () => {
      this.model.set('locations', this.locationRepeater.getItems())
    })

    this.trackRepeater.on('itemsUpdated', () => {
      this.model.set('tracks', this.trackRepeater.getItems())
    })

    this.speakerRoleRepeater.on('itemsUpdated', () => {
      this.model.set('speakerRoles', this.speakerRoleRepeater.getItems())
    })

    this.tierRepeater.on('itemsUpdated', () => {
      this.model.set('tiers', this.tierRepeater.getItems())
    })

    this.sessionTypeRepeater.on('itemsUpdated', () => {
      this.model.set('sessionTypes', this.sessionTypeRepeater.getItems())
    })

    this.articleCategoryRepeater.on('itemsUpdated', () => {
      this.model.set(
        'articleCategories',
        this.articleCategoryRepeater.getItems()
      )
    })

    this.pianoActionRepeater.on('itemsUpdated', () => {
      this.model.set('pianoActions', this.pianoActionRepeater.getItems())
    })

    // this.$el.find('.js-tracks').empty().append(this.trackRepeater.render().$el)
    this.$el
      .find('.js-speaker-roles')
      .empty()
      .append(this.speakerRoleRepeater.render().$el)
    this.$el
      .find('.js-session-types')
      .empty()
      .append(this.sessionTypeRepeater.render().$el)
    this.$el.find('.js-tiers').empty().append(this.tierRepeater.render().$el)
    this.$el
      .find('.js-locations')
      .empty()
      .append(this.locationRepeater.render().$el)
    this.$el
      .find('.js-article-categories')
      .empty()
      .append(this.articleCategoryRepeater.render().$el)
    this.$el
      .find('.js-piano-actions')
      .empty()
      .append(this.pianoActionRepeater.render().$el)

    this.navigationCtaRepeater = new ItemRepeater(
      this.serviceLocator,
      navigationRepeaterConfig,
      this.model.get('navigationCtas'),
      'Add CTA'
    )

    this.navigationCtaRepeater.on('itemsUpdated', () => {
      this.model.set('navigationCtas', this.navigationCtaRepeater.getItems())
    })

    this.$el
      .find('.js-navigation-cta')
      .empty()
      .append(this.navigationCtaRepeater.render().$el)
  },

  renderSelects() {
    this.$el.find('.js-selectize').each((i, el) => {
      $(el).selectize({
        delimiter: ',',
        createOnBlur: true,
        create: true,
        onChange: (value) => {
          // if ($(el).hasClass('js-tiers')) {
          //   return this.handleTierChange(value)
          // }
        },
        onInitialize: () => {
          el.selectize.on('change', () => {
            this.model.set(el.name, el.selectize.getValue())
          })
        }
      })
    })
  },

  renderCountrySelect() {
    this.countrySelect = new CountrySelect(
      this.serviceLocator,
      this.model.get('country')
    )
    this.$el.find('.js-country-select').empty().append(this.countrySelect.$el)
    this.countrySelect.on('change', (country) => {
      this.model.set('country', country)
      this.renderTimezoneSelect(country, null)
    })
  },

  renderTimezoneSelect(country, timezone) {
    this.timezoneSelect = new TimezoneSelect(
      this.serviceLocator,
      country,
      timezone
    )
    this.$el.find('.js-timezone-select').empty().append(this.timezoneSelect.$el)
    this.timezoneSelect.on('change', (tz) => {
      this.model.set('timezone', tz)
    })
  },

  renderEventPresetSelect() {
    // Destroy existing control if it exists to prevent memory leaks
    if (this.eventPresetSelectControl) {
      this.eventPresetSelectControl.remove()
    }

    this.eventPresetSelectControl = new EventPresetSelect(
      this.serviceLocator,
      this.model.get('eventPresetId')
    )

    this.$('.js-event-preset-select')
      .empty()
      .append(this.eventPresetSelectControl.render().$el)

    this.eventPresetSelectControl.on('change', (value) => {
      this.model.set('eventPresetId', value)
      return this.handleEventPresetChange(value)
    })
  },

  handleEventPresetChange(value) {
    this.serviceLocator.eventPresetService.read(value, (err, em) => {
      if (err) {
        this.serviceLocator.logger.error(err)
        return
      }

      if (!this.model.get('_id')) {
        const defaults = {
          speakerRoles: em.defaults.speakerRoles,
          locations: em.defaults.locations,
          tracks: em.defaults.tracks,
          sessionTypes: em.defaults.sessionTypes,
          articleBaseSlugs: em.defaults.articleBaseSlugs,
          articleCategories: em.defaults.articleCategories,
          tiers: em.defaults.tiers,
          pianoActions: em.defaults.pianoActions,
          navigation: em.defaults.navigation
        }

        this.model.set(defaults)
        this.render()

        const getPropertyName = (name) =>
          capitalize.words(decamelize(name, ' '))

        modal({
          title: `✅ ${em.name} Preset Applied`,
          content: `${
            em.name
          } defaults have been applied to this event: (${Object.keys(defaults)
            .map(getPropertyName)
            .join(', ')}).`,
          buttons: [{ text: 'Close', className: 'btn' }]
        })
      }

      if (
        this.eventPresetSelectControl &&
        this.eventPresetSelectControl.el.selectize
      ) {
        this.eventPresetSelectControl.el.selectize.setValue(value, true)
      }
    })
  },

  renderFormDescriptions() {
    const { Event } = validationConfig
    const properties = Object.keys(Event).map((key) => ({
      name: key,
      maxLength: Event[key],
      initialValue: this.model.get(key)
    }))

    new FormFieldDescriptions(
      this.serviceLocator,
      this.$el,
      properties
    ).render()
  },

  renderEventSelect() {
    this.eventSelect = new EventSelect(
      this.serviceLocator,
      this.model.get('navigationProxyEventId')
    )
    this.$el
      .find('.js-event-select')
      .empty()
      .append(this.eventSelect.render().$el)
    this.eventSelect.on('change', (value) => {
      this.model.set('navigationProxyEventId', value)
    })
  },

  render() {
    // Store scroll position
    const scrollPosition = window.scrollY

    if (this.richTextEditorInstanceManager) {
      this.richTextEditorInstanceManager.destroy()
    }

    // Clear existing repeaters and controls
    this.cleanup()

    const data = this.model.toJSON()
    const country = data.country
    const timezone = data.timezone

    const hasBeenPublished =
      data.stateLog.some((log) => log.state === 'Published') ||
      data.state === 'Published'

    this.$el.empty().append(
      template({
        data,
        title: this.formTitle(this.model.toJSON()),
        config: config,
        allowed: this.serviceLocator.allowed,
        showEventPreset: !data._id && !data.eventPresetId,
        hasBeenPublished
      })
    )

    // Re-render all components
    this.$el.find('.js-text-editor').each((i, el) => {
      this.richTextEditorInstanceManager.create(el, { height: 150 }, el.id)
    })

    this.renderCountrySelect(country)
    this.renderTimezoneSelect(country, timezone)
    this.renderEventSelect()
    this.renderImageWidgetArea()
    this.updateLightLogoPreview()
    this.updateDarkLogoPreview()
    this.updatePortfolioLogoPreview()
    this.renderShortcutNav()
    this.renderWidgetReferences()
    this.renderDateTimePicker()
    this.renderItemRepeater()
    this.renderSelects()
    this.renderFormDescriptions()
    this.renderItemRepeater()

    this.hydrate()

    // Restore scroll position
    window.scrollTo(0, scrollPosition)

    return this
  },

  hydrate() {
    const eventPresetId = this.model.get('eventPresetId')
    if (!eventPresetId) return
    this.serviceLocator.eventPresetService.read(eventPresetId, (err, em) => {
      if (err) return this.serviceLocator.logger.error(err)
      this.$el.find('.js-event-preset-name').text(em.name)
    })
  },

  cleanup() {
    // Cleanup existing repeaters
    const repeaters = [
      'locationRepeater',
      'trackRepeater',
      'speakerRoleRepeater',
      'sessionTypeRepeater',
      'tierRepeater',
      'articleCategoryRepeater',
      'pianoActionRepeater'
    ]

    repeaters.forEach((repeater) => {
      if (this[repeater]) {
        this[repeater].remove()
        this[repeater] = null
      }
    })

    // Cleanup other controls
    if (this.eventPresetSelectControl) {
      this.eventPresetSelectControl.remove()
    }
  }
})
