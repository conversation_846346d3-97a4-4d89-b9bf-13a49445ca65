const compileJade = require('browjadify-compile')
const join = require('path').join
const modal = require('modal')
const template = compileJade(join(__dirname, '/../templates/list.jade'))
const ListItemView = require('./list-item')
const ListFilterView = require('./list-filter')
const GenericListView = require('../../../../admin/source/js/lib/generic/list-view')
const extend = require('lodash.assign')
const listDeleteDelegate = require('../../../../admin/source/js/lib/list-delete-delegate')
const async = require('async')
const config = window.config
const CopyEventModal = require('./copy-event-modal')

module.exports = GenericListView.extend({
  events: extend({}, GenericListView.prototype.events, {
    'click .js-more': 'loadMore',
    'click .js-sync': 'syncWidgets',
    'click .js-edit-order': 'handleEditOrder',
    'click .js-back': 'handleBack',
    'click .js-copy': 'handleCopy'
  }),

  template,

  initialize() {
    GenericListView.prototype.initialize.apply(this, arguments)
    this.renderFilters()
    this.listenTo(this.collection, 'filter', this.maintainOrder)
    this.listenTo(this.collection, 'filter', () => {
      this.trigger('clearSelection')
    })
  },

  handleDelete() {
    const articles = []
    const checkArticles = (model, cb) => {
      this.options.serviceLocator.articleService.find(
        '',
        { eventId: model.get('_id') },
        [],
        {},
        (err, res) => {
          if (err) return cb(err)
          if (res.results.length > 0) {
            articles.push(res.results)
          }
          cb()
        }
      )
    }
    async.each(this.selectedCollection.models, checkArticles, () => {
      const flatArticles = articles.flat()
      if (flatArticles.length > 0) {
        modal({
          title: 'Cannot Delete Event',
          content:
            'You must update all articles associated with this event before deleting it.' +
            '\n' +
            'The following articles need to be updated:' +
            '\n' +
            flatArticles.map((a) => a.headline).join(', \n'),
          buttons: [{ text: 'Exit', event: 'cancel', className: 'btn' }]
        })
      } else {
        listDeleteDelegate.call(this, this.selectedCollection)
      }
    })
  },

  renderFilters() {
    this.filters = new ListFilterView()
    this.filters.on('filter', this.applyFilters, this)
    this.$('.js-filters').append(this.filters.$el)
  },

  applyFilters(params) {
    this.collection.applyFilter(params)
  },

  addLabelRow() {
    this.$('.js-items').prepend(
      '<tr><th>Status</th><th>Start Date</th><th>Name</th><th>Actions</th></tr>'
    )
  },

  handleEditOrder() {
    this.trigger('reorder')
  },

  handleBack() {
    this.options.serviceLocator.router.navigate(`event-umbrellas`, {
      trigger: true
    })
  },

  handleCopy() {
    const copyEventModal = new CopyEventModal({
      serviceLocator: this.options.serviceLocator,
      eventUmbrellaId: this.options.ids.eventUmbrella
    })

    const modalInstance = modal({
      title: 'Copy Event',
      content: copyEventModal.render().$el,
      buttons: [],
      className: 'modal--center modal--medium',
      clickOutsideToClose: false
    })

    copyEventModal.on('cancel', () => {
      modalInstance.close()
    })

    copyEventModal.on('confirm', (data) => {
      modalInstance.close()

      // Show confirmation modal
      modal({
        title: 'Confirm Copy',
        content: `Are you sure you want to copy "${data.sourceEventName}"? This will create a new draft event with all layouts copied.`,
        buttons: [
          {
            text: 'Cancel',
            event: 'cancel',
            className: 'btn',
            keyCodes: [27]
          },
          {
            text: 'Copy Event',
            event: 'confirm',
            className: 'btn btn--action',
            keyCodes: [13]
          }
        ]
      }).on('confirm', () => {
        this.performCopy(data.sourceEventId)
      })
    })
  },

  performCopy(sourceEventId) {
    this.options.serviceLocator.eventService.copyEvent(
      sourceEventId,
      this.options.ids.eventUmbrella,
      (err, newEvent) => {
        if (err) {
          modal({
            title: 'Copy Failed',
            content: 'Failed to copy event. Please try again.',
            buttons: [{ text: 'Close', className: 'btn' }]
          })
          return this.options.serviceLocator.logger.error(err)
        }

        modal({
          title: 'Copy Successful',
          content: `Event "${newEvent.name}" has been created successfully.`,
          buttons: [
            { text: 'Stay Here', event: 'stay', className: 'btn' },
            {
              text: 'Edit New Event',
              event: 'edit',
              className: 'btn btn--action'
            }
          ]
        })
          .on('edit', () => {
            this.options.serviceLocator.router.navigate(
              `event-umbrellas/${this.options.ids.eventUmbrella}/events/${newEvent._id}/form`,
              { trigger: true }
            )
          })
          .on('stay', () => {
            // Refresh the list to show the new event
            this.collection.load()
          })
      }
    )
  },

  createListItemView(model) {
    const listItemView = new ListItemView({
      model: model,
      serviceLocator: this.options.serviceLocator,
      ids: this.options.ids
    })
    listItemView.on('preview', (instance, options = {}) =>
      this.trigger('preview', instance, model, options)
    )
    listItemView.on('listEventArticles', (model) => {
      this.trigger('listEventArticles', model)
    })
    listItemView.on('listEventSpeakers', (model) => {
      this.trigger('listEventSpeakers', model)
    })
    listItemView.on('listEventSponsors', (model) => {
      this.trigger('listEventSponsors', model)
    })
    listItemView.on('listEventLayouts', (model) => {
      this.trigger('listEventLayouts', model)
    })
    listItemView.on('listEventAgendaItems', (model) => {
      this.trigger('listEventAgendaItems', model)
    })
    listItemView.on('listEventVideos', (model) => {
      this.trigger('listEventVideos', model)
    })
    listItemView.on('listEventMobileApp', (model) => {
      this.trigger('listEventMobileApp', model)
    })
    return listItemView
  },

  loadMore() {
    this.collection.loadMore()
  },

  syncWidgets() {
    this.options.serviceLocator.widgetMetaService.sync((err, message) => {
      if (err) {
        modal({
          title: 'Sync Faild',
          content: 'Check the console for more information.',
          buttons: [{ text: 'Dismiss', className: 'btn' }]
        })
        return this.options.serviceLocator.logger.error(err)
      }
      modal({
        title: 'Sync Success',
        content: message,
        buttons: [{ text: 'Dismiss', className: 'btn' }]
      })
    })
  },

  render() {
    this.$el.empty().append(
      this.template({
        config,
        displayName: this.options.displayName,
        allowed: this.options.serviceLocator.allowed
      })
    )
    this.addLabelRow()
    return this
  }
})
