const join = require('path').join
const compileJade = require('browjadify-compile')
const template = compileJade(
  join(__dirname, '/../templates/reject-event-modal.jade')
)

module.exports = Backbone.View.extend({
  events: {
    'click .js-cancel': 'handleCancel',
    'click .js-confirm': 'handleConfirm'
  },

  initialize(options) {
    this.serviceLocator = options.serviceLocator
  },

  handleCancel() {
    this.trigger('cancel')
  },

  handleConfirm() {
    const note = this.$('.js-reject-note').val().trim()

    if (!note) {
      this.$('.js-error-message').show()
      return
    }

    this.$('.js-error-message').hide()
    this.trigger('confirm', { note: `Event rejected: ${note}` })
  },

  render() {
    this.$el.empty().append(template({}))
    return this
  }
})
