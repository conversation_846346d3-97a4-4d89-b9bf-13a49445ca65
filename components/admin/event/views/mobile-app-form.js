const join = require('path').join
const compileJade = require('browjadify-compile')
const RichTextEditorInstanceManager = require('../../../../admin/source/js/lib/rich-text-editor-instance-manager')
const modal = require('modal')
const _ = require('lodash')
const template = compileJade(
  join(__dirname, '/../templates/mobile-app-form.jade')
)
const debug = require('../../../../admin/source/js/lib/debug')(
  'mobile app form view'
)
const formSaveDelegate = require('../../../../admin/source/js/lib/form-save-delegate')(
  debug
)

module.exports = Backbone.View.extend({
  initialize(options) {
    this.serviceLocator = options.serviceLocator
    this.model = options.model
    this.eventUmbrellaId = options.eventUmbrellaId
    this.eventId = options.eventId

    this.richTextEditorInstanceManager = new RichTextEditorInstanceManager()
    this.on('remove', () => {
      this.richTextEditorInstanceManager.destroy()
    })

    this.render()
    this.watchFields()

    this.initialFields = this.getInputs()

    this.$el.on('click', '.js-save', this.handleSave.bind(this))
    this.$el.on('click', '.js-back', this.handleBack.bind(this))
    this.$el.on('click', '.js-open-event', this.handleOpenEvent.bind(this))
  },

  handleBack() {
    this.preventLostChanges(() => {
      this.serviceLocator.router.navigate(
        `event-umbrellas/${this.eventUmbrellaId}/events/`,
        { trigger: true }
      )
    })
  },

  handleOpenEvent() {
    this.preventLostChanges(() => {
      this.serviceLocator.router.navigate(
        `event-umbrellas/${this.eventUmbrellaId}/events/${this.eventId}/form`,
        { trigger: true }
      )
    })
  },

  preventLostChanges(cb) {
    const currentFields = this.getInputs()
    const hasChanged = !_.isEqual(this.initialFields, currentFields)
    if (hasChanged) {
      return modal({
        title: 'You have unsaved changes',
        content:
          'Would you like to continue editing, or discard these changes?',
        buttons: [
          {
            text: 'Continue editing',
            event: 'continue',
            className: 'btn btn--success',
            keyCodes: [27]
          },
          { text: 'Discard changes', event: 'discard', className: 'btn' }
        ]
      }).on('discard', () => {
        cb()
      })
    }

    cb()
  },

  watchFields() {
    this.$el.on('change', 'input', (event) => {
      const target = event.target
      const name = target.name
      let $video, $img
      switch (name) {
        case 'homepageSplashVideoUrl':
          // Rerender video preview
          $video = this.$el.find('.js-homepageSplashVideoUrl--preview source')
          $video.attr('src', target.value)
          $video.parent().get(0).load()
          $video.parent().get(0).play()
          break
        case 'whiteUmbrellaLogoUrl':
          $img = this.$el.find('.js-whiteUmbrellaLogoUrl--preview')
          $img.attr('src', target.value)
          $img.parent().get(0).load()
          break
        case 'whiteBrandLogoUrl':
          $img = this.$el.find('.js-whiteBrandLogoUrl--preview')
          $img.attr('src', target.value)
          $img.parent().get(0).load()
          break
        case 'darkCombinedLogoUrl':
          $img = this.$el.find('.js-darkCombinedLogoUrl--preview')
          $img.attr('src', target.value)
          $img.parent().get(0).load()
          break
        default:
          break
      }
    })
  },

  handleSave() {
    const data = this.getInputs()
    this.model.set('appOnly', data)
    formSaveDelegate.call(this, debug, (err, savedModel) => {
      if (err) {
        this.showErrors(err.errors)
        this.serviceLocator.logger.error(err)
        return modal({
          title: 'Save Failed',
          content: 'An error occurred while saving. Please try again.',
          buttons: [
            {
              text: 'OK',
              event: 'confirm',
              className: 'btn btn--action',
              keyCodes: [27]
            }
          ]
        })
      }
      if (!savedModel) return
      this.serviceLocator.logger.info('Saved', savedModel.toJSON())
      modal({
        title: 'Success',
        content: 'Mobile app settings saved successfully.',
        buttons: [
          {
            text: 'OK',
            event: 'confirm',
            className: 'btn btn--action',
            keyCodes: [27]
          }
        ]
      })
      this.initialFields = this.getInputs()
    })
  },

  getInputs() {
    const inputs = this.$el.find('form input, form textarea')
    const data = {}

    inputs.each((index, element) => {
      const $element = $(element)
      const name = $element.attr('name')
      if (name) {
        data[name] = $element.val()
      }
    })

    return data
  },

  renderRichTextEditor() {
    // Re-render all components
    this.$el.find('.js-text-editor').each((i, el) => {
      this.richTextEditorInstanceManager.create(el, { height: 150 }, el.id)
    })
    return this
  },

  render() {
    if (this.richTextEditorInstanceManager) {
      this.richTextEditorInstanceManager.destroy()
    }
    const appOnlyData = this.model.get('appOnly') || {}
    this.$el.append(
      template({
        data: appOnlyData,
        allowed: this.serviceLocator.allowed
      })
    )
    this.renderRichTextEditor()
    return this
  }
})

/**
 * homepageSplashVideoUrl
 * broughtToYouByTitle
 * broughtToYouByDescription
 * whiteUmbrellaLogoUrl
 * whiteBrandLogoUrl
 * darkCombinedLogoUrl
 */

/**
 * - API needs to send instanceUrl if not already (for <url>/magazines for instances)
 */
