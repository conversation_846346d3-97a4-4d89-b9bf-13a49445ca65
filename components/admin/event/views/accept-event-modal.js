const join = require('path').join
const compileJade = require('browjadify-compile')
const template = compileJade(
  join(__dirname, '/../templates/accept-event-modal.jade')
)

module.exports = Backbone.View.extend({
  events: {
    'click .js-cancel': 'handleCancel',
    'click .js-confirm': 'handleConfirm'
  },

  initialize(options) {
    this.serviceLocator = options.serviceLocator
  },

  handleCancel() {
    this.trigger('cancel')
  },

  handleConfirm() {
    const note =
      this.$('.js-accept-note').val().trim() || 'Event approved and published'
    this.trigger('confirm', { note })
  },

  render() {
    this.$el.empty().append(template({}))
    return this
  }
})
