const config = window.config
const compileJade = require('browjadify-compile')
const join = require('path').join
const stateMap = require('../lib/state-map')
const template = compileJade(join(__dirname, '/../templates/list-item.jade'))
const createImageUrlBuilder = require('cf-image-url-builder')
const createLayoutGenerator = require('../../../service/event/generators/layout-generator.js')
const InstanceSelectView = require('./list-item-instance-select')
const modal = require('modal')
const moment = require('moment')

module.exports = Backbone.View.extend({
  className: '',

  initialize(options) {
    this.options = options
    this.eventUmbrellaId = this.options.ids.eventUmbrella
    this.layoutGenerator = createLayoutGenerator(
      this.options.serviceLocator,
      this.model.get('_id')
    )
    this.listenTo(this.model, 'select', this.select)
    this.listenTo(this.model, 'deSelect', this.deSelect)
    this.render()
    this.$el = this.$el.find('tr')
    this.$el.on('click', this.editWithNoPropagation.bind(this))
    this.$el.on('click', '.js-edit', this.edit.bind(this))
    this.$el.on('click', '.js-select', this.toggleSelect.bind(this))
    this.$el.on(
      'click',
      '.js-manage-articles',
      this.listEventArticles.bind(this)
    )
    this.$el.on(
      'click',
      '.js-manage-speakers',
      this.listEventSpeakers.bind(this)
    )
    this.$el.on(
      'click',
      '.js-manage-sponsors',
      this.listEventSponsors.bind(this)
    )
    this.$el.on('click', '.js-manage-layouts', this.listEventLayouts.bind(this))
    this.$el.on(
      'click',
      '.js-manage-agenda',
      this.listEventAgendaItems.bind(this)
    )
    this.$el.on(
      'click',
      '.js-manage-mobile-app',
      this.listEventMobileApp.bind(this)
    )
    this.$el.on('click', '.js-manage-videos', this.listEventVideos.bind(this))
  },

  preview(options = {}) {
    this.options.serviceLocator.eventUmbrellaService.read(
      this.eventUmbrellaId,
      (err, eventUmbrella) => {
        if (err) return this.options.serviceLocator.logger.error(err)
        const instanceSelectView = new InstanceSelectView({
          serviceLocator: this.options.serviceLocator,
          instances: [
            eventUmbrella.primaryInstance,
            ...eventUmbrella.secondaryInstances
          ]
        })
        const instanceModal = modal({
          title: 'Select Instance',
          content: instanceSelectView.render().$el,
          buttons: []
        })
        instanceSelectView.on('preview', (instance) => {
          instanceModal.close()
          return this.trigger('preview', instance, options)
        })
        instanceSelectView.on('cancel', () => instanceModal.close())
      }
    )
  },

  editWithNoPropagation(e) {
    if (e.which === 2 || e.metaKey) return
    e.stopPropagation()
    // Hack to prevent edit from being triggered when clicking other elements
    if (e.target.tagName !== 'TD' && e.target.tagName !== 'IMG') return
    this.trigger('edit')
  },

  edit(e) {
    // Detect middle click or CMD click to allow <a> to open in new tab
    if (e.which === 2 || e.metaKey) return
    e.preventDefault()
    this.trigger('edit')
  },

  setupGenerateLayouts() {
    this.layoutGenerator.isReady((err, isReady, info) => {
      if (err) return this.options.serviceLocator.logger.error(err)
      if (isReady) {
        this.$el.find('.js-generate-layouts').addClass('btn--success')
        this.$el.find('.js-generate-layouts').attr('disabled', false)
        this.$el.find('.js-generate-layouts').on('click', (e) => {
          e.preventDefault()
          this.generateLayouts()
        })
      } else {
        this.$el.find('.js-generate-layouts').addClass('btn--info')
        this.$el.find('.js-generate-layouts').attr('disabled', false)
        this.$el.find('.js-generate-layouts').css('opacity', '0.5')
        this.$el.find('.js-generate-layouts').on('click', (e) => {
          e.preventDefault()
          const message = info.messages.join('\n')
          alert(message)
        })
      }
    })
  },

  generateLayouts() {
    this.layoutGenerator.generate((err, res) => {
      if (err) return this.options.serviceLocator.logger.error(err)
      this.$el.find('.js-generate-layouts').removeClass('btn--success')
      this.$el.find('.js-generate-layouts').addClass('btn--info')
      this.$el.find('.js-generate-layouts').attr('disabled', true)
    })
  },

  listEventArticles(e) {
    e.preventDefault()
    this.trigger('listEventArticles', this.model)
  },

  listEventSpeakers(e) {
    e.preventDefault()
    this.trigger('listEventSpeakers', this.model)
  },

  listEventSponsors(e) {
    e.preventDefault()
    this.trigger('listEventSponsors', this.model)
  },

  listEventLayouts(e) {
    e.preventDefault()
    this.trigger('listEventLayouts', this.model)
  },

  listEventAgendaItems(e) {
    e.preventDefault()
    this.trigger('listEventAgendaItems', this.model)
  },

  listEventMobileApp(e) {
    e.preventDefault()
    this.trigger('listEventMobileApp', this.model)
  },

  listEventVideos(e) {
    e.preventDefault()
    this.trigger('listEventVideos', this.model)
  },

  toggleSelect(e) {
    var isChecked = $(e.target).is(':checked')
    this.model.trigger(isChecked ? 'select' : 'deSelect', this.model)
  },

  select() {
    this.$('.js-select')[0].checked = true
  },

  deSelect() {
    this.$('.js-select')[0].checked = false
  },

  loadButtonStyles() {
    this.options.serviceLocator.eventService.read(
      this.model.get('_id'),
      (err, event) => {
        if (err) return this.options.serviceLocator.logger.error(err)
        if (event.state === 'Published') {
          this.$el.find('.js-preview').text('View 🎉')
          this.$el.find('.js-preview').addClass('btn--success')
          this.$el.off('click', '.js-preview')
          this.$el.on(
            'click',
            '.js-preview',
            this.preview.bind(this, { view: true })
          )
        } else if (event.state === 'Draft') {
          this.$el.find('.js-preview').text('Preview')
          this.$el.off('click', '.js-preview')
          this.$el.on(
            'click',
            '.js-preview',
            this.preview.bind(this, { view: false })
          )
        }
      }
    )
  },

  render() {
    var data = this.model.toJSON()

    const images = this.model.get('images').widgets
    const drUrl = config.darkroom.url
    const drKey = config.darkroom.salt
    const urlBuilder = createImageUrlBuilder(
      drUrl,
      drKey,
      this.model.get('images').widgets
    )

    if (
      images &&
      images.length &&
      urlBuilder.getImage('Logo') &&
      urlBuilder.getImage('Logo').crop('1:1')
    ) {
      data.previewImageUrlSmall = urlBuilder
        .getImage('Logo')
        .crop('1:1')
        .constrain(150)
        .url()
      data.previewImageUrlLarge = urlBuilder
        .getImage('Logo')
        .crop('1:1')
        .constrain(400)
        .url()
    } else {
      data.previewImageUrlSmall = '/assets/img/content/no-photo-medium.png'
      data.previewImageUrlLarge = '/assets/img/content/no-photo-large.png'
    }

    data.visibility = stateMap.visibility.get(this.model.get('state'))()
    data.status = stateMap.status.get(this.model.getStatus())(
      this.model.get('startDate'),
      this.model.get('endDate')
    )

    data.__formattedStartDate = data.startDate
      ? moment(data.startDate).format('D MMM YYYY')
      : 'N/A'

    this.$el.empty().append(
      template({
        data,
        allowed: this.options.serviceLocator.allowed,
        format: this.options.serviceLocator.format
      })
    )
    this.setupGenerateLayouts()
    this.loadButtonStyles()
    this.$el.find('.js-tooltip-trigger').tooltip({ html: true })
    return this
  }
})
