const join = require('path').join
const compileJade = require('browjadify-compile')
const template = compileJade(
  join(__dirname, '/../templates/copy-event-modal.jade')
)
const eventSchema = require('../../../service/event/schema')()
const EventSelect = require('./components/event-select')

module.exports = Backbone.View.extend({
  events: {
    'click .js-cancel': 'handleCancel',
    'click .js-confirm': 'handleConfirm'
  },

  initialize(options) {
    this.serviceLocator = options.serviceLocator
    this.eventUmbrellaId = options.eventUmbrellaId
    this.selectedEventId = null
    this.selectedEventName = null
  },

  handleCancel() {
    this.trigger('cancel')
  },

  handleConfirm() {
    if (this.selectedEventId) {
      this.trigger('confirm', {
        sourceEventId: this.selectedEventId,
        sourceEventName: this.selectedEventName
      })
    }
  },

  updatePreview() {
    if (this.selectedEventName) {
      this.$('.js-event-name-preview').text(`Copy of ${this.selectedEventName}`)
      this.$('.js-confirm').prop('disabled', false)
    } else {
      this.$('.js-event-name-preview').text(
        'Select an event to see the preview name'
      )
      this.$('.js-confirm').prop('disabled', true)
    }
  },

  render() {
    this.$el.empty().append(template({}))

    const requiredFields = Object.keys(eventSchema.getProperties()).filter(
      (key) => {
        if (!eventSchema.getProperties()[key].tag) return false
        return eventSchema
          .getProperties()
          [key].tag.includes('copy-event:required')
      }
    )

    const filter = {
      $and: requiredFields.map((field) => ({
        [field]: { $exists: true, $ne: null }
      }))
    }

    // Create event select
    this.eventSelect = new EventSelect(this.serviceLocator, null, { filter })

    this.eventSelect.on('change', (eventId) => {
      this.selectedEventId = eventId

      // Get event name for preview
      if (eventId) {
        this.serviceLocator.eventService.read(eventId, (err, event) => {
          if (err) return this.serviceLocator.logger.error(err)
          this.selectedEventName = event.name
          this.updatePreview()
        })
      } else {
        this.selectedEventName = null
        this.updatePreview()
      }
    })

    this.$('.js-event-select').append(this.eventSelect.render().$el)
    this.updatePreview()

    return this
  }
})
