const View = require('ventnor')
const modal = require('modal')
const compileJade = require('browjadify-compile')
const createImageUrlBuilder = require('cf-image-url-builder')
const join = require('path').join
const moment = require('moment-timezone')
const retrieveEventIdByPath = require('../../../../event/lib/event-id-by-path-retriever')
const deleteDelegate = require('../../components/widget-reference-area/lib/delete-delegate')
const template = compileJade(
  join(
    __dirname,
    '/../../../templates/sub-content-types/agenda-item/list-item.jade'
  )
)
const config = window.config

class EventAgendaItemListItemView extends View {
  constructor(serviceLocator, model, options) {
    super(serviceLocator)
    this.serviceLocator = serviceLocator
    this.model = model
    this.options = options
    this.eventId = retrieveEventIdByPath(window.location.pathname)
    this.event = null // Will be populated when event is loaded
    this.render()
    this.$el = this.$el.find('tr')
    this.$el.on('click', this.handleEdit.bind(this))
    this.$el.on('click', '.js-delete', this.handleDelete.bind(this))
    this.$el.on('click', '.js-select', this.handleToggleSelect.bind(this))
  }

  renderStatus() {
    // Pass the event timezone to getStatus for proper timezone-aware comparison
    const eventTimezone = this.event ? this.event.timezone : null

    const status = this.model.getStatus(eventTimezone)

    // Clear existing status classes
    this.$el
      .find('tr')
      .removeClass(
        'event-agenda-row--upcoming event-agenda-row--live event-agenda-row--past'
      )
    this.$el
      .find('.js-status-icon')
      .removeClass('icon-upcoming icon-live icon-past')

    switch (status) {
      case 'Upcoming':
        this.$el.find('tr').addClass('event-agenda-row--upcoming')
        this.$el.find('.js-status-icon').addClass('icon-upcoming')
        break
      case 'Live':
        this.$el.find('tr').addClass('event-agenda-row--live')
        this.$el.find('.js-status-icon').addClass('icon-live')
        break
      case 'Past':
        this.$el.find('tr').addClass('event-agenda-row--past')
        this.$el.find('.js-status-icon').addClass('icon-past')
        break
      default:
        this.$el.find('tr').addClass('event-agenda-row--upcoming')
        this.$el.find('.js-status-icon').addClass('icon-upcoming')
        break
    }
  }

  handleEdit(e) {
    e.preventDefault()
    this.emit('edit', this.model)
  }

  confirmDelete(model) {
    modal({
      title: 'Confirm Delete',
      content: 'Are you sure you want to delete this item?',
      buttons: [
        { text: 'Cancel', className: 'btn', event: 'cancel' },
        {
          text: 'Delete Agenda Item',
          className: 'btn btn--error',
          event: 'confirm'
        }
      ]
    }).on('confirm', () => this.emit('delete', model))
  }

  handleDelete(e) {
    e.preventDefault()
    e.stopPropagation()
    deleteDelegate(this.serviceLocator, this.model, 'agendaItem', () => {
      this.confirmDelete(this.model)
    })
  }

  handleToggleSelect(e) {
    e.stopPropagation()
    var isChecked = $(e.target).is(':checked')
    this.model.trigger(isChecked ? 'select' : 'deSelect', this.model)
  }

  getPreviewImageUrl(agendaItem) {
    const images = agendaItem && agendaItem.images && agendaItem.images.widgets
    const drUrl = config.darkroom.url
    const drKey = config.darkroom.salt
    const urlBuilder = createImageUrlBuilder(drUrl, drKey, images)
    if (
      images &&
      images.length &&
      urlBuilder.getImage('Thumbnail') &&
      urlBuilder.getImage('Thumbnail').crop('1:1')
    ) {
      return urlBuilder.getImage('Thumbnail').crop('1:1').constrain(347).url()
    }
  }

  getDuration() {
    var now = moment(this.model.get('startDate'))
    var end = moment(this.model.get('endDate'))
    var duration = moment.duration(end.diff(now))
    if (duration.isValid()) {
      return duration.asMinutes() + ' m'
    } else {
      return 'N/A'
    }
  }

  render() {
    // Render immediately with basic data, then enhance with timezone info
    this.renderBasic()
    this.renderStatus() // Initial status without timezone

    // Load event data asynchronously to enhance with timezone information
    this.loadEventAndEnhance()

    return this
  }

  renderBasic() {
    this.$el.append(
      template({
        id: this.model.get('_id'),
        name: this.model.get('name'),
        day: moment(this.model.get('startDate')).format('D MMM'),
        time: moment(this.model.get('startDate')).format('hh:mm a'),
        duration: this.getDuration(),
        allowed: this.serviceLocator.allowed,
        sessionType: this.model.get('sessionType'),
        previewImageUrl: this.getPreviewImageUrl(this.model.toJSON())
      })
    )
  }

  loadEventAndEnhance() {
    this.serviceLocator.eventService.read(this.eventId, (err, event) => {
      if (err) {
        console.error('AgendaItemListItemView: Error loading event data:', err)
        this.serviceLocator.logger.error(err)
        return
      }
      this.event = event
      this.enhanceWithTimezone()
      this.renderStatus() // Re-render status with timezone
      this.hydrateSessionAndLocation()
    })
  }

  enhanceWithTimezone() {
    if (!this.event || !this.event.timezone) {
      // eslint-disable-next-line no-console
      console.log(
        'AgendaItemListItemView: No timezone available, skipping enhancement'
      )
      this.$el.find('td:nth-child(2) span').text('Error')
      this.$el
        .find('.list-item-actions')
        .prepend(
          '<span class="label label--warning">No timezone available</span> '
        )
      return
    }

    if (!this.model.get('startDateISOWithoutTZ')) {
      this.$el.find('td:nth-child(2) span').text('Error')
      this.$el
        .find('.list-item-actions')
        .prepend(
          '<span class="label label--warning">No startDateISOWithoutTZ available</span> '
        )
      return
    }

    const startDateISOWithoutTZ = this.model.get('startDateISOWithoutTZ')
    const startDateWithoutTZ = moment.utc(startDateISOWithoutTZ)
    const timezoneAbbreviation = moment
      .tz(startDateWithoutTZ, this.event.timezone)
      .format('z')
    const display =
      startDateWithoutTZ.format('D MMM @ hh:mm a') +
      ' (' +
      timezoneAbbreviation +
      ')'
    this.$el.find('td:nth-child(2) span').text(display)
  }

  hydrateSessionAndLocation() {
    // Event is already loaded in this.event from render method
    if (!this.event) return

    const sessionType = this.event.sessionTypes.find(
      (s) => s.key === this.model.get('sessionTypeKey')
    )
    if (sessionType) this.$el.find('.js-session-type').html(sessionType.name)

    const location = this.event.locations.find(
      (l) => l.key === this.model.get('locationKey')
    )
    if (location) this.$el.find('.js-location').html(location.name)
  }
}

module.exports = EventAgendaItemListItemView
