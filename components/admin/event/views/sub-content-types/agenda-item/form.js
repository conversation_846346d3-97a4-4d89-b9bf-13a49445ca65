const modal = require('modal')
const debug = require('../../../../../../admin/source/js/lib/debug')(
  'event agenda item form view'
)
const View = require('ventnor')
const compileJade = require('browjadify-compile')
const join = require('path').join
const ItemRepeater = require('../../../../widget/views/item-repeater')
const SponsorSelect = require('../../../../event/views/components/sponsor-select')
const CompanySelectView = require('../../../../company/views/company-select')
const WidgetAreaView = require('../../../../widget/views/widget-area')
const WidgetReferenceArea = require('../../components/widget-reference-area/form')
const imageConfig = require('../../../../../service/event/sub-content-types/agenda-item/image-config.json')
const AssetCollection = require('../../../../asset/collections/asset')
const AssetPickerView = require('../../../../asset/views/picker')
const getImageFactory = require('../../../../asset/lib/image-factory')()
const formCancelDelegate = require('cf-form-cancel-delegate')(debug)
const RichTextEditorInstanceManager = require('../../../../../../admin/source/js/lib/rich-text-editor-instance-manager')
const dateTimePickers = require('../../../../../../admin/source/js/lib/init-date-time-pickers')
const formErrorsDelegate = require('../../../../../../admin/source/js/lib/form-errors-delegate')(
  debug
)
const template = compileJade(
  join(__dirname, '/../../../templates/sub-content-types/agenda-item/form.jade')
)
const generateWidgetMetaFilter = require('../../components/widget-reference-area/lib/widget-meta-filter-generator')
const retrieveEventIdByPath = require('../../../lib/event-id-by-path-retriever')
const formSaveDelegate = require('../../../../../../admin/source/js/lib/form-save-delegate')(
  debug
)
const agendaItemVisibility = require('../../../../../service/event/sub-content-types/agenda-item/visibility-config')
const config = window.config
const FormFieldDescriptions = require('../../../../widget/views/form/form-field-descriptions')
const {
  config: validationConfig
} = require('../../../../../service/event/validation-config')

const speakerRepeaterConfig = {
  itemModel: require('../../../models/agenda-item-speaker'),
  itemView: require('./item-repeater/speaker/item'),
  formView: require('./item-repeater/speaker/form'),
  itemNames: {
    singular: 'Speaker',
    plural: 'Speakers'
  }
}

class EventAgendaItemForm extends View {
  constructor(serviceLocator, model, options) {
    super(serviceLocator)
    this.model = model
    this.initialModel = model.toJSON()
    this.trigger = this.emit
    this.options = options
    this.eventId = retrieveEventIdByPath(window.location.pathname)

    // Compatability with formErrorsDelegate
    this.showErrors = formErrorsDelegate.showErrors
    this.clearErrors = formErrorsDelegate.clearErrors
    this.$ = (selector) => this.$el.find(selector)

    this.initDateTimePickers = dateTimePickers(
      window.config.locale.longDateFormat.LLLL,
      this.model
    )

    this.speakerRepeater = new ItemRepeater(
      serviceLocator,
      speakerRepeaterConfig,
      this.model.get('speakers'),
      'Add Speaker',
      { options: this.options }
    )

    this.$el.on(
      'change',
      '.js-location-select',
      this.handleLocationChange.bind(this)
    )

    // this.$el.on('change', '.js-track-select', this.handleTrackChange.bind(this))

    this.companySelect = new CompanySelectView(
      this.serviceLocator,
      this.model.get('companyId')
    )

    this.companySelect.on('change', (value) => {
      this.model.set('companyId', value)
    })

    this.richTextEditorInstanceManager = new RichTextEditorInstanceManager()
    this.on('remove', () => {
      this.richTextEditorInstanceManager.destroy()
    })

    this.$el.on('click', '.js-images-add', this.handleAddImages.bind(this))
    this.$el.on('click', '.js-cancel', this.handleCancel.bind(this))
    this.$el.on('click', '.js-save', this.handleSave.bind(this))
    this.render()
    this.listenTo(this, 'afterAppend', this.focusForm)
    this.speakerRepeater.on('itemsUpdated', () => {
      this.model.set('speakers', this.speakerRepeater.getItems())
    })
  }

  focusForm() {
    this.$(':input[name=title]').focus()
  }

  handleCancel() {
    this.emit.bind(this, 'close')
    formCancelDelegate.call(this)
  }

  handleSave() {
    this.model.set('eventId', this.eventId)

    const startDate = this.model.get('startDate')
    const endDate = this.model.get('endDate')

    const startTimezoneOffset = startDate.getTimezoneOffset()
    const endTimezoneOffset = endDate.getTimezoneOffset()

    this.model.set(
      'startDateISOWithoutTZ',
      new Date(
        startDate.getTime() - startTimezoneOffset * (60 * 1000)
      ).toISOString()
    )

    this.model.set(
      'endDateISOWithoutTZ',
      new Date(
        endDate.getTime() - endTimezoneOffset * (60 * 1000)
      ).toISOString()
    )

    formSaveDelegate.call(this)
  }

  handleAddImages(e) {
    e.preventDefault()

    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      type: 'image',
      serviceLocator: this.serviceLocator
    })

    collection.getByType('image')

    modal({
      title: 'Images',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        { text: 'Cancel', event: 'Cancel', keyCodes: [27], className: 'btn' },
        {
          text: 'Add selected images',
          event: 'add',
          className: 'btn btn--action',
          keyCodes: [13]
        }
      ]
    })
      .on('add', this.addImages.bind(this, assetListView.selectedCollection))
      .on('close', assetListView.remove.bind(assetListView))
  }

  addImages(images) {
    const ImageModel = getImageFactory('image').model
    images.map((model) => {
      const image = new ImageModel(model.attributes)
      this.model.images.add(image)
      image.setDefaultCrops(imageConfig.crops)
      return image
    })
  }

  renderImageWidgetArea() {
    this.imageArea = new WidgetAreaView({
      model: this.model.images,
      receiveDrops: false,
      serviceLocator: this.serviceLocator,
      widgetItemOptions: imageConfig
    })
    this.$el.find('.js-image-area').append(this.imageArea.$el)
  }

  handleLocationChange(e) {
    e.preventDefault()
    const location = $(e.currentTarget).val()
    this.model.set('location', location)
  }

  // handleTrackChange(e) {
  //   e.preventDefault()
  //   const track = $(e.currentTarget).val()
  //   this.model.set('track', track)
  // }

  renderItemRepeater() {
    this.$el.find('.js-speakers').append(this.speakerRepeater.render().$el)
  }

  renderSponsorSelect() {
    this.sponsorSelect = new SponsorSelect(
      this.serviceLocator,
      this.options.ids.event,
      this.model.get('sponsorId')
    )
    this.sponsorSelect.on('change', (value) => {
      this.model.set('sponsorId', value)
    })
    this.$el
      .find(`.js-sponsor-select`)
      .empty()
      .append(this.sponsorSelect.render().$el)
  }

  // renderTagSelect() {
  //   this.serviceLocator.eventService.read(
  //     this.options.ids.event,
  //     (err, event) => {
  //       if (err) return this.serviceLocator.logger.error(err)

  //       this.$el.find('.js-selectize').each((i, el) => {
  //         $(el).selectize({
  //           delimiter: ',',
  //           createOnBlur: false,
  //           create: false,
  //           options: event.tags.map((t) => ({
  //             text: t,
  //             value: t
  //           })),
  //           onInitialize: () =>
  //             el.selectize.on('change', () => {
  //               this.model.set(el.name, el.selectize.getValue())
  //             })
  //         })
  //       })
  //     }
  //   )
  // }

  renderDateTimePicker() {
    this.dateTimePickers = this.$el
      .find('.js-date-time-picker')
      .toArray()
      .map(this.initDateTimePickers)
  }

  renderRichTextEditor() {
    this.$el.find('.js-text-editor').each((i, el) => {
      this.richTextEditorInstanceManager.create(el, { height: 150 }, el.id)
    })
  }

  renderCompanySelect() {
    this.$el.find('.js-company-select').append(this.companySelect.render().$el)
  }

  renderWidgetReferences() {
    const widgetReferenceArea = new WidgetReferenceArea(
      this.serviceLocator,
      this.model,
      {
        imageConfig,
        filter: generateWidgetMetaFilter(this.model, 'agendaItem')
      }
    )
    this.$el
      .find('.js-widget-reference-area')
      .append(widgetReferenceArea.render().$el)
  }

  renderFormDescriptions() {
    const { EventAgendaItem } = validationConfig
    const properties = Object.keys(EventAgendaItem).map((key) => ({
      name: key,
      maxLength: EventAgendaItem[key],
      initialValue: this.model.get(key)
    }))

    new FormFieldDescriptions(
      this.serviceLocator,
      this.$el,
      properties
    ).render()
  }

  render() {
    this.serviceLocator.eventService.read(
      this.options.ids.event,
      (err, event) => {
        if (err) return this.serviceLocator.logger.error(err)
        this.$el.append(
          template({
            data: this.model.toJSON(),
            title: 'Event Agenda Item',
            config,
            locations: event.locations || [],
            // tracks: event.tracks || [],
            sessionTypes: event.sessionTypes,
            agendaItemVisibility
          })
        )
        this.renderCompanySelect()
        this.renderRichTextEditor()
        this.renderDateTimePicker()
        this.renderItemRepeater()
        // this.renderTagSelect()
        this.renderImageWidgetArea()
        this.renderWidgetReferences()
        this.renderSponsorSelect()
        this.renderFormDescriptions()
      }
    )
    return this
  }
}

module.exports = EventAgendaItemForm
