const CrudService = require('../../../../admin/source/js/lib/crud-service')
const createLayoutHandlers = require('./lib/create-layout-handlers')

const {
  findSpeakers,
  createSpeaker,
  findSpeaker,
  deleteSpeaker,
  updateSpeaker
} = require('./lib/event-speaker-helpers')
const {
  findSponsor,
  deleteSponsor,
  updateSponsor,
  createSponsor,
  findSponsors
} = require('./lib/event-sponsor-helpers')
const {
  findAgenda,
  findAgendaItem,
  createAgendaItem,
  deleteAgendaItem,
  updateAgendaItem
} = require('./lib/event-agenda-item-helpers')

const {
  findAll: findLayouts,
  create: createLayout,
  find: findLayout,
  update: updateLayout,
  delete: deleteLayout
} = createLayoutHandlers()

class EventService extends CrudService {
  constructor() {
    super(...arguments)
    this.findSpeakers = findSpeakers.bind(this)
    this.createSpeaker = createSpeaker.bind(this)
    this.findSpeaker = findSpeaker.bind(this)
    this.updateSpeaker = updateSpeaker.bind(this)
    this.deleteSpeaker = deleteSpeaker.bind(this)
    this.generateSpeaker = require('../../../service/event/generators/speaker-generator')

    this.findSponsors = findSponsors.bind(this)
    this.createSponsor = createSponsor.bind(this)
    this.findSponsor = findSponsor.bind(this)
    this.updateSponsor = updateSponsor.bind(this)
    this.deleteSponsor = deleteSponsor.bind(this)
    this.generateSponsor = require('../../../service/event/generators/sponsor-generator')

    this.findAgenda = findAgenda.bind(this)
    this.createAgendaItem = createAgendaItem.bind(this)
    this.findAgendaItem = findAgendaItem.bind(this)
    this.updateAgendaItem = updateAgendaItem.bind(this)
    this.deleteAgendaItem = deleteAgendaItem.bind(this)
    this.generateAgendaItem = require('../../../service/event/generators/agenda-item-generator')

    this.findLayouts = findLayouts.bind(this)
    this.createLayout = createLayout.bind(this)
    this.findLayout = findLayout.bind(this)
    this.updateLayout = updateLayout.bind(this)
    this.deleteLayout = deleteLayout.bind(this)
  }

  get name() {
    return 'EventService'
  }

  get urlRoot() {
    return '/events'
  }

  importLayouts(id, data, cb) {
    this.authedRequest(
      'POST',
      this.urlRoot + '/' + id + '/layouts/import',
      data,
      (err, res, body) => {
        if (err) return cb(err)
        if (res.statusCode >= 300)
          return this.handleError(res.statusCode, body, cb)
        cb(null, body)
      }
    )
  }

  updateNavigation(id, data, cb) {
    this.authedRequest(
      'PATCH',
      this.urlRoot + '/' + id + '/navigation',
      data,
      (err, res, body) => {
        if (err) return cb(err)
        if (res.statusCode >= 300)
          return this.handleError(res.statusCode, body, cb)
        cb(null, body)
      }
    )
  }

  findProperty(_id, property, cb) {
    this.serviceLocator.eventService.find(
      '',
      { _id },
      {},
      [property],
      (err, res) => {
        if (err) cb(err)
        cb(null, res.results[0][property])
      }
    )
  }

  copyEvent(sourceEventId, targetEventUmbrellaId, cb) {
    this.authedRequest(
      'POST',
      this.urlRoot + '/copy',
      { sourceEventId, targetEventUmbrellaId },
      (err, res, body) => {
        if (err) return cb(err)
        if (res.statusCode >= 300)
          return this.handleError(res.statusCode, body, cb)
        cb(null, body)
      }
    )
  }
}

module.exports = EventService
