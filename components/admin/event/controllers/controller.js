const ListView = require('../views/list')
const FormView = require('../views/form')
const createCollection = require('../collections/event')
const EventModel = require('../models/event')
const debug = require('../../../../admin/source/js/lib/debug')('event')
const notify = require('../../notification/foreground')
const createLayoutRoutes = require('./lib/layout-route-creator')
const createSpeakerRoutes = require('./lib/speaker-route-creator')
const createSponsorRoutes = require('./lib/sponsor-route-creator')
const createAgendaItemRoutes = require('./lib/agenda-item-route-creator')
const createArticleRoutes = require('./lib/article-route-creator')
const MobileAppFormView = require('../views/mobile-app-form')

module.exports = (serviceLocator) => {
  const router = serviceLocator.router
  let collection
  const displayName = { singular: 'Event' }

  const getInstance = (id, cb) => {
    serviceLocator.instanceService.read(id, (err, instance) => {
      if (err) return cb(err)
      return cb(null, instance)
    })
  }

  const getAccount = (cb) => {
    serviceLocator.accountService.find(
      '',
      {},
      ['name'],
      { pageSize: 1000 },
      (err, res) => {
        if (err) return cb(err)
        return cb(null, res.results[0])
      }
    )
  }

  router.route('event-umbrellas/:id/events(/)', 'manageEvents', async (id) => {
    debug('event url list view route triggered')
    if (!serviceLocator.allow('event', 'read')) return false

    serviceLocator.eventService.find(
      '',
      { eventUmbrellaId: id },
      {},
      [],
      (err, _) => {
        if (err)
          return serviceLocator.logger.error(err, 'Cannot find existing event')

        serviceLocator.eventUmbrellaService.read(id, (err, eventUmbrella) => {
          if (err)
            return serviceLocator.logger.error(
              err,
              'Cannot find existing event umbrella'
            )

          displayName.plural = `${eventUmbrella.name} Events`

          const Collection = createCollection(id)
          collection = new Collection()

          var listView = new ListView({
            collection,
            serviceLocator,
            displayName,
            ids: { eventUmbrella: id }
          })

          listView.on(
            'new',
            router.navigate.bind(router, `event-umbrellas/${id}/events/form`, {
              trigger: true
            })
          )

          listView.on('edit', (model) => {
            router.navigate(`event-umbrellas/${id}/events/${model.id}/form`, {
              trigger: true
            })
          })

          listView.on('preview', (instance, model, options) => {
            var w = window.open('')
            getAccount((err, account) => {
              if (err) return err
              getInstance(instance, (err, instance) => {
                if (err) return err
                const previewId =
                  options && options.view
                    ? ''
                    : '?previewId=' + model.get('previewId')

                const url =
                  serviceLocator.instanceService.createUrl(instance, account) +
                  '/events/' +
                  eventUmbrella.slug +
                  '/' +
                  model.get('slug') +
                  previewId
                w.location = url
              })
            })
          })

          listView.on('listEventArticles', (model) => {
            router.navigate(
              `event-umbrellas/${id}/events/${model.id}/articles`,
              {
                trigger: true
              }
            )
          })

          listView.on('listEventSpeakers', (model) => {
            router.navigate(
              `event-umbrellas/${id}/events/${model.id}/speakers`,
              {
                trigger: true
              }
            )
          })

          listView.on('listEventSponsors', (model) => {
            router.navigate(
              `event-umbrellas/${id}/events/${model.id}/sponsors`,
              {
                trigger: true
              }
            )
          })

          listView.on('listEventLayouts', (model) => {
            router.navigate(
              `event-umbrellas/${id}/events/${model.id}/layouts`,
              {
                trigger: true
              }
            )
          })

          listView.on('listEventAgendaItems', (model) => {
            router.navigate(`event-umbrellas/${id}/events/${model.id}/agenda`, {
              trigger: true
            })
          })

          listView.on('listEventVideos', (model) => {
            router.navigate(`event-umbrellas/${id}/events/${model.id}/videos`, {
              trigger: true
            })
          })

          listView.on('listEventMobileApp', (model) => {
            router.navigate(
              `event-umbrellas/${id}/events/${model.id}/mobile-app`,
              {
                trigger: true
              }
            )
          })

          router.render(listView, displayName.plural)
          collection.load()
        })
      }
    )
  })

  router.route(
    'event-umbrellas/:id/events/:eventId/mobile-app(/)',
    'editEventMobileApp',
    (id, eventId) => {
      if (!serviceLocator.allow('event', 'update')) {
        return false
      }
      debug('event app edit view route triggered')
      const Collection = createCollection(id)
      collection = new Collection()
      collection.retrieve(eventId, (err, model) => {
        if (err) {
          router.trigger('notFound', err.message)
          return
        }
        const view = new MobileAppFormView({
          model,
          serviceLocator,
          eventUmbrellaId: id,
          eventId
        })
        router.render(view, 'Edit Mobile App')
      })
    }
  )

  const bindSaveAndCancel = (view, id) => {
    view.on('cancel', () => {
      router.navigate(`event-umbrellas/${id}/events`, { trigger: true })
    })

    view.on('save', () => {
      notify('Saved', 'save')
      router.navigate(`event-umbrellas/${id}/events`, { trigger: true })
    })
    return view
  }

  const updateState = (id, eventId, model, state) => {
    serviceLocator.eventService.update(
      eventId,
      { ...model.toJSON(), state },
      (err) => {
        if (err) {
          notify('Error updating status', 'error')
          return
        }
        notify('Status updated', 'save')
        router.navigate(`event-umbrellas/${id}/events`, { trigger: true })
      }
    )
  }

  router.route('event-umbrellas/:id/events/form(/)', 'newEvent', (id) => {
    if (!serviceLocator.allow('event', 'create')) {
      return false
    }
    debug('event create view route triggered')

    const Collection = createCollection(id)
    const model = new EventModel({}, { collection: new Collection() })
    const view = new FormView({
      model,
      serviceLocator,
      title: displayName.singular,
      eventUmbrellaId: id
    })

    router.render(bindSaveAndCancel(view, id), 'New ' + displayName.singular)
  })

  router.route(
    `event-umbrellas/:id/events/:eventId/form(/)`,
    'editEvent',
    (id, eventId) => {
      if (!serviceLocator.allow('event', 'update')) {
        return false
      }
      debug('event edit view route triggered')
      const Collection = createCollection(id)
      collection = new Collection()
      collection.retrieve(eventId, (err, model) => {
        if (err) {
          router.trigger('notFound', err.message)
          return
        }
        debug('Loading form view', model)
        var view = new FormView({
          model,
          serviceLocator,
          title: displayName.singular,
          eventUmbrellaId: id
        })

        view.on('publish', (updatedModel) => {
          updateState(id, eventId, updatedModel, 'Published')
        })

        view.on('draft', (updatedModel) => {
          updateState(id, eventId, updatedModel, 'Draft')
        })

        view.on('review', (updatedModel) => {
          updateState(id, eventId, updatedModel, 'Review')
        })

        view.on('accept', (updatedModel) => {
          updateState(id, eventId, updatedModel, 'Published')
        })

        view.on('reject', (updatedModel) => {
          updateState(id, eventId, updatedModel, 'Draft')
        })

        view.on('updateLayout', (model, layoutDescriptor) => {
          serviceLocator.eventService.updateLayout(
            eventId,
            layoutDescriptor,
            (err) => {
              if (err) return serviceLocator.logger.error(err)
            }
          )
        })

        view.on('deleteLayout', (model, layoutDescriptor) => {
          serviceLocator.eventService.deleteLayout(
            eventId,
            layoutDescriptor,
            (err) => {
              if (err) return serviceLocator.logger.error(err)
            }
          )
        })

        view.on('listEventArticles', (modelx) => {
          router.navigate(
            `event-umbrellas/${id}/events/${modelx.id}/articles`,
            {
              trigger: true
            }
          )
        })

        view.on('listEventSpeakers', (modelx) => {
          router.navigate(
            `event-umbrellas/${id}/events/${modelx.id}/speakers`,
            {
              trigger: true
            }
          )
        })

        view.on('listEventSponsors', (modelx) => {
          router.navigate(
            `event-umbrellas/${id}/events/${modelx.id}/sponsors`,
            {
              trigger: true
            }
          )
        })

        view.on('listEventLayouts', (modelx) => {
          router.navigate(`event-umbrellas/${id}/events/${modelx.id}/layouts`, {
            trigger: true
          })
        })

        view.on('listEventAgendaItems', (modelx) => {
          router.navigate(`event-umbrellas/${id}/events/${modelx.id}/agenda`, {
            trigger: true
          })
        })

        view.on('listEventVideos', (modelx) => {
          router.navigate(`event-umbrellas/${id}/events/${modelx.id}/videos`, {
            trigger: true
          })
        })

        view.on('listEventMobileApp', (modelx) => {
          router.navigate(
            `event-umbrellas/${id}/events/${modelx.id}/mobile-app`,
            {
              trigger: true
            }
          )
        })

        router.render(
          bindSaveAndCancel(view, id),
          'Edit ' + displayName.singular
        )
      })
    }
  )

  createLayoutRoutes(serviceLocator)
  createSpeakerRoutes(serviceLocator)
  createSponsorRoutes(serviceLocator)
  createAgendaItemRoutes(serviceLocator)
  createArticleRoutes(serviceLocator)
}
