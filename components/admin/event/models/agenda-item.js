const BaseModel = require('cf-base-model')
const ImageAreaModel = require('../../asset/models/image-area')
const schemata = require('../../../service/event/sub-content-types/agenda-item/schema')()
const moment = require('moment-timezone')

module.exports = BaseModel.extend({
  idAttribute: '_id',
  schemata,
  defaults() {
    return schemata.makeDefault()
  },
  initialize() {
    BaseModel.prototype.initialize.call(this)
    this.images = new ImageAreaModel(this.get('images'))
    this.images.on('add remove change', () => {
      this.set('images', this.images.toJSON())
    })
  },
  getStatus(timezone) {
    const now = moment.tz(moment(), timezone).toDate()
    const startDate = moment
      .tz(this.get('startDateISOWithoutTZ'), timezone)
      .toDate()
    const endDate = moment
      .tz(this.get('endDateISOWithoutTZ'), timezone)
      .toDate()

    if (startDate > now) {
      return 'Upcoming'
    } else if (startDate <= now && endDate >= now) {
      return 'Live'
    } else if (endDate < now) {
      return 'Past'
    }
  },
  validate(cb) {
    this.schemata.validate(this.attributes, (ignoreErr, errors) => {
      if (!errors) return null
      return cb(Object.keys(errors).length > 0 ? errors : undefined)
    })
  }
})
