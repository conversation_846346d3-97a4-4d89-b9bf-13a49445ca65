div 
  each datum in data
    .form-row
      label
        strong.form-label-text= datum.name
        .form-field
          each field in datum.fields
            label.list-item-select
              input(type='checkbox', name=datum._id + '[' + field.name + ']', checked=field.value)
              span=field.title
            
            if field.children 
              .form-field(style='background: #EEE; padding: .5em 1em; margin-block: .5em 1em; border-radius: 3px;')
                each child in field.children
                  label.list-item-select
                    input(type='checkbox', name=datum._id + '[' + child.name + ']', checked=child.value)
                    span=child.title
