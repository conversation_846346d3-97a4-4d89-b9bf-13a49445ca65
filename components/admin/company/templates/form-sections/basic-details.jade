.panel.panel-styled
  .panel-header
    h2 Basic Details
  .panel-content

    .form-row(id='field--name', data-field='name')
      label
        span.form-label-text Name
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='name', value=data.name, autofocus)
      .js-error

    .form-row(id='field--slug', data-field='slug')
      label
        span.form-label-text Slug
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='slug', value=data.slug)
      .js-error

    .form-row(id='field--oneLiner', data-field='oneLiner')
      label
        span.form-label-text Hero sub-title
        input.control.control--text.form-field(type='text', name='oneLiner', value=data.oneLiner)
      .js-error

    //- Not appropriate to be merged with apollo
    .form-row(id='field--shortDescription', data-field='shortDescription')
      label
        span.form-label-text Short Description
        input.control.control--text.form-field(type='text', name='shortDescription', value=data.shortDescription)
      .js-error

    //- HIDDEN DUE TO NEW APOLLO FIELDS, HOWEVER PROPERTIES STILL USED ACROSS APPLICATION
    //- .form-row(id='field--linkedinId', data-field='linkedinId')
    //-   label
    //-     span.form-label-text LinkedIn ID
    //-     input.control.control--text.form-field(type='text', name='linkedinId', value=data.linkedinId)
    //-   .js-error

    //- .form-row(id='field--website', data-field='website')
    //-   label
    //-     span.form-label-text Website URL
    //-     input.control.control--text.form-field(type='text', name='website', value=data.website)
    //-   .js-error

    .form-row.form-row-boolean(id='field--showProfilePage', data-field='showProfilePage')
      label
        span.form-label-text Show profile page?
        .form-field
          input.control.control--boolean(type='checkbox', name='showProfilePage', checked=data.showProfilePage)
      .js-error

    .form-row(id='field--formCraftId', data-field='formCraftId')
      label
        span.form-label-text Form Craft ID
        input.control.control--text.form-field(type='text', name='formCraftId', value=data.formCraftId)
      .js-error
