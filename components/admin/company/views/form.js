const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const debug = require('../../../../admin/source/js/lib/debug')(
  'company form view'
)
const RichTextEditorInstanceManager = require('../../../../admin/source/js/lib/rich-text-editor-instance-manager')
const slugg = require('slugg')
const BaseView = require('cf-base-view')
const modal = require('modal')
const formSaveDelegate = require('../../../../admin/source/js/lib/form-save-delegate')(
  debug
)
const formCancelDelegate = require('cf-form-cancel-delegate')(debug)
const formTitleDelegate = require('../../../../admin/source/js/lib/form-title-delegate')
const AssetCollection = require('../../asset/collections/asset')
const AssetPickerView = require('../../asset/views/picker')
const getImageFactory = require('../../asset/lib/image-factory')()
const WidgetAreaView = require('../../widget/views/widget-area')
const imageConfig = require('../../../service/company/image-config.json')
const clientImageConfig = require('../../../service/company/client-image-config.json')
const notify = require('../../notification/foreground')
const AssetModel = require('../../asset/models/asset')
const ItemRepeater = require('../../widget/views/item-repeater')
const industriesJson = require('../../../service/company/lib/industries.json')
const mapFormToObject = require('cf-map-form-to-object')
const config = window.config
const ExecutiveSelectMulti = require('./executive-select-multi')
const InstancePermissionField = require('./instance-permissions-field')

const statsRepeaterConfig = {
  formView: require('./item-repeater/stats/form'),
  itemView: require('./item-repeater/stats/item'),
  itemModel: require('../models/item-repeater/stats/item'),
  itemNames: {
    singular: 'Stat',
    plural: 'Stats'
  }
}

const executivesRepeaterConfig = {
  formView: require('./item-repeater/executives/form'),
  itemView: require('./item-repeater/executives/item'),
  itemModel: require('../models/item-repeater/executives/item'),
  itemNames: {
    singular: 'Executive Preferred Sort Order',
    plural: 'Executives Preferred Sort Order'
  }
}

const currentTechnologiesRepeaterConfig = {
  itemModel: require('../models/current-technology'),
  itemView: require('./item-repeater/current-technology/item'),
  formView: require('./item-repeater/current-technology/form'),
  itemNames: {
    singular: 'Technology',
    plural: 'Technologies'
  }
}

const aiMetaRepeaterConfig = {
  itemModel: require('../models/ai-meta'),
  itemView: require('./item-repeater/ai-meta/item'),
  formView: require('./item-repeater/ai-meta/form'),
  itemNames: {
    singular: 'Item',
    plural: 'Ai Meta'
  }
}

const apolloMap = {
  website_url: 'websiteUrl',
  primary_domain: 'primaryDomain',
  industries: 'industries',
  keywords: 'keywords',
  founded_year: 'foundedYear',
  street_address: 'streetAddress',
  city: 'city',
  state: 'state',
  country: 'country',
  postal_code: 'postalCode',
  sanitized_phone: 'hqPhone',
  linkedin_url: 'linkedinUrl',
  twitter_url: 'twitterUrl',
  facebook_url: 'facebookUrl',
  publicly_traded_symbol: 'publiclyTradedSymbol',
  publicly_traded_exchange: 'publiclyTradedExchange',
  market_cap: 'marketCap',
  annual_revenue: 'annualRevenue',
  estimated_num_employees: 'estimatedNumEmployees',
  current_technologies: 'currentTechnologies',
  id: 'apolloId',
  'account.salesforce_id': 'salesforceId',
  logo_url: '__logo_url',
  ai_description: 'description'
}

const FAKE_TIMEOUT = 100

const FetchState = {
  IDLE: 'idle',
  FETCHING: 'fetching',
  CANCELLING: 'cancelling',
  SUCCESS: 'success',
  ERROR: 'error'
}

const galleryImageConfig = {
  crops: [
    {
      name: '16:9',
      aspectRatio: '16:9'
    }
  ],
  contexts: [
    {
      name: 'Image',
      allowMultipleSelection: true
    }
  ]
}

const reverseApolloMap = Object.keys(apolloMap).reduce((acc, key) => {
  acc[apolloMap[key]] = key
  return acc
}, {})

module.exports = BaseView.extend({
  events: {
    'click .js-save': 'handleSave',
    'click .js-cancel': 'handleCancel',
    submit: (e) => e.preventDefault()
  },

  galleryImageConfig,

  initialize({ serviceLocator, title, isNew, queryParams }) {
    this.initialModel = this.model.toJSON()
    this.formTitle = formTitleDelegate(title)
    this.isNew = isNew
    this.queryParams = queryParams || {}
    window.companyForm = window.companyForm || {}
    window.companyForm.fetchState = FetchState.IDLE

    this.persistedExcludedFields = []

    this.serviceLocator = serviceLocator

    // Set query parameters on the model if they exist
    if (this.isNew && this.queryParams) {
      const modelData = {}
      if (this.queryParams.name) {
        modelData.name = this.queryParams.name
      }
      if (this.queryParams.slug) {
        modelData.slug = this.queryParams.slug
      }
      if (Object.keys(modelData).length > 0) {
        this.model.set(modelData)
      }
    }

    this.on('remove', this.destroy)
    this.$el.on('change', '[name=name]', this.generateSlug.bind(this))
    this.$el.on('click', '.js-images-add', this.handleAddImages.bind(this))
    this.$el.on('click', '.js-clients-add', this.handleAddClients.bind(this))
    this.$el.on(
      'click',
      '.js-gallery-image-add',
      this.handleAddGalleryImage.bind(this)
    )

    this.$el.on(
      'click',
      '.js-fetch-apollo-fields',
      this.fetchApolloFields.bind(this)
    )
    this.$el.on('click', '.js-cancel-fetch', this.cancelFetch.bind(this))

    this.executiveMultiSelect = new ExecutiveSelectMulti(
      this.serviceLocator,
      this.model.get('leadershipTeam'),
      this.model.get('_id')
    )

    this.instancePermissionField = new InstancePermissionField(
      this.serviceLocator,
      this.model.get('instancePermissions') || {}
    )

    this.render()

    this.listenTo(this, 'afterAppend', this.focusForm)
    // this.listenTo(this, 'afterAppend', this.loadLinkedArticles)
    // this.listenTo(this, 'afterAppend', this.loadLinkedInPosts)
    this.listenTo(this, 'externalSave:init', () =>
      this.save((err, data) => {
        if (err) return this.trigger('externalSave:error', err)
        if (!data)
          return this.trigger('externalSave:error', 'Company not saved')
        this.trigger('externalSave:complete', data)
      })
    )
  },

  cleanup() {
    if (this.richTextEditorInstanceManager)
      this.richTextEditorInstanceManager.destroy()
  },

  handleUiState(state) {
    switch (state) {
      case 'succes':
        this.$el.find('.js-cancel-fetch').attr('disabled', true)
        this.$el.find('.js-fetch-apollo-fields').html('Success')
        setTimeout(() => {
          this.handleUiState('default')
        }, FAKE_TIMEOUT * 25)
        break
      case 'error':
        this.$el.find('.js-cancel-fetch').attr('disabled', true)
        this.$el.find('.js-fetch-apollo-fields').attr('disabled', true)
        this.$el.find('.js-fetch-apollo-fields').html('Error fetching')
        setTimeout(() => {
          this.handleUiState('default')
        }, FAKE_TIMEOUT * 25)
        break
      case 'loading':
        this.$el.find('.js-cancel-fetch').attr('disabled', false)
        this.$el.find('.js-fetch-apollo-fields').attr('disabled', true)
        this.$el.find('.js-fetch-apollo-fields').html('Loading...')
        this.$el.find('.js-apollo-fields').addClass('disabled-div')
        this.$el.find('.js-apollo-fields').addClass('loading')
        break
      case 'default':
        this.$el.find('.js-cancel-fetch').attr('disabled', true)
        this.$el.find('.js-fetch-apollo-fields').attr('disabled', false)
        this.$el.find('.js-fetch-apollo-fields').html('Fetch Apollo Data')
        this.$el.find('.js-apollo-fields').removeClass('disabled-div')
        this.$el.find('.js-apollo-fields').removeClass('loading')
        break
      case 'cancelling':
        this.$el.find('.js-cancel-fetch').attr('disabled', true)
        this.$el.find('.js-fetch-apollo-fields').attr('disabled', true)
        this.$el.find('.js-fetch-apollo-fields').html('Cancelling...')
        this.$el.find('.js-apollo-fields').addClass('disabled-div')
        this.$el.find('.js-apollo-fields').addClass('loading')
        break
      default:
        break
    }
  },

  getAccount(cb) {
    this.serviceLocator.accountService.find(
      '',
      {},
      ['name'],
      { pageSize: 1000 },
      (err, res) => {
        if (err) return cb(err)
        return cb(null, res.results[0])
      }
    )
  },

  fetchApolloFields() {
    // Sets all current values
    const form = this.$el.find('form')
    const formData = mapFormToObject(form, this.model.schemata)
    this.model.set(formData)

    const websiteUrl = this.model.get('websiteUrl')
    if (!websiteUrl) {
      this.handleUiState('error')
      return modal({
        title: 'Error',
        content: 'Please enter a website url before fetching Apollo data.',
        buttons: [
          { text: 'OK', event: 'close', keyCodes: [27], className: 'btn' }
        ]
      })
    }
    // Check for exclusions
    const excludedFields = this.$el
      .find('select[name="excludedFields"]')[0]
      .selectize.getValue()

    if (excludedFields.length === Object.keys(apolloMap).length) {
      this.handleUiState('error')
      return modal({
        title: 'Error',
        content:
          'You are excluding all fields currently, please deselect at least one to continue.',
        buttons: [
          { text: 'OK', event: 'close', keyCodes: [27], className: 'btn' }
        ]
      })
    }

    this.persistedExcludedFields = excludedFields

    const domain = new URL(websiteUrl).hostname
    const projection = Object.keys(apolloMap).reduce((acc, key) => {
      if (!excludedFields.includes(key)) acc[apolloMap[key]] = 1
      return acc
    }, {})
    const apolloProjection = Object.keys(projection).reduce((acc, key) => {
      acc[reverseApolloMap[key]] = 1
      return acc
    }, {})

    this.handleUiState('loading')
    this.getAccount((err, account) => {
      if (err) {
        this.serviceLocator.logger.error(err)
        this.handleUiState('error')
        return modal({
          title: 'Error',
          content:
            'There was an error fetching Apollo data. Please try again later.',
          buttons: [
            { text: 'OK', event: 'close', keyCodes: [27], className: 'btn' }
          ]
        })
      }
      this.serviceLocator.companyService.fetchApolloFields(
        { domain, accountId: account },
        { projection: apolloProjection },
        (err, res) => {
          if (window.companyForm.fetchState === FetchState.CANCELLING) {
            this.serviceLocator.logger.info('fetch cancelled')
            window.companyForm.fetchState = FetchState.IDLE
            this.handleUiState('default')
            return
          }

          if (err) {
            this.serviceLocator.logger.error(err)
            this.handleUiState('error')
            return modal({
              title: 'Error',
              content:
                'There was an error fetching Apollo data. Please try again later.',
              buttons: [
                { text: 'OK', event: 'close', keyCodes: [27], className: 'btn' }
              ]
            })
          }
          setTimeout(() => {
            this.handleUiState('success')
            notify('Apollo data fetched successfully.', 'save')
            this.serviceLocator.logger.info('res (apollo)', res)
            const data = res.data
            const filteredData = Object.keys(apolloMap).reduce((acc, key) => {
              if (key === 'logo_url') return acc
              if (key.includes('.')) {
                const [key1, key2] = key.split('.')
                if (data[key1] && data[key1][key2]) {
                  acc[apolloMap[key]] = data[key1][key2]
                }
              } else if (data[key]) {
                acc[apolloMap[key]] = data[key]
              }
              return acc
            }, {})
            if (data.__ai_meta) {
              const keys = Object.keys(data.__ai_meta)
              const labelMap = {
                location: 'HQ Location',
                employeeCount: 'Employee Count',
                ceo: 'CEO',
                revenue: 'Revenue',
                keywordsAndServices: 'Keywords and Services'
              }
              filteredData.aiMeta = keys.map((key) => {
                let value = data.__ai_meta[key]
                if (Array.isArray(value)) {
                  value = value.join(';')
                }
                return { label: labelMap[key], value }
              })
            }
            // Sets logo
            if (res.asset) {
              const collection = new window.Backbone.Collection()
              collection.add(new AssetModel(res.asset))
              this.addImages(collection)
            }
            // Sets fields
            this.model.set({
              ...this.model.toJSON(),
              ...filteredData
            })
            this.serviceLocator.logger.info('res (apollo)', res)
            this.render()
          }, FAKE_TIMEOUT)
        }
      )
    })
  },

  cancelFetch() {
    window.companyForm.fetchState = FetchState.CANCELLING
    this.handleUiState('cancelling')
  },

  generateSlug() {
    const name = this.$el.find('[name=name]').val()
    const $slug = this.$el.find('[name=slug]')
    if (!$slug.val()) $slug.val(slugg(name)).change()
  },

  handleAddImages(e) {
    e.preventDefault()

    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      type: 'image',
      serviceLocator: this.serviceLocator
    })

    collection.getByType('image')

    modal({
      title: 'Images',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        { text: 'Cancel', event: 'Cancel', keyCodes: [27], className: 'btn' },
        {
          text: 'Add selected images',
          event: 'add',
          className: 'btn btn--action',
          keyCodes: [13]
        }
      ]
    })
      .on('add', this.addImages.bind(this, assetListView.selectedCollection))
      .on('close', assetListView.remove.bind(assetListView))
  },

  handleAddClients(e) {
    e.preventDefault()

    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      type: 'image',
      serviceLocator: this.serviceLocator
    })

    collection.getByType('image')

    modal({
      title: 'Clients',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        { text: 'Cancel', event: 'Cancel', keyCodes: [27], className: 'btn' },
        {
          text: 'Add selected clients',
          event: 'add',
          className: 'btn btn--action',
          keyCodes: [13]
        }
      ]
    })
      .on('add', () => {
        const ImageModel = getImageFactory('image').model
        assetListView.selectedCollection.map((model) => {
          const image = new ImageModel(model.attributes)
          this.model.clients.add(image)
          image.setDefaultCrops(clientImageConfig.crops)

          return image
        })
      })
      .on('close', assetListView.remove.bind(assetListView))
  },

  addImages(images) {
    const ImageModel = getImageFactory('image').model
    images.map((model) => {
      const image = new ImageModel(model.attributes)
      this.model.images.add(image)
      image.setDefaultCrops(imageConfig.crops)
      return image
    })
  },

  focusForm() {
    // Focus on name field if it's empty, otherwise focus on the first empty required field
    const $nameField = this.$(':input[name=name]')
    if (!$nameField.val()) {
      $nameField.focus()
    } else {
      // If name is already filled (from query params), focus on the next empty required field
      const $slugField = this.$(':input[name=slug]')
      if (!$slugField.val()) {
        $slugField.focus()
      } else {
        // If both name and slug are filled, focus on website URL
        const $websiteField = this.$(':input[name=websiteUrl]')
        $websiteField.focus()
      }
    }
  },

  handleCancel: formCancelDelegate,

  save(cb) {
    if (!cb) return formSaveDelegate.call(this)
    else return formSaveDelegate.call(this, debug, cb)
  },

  addExecutives() {
    formSaveDelegate.call(this, this.model)
  },

  extractLinkedinId(url) {
    try {
      const urlObj = new URL(url)
      const segments = urlObj.pathname.split('/').filter((segment) => segment)
      return segments[segments.length - 1]
    } catch (error) {
      this.serviceLocator.logger.error(
        'An erro occured extracting the Linkedin URL',
        url,
        error
      )
      return null
    }
  },

  handleSave() {
    this.model.set('website', this.model.get('websiteUrl'))
    const extractedLinkedinId = this.extractLinkedinId(
      this.model.get('linkedinUrl')
    )
    if (extractedLinkedinId) this.model.set('linkedinId', extractedLinkedinId)
    if (this.isNew) {
      modal({
        title: 'Hold on...',
        content: 'Would you like to create an executive for this company?',
        className: 'modal-asset-view narrow',
        buttons: [
          { text: 'No', event: 'save', keyCodes: [27], className: 'btn' },
          {
            text: 'Yes',
            event: 'add',
            className: 'btn btn--action',
            keyCodes: [13]
          }
        ]
      })
        .on('add', this.addExecutives.bind(this))
        .on('save', () => formSaveDelegate.call(this))
    } else {
      formSaveDelegate.call(this)
    }
  },

  renderImageWidgetArea() {
    this.imageArea = new WidgetAreaView({
      model: this.model.images,
      receiveDrops: false,
      serviceLocator: this.serviceLocator,
      widgetItemOptions: imageConfig
    })
    this.$el.find('.js-image-area').append(this.imageArea.$el)
  },

  renderClientsWidgetArea() {
    this.clientArea = new WidgetAreaView({
      model: this.model.clients,
      receiveDrops: false,
      serviceLocator: this.serviceLocator,
      widgetItemOptions: clientImageConfig
    })
    this.$el.find('.js-clients-area').append(this.clientArea.$el)
  },

  renderTextArea() {
    this.richTextEditorInstanceManager = new RichTextEditorInstanceManager()
    this.$el.find('.js-text-editor').each((i, el) => {
      this.richTextEditorInstanceManager.create(el, { height: 500 }, el.id)
    })
    this.on('remove', () => {
      this.richTextEditorInstanceManager.destroy()
    })
  },

  renderItemRepeaters() {
    this.currentTechnologyRepeater = new ItemRepeater(
      this.serviceLocator,
      currentTechnologiesRepeaterConfig,
      this.model.get('currentTechnologies'),
      'Add Technology'
    )

    this.aiMetaRepeater = new ItemRepeater(
      this.serviceLocator,
      aiMetaRepeaterConfig,
      this.model.get('aiMeta'),
      'Add Item'
    )

    this.currentTechnologyRepeater.on('itemsUpdated', () => {
      this.model.set(
        'currentTechnologies',
        this.currentTechnologyRepeater.getItems()
      )
    })

    this.aiMetaRepeater.on('itemsUpdated', () => {
      this.model.set('aiMeta', this.aiMetaRepeater.getItems())
    })

    this.$el
      .find('.js-current-technology-repeater')
      .empty()
      .append(this.currentTechnologyRepeater.render().$el)

    this.$el
      .find('.js-ai-meta-repeater')
      .empty()
      .append(this.aiMetaRepeater.render().$el)

    this.statsItemRepeater = new ItemRepeater(
      this.serviceLocator,
      statsRepeaterConfig,
      this.model.get('stats'),
      'Add Stat'
    )

    this.statsItemRepeater.on('itemsUpdated', () => {
      this.model.set('stats', this.statsItemRepeater.getItems())
    })

    this.$el
      .find('.js-stats-repeater')
      .empty()
      .append(this.statsItemRepeater.render().$el)
  },

  async renderExecutiveSorter() {
    const existingSortOrder = this.model.get('executiveSortOrder') || []

    try {
      // Promisify the `find` method
      const findExecutives = (query, serviceLocator) => {
        return new Promise((resolve, reject) => {
          serviceLocator.executiveService.find(
            query,
            query,
            {},
            {},
            (err, res) => {
              if (err) return reject(err)
              if (!res || !res.results)
                return reject(new Error('No results found'))
              resolve(res.results)
            }
          )
        })
      }

      const executives = await findExecutives(
        {
          companyId: this.model.get('_id')
        },
        this.serviceLocator
      )

      const sortedExecutives = [
        ...existingSortOrder
          .map((id) => executives.find((exec) => exec._id === id))
          .filter(Boolean),
        ...executives.filter((exec) => !existingSortOrder.includes(exec._id))
      ]

      this.executivesItemRepeater = new ItemRepeater(
        this.serviceLocator,
        executivesRepeaterConfig,
        sortedExecutives,
        ''
      )

      this.executivesItemRepeater.on('itemsUpdated', () => {
        const newSortOrder = this.executivesItemRepeater
          .getItems()
          .map((exec) => exec._id)

        this.model.set('executiveSortOrder', newSortOrder)
      })

      const area = this.executivesItemRepeater.render().$el
      area.find('.js-new-item').remove()

      this.$el.find('.js-executives-sort-order-area').empty().append(area)
    } catch (error) {
      // Log detailed error and notify user
      this.serviceLocator.logger.error(error)
      return modal({
        title: 'Error',
        content:
          'There was an error fetching the executive list. Please try again later.',
        buttons: [
          { text: 'OK', event: 'close', keyCodes: [27], className: 'btn' }
        ]
      })
    }
  },

  convertFormat(str) {
    return str.replace(/_/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase())
  },

  loadLinkedArticles() {
    // Only load articles if this is not a new company
    if (this.isNew || !this.model.get('_id')) {
      this.$el.find('.js-no-articles').show()
      return
    }

    const companyId = this.model.get('_id')
    this.$el.find('.js-loading-indicator').show()

    // Query articles linked to this company
    this.serviceLocator.articleService.find(
      '', // no keywords
      { 'companies.company': companyId }, // filter by company ID
      [], // no sort
      {
        pageSize: 50,
        projection: ['headline', 'slug', 'state', 'displayDate', 'contentType']
      }, // pagination and projection
      (err, result) => {
        this.$el.find('.js-loading-indicator').hide()

        if (err) {
          this.serviceLocator.logger.error(
            'Error loading linked articles:',
            err
          )
          this.$el.find('.js-no-articles').show()
          return
        }

        const articles = result.results || []

        if (articles.length === 0) {
          this.$el.find('.js-no-articles').show()
          return
        }

        // Update article count
        this.$el.find('.js-article-count').text(articles.length)

        // Clear existing articles
        const $articlesContainer = this.$el.find('.js-articles-items')
        $articlesContainer.empty()

        // Add each article
        articles.forEach((article) => {
          const publishedDate = article.displayDate
            ? new Date(article.displayDate).toLocaleDateString()
            : 'Not published'

          // Determine state badge class
          const stateBadgeClass =
            article.state === 'Published'
              ? 'state--published'
              : article.state === 'Archived'
              ? 'state--archived'
              : 'state--draft'

          // Create article item element safely matching smart actions structure
          const $articleItem = $(`
            <div class="article-item">
              <div class="article-header">
                <h4 class="article-title">
                  <a href="/articles/${article._id}/form" target="_blank" class="article-link">
                  </a>
                </h4>
                <div class="article-actions">
                  <a href="/articles/${article._id}/form" class="action-btn" target="_blank">Edit</a>
                </div>
              </div>
              <div class="article-meta">
                <span class="article-badge badge--type"></span>
                <span class="article-badge badge--state ${stateBadgeClass}"></span>
              </div>
              <div class="article-date"></div>
            </div>
          `)

          // Set text content safely to prevent XSS
          $articleItem
            .find('.article-link')
            .text(article.headline || 'Untitled Article')
          $articleItem
            .find('.badge--type')
            .text(article.contentType || 'Article')
          $articleItem.find('.badge--state').text(article.state || 'Draft')
          $articleItem.find('.article-date').text(publishedDate)

          $articlesContainer.append($articleItem)
        })

        this.$el.find('.js-articles-list').show()
      }
    )
  },

  loadLinkedInPosts() {
    // Only load posts if this is not a new company
    if (this.isNew || !this.model.get('_id')) {
      this.$el.find('.js-no-posts').show()
      return
    }

    const linkedinPosts = this.model.get('linkedinPosts') || []

    if (linkedinPosts.length === 0) {
      this.$el.find('.js-no-posts').show()
      return
    }

    // Update posts count
    this.$el.find('.js-posts-count').text(linkedinPosts.length)

    // Clear existing posts
    const $postsContainer = this.$el.find('.js-posts-items')
    $postsContainer.empty()

    // Add each post
    linkedinPosts.forEach((post) => {
      const postDate = post.activityDate
        ? new Date(post.activityDate).toLocaleDateString()
        : 'No date'

      // Truncate text for preview
      const postText = post.text || 'No content available'

      // Create post item element
      const $postItem = $(`
        <div class="article-item">
          <div class="article-header">
            <h4 class="article-title">
              <span class="post-text"></span>
            </h4>
          </div>
          <div class="article-meta">
            <span class="article-badge badge--type">LinkedIn Post</span>
          </div>
          <div class="article-date"></div>
        </div>
      `)

      // Set text content safely to prevent XSS
      $postItem.find('.post-text').text(postText)
      $postItem.find('.article-date').text(postDate)

      $postsContainer.append($postItem)
    })

    this.$el.find('.js-posts-list').show()
  },

  addCustomFormData(formData) {
    formData.galleryImages = this.model.galleryImages.toJSON()

    return formData
  },

  handleAddGalleryImage(e) {
    e.preventDefault()
    debug('adding gallery image', arguments)

    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      serviceLocator: this.serviceLocator,
      type: 'image',
      multiple: true
    })

    collection.getByType('image')

    modal({
      title: 'Add Gallery Image',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        { text: 'Add Image', event: 'add', className: 'btn', keyCodes: [13] }
      ]
    })
      .on(
        'add',
        this.addGalleryImage.bind(this, assetListView.selectedCollection)
      )
      .on('close', assetListView.remove.bind(assetListView))
  },

  addGalleryImage(images) {
    debug('gallery images', images)
    const factory = getImageFactory('image')
    const Model = factory.model
    images.map(function (model) {
      const image = new Model(model.attributes)
      this.model.galleryImages.add(image)
      image.setDefaultCrops(this.galleryImageConfig.crops)
      debug('Adding new image', image)
      return image
    }, this)
  },

  renderGalleryImageSelector() {
    this.galleryArea = new WidgetAreaView({
      model: this.model.galleryImages,
      receiveDrops: false,
      serviceLocator: this.serviceLocator,
      widgetItemOptions: this.galleryImageConfig,
      multiple: true
    })
    this.$el.find('.js-gallery-image-area').append(this.galleryArea.$el)
  },

  render() {
    this.cleanup()

    this.$el.empty().append(
      template({
        data: this.model.toJSON(),
        title: this.formTitle(this.model.toJSON()),
        config: config,
        excludedFields: this.persistedExcludedFields
      })
    )

    this.renderTextArea()
    this.renderImageWidgetArea()
    this.renderItemRepeaters()
    this.renderClientsWidgetArea()
    this.renderExecutiveSorter()
    this.renderGalleryImageSelector()
    this.loadLinkedArticles()
    this.loadLinkedInPosts()

    this.$el
      .find('#permissions')
      .append(this.instancePermissionField.render().$el)
    this.instancePermissionField.on('change', (data) => {
      this.model.set('instancePermissions', data)

      const isVerified = (permission) => {
        return (
          permission &&
          typeof permission === 'object' &&
          permission.isVerified === true
        )
      }

      const hasVerifiedPermission = Object.values(data).some(isVerified)

      this.model.set('isVerified', hasVerifiedPermission || false)
    })

    this.$el
      .find('.js-leadership-team')
      .append(this.executiveMultiSelect.render().$el)
    this.executiveMultiSelect.on('change', (instance) =>
      this.model.set('leadershipTeam', instance)
    )
    this.attachView(this.executiveMultiSelect)

    const industryOptions = []
    for (let i = 0; i < industriesJson.length; i++) {
      industryOptions.push({
        value: industriesJson[i].cleaned_name,
        text: industriesJson[i].cleaned_name
      })
    }

    const excludedFieldsOptions = Object.keys(apolloMap).map((key) => {
      let text
      switch (key) {
        case 'logo_url':
          text = 'Logo'
          break
        case 'id':
          text = 'Apollo ID'
          break
        case 'account.salesforce_id':
          text = 'Salesforce ID'
          break
        default:
          text = this.convertFormat(key)
      }
      return { value: key, text }
    })

    this.$el.find('.js-selectize').each((i, el) => {
      const elMap = {
        industries: {
          persist: false,
          create: false,
          options: industryOptions
        },
        keywords: {
          persist: true,
          create: true,
          options: []
        },
        excludedFields: {
          persist: false,
          create: false,
          options: excludedFieldsOptions
        }
      }
      $(el).selectize({
        delimiter: ',',
        createOnBlur: true,
        create: elMap[el.name].create,
        persist: elMap[el.name].persist,
        options: elMap[el.name].options,
        onInitialize: () =>
          el.selectize.on('change', () => {
            this.model.set(el.name, el.selectize.getValue())
          })
      })
    })

    return this
  }
})
