const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/list-item.jade'))

module.exports = Backbone.View.extend({
  events: {
    'click .js-edit': 'edit',
    'click .js-select': 'toggleSelect'
  },

  className: '',

  initialize(options) {
    this.options = options
    this.listenTo(this.model, 'select', this.select)
    this.listenTo(this.model, 'deSelect', this.deSelect)
    this.render()
  },

  getVerifiedInstances() {
    const instances = this.options.instances
    const instancePermissions = this.model.get('instancePermissions')

    if (
      typeof instances === 'object' &&
      typeof instancePermissions === 'object'
    ) {
      const verifiedInstanceIDs = Object.keys(instancePermissions).map((key) =>
        instancePermissions[key].isVerified ? key : null
      )

      if (verifiedInstanceIDs) {
        const verifiedInstances = []

        for (const id of verifiedInstanceIDs) {
          const instance = instances.find((instance) => instance._id === id)
          if (!instance) {
            continue
          }

          verifiedInstances.push(instance.name)
        }

        return verifiedInstances.length
          ? verifiedInstances.sort((a, b) => a.localeCompare(b)).join(', ')
          : 'None'
      }
    }

    return 'None'
  },

  attributes() {
    return { id: 'redirect-id-' + this.model.get('_id') }
  },

  edit(e) {
    // Detect middle click or CMD click to allow <a> to open in new tab
    if (e.which === 2 || e.metaKey) return
    e.preventDefault()
    this.trigger('edit')
  },

  toggleSelect(e) {
    var isChecked = $(e.target).is(':checked')
    this.model.trigger(isChecked ? 'select' : 'deSelect', this.model)
  },

  select() {
    this.$('.js-select')[0].checked = true
  },

  deSelect() {
    this.$('.js-select')[0].checked = false
  },

  render() {
    const data = this.model.toJSON()
    data.verifiedInstances = this.getVerifiedInstances()

    this.$el.empty().append(
      template({
        data,
        allowed: this.options.serviceLocator.allowed,
        format: this.options.serviceLocator.format
      })
    )
    return this
  }
})
