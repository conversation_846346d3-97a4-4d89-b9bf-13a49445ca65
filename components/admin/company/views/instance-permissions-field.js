const View = require('ventnor')
const compileJade = require('browjadify-compile')
const join = require('path')
const template = compileJade(
  join(__dirname, '../templates/instance-permissions-field/form.jade')
)
class InstancePermissionFields extends View {
  constructor(serviceLocator, selectedPermissions) {
    super(...arguments)
    this.fields = [
      {
        name: 'isVerified',
        value: false,
        title: 'Is Verified?'
      },
      {
        name: 'enableContentHubNavigation',
        value: false,
        title: 'Content Hub',
        children: [
          {
            name: 'enableContentHubArticle',
            title: 'Articles',
            value: false
          },
          {
            name: 'enableContentHubEvent',
            title: 'Events',
            value: false
          },
          {
            name: 'enableContentHubInterview',
            title: 'Interviews',
            value: false
          },
          {
            name: 'enableContentHubPodcast',
            title: 'Podcasts',
            value: false
          },
          {
            name: 'enableContentHubCompanyReport',
            title: 'Reports',
            value: false
          },
          {
            name: 'enableContentHubVideo',
            title: 'Videos',
            value: false
          },
          {
            name: 'enableContentHubWebinar',
            title: 'Webinars',
            value: false
          },
          {
            name: 'enableContentHubWhitepaper',
            title: 'Whitepapers',
            value: false
          }
        ]
      },
      {
        name: 'enableExecutivesNavigation',
        value: false,
        title: 'Executives'
      },
      {
        name: 'enablePartnershipsNavigation',
        value: false,
        title: 'Partnerships'
      },
      {
        name: 'enableGetInTouchNavigation',
        value: false,
        title: 'Contact Us'
      }
    ]
    this.$el = $('<div />')
    this.el = this.$el[0]
    this.selectedPermissions = selectedPermissions || {}
    this.permissionsForTemplate = {}
    this.template = template
    this.templateData = []
  }

  initialize() {
    this.serviceLocator.instanceService.find({}, {}, [], {}, (err, res) => {
      if (err) {
        return this.serviceLocator.logger.error('Cannot find instances', err)
      }

      // reset template data
      this.templateData = []

      res.results.forEach(({ _id, name }) => {
        const obj = {
          _id,
          name,
          fields: this.fields.map((f) => {
            const field = { ...f }
            if (field.children) {
              field.children = field.children.map((c) => ({ ...c }))
            }

            return field
          })
        }

        for (const field of obj.fields) {
          if (!(_id in this.selectedPermissions)) {
            this.selectedPermissions[_id] = {}
          }

          if (field.name in this.selectedPermissions[_id]) {
            field.value = this.selectedPermissions[_id][field.name]
          } else {
            this.selectedPermissions[_id][field.name] = field.value
          }

          if (field.children) {
            field.children.forEach((child) => {
              if (child.name in this.selectedPermissions[_id]) {
                child.value = this.selectedPermissions[_id][child.name]
              } else {
                this.selectedPermissions[_id][child.name] = child.value
              }
            })
          }
        }

        this.permissionsForTemplate[_id] = obj
        this.templateData.push(this.permissionsForTemplate[_id])
      })
    })
  }

  showHideChildren() {
    const fields = this.$el.find('input')

    Object.keys(fields).forEach((key) => {
      const field = fields[key]

      if (typeof field !== 'object') {
        return
      }

      const { checked, name } = field

      if (name && name.includes('enableContentHubNavigation')) {
        const formField = $(field).closest('.form-field')

        if (formField) {
          const children = formField.find('.form-field')

          if (children) {
            children.css('display', checked ? 'block' : 'none')
          }
        }
      }
    })
  }

  updateSelection() {
    const fields = this.$el.find('input')

    Object.keys(fields).forEach((key) => {
      const field = fields[key]

      if (typeof field !== 'object') {
        return
      }

      if (!('name' in field)) {
        return
      }

      const match = field.name.match(/^([^[]+)\[([^\]]+)]$/)
      if (!match) {
        // handle invalid name format
        return
      }

      const [, id, fieldName] = match

      this.selectedPermissions[id][fieldName] =
        'checked' in field ? field.checked : field.value
    })

    this.emit('change', this.selectedPermissions)
  }

  render() {
    this.initialize()

    setTimeout(() => {
      const data = this.templateData.sort((a, b) =>
        a.name.localeCompare(b.name)
      )
      this.$el.append(this.template({ data }))

      this.$el.on(
        'change',
        'input[type="checkbox"]',
        this.updateSelection.bind(this)
      )

      this.$el.on(
        'change',
        'input[type="checkbox"]',
        this.showHideChildren.bind(this)
      )
      this.showHideChildren()
    }, 2000)

    return this
  }
}

module.exports = InstancePermissionFields
