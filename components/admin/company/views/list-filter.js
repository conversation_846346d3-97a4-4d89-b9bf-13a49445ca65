const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/list-filter.jade'))
const BaseView = require('cf-base-view')

module.exports = BaseView.extend({
  className: 'list-filters',

  events: {
    'submit form': 'handleSubmit',
    'click input[type=reset]': 'handleClear',
    'click .js-filter': 'handleSubmit',
    'click .js-clear': 'handleClear',
    'click .dropdown-menu a': 'handleDropDownAction'
  },

  initialize() {
    this.render()
  },

  handleDropDownAction(e) {
    e.preventDefault()
    var $target = $(e.target)
    if ($target.hasClass('js-state')) {
      this.$('.js-state-current').text('Current: ' + $target.text())
      this.$('form [name=state]').val($target.attr('data-state'))
    } else if ($target.hasClass('js-sort')) {
      this.$('.js-sort-current').text('Current: ' + $target.text())
      this.$('form [name=sort]').val($target.attr('data-direction'))
    } else if ($target.hasClass('js-type')) {
      this.$('.js-type-current').text('Current: ' + $target.text())
      this.$('form [name=type]').val($target.attr('data-type'))
    }
  },

  handleSubmit(e) {
    if (e) e.preventDefault()

    const params = { filter: {}, sort: [] }
    const map = {
      dateCreated: ['createdDate', 'asc'],
      '-dateCreated': ['createdDate', 'desc'],
      '-score': ['score', 'desc']
    }
    const keywords = this.$('form [name=keywords]').val()
    let instance = this.$('form [name=instance]').val()
    const isVerified = this.$('form [name=isVerified]:checked').length

    params.sort = map[this.$('form [name=sort]').val()]

    if (keywords.length) {
      params.keywords = keywords
    }

    if (instance) {
      instance = this.instanceCollection.get(instance)
      params.filter.instance = instance.id
    }

    if (isVerified) {
      params.filter.isVerified = 1
    }

    this.trigger('filter', params)
  },

  handleClear(e) {
    e.preventDefault()
    this.$('form [name=keywords]').val('')
    this.trigger('filter', null)
  },

  render() {
    this.$el.empty().append(template)
    this.$el.find('.js-tooltip-trigger').tooltip({ html: true })
  }
})
