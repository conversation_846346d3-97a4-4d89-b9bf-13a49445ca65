const debug = require('../../../../admin/source/js/lib/debug')(
  'company list view'
)
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/list.jade'))
const ListItemView = require('./list-item')
const ListFilterView = require('./list-filter')
const GenericListView = require('../../../../admin/source/js/lib/generic/list-view')
const listDeleteDelegate = require('../../../../admin/source/js/lib/list-delete-delegate')
const extend = require('lodash.assign')
const config = window.config
const modal = require('modal')

module.exports = GenericListView.extend({
  events: extend({}, GenericListView.prototype.events, {
    'click .js-more': 'loadMore'
  }),

  template,

  initialize() {
    GenericListView.prototype.initialize.apply(this, arguments)
    this.arguments = arguments
    this.renderFilters()
    this.listenTo(this.collection, 'filter', this.maintainOrder)
    this.listenTo(this.collection, 'filter', () => {
      this.trigger('clearSelection')
    })
  },

  renderFilters() {
    this.filters = new ListFilterView()
    this.filters.on('filter', this.applyFilters, this)
    this.$('.js-filters').append(this.filters.$el)
  },

  applyFilters(params) {
    this.collection.applyFilter(params)
  },

  createListItemView(model) {
    return new ListItemView({
      model: model,
      serviceLocator: this.options.serviceLocator,
      instances: this.options.instances
    })
  },

  loadMore() {
    this.collection.loadMore()
  },
  handleDelete() {
    debug('delete')
    if (!this.selectedCollection.length) return

    const models = this.selectedCollection.models
    const preventDeleteCompanies = models.filter((m) =>
      m.get('showProfilePage')
    )

    if (!preventDeleteCompanies.length)
      return listDeleteDelegate.call(this, this.selectedCollection)

    modal({
      title: 'Cannot Delete Companies',
      content:
        `The following companies have a profile page and cannot be deleted. If you wish to delete them, please disable the profile page and add a redirect first. \n\n` +
        preventDeleteCompanies.map((m) => m.get('name')).join(', '),
      buttons: [{ text: 'Dismiss', className: 'btn' }]
    })
  },
  render() {
    this.$el.empty().append(
      this.template({
        config,
        displayName: this.options.displayName,
        allowed: this.options.serviceLocator.allowed
      })
    )
    return this
  }
})
