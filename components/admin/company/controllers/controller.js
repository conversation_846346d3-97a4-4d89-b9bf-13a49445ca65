const ListView = require('../views/list')
const FormView = require('../views/form')
const Collection = require('../collections/company')
const RedirectModel = require('../models/company')
const async = require('async')
const debug = require('../../../../admin/source/js/lib/debug')('company')

module.exports = (serviceLocator) => {
  const router = serviceLocator.router
  const collection = new Collection()
  const displayName = { singular: 'Company', plural: 'Companies' }

  const getInstances = (cb) => {
    serviceLocator.instanceService.find(
      '',
      {},
      ['name'],
      { pageSize: 1000 },
      (err, res) => {
        if (err) {
          return cb(err)
        }

        return cb(null, res.results)
      }
    )
  }

  router.route('companies(/)', 'listCompanies', () => {
    debug('company url list view route triggered')
    if (!serviceLocator.allow('company', 'read')) return false

    async.parallel(
      {
        instances: getInstances
      },
      (err, res) => {
        if (err) {
          return this.serviceLocator.logger.error(
            err,
            'Could not load instances'
          )
        }

        const listView = new ListView({
          collection,
          serviceLocator,
          displayName,
          instances: res.instances || []
        })

        listView.on(
          'new',
          router.navigate.bind(router, 'companies/form', { trigger: true })
        )

        listView.on('edit', (model) => {
          router.navigate('companies/' + model.id + '/form', {
            trigger: true
          })
        })

        router.render(listView, displayName.plural)
        collection.load()
      }
    )
  })

  const bindSaveAndCancel = (view) => {
    view.on('save', (model) => {
      router.navigate('companies', { trigger: true })
    })

    view.on('cancel', (model) => {
      router.navigate('companies', { trigger: true })
    })
    return view
  }

  router.route('companies/form(/)', 'newCompany', () => {
    if (!serviceLocator.allow('company', 'create')) {
      return false
    }
    debug('company create view route triggered')

    // Extract query parameters from URL
    const urlParams = new URLSearchParams(window.location.search)
    const queryParams = {
      name: urlParams.get('name'),
      slug: urlParams.get('slug')
    }

    const model = new RedirectModel({}, { collection: collection })
    const view = new FormView({
      model,
      serviceLocator,
      title: displayName.singular,
      isNew: true,
      queryParams: queryParams
    })

    router.render(bindSaveAndCancel(view), 'New ' + displayName.singular)
  })

  router.route('companies/:id/form(/)', 'editCompany', (id) => {
    if (!serviceLocator.allow('company', 'update')) {
      return false
    }
    debug('company edit view route triggered')

    collection.retrieve(id, (err, model) => {
      if (err) {
        router.trigger('notFound', err.message)
        return
      }
      debug('Loading form view', model)
      var view = new FormView({
        model,
        serviceLocator,
        title: displayName.singular
      })

      router.render(bindSaveAndCancel(view), 'Edit ' + displayName.singular)
    })
  })
}
