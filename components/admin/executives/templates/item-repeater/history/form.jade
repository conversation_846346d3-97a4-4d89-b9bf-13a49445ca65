extends ../../../../widget/templates/form/base-item-repeater

block custom-form
  .panel.panel-styled
    .panel-header
      h2 History
    .panel-content
      .form-row(id='field--company', data-field='company')
        label
          span.form-label-text Company
          .js-company-select.form-field
        .js-error

      .form-row(id='field--fallbackCompanyName', data-field='fallbackCompanyName')
        label
          span.form-label-text Fallback Company Name
          input.control.control--text.form-field(type='text', name='fallbackCompanyName', value=data.fallbackCompanyName)
        .js-error

      .form-row(id='field--jobTitle', data-field='jobTitle')
        label
          span.form-label-text Job title
            abbr(title='This field is required') *
          input.control.control--text.form-field(type='text', name='jobTitle', value=data.jobTitle)
        .js-error

      .form-row(id='field--startMonth', data-field='startMonth')
        label
          span.form-label-text Start Month
            abbr(title='This field is required') *
          select.control.control--choice.form-field(name='startMonth')
            option(value=1, selected=data.startMonth === 1) January
            option(value=2, selected=data.startMonth === 2) February
            option(value=3, selected=data.startMonth === 3) March 
            option(value=4, selected=data.startMonth === 4) April
            option(value=5, selected=data.startMonth === 5) May
            option(value=6, selected=data.startMonth === 6) June
            option(value=7, selected=data.startMonth === 7) July
            option(value=8, selected=data.startMonth === 8) August
            option(value=9, selected=data.startMonth === 9) September
            option(value=10, selected=data.startMonth === 10) October
            option(value=11, selected=data.startMonth === 11) November
            option(value=12, selected=data.startMonth === 12) December
        .js-error

      .form-row(id='field--startYear', data-field='startYear')
        label
          span.form-label-text Start Year
            abbr(title='This field is required') *
          input.control.control--text.form-field(type='number', name='startYear', value=data.startYear, placeholder=2012, min='1910', max='2100')
        .js-error

      .form-row(id='field--endMonth', data-field='endMonth')
        label
          span.form-label-text End Month
          select.control.control--choice.form-field(name='endMonth')
            option(value=1, selected=data.endMonth === 1) January
            option(value=2, selected=data.endMonth === 2) February
            option(value=3, selected=data.endMonth === 3) March 
            option(value=4, selected=data.endMonth === 4) April
            option(value=5, selected=data.endMonth === 5) May
            option(value=6, selected=data.endMonth === 6) June
            option(value=7, selected=data.endMonth === 7) July
            option(value=8, selected=data.endMonth === 8) August
            option(value=9, selected=data.endMonth === 9) September
            option(value=10, selected=data.endMonth === 10) October
            option(value=11, selected=data.endMonth === 11) November
            option(value=12, selected=data.endMonth === 12) December
        .js-error

      .form-row(id='field--endYear', data-field='endYear')
        label
          span.form-label-text End Year
          input.control.control--text.form-field(type='number', name='endYear', value=data.endYear, placeholder='Now', min='1910', max='2100')
        .js-error
        .form-row-description.form-copy
          p Leave blank for to display "Now"
