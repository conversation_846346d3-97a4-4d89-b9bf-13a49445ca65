.page-content
  .toolbar
    .centering.js-toolbar
      .toolbar__left
        .control-group
          .btn-group
            button.btn.js-cancel(type='button') Cancel
          .btn-group
            .dropdown
              button.btn.btn--secondary.dropdown-toggle(type='button', data-toggle='dropdown', aria-haspopup='true', aria-expanded='false', disabled)
                | Actions
                span.caret
              ul.dropdown-menu
                li
                  a.js-show-quick-form(href='#', style="font-size:1rem;") Show Quick Form
        .btn-group.smart-actions-toolbar
          .js-smart-actions-container
      .control-group
        button.btn.btn--action.js-save(type='button') Save

  .centering

    header.page-header

      h1= title

    form
      .js-errors-summary

      //- Allow submission of form using enter button
      input.hidden(type='submit')

      .js-quick-create-container(style='display: none;')
      include form-sections/linked-articles
      include form-sections/linkedin
      include form-sections/basic-details
      include form-sections/apollo
      include form-sections/bio
      include form-sections/images
      include form-sections/additional-details
      include form-sections/history
      include form-sections/milestones
      include form-sections/gallery
      include form-sections/linkedin-posts
        
