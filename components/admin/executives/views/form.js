const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const RichTextEditorInstanceManager = require('../../../../admin/source/js/lib/rich-text-editor-instance-manager')
const CompanySelectView = require('../../company/views/company-select')
const debug = require('../../../../admin/source/js/lib/debug')(
  'executive form view'
)
const slugg = require('slugg')
const BaseView = require('cf-base-view')
const modal = require('modal')
const formSaveDelegate = require('../../../../admin/source/js/lib/form-save-delegate')(
  debug
)
const formCancelDelegate = require('cf-form-cancel-delegate')(debug)
const formTitleDelegate = require('../../../../admin/source/js/lib/form-title-delegate')
const AssetCollection = require('../../asset/collections/asset')
const AssetPickerView = require('../../asset/views/picker')
const getImageFactory = require('../../asset/lib/image-factory')()
const WidgetAreaView = require('../../widget/views/widget-area')
const imageConfig = require('../../../service/executive/image-config.json')
const ItemRepeater = require('../../widget/views/item-repeater')
const InstanceSelect = require('../../instance/views/instance-select')
const QuickCreatePopoverView = require('../../quick-create/views/popover')
const SmartActionsToolbarView = require('./smart-actions-toolbar')
const config = window.config
const notify = require('../../notification/foreground')
const mapFormToObject = require('cf-map-form-to-object')
const AssetModel = require('../../asset/models/asset')
const async = require('async')

const milestonesRepeaterConfig = {
  itemModel: require('../models/milestone'),
  itemView: require('./item-repeater/milestones/item'),
  formView: require('./item-repeater/milestones/form'),
  itemNames: {
    singular: 'Milestone',
    plural: 'Milestones'
  }
}

const historyRepeaterConfig = {
  itemModel: require('../models/history'),
  itemView: require('./item-repeater/history/item'),
  formView: require('./item-repeater/history/form'),
  itemNames: {
    singular: 'History',
    plural: 'History'
  }
}

// Fetch states for LinkedIn data fetching
const FetchState = {
  IDLE: 'idle',
  FETCHING: 'fetching',
  CANCELLING: 'cancelling'
}

// Initialize global fetch state
if (!window.executiveForm) {
  window.executiveForm = { fetchState: FetchState.IDLE }
}

const galleryImageConfig = {
  crops: [
    {
      name: '16:9',
      aspectRatio: '16:9'
    }
  ],
  contexts: [
    {
      name: 'Image',
      allowMultipleSelection: true
    }
  ]
}

module.exports = BaseView.extend({
  events: {
    'click .js-save': 'handleSave',
    'click .js-cancel': 'handleCancel',
    'click .js-fetch-linkedin-fields': 'fetchLinkedInFields',
    'click .js-cancel-fetch': 'cancelFetch',
    submit: (e) => e.preventDefault()
  },

  galleryImageConfig,

  initialize({ serviceLocator, title, queryParams }) {
    this.initialModel = this.model.toJSON()
    this.formTitle = formTitleDelegate(title)

    this.queryParams = queryParams || {}
    if (this.queryParams.name) {
      this.model.set('name', this.queryParams.name)
      this.model.set('slug', slugg(this.queryParams.name))
    }

    this.serviceLocator = serviceLocator

    this.$el.on('change', '[name=name]', this.generateSlug.bind(this))
    this.$el.on('click', '.js-images-add', this.handleAddImages.bind(this))
    this.$el.on('click', '.js-show-quick-form', this.showQuickForm.bind(this))
    this.$el.on(
      'click',
      '.js-gallery-image-add',
      this.handleAddGalleryImage.bind(this)
    )

    this.on('remove', () => {
      this.cleanup()

      // Clean up quick create popover
      if (this.quickCreatePopover) {
        this.quickCreatePopover.remove()
        this.quickCreatePopover = null
      }

      // Clean up smart actions toolbar
      if (this.smartActionsToolbar) {
        this.smartActionsToolbar.remove()
        this.smartActionsToolbar = null
      }
    })

    this.on('remove', this.destroy)

    this.render()
    this.initializeSmartActionsToolbar()
    this.populateSmartActionsFromCache()

    this.listenTo(this, 'afterAppend', this.focusForm)
    // this.listenTo(this, 'afterAppend', this.loadLinkedArticles)
    // this.listenTo(this, 'afterAppend', this.loadLinkedInPosts)

    this.articles = null
  },

  cleanup() {
    if (this.richTextEditorInstanceManager) {
      this.richTextEditorInstanceManager.destroy()
    }
  },

  generateSlug() {
    const name = this.$el.find('[name=name]').val()
    const $slug = this.$el.find('[name=slug]')
    if (!$slug.val()) $slug.val(slugg(name)).change()
  },

  handleAddImages(e) {
    e.preventDefault()

    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      type: 'image',
      serviceLocator: this.serviceLocator
    })

    collection.getByType('image')

    modal({
      title: 'Images',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        { text: 'Cancel', event: 'Cancel', keyCodes: [27], className: 'btn' },
        {
          text: 'Add selected images',
          event: 'add',
          className: 'btn btn--action',
          keyCodes: [13]
        }
      ]
    })
      .on('add', this.addImages.bind(this, assetListView.selectedCollection))
      .on('close', assetListView.remove.bind(assetListView))
  },

  addImages(images) {
    const ImageModel = getImageFactory('image').model
    images.map((model) => {
      const image = new ImageModel(model.attributes)
      this.model.images.add(image)
      image.setDefaultCrops(imageConfig.crops)
      return image
    })
  },

  // Add photo asset from LinkedIn data
  addPhotoAsset(asset) {
    const collection = new window.Backbone.Collection()
    collection.add(new AssetModel(asset))
    this.addImages(collection)
  },

  focusForm() {
    this.$(':input[name=title]').focus()
  },

  handleCancel: formCancelDelegate,

  handleSave() {
    this.model.set('url', `${this.serviceLocator.config.apiUrl}/executives`)
    formSaveDelegate.call(this)
  },

  // Initialize quick create popover
  initializeQuickCreatePopover() {
    // Clean up existing popover if it exists
    if (this.quickCreatePopover) {
      this.quickCreatePopover.remove()
    }

    this.quickCreatePopover = new QuickCreatePopoverView(this.serviceLocator)

    // Listen for company saved events
    this.quickCreatePopover.on('companySaved', () => {
      // Optionally refresh the company select or show a notification
      // this.serviceLocator.logger.info('Company saved from quick create')
      this.renderCompanySelect()
    })

    // Render and append to the quick create container
    this.$el
      .find('.js-quick-create-container')
      .empty()
      .append(this.quickCreatePopover.render().$el)
  },

  // Show quick form via dropdown action
  showQuickForm(e) {
    e.preventDefault()
    if (this.quickCreatePopover) {
      this.quickCreatePopover.openPopover()
    }
  },

  // Show quick form with pre-filled data (called from smart actions)
  showQuickFormWithData(companyData) {
    if (this.quickCreatePopover) {
      this.quickCreatePopover.openPopover()

      // Pre-fill the form with company data after a short delay
      setTimeout(() => {
        this.prefillCompanyForm(companyData)
      }, 200)
    }
  },

  // Pre-fill company form with data
  prefillCompanyForm(companyData) {
    if (!companyData) return

    const $form = $('.quick-create-popover form')
    if ($form.length === 0) return

    // Pre-fill name
    if (companyData.name) {
      $form.find('[name="name"]').val(companyData.name).trigger('change')
    }

    // Pre-fill LinkedIn URL
    if (companyData.linkedInUrl) {
      $form
        .find('[name="linkedinUrl"]')
        .val(companyData.linkedInUrl)
        .trigger('change')
    }
  },

  // Initialize smart actions toolbar
  initializeSmartActionsToolbar() {
    // Only initialize if executive has an ID (not new)
    // if (!this.model.get('_id')) return

    // Clean up existing toolbar if it exists
    if (this.smartActionsToolbar) {
      this.smartActionsToolbar.remove()
    }

    this.smartActionsToolbar = new SmartActionsToolbarView({
      serviceLocator: this.serviceLocator,
      executive: this.model.toJSON()
    })

    // Listen for company creation events
    this.renderSmartActionsToolbar()
  },

  renderSmartActionsToolbar() {
    if (!this.smartActionsToolbar) return
    this.smartActionsToolbar.render()
    this.smartActionsToolbar.bindEvents()
    this.$el
      .find('.js-smart-actions-container')
      .empty()
      .append(this.smartActionsToolbar.$el)
    this.smartActionsToolbar.on('companyCreated', () => {
      this.renderCompanySelect()
      this.smartActionsToolbar.load()
    })
    // // Listen for quick create requests from smart actions
    this.smartActionsToolbar.on('showQuickCreate', (companyData) => {
      this.showQuickFormWithData(companyData)
    })
    if (this.smartActionsToolbar.actions.length > 0)
      this.smartActionsToolbar.showLoaded()
  },

  // Handle UI state changes during fetch operations
  handleUiState(state) {
    const $fetchButton = this.$el.find('.js-fetch-linkedin-fields')
    const $cancelButton = this.$el.find('.js-cancel-fetch')

    switch (state) {
      case 'fetching':
        $fetchButton.prop('disabled', true).text('Fetching...')
        $cancelButton.prop('disabled', false)
        this.$el.find('.js-linkedin-fields').addClass('loading')
        this.$el.find('form').addClass('disabled-div')
        this.$el.find('form').addClass('loading')
        break
      case 'cancelling':
        $fetchButton.prop('disabled', true).text('Cancelling...')
        $cancelButton.prop('disabled', true)
        this.$el.find('form').addClass('disabled-div')
        this.$el.find('form').addClass('loading')
        break
      case 'success':
        $fetchButton.prop('disabled', false).text('Scrape LinkedIn')
        $cancelButton.prop('disabled', true)
        this.$el.find('.js-linkedin-fields').removeClass('loading')
        this.$el.find('form').removeClass('disabled-div')
        this.$el.find('form').removeClass('loading')
        break
      case 'error':
        $fetchButton.prop('disabled', false).text('Scrape LinkedIn')
        $cancelButton.prop('disabled', true)
        this.$el.find('.js-linkedin-fields').removeClass('loading')
        this.$el.find('form').removeClass('disabled-div')
        this.$el.find('form').removeClass('loading')
        break
      default:
        $fetchButton.prop('disabled', false).text('Scrape LinkedIn')
        $cancelButton.prop('disabled', true)
        this.$el.find('.js-linkedin-fields').removeClass('loading')
        this.$el.find('form').removeClass('disabled-div')
        this.$el.find('form').removeClass('loading')
        break
    }
  },

  // Extract LinkedIn ID from URL (same logic as company form)
  extractLinkedinPublicId(url) {
    try {
      const urlObj = new URL(url)
      const segments = urlObj.pathname.split('/').filter((segment) => segment)
      return segments[segments.length - 1]
    } catch (error) {
      this.serviceLocator.logger.error(
        'An error occurred extracting the LinkedIn URL',
        url,
        error
      )
      return null
    }
  },

  // Match company based on LinkedIn URL (using callback pattern)
  matchCompany(companyDetails, callback) {
    if (!companyDetails) {
      this.serviceLocator.logger.info('No company details provided')
      return callback(null, null)
    }
    const companyLinkedinUrl = companyDetails.linkedInUrl
    const companyName = companyDetails.name

    if (!companyLinkedinUrl) {
      this.serviceLocator.logger.info('No company LinkedIn URL provided')
      return callback(null, null)
    }

    if (!companyName) {
      this.serviceLocator.logger.info('No company name provided')
      return callback(null, null)
    }

    const linkedinPublicId = this.extractLinkedinPublicId(companyLinkedinUrl)
    if (!linkedinPublicId) {
      this.serviceLocator.logger.warn(
        'Could not extract LinkedIn ID from company URL:',
        companyLinkedinUrl
      )
      return callback(null, null)
    }

    // this.serviceLocator.logger.info(
    //   'Searching for company with LinkedIn ID:',
    //   linkedinPublicId
    // )

    const query = { name: { $regex: linkedinPublicId, $options: 'i' } }

    // this.serviceLocator.logger.info('Query:', query)
    this.serviceLocator.companyService.find(
      '',
      query,
      [],
      {},
      (err, companies) => {
        if (err) {
          this.serviceLocator.logger.error(
            'Error finding company by LinkedIn ID:',
            err
          )
          return callback(err, null)
        }

        if (companies && companies.results && companies.results.length > 0) {
          // this.serviceLocator.logger.info(
          //   'Found matching company:',
          //   companies.results[0].name
          // )
          return callback(null, companies.results[0]._id)
        } else {
          this.serviceLocator.logger.warn(
            'No company found with LinkedIn ID:',
            linkedinPublicId
          )

          // Show popup when company cannot be matched
          const data = {
            name: companyName,
            linkedInUrl: companyLinkedinUrl
          }
          this.smartActionsToolbar.addSmartAction({
            type: companyDetails.isFromJobHistory
              ? 'create-history-company'
              : 'create-company',
            name: companyName,
            companyData: data,
            buttonDisabled: false
          })

          return callback(null, null)
        }
      }
    )
  },

  // Process position history with company matching
  processPositionHistory(positionHistory, callback) {
    // this.serviceLocator.logger.info(
    //   'Processing position history:',
    //   positionHistory.length,
    //   'positions',
    //   positionHistory
    // )

    const processedPositions = []
    let processedCount = 0

    if (positionHistory.length === 0) {
      return callback(null, [])
    }

    positionHistory.forEach((position, index) => {
      const startYear =
        position.startEndDate && position.startEndDate.start
          ? String(position.startEndDate.start.year)
          : null

      const startMonth =
        position.startEndDate && position.startEndDate.start
          ? String(position.startEndDate.start.month)
          : null

      const endYear =
        position.startEndDate && position.startEndDate.end
          ? String(position.startEndDate.end.year)
          : null

      const endMonth =
        position.startEndDate && position.startEndDate.end
          ? String(position.startEndDate.end.month)
          : null

      const isCurrent = !endYear

      // Create base history item according to schema
      const historyItem = {
        jobTitle: position.title || '',
        startYear: startYear,
        endYear: endYear,
        startMonth: parseInt(startMonth),
        endMonth: parseInt(endMonth),
        isCurrent: isCurrent,
        apollo_id: null, // Will be set if we can match the company
        companyId: null, // Will be set if we can match the company.
        fallbackCompanyName: position.companyName
      }

      // Try to match company using LinkedIn URL
      if (position.linkedInUrl) {
        this.matchCompany(
          {
            name: position.companyName,
            linkedInUrl: position.linkedInUrl,
            isFromJobHistory: true
          },
          (err, companyId) => {
            if (err) {
              callback(null, null)
            }
            if (!err && companyId) {
              historyItem.companyId = companyId
              // this.serviceLocator.logger.info(
              //   `Matched company for position ${index}:`,
              //   companyId
              // )

              // Try to get Apollo ID from the company
              this.getCompanyApolloId(companyId, (apolloErr, apolloId) => {
                if (!apolloErr && apolloId) {
                  historyItem.apollo_id = apolloId
                  // this.serviceLocator.logger.info(
                  //   `Found Apollo ID for position ${index}:`,
                  //   apolloId
                  // )
                }

                processedPositions[index] = historyItem
                processedCount++

                if (processedCount === positionHistory.length) {
                  // Filter out null entries and return
                  const validPositions = processedPositions.filter(
                    (pos) => pos !== null && pos !== undefined
                  )
                  callback(null, validPositions)
                }
              })
            } else {
              this.serviceLocator.logger.warn(
                `Could not match company for position ${index}:`,
                position.linkedInUrl
              )
              // Still include the position without companyId
              processedPositions[index] = historyItem
              processedCount++

              if (processedCount === positionHistory.length) {
                const validPositions = processedPositions.filter(
                  (pos) => pos !== null && pos !== undefined
                )
                callback(null, validPositions)
              }
            }
          }
        )
      } else {
        this.serviceLocator.logger.warn(`No LinkedIn URL for position ${index}`)
        processedPositions[index] = historyItem
        processedCount++

        if (processedCount === positionHistory.length) {
          const validPositions = processedPositions.filter(
            (pos) => pos !== null && pos !== undefined
          )
          callback(null, validPositions)
        }
      }
    })
  },

  // Get Apollo ID from company
  getCompanyApolloId(companyId, callback) {
    this.serviceLocator.companyService.read(companyId, (err, company) => {
      if (err) {
        this.serviceLocator.logger.error(
          'Error reading company for Apollo ID:',
          err
        )
        return callback(err, null)
      }

      if (company && company.apolloId) {
        return callback(null, company.apolloId)
      } else {
        // this.serviceLocator.logger.info(
        //   'No Apollo ID found for company:',
        //   companyId
        // )
        return callback(null, null)
      }
    })
  },

  // Apply updates and finalize the process
  applyUpdatesAndFinalize(updates) {
    // Apply all updates to model
    this.model.set(updates)

    // Update form fields without full re-render to preserve widgets
    // this.updateFormFields(updates)
    this.render()

    // Set fetch state to success
    window.executiveForm.fetchState = FetchState.IDLE
    this.handleUiState('success')
  },

  // Main method to fetch LinkedIn data
  fetchLinkedInFields() {
    // Set current form values
    const form = this.$el.find('form')
    const formData = mapFormToObject(form, this.model.schemata)
    this.model.set(formData)

    const linkedinUrl = this.model.get('linkedinProfileUrlV2')
    if (!linkedinUrl) {
      this.handleUiState('error')
      return modal({
        title: 'Error',
        content: 'Please enter a LinkedIn profile URL before fetching data.',
        buttons: [
          { text: 'OK', event: 'close', keyCodes: [27], className: 'btn' }
        ]
      })
    }

    // Get excluded fields
    const excludedFields = this.$el
      .find('select[name="excludedFields"]')[0]
      .selectize.getValue()

    // this.serviceLocator.logger.info('Excluded fields:', excludedFields)

    // Set fetch state and UI
    window.executiveForm.fetchState = FetchState.FETCHING
    this.handleUiState('fetching')

    // Get account for API calls
    this.getAccount((err, account) => {
      if (err) {
        this.serviceLocator.logger.error('Error getting account:', err)
        this.handleUiState('error')
        return modal({
          title: 'Error',
          content: 'There was an error getting account information.',
          buttons: [
            { text: 'OK', event: 'close', keyCodes: [27], className: 'btn' }
          ]
        })
      }

      // Start both API calls simultaneously
      this.fetchLinkedInData(linkedinUrl, excludedFields)
      this.fetchApolloData(linkedinUrl)
    })
  },

  populateSmartActionsFromCache() {
    const cachedData = this.model.get('cachedScrapinIoData')
    if (!cachedData) return
    if (!cachedData.success) return
    if (
      cachedData.person &&
      cachedData.person.positions &&
      cachedData.person.positions.positionHistory &&
      cachedData.person.positions.positionHistory.length
    ) {
      // ...
      const companies = cachedData.person.positions.positionHistory.map(
        (c) => ({
          name: c.companyName,
          linkedInUrl: c.linkedInUrl
        })
      )
      async.parallel(
        companies.map((c) => (cb) => this.matchCompany(c, cb)),
        (_, __) => this.smartActionsToolbar.bindEvents()
      )
    }
  },

  // Fetch LinkedIn scraping data (using callback pattern)
  fetchLinkedInData(linkedinUrl, excludedFields) {
    // this.serviceLocator.logger.info(
    //   'Fetching LinkedIn scrapin.io data for:',
    //   linkedinUrl
    // )

    // Use executive service method with authedRequest
    this.serviceLocator.executiveService.fetchBasicProfileDataFromScrapin(
      { linkedinUrl },
      (err, data) => {
        if (err) {
          if (window.executiveForm.fetchState === FetchState.CANCELLING) {
            // this.serviceLocator.logger.info('LinkedIn fetch cancelled')
            return
          }

          this.handleUiState('error')
          this.serviceLocator.logger.error('LinkedIn fetch error:', err)
          notify('Bad Request (check URL)', 'error')
          return
        }

        this.model.set('cachedScrapinIoData', {
          ...data,
          __postFetchMeta: {
            fetchDate: new Date(),
            fetchAdminId: window.localStorage.getItem('apiId')
          }
        })
        this.model.set('linkedinPublicId')

        if (window.executiveForm.fetchState === FetchState.CANCELLING) {
          return
        }

        if (!data.success || !data.person) {
          this.handleUiState('error')
          this.serviceLocator.logger.error('LinkedIn API returned error:', data)
          notify('Could not find person', 'error')
          return
        }

        this.processLinkedInData(
          data.person,
          excludedFields,
          data.asset,
          data.company
        )
        notify('Success', 'success')
      }
    )
  },

  // Fetch Apollo data (using callback pattern)
  fetchApolloData(linkedinUrl) {
    // this.serviceLocator.logger.info('Fetching Apollo data for:', linkedinUrl)

    // Use executive service method with authedRequest
    this.serviceLocator.executiveService.fetchApolloId(
      { linkedinUrl },
      (err, apolloId) => {
        if (err) {
          if (window.executiveForm.fetchState === FetchState.CANCELLING) {
            // this.serviceLocator.logger.info('Apollo fetch cancelled')
            return
          }

          this.serviceLocator.logger.error('Apollo fetch error:', err)
          notify('Error fetching Apollo data', 'error')
          return
        }

        if (window.executiveForm.fetchState === FetchState.CANCELLING) {
          // this.serviceLocator.logger.info('Apollo fetch cancelled')
          return
        }

        // this.serviceLocator.logger.info('Apollo ID received:', apolloId)

        if (apolloId) {
          this.model.set({ apolloId })
          this.$el.find('[name="apolloId"]').val(apolloId)
        } else {
          this.serviceLocator.logger.warn('No Apollo ID returned')
        }
      }
    )
  },

  // AUGMENT GENERATED - Convert plain text to rich HTML with proper formatting
  convertPlainTextToRichHtml(text) {
    if (!text) return ''

    try {
      // Split text into lines and process each one
      const lines = text
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => line.length > 0)
      const htmlLines = []

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]

        // Check if line starts with bullet point indicators
        if (line.match(/^[•·*-]\s+/) || line.match(/^\d+\.\s+/)) {
          // This is a bullet point
          const content = line
            .replace(/^[•·*-]\s+/, '')
            .replace(/^\d+\.\s+/, '')
          htmlLines.push(`<li>${content}</li>`)
        } else if (line.length > 0) {
          // This is a regular paragraph
          // Check if previous line was a bullet point to close the list
          if (
            i > 0 &&
            htmlLines[htmlLines.length - 1] &&
            htmlLines[htmlLines.length - 1].includes('<li>')
          ) {
            // Previous line was a bullet, but this isn't - we need to wrap previous bullets in <ul>
            let j = htmlLines.length - 1
            const bulletItems = []
            while (j >= 0 && htmlLines[j].includes('<li>')) {
              bulletItems.unshift(htmlLines[j])
              j--
            }
            // Remove the bullet items from htmlLines
            htmlLines.splice(j + 1, bulletItems.length)
            // Add them as a proper list
            htmlLines.push(`<ul>${bulletItems.join('')}</ul>`)
          }
          htmlLines.push(`<p>${line}</p>`)
        }
      }

      // Handle case where text ends with bullet points
      if (
        htmlLines.length > 0 &&
        htmlLines[htmlLines.length - 1].includes('<li>')
      ) {
        let j = htmlLines.length - 1
        const bulletItems = []
        while (j >= 0 && htmlLines[j].includes('<li>')) {
          bulletItems.unshift(htmlLines[j])
          j--
        }
        // Remove the bullet items from htmlLines
        htmlLines.splice(j + 1, bulletItems.length)
        // Add them as a proper list
        htmlLines.push(`<ul>${bulletItems.join('')}</ul>`)
      }

      return htmlLines.join('')
    } catch (err) {
      this.serviceLocator.logger.error(err)
      return ''
    }
  },

  // Process LinkedIn data and update model (using callback pattern)
  processLinkedInData(person, excludedFields, asset, company) {
    // this.serviceLocator.logger.info('Processing LinkedIn data:', person)
    // this.serviceLocator.logger.info('Processing LinkedIn asset:', asset)

    const updates = {}
    const functions = []

    // Name (merge firstName and lastName)
    if (
      !excludedFields.includes('name') &&
      person.firstName &&
      person.lastName
    ) {
      updates.name = `${person.firstName} ${person.lastName}`
      // this.serviceLocator.logger.info('Setting name:', updates.name)
    }

    // Slug (slugified name)
    if (
      !excludedFields.includes('slug') &&
      person.firstName &&
      person.lastName &&
      !this.model.get('slug')
    ) {
      updates.slug = slugg(`${person.firstName} ${person.lastName}`)
    }

    // Bio (summary) - convert plain text to rich HTML
    if (!excludedFields.includes('bio') && person.summary) {
      updates.bio = this.convertPlainTextToRichHtml(person.summary)
    }

    // City (person.location.city)
    if (!excludedFields.includes('city') && person.location) {
      updates.city = person.location ? person.location.city : null
    }

    // State (person.location.state)
    if (!excludedFields.includes('state') && person.location) {
      updates.state = person.location ? person.location.state : null
    }

    // Country (person.location.country)
    if (!excludedFields.includes('country') && person.location) {
      updates.country = person.location ? person.location.country : null
    }

    // Job Title (first position with null end date from positionHistory)
    if (
      !excludedFields.includes('jobTitle') &&
      person &&
      person.positions &&
      person.positions.positionHistory &&
      person.positions.positionHistory.length > 0
    ) {
      const currentPosition = person.positions.positionHistory.find((pos) =>
        pos.startEndDate ? !pos.startEndDate.end : null
      )
      if (currentPosition && currentPosition.title) {
        updates.jobTitle = currentPosition.title
      }
    }

    if (
      !excludedFields.includes('company') &&
      person &&
      person.positions &&
      person.positions.positionHistory &&
      person.positions.positionHistory.length > 0
    ) {
      const c =
        person.positions.positionHistory.find((historyItem) =>
          historyItem.startEndDate ? !historyItem.startEndDate.end : null
        ) ||
        person.positions.positionHistory[
          person.positions.positionHistory.length - 1
        ]

      functions.push((cb) => {
        this.matchCompany(
          {
            name: c.companyName,
            linkedInUrl: c.linkedInUrl
          },
          (err, companyId) => {
            if (err) {
              this.serviceLocator.logger.error('Error matching company:', err)
            } else if (companyId) {
              updates.companyId = companyId
            }

            cb(null, { companyId })
          }
        )
      })
    }

    // History (map from positionHistory) - process asynchronously due to company matching
    const shouldProcessHistory =
      !excludedFields.includes('history') &&
      person &&
      person.positions &&
      person.positions.positionHistory &&
      person.positions.positionHistory.length > 0

    if (shouldProcessHistory) {
      functions.push((cb) => {
        this.processPositionHistory(
          person.positions.positionHistory,
          (err, processedHistory) => {
            if (err) {
              this.serviceLocator.logger.error(err)
              this.handleUiState('error')
            }
            if (processedHistory && processedHistory.length > 0) {
              updates.history = processedHistory
              this.serviceLocator.logger.info(
                'Setting history with',
                updates.history.length,
                'positions',
                updates.history
              )
            }

            // Apply all updates (including history if processed)
            cb(null, { history: processedHistory })
          }
        )
      })
    }

    // Handle photo asset if provided
    if (asset && !excludedFields.includes('images')) {
      functions.push((cb) => {
        this.addPhotoAsset(asset)
        cb(null, {})
      })
    }

    async.parallel(functions, (err, results) => {
      if (err) {
        this.serviceLocator.logger.error('Error matching company:', err)
        notify('Error fetching LinkedIn data', 'error')
        this.handleUiState('error')
      } else {
        this.applyUpdatesAndFinalize(updates)
      }
    })
  },

  // Cancel fetch operation
  cancelFetch() {
    window.executiveForm.fetchState = FetchState.CANCELLING
    this.handleUiState('cancelling')
    // this.serviceLocator.logger.info('Fetch operation cancelled')
  },

  // Get account information (similar to company form)
  getAccount(cb) {
    this.serviceLocator.accountService.find(
      '',
      {},
      ['name'],
      { pageSize: 1000 },
      (err, res) => {
        if (err) return cb(err)
        return cb(null, res.results[0])
      }
    )
  },

  renderImageWidgetArea() {
    this.imageArea = new WidgetAreaView({
      model: this.model.images,
      receiveDrops: false,
      serviceLocator: this.serviceLocator,
      widgetItemOptions: imageConfig
    })
    this.$el.find('.js-image-area').append(this.imageArea.$el)
  },

  renderVerifiedSelect() {
    this.verifiedIntancesSelect = new InstanceSelect(
      this.serviceLocator,
      this.model.get('verifiedInstances') || [],
      {
        isMultiple: true
      }
    )

    this.$el
      .find('.js-instance-multi-select')
      .append(this.verifiedIntancesSelect.render().$el)
    this.verifiedIntancesSelect.on('change', (instances) => {
      this.model.set('verifiedInstances', instances)
    })
  },

  renderTextArea() {
    // Clean up existing rich text editor if it exists
    this.cleanup()

    // Create new instance
    this.richTextEditorInstanceManager = new RichTextEditorInstanceManager()
    this.$el.find('.js-text-editor').each((i, el) => {
      this.richTextEditorInstanceManager.create(el, { height: 150 }, el.id)
    })
  },

  renderCompanySelect() {
    this.companySelect = new CompanySelectView(
      this.serviceLocator,
      this.model.get('companyId'),
      { filter: { $and: [{ slug: { $ne: null } }] } }
    )

    this.companySelect.on('change', (value) => {
      this.model.set('companyId', value)
    })

    this.$el
      .find('.js-company-select')
      .empty()
      .append(this.companySelect.render().$el)
  },

  renderItemRepeaters() {
    this.milestonesRepeater = new ItemRepeater(
      this.serviceLocator,
      milestonesRepeaterConfig,
      this.model.get('milestones'),
      'Add Milestone'
    )

    this.milestonesRepeater.on('itemsUpdated', () => {
      this.model.set('milestones', this.milestonesRepeater.getItems())
    })

    this.$el
      .find('.js-milestone-repeater')
      .empty()
      .append(this.milestonesRepeater.render().$el)

    this.historyRepeater = new ItemRepeater(
      this.serviceLocator,
      historyRepeaterConfig,
      this.model.get('history'),
      'Add History'
    )

    this.historyRepeater.on('itemsUpdated', () => {
      this.model.set('history', this.historyRepeater.getItems())
    })

    this.$el
      .find('.js-history-repeater')
      .empty()
      .append(this.historyRepeater.render().$el)
  },

  loadLinkedArticles() {
    // Only load articles if this is not a new executive
    if (!this.model.get('_id')) {
      this.$el.find('.js-no-articles').show()
      return
    }

    const executiveId = this.model.get('_id')
    this.$el.find('.js-loading-indicator').show()

    const renderArticles = (articles) => {
      this.$el.find('.js-loading-indicator').hide()

      if (articles.length === 0) {
        this.$el.find('.js-no-articles').show()
        return
      }

      // Update article count
      this.$el.find('.js-article-count').text(articles.length)

      // Clear existing articles
      const $articlesContainer = this.$el.find('.js-articles-items')
      $articlesContainer.empty()

      // Add each article
      articles.forEach((article) => {
        const publishedDate = article.displayDate
          ? new Date(article.displayDate).toLocaleDateString()
          : 'Not published'

        // Determine state badge class
        const stateBadgeClass =
          article.state === 'Published'
            ? 'state--published'
            : article.state === 'Archived'
            ? 'state--archived'
            : 'state--draft'

        // Create article item element safely matching smart actions structure
        const $articleItem = $(`
            <div class="article-item">
              <div class="article-header">
                <h4 class="article-title">
                  <a href="/articles/${article._id}/form" target="_blank" class="article-link">
                  </a>
                </h4>
                <div class="article-actions">
                  <a href="/articles/${article._id}/form" class="action-btn" target="_blank">Edit</a>
                </div>
              </div>
              <div class="article-meta">
                <span class="article-badge badge--type"></span>
                <span class="article-badge badge--state ${stateBadgeClass}"></span>
              </div>
              <div class="article-date"></div>
            </div>
          `)

        // Set text content safely to prevent XSS
        $articleItem
          .find('.article-link')
          .text(article.headline || 'Untitled Article')
        $articleItem.find('.badge--type').text(article.contentType || 'Article')
        $articleItem.find('.badge--state').text(article.state || 'Draft')
        $articleItem.find('.article-date').text(publishedDate)

        $articlesContainer.append($articleItem)
      })

      this.$el.find('.js-articles-list').show()
    }

    // Query articles linked to this executive
    if (this.articles) {
      renderArticles(this.articles)
    } else {
      this.serviceLocator.articleService.find(
        '', // no keywords
        { 'executives.executive': executiveId }, // filter by executive ID
        [], // no sort
        {
          pageSize: 50,
          projection: [
            'headline',
            'slug',
            'state',
            'displayDate',
            'contentType'
          ]
        }, // pagination and projection
        (err, result) => {
          if (err) {
            this.serviceLocator.logger.error(
              'Error loading linked articles:',
              err
            )
            this.$el.find('.js-no-articles').show()
            return
          }
          this.articles = result.results || []
          renderArticles(this.articles)
        }
      )
    }
  },

  loadLinkedInPosts() {
    // Only load posts if this is not a new executive
    if (this.isNew || !this.model.get('_id')) {
      this.$el.find('.js-no-posts').show()
      return
    }

    const linkedinPosts = this.model.get('linkedinPosts') || []

    if (linkedinPosts.length === 0) {
      this.$el.find('.js-no-posts').show()
      return
    }

    // Update posts count
    this.$el.find('.js-posts-count').text(linkedinPosts.length)

    // Clear existing posts
    const $postsContainer = this.$el.find('.js-posts-items')
    $postsContainer.empty()

    // Add each post
    linkedinPosts.forEach((post) => {
      const postDate = post.activityDate
        ? new Date(post.activityDate).toLocaleDateString()
        : 'No date'

      // Truncate text for preview
      const postText = post.text || 'No content available'
      // Create post item element
      const $postItem = $(`
        <div class="article-item">
          <div class="article-header">
            <h4 class="article-title">
              <span class="post-text"></span>
            </h4>
          </div>
          <div class="article-meta">
            <span class="article-badge badge--type">LinkedIn Post</span>
          </div>
          <div class="article-date"></div>
        </div>
      `)

      // Set text content safely to prevent XSS
      $postItem.find('.post-text').text(postText)
      $postItem.find('.article-date').text(postDate)

      $postsContainer.append($postItem)
    })

    this.$el.find('.js-posts-list').show()
  },

  addCustomFormData(formData) {
    formData.galleryImages = this.model.galleryImages.toJSON()

    return formData
  },

  handleAddGalleryImage(e) {
    e.preventDefault()
    debug('adding gallery image', arguments)

    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      serviceLocator: this.serviceLocator,
      type: 'image',
      multiple: true
    })

    collection.getByType('image')

    modal({
      title: 'Add Gallery Image',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        { text: 'Add Image', event: 'add', className: 'btn', keyCodes: [13] }
      ]
    })
      .on(
        'add',
        this.addGalleryImage.bind(this, assetListView.selectedCollection)
      )
      .on('close', assetListView.remove.bind(assetListView))
  },

  addGalleryImage(images) {
    debug('gallery images', images)
    const factory = getImageFactory('image')
    const Model = factory.model
    images.map(function (model) {
      const image = new Model(model.attributes)
      this.model.galleryImages.add(image)
      image.setDefaultCrops(this.galleryImageConfig.crops)
      debug('Adding new image', image)
      return image
    }, this)
  },

  renderGalleryImageSelector() {
    this.galleryArea = new WidgetAreaView({
      model: this.model.galleryImages,
      receiveDrops: false,
      serviceLocator: this.serviceLocator,
      widgetItemOptions: this.galleryImageConfig,
      multiple: true
    })
    this.$el.find('.js-gallery-image-area').append(this.galleryArea.$el)
  },

  render() {
    this.$el.empty().append(
      template({
        data: this.model.toJSON(),
        title: this.formTitle(this.model.toJSON()),
        config: config
      })
    )

    this.renderVerifiedSelect()
    this.renderItemRepeaters()
    this.renderImageWidgetArea()
    this.renderTextArea()
    this.renderCompanySelect()
    this.loadLinkedArticles()
    this.loadLinkedInPosts()
    this.renderGalleryImageSelector()

    // Initialize quick create popover
    this.initializeQuickCreatePopover()

    // Initialize smart actions toolbar
    this.renderSmartActionsToolbar()
    // Initialize selectize for excluded fields
    this.$el.find('.js-excluded-fields').selectize({
      plugins: ['remove_button'],
      delimiter: ',',
      persist: false,
      create: false
    })

    return this
  }
})
