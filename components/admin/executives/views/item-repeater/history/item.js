const compileJade = require('browjadify-compile')
const join = require('path')
const BaseItemRepeaterItemView = require('../../../../widget/views/item/base-item-repeater')
const template = compileJade(
  join(__dirname, '../../../templates/item-repeater/history/item.jade')
)

class HistoryRepeaterItemView extends BaseItemRepeaterItemView {
  render() {
    const data = this.model.toJSON()
    const endYear = data.endYear || 'Now'
    const { jobTitle, startYear, startMonth, endMonth } = data

    const getMonth = (monthNumber) => {
      switch (monthNumber) {
        case 1:
          return 'January'
        case 2:
          return 'February'
        case 3:
          return 'March'
        case 4:
          return 'April'
        case 5:
          return 'May'
        case 6:
          return 'June'
        case 7:
          return 'July'
        case 8:
          return 'August'
        case 9:
          return 'September'
        case 10:
          return 'October'
        case 11:
          return 'November'
        case 12:
          return 'December'
        default:
          return null
      }
    }

    data.itemString = `${jobTitle} | ${getMonth(startMonth)} ${startYear} - ${
      endYear === 'Now' ? endYear : `${getMonth(endMonth)} ${endYear}`
    }`

    this.$el.append(
      template({
        data
      })
    )
    return this
  }
}

module.exports = HistoryRepeaterItemView
