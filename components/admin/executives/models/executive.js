const BaseModel = require('cf-base-model')
const schemata = require('../../../service/executive/schema')(
  { config: window.config },
  {}
)
const ImageAreaModel = require('../../asset/models/image-area')

module.exports = BaseModel.extend({
  idAttribute: '_id',
  initialize() {
    BaseModel.prototype.initialize.call(this)
    this.images = new ImageAreaModel(this.get('images'))

    this.images.on('add remove change', () => {
      this.set('images', this.images.toJSON())
    })

    this.galleryImages = new ImageAreaModel(this.get('galleryImages'))
    this.galleryImages.on('add remove change', () => {
      this.set('galleryImages', this.galleryImages.toJSON())
    })
  },
  schemata,
  defaults() {
    return schemata.makeDefault()
  }
})
