.page-content

  .toolbar
    .centering.js-toolbar
      .toolbar__left
        button.btn.js-btn-action(type='button') No Actions

  .centering

    header.page-header
      h1 GlobeNewswire: #{title}

    .panel.panel-styled.globenewswire-metadata-panel
      .panel-header
        h3 Press Releases from Database
      .panel-content
        if isLoading
          .globenewswire-loading
            .loading-spinner
            p Loading press releases...
        else if error
          .globenewswire-error
            .alert.alert--error
              strong Error:
              = error
        else if pressReleases && pressReleases.length > 0
          .globenewswire-metadata
            .metadata-summary
              .metadata-summary-header
                p.metadata-stats
                  strong Total Press Releases:
                  = pagination.total
                  if pagination.total > pressReleases.length
                    |  (showing #{pressReleases.length} of #{pagination.total})
                if pagination.totalPages > 1
                  .pagination-controls
                    - var currentPage = pagination.page
                    - var totalPages = pagination.totalPages
                    - var beforeCurrent = 3
                    - var afterCurrent = 3

                    // Calculate the range of pages to show around current page
                    - var startPage = Math.max(1, currentPage - beforeCurrent)
                    - var endPage = Math.min(totalPages, currentPage + afterCurrent)

                    // Adjust range to always show at least 7 pages when possible
                    - var totalVisible = endPage - startPage + 1
                    - if (totalVisible < 7 && totalPages >= 7)
                      - if (startPage === 1)
                        - endPage = Math.min(totalPages, startPage + 6)
                      - else if (endPage === totalPages)
                        - startPage = Math.max(1, endPage - 6)

                    // Show pages in the calculated range
                    - for (var i = startPage; i <= endPage; i++)
                      if i === currentPage
                        span.pagination-btn.pagination-btn--active= i
                      else
                        button.pagination-btn.js-pagination-btn(type='button', data-page=i)= i

                    // Show ellipsis and last page if needed
                    if endPage < totalPages
                      if endPage < totalPages - 1
                        span.pagination-ellipsis ...
                      if totalPages === currentPage
                        span.pagination-btn.pagination-btn--active= totalPages
                      else
                        button.pagination-btn.js-pagination-btn(type='button', data-page=totalPages)= totalPages

            .metadata-list
              each item in pressReleases
                .metadata-item
                  .metadata-item-header
                    h4.metadata-title= item['gn:Title']
                    .metadata-date= formatDate(item['gn:ReleaseDateTime'])
                  .metadata-item-content
                    if item['gn:Url']
                      a.metadata-link(href=item['gn:Url'], target='_blank', rel='noopener noreferrer')
                        | View Press Release ↗
                    .metadata-id
                      small Distribution ID: #{item['gn:Identifier']}
                    if item['gn:Subjects'] && item['gn:Subjects'].length > 0
                      .metadata-subjects
                        small Subjects: #{item['gn:Subjects'].join(', ')}
                    if item.instanceId
                      .metadata-instance
                        small Instance ID: #{item.instanceId}
                    if item.crawlDate 
                      .metadata-crawl-date
                        small Crawl Date: #{formatDate(item.crawlDate)}
        else
          .globenewswire-empty
            p No press releases found in the database.
