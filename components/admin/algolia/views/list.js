const View = require('ventnor')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/list.jade'))

class AlgoliaView extends View {
  constructor(serviceLocator) {
    super(serviceLocator)
    this.serviceLocator = serviceLocator
    serviceLocator.logger.info('[Algolia] I am a view')
  }

  // Compatibility with backbone admin
  trigger(e, val) {
    this.emit(e, val)
  }

  render() {
    this.$el.html(template({}))

    // Initialize tab state
    this.tabData = {}
    this.currentParams = null

    // Bind form submission
    this.$el.find('.js-search-form').on('submit', (e) => {
      e.preventDefault()
      this.performSearch()
    })

    // Bind tab clicks
    this.$el.find('.js-tab-button').on('click', (e) => {
      e.preventDefault()
      const tabName = $(e.target).data('tab')
      this.switchTab(tabName)
    })

    // Set default dates (last 30 days)
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 30)

    this.$el.find('[name="startDate"]').val(this.formatDate(startDate))
    this.$el.find('[name="endDate"]').val(this.formatDate(endDate))

    return this
  }

  formatDate(date) {
    return date.toISOString().split('T')[0]
  }

  performSearch() {
    const formData = {
      index: this.$el.find('[name="index"]').val(),
      limit: parseInt(this.$el.find('[name="limit"]').val()) || 100,
      startDate: this.$el.find('[name="startDate"]').val(),
      endDate: this.$el.find('[name="endDate"]').val()
    }

    // Store current params and clear tab data
    this.currentParams = formData
    this.tabData = {}

    // Show tabs and set first tab as active
    this.$el.find('.js-tabs').show()
    this.switchTab('top-searches')
  }

  switchTab(tabName) {
    // Update tab buttons
    this.$el.find('.js-tab-button').removeClass('active')
    this.$el.find(`[data-tab="${tabName}"]`).addClass('active')

    // Show loading if data not yet fetched
    if (!this.tabData[tabName]) {
      this.$el.find('.js-tab-content').html('<p>Loading...</p>')
      this.fetchTabData(tabName)
    } else {
      this.renderTabContent(tabName)
    }
  }

  fetchTabData(tabName) {
    if (!this.currentParams) return

    const methodMap = {
      'top-searches': 'getTopSearches',
      'top-hits': 'getTopHits',
      'searches-no-results': 'getSearchesNoResults'
    }

    const method = methodMap[tabName]
    if (!method) return

    this.serviceLocator.algoliaService[method](
      this.currentParams,
      (err, res) => {
        if (err) {
          this.serviceLocator.logger.error(`[Algolia] ${tabName} error:`, err)
          this.tabData[tabName] = {
            error: err.message || `Error fetching ${tabName}`
          }
        } else {
          this.tabData[tabName] = res
        }
        this.renderTabContent(tabName)
      }
    )
  }

  renderTabContent(tabName) {
    const data = this.tabData[tabName]
    let html = ''

    const indexMap = {
      company: 'companies',
      executive: 'executives',
      article: 'articles'
    }

    if (tabName === 'top-searches') {
      if (data.error) {
        html += `<p class="error">Error: ${data.error}</p>`
      } else if (data.searches && data.searches.length > 0) {
        html += '<table class="algolia-table">'
        html +=
          '<thead><tr><th>Search Term</th><th>Count</th><th>Total Hits on Query</th></tr></thead>'
        html += '<tbody>'
        data.searches.forEach((search) => {
          html += `<tr><td>${search.search}</td><td>${search.count}</td><td>${search.nbHits}</td></tr>`
        })
        html += '</tbody></table>'
      } else {
        html += '<p>No top searches found</p>'
      }
    } else if (tabName === 'top-hits') {
      if (data.error) {
        html += `<p class="error">Error: ${data.error}</p>`
      } else if (data.hits && data.hits.length > 0) {
        html += '<table class="algolia-table">'
        html +=
          '<thead><tr><th>Hit</th><th>Name</th><th>Count</th></tr></thead>'
        html += '<tbody>'
        data.hits.forEach((hit) => {
          html += `<tr>
            <td><a href="/${indexMap[this.currentParams.index]}/${
            hit.hit
          }/form">${hit.hit}</a></td>
            <td class="js-hit-name" data-hit-id="${hit.hit}" data-hit-index="${
            this.currentParams.index
          }">...</td>
            <td>${hit.count}</td>
          </tr>`
        })
        html += '</tbody></table>'
      } else {
        html += '<p>No top hits found</p>'
      }
    } else if (tabName === 'searches-no-results') {
      if (data.error) {
        html += `<p class="error">Error: ${data.error}</p>`
      } else if (data.searches && data.searches.length > 0) {
        html += '<table class="algolia-table">'
        html +=
          '<thead><tr><th>Search Term</th><th>Count</th><th>With Filter Count</th></tr></thead>'
        html += '<tbody>'
        data.searches.forEach((search) => {
          html += `<tr><td>${search.search}</td><td>${search.count}</td><td>${search.withFilterCount}</td></tr>`
        })
        html += '</tbody></table>'
      } else {
        html += '<p>No searches with no results found</p>'
      }
    }

    this.$el.find('.js-tab-content').html(html)

    // setTimeout(() => {
    //   if (tabName === 'top-hits') this.hydrateTabContent()
    // }, 0)
    if (tabName === 'top-hits') this.hydrateTabContent()
  }

  hydrateTabContent() {
    this.$el.find('.js-hit-name').each((i, el) => {
      const hitId = $(el).data('hit-id')
      const index = $(el).data('hit-index')
      this.serviceLocator.algoliaService.getHitName(
        hitId,
        index,
        (err, res) => {
          if (err) {
            this.serviceLocator.logger.error('Error fetching hit name:', err)
            return
          }
          if (index === 'article') return $(el).text(res.headline)
          $(el).text(res.name)
        }
      )
    })
  }
}

module.exports = AlgoliaView
