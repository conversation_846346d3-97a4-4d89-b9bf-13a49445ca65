const createAuthedRequest = require('../../../../admin/source/js/lib/authed-request')

class AlgoliaService {
  constructor(serviceLocator) {
    this.authedRequest = createAuthedRequest(serviceLocator.config.apiUrl)
  }

  getTopSearches(params, cb) {
    if (typeof params === 'function') {
      cb = params
      params = {}
    }
    this.authedRequest(
      'GET',
      '/algolia/top-searches',
      params,
      (err, res, body) => {
        if (err) return cb(err)
        cb(null, body)
      }
    )
  }

  getTopHits(params, cb) {
    if (typeof params === 'function') {
      cb = params
      params = {}
    }
    this.authedRequest('GET', '/algolia/top-hits', params, (err, res, body) => {
      if (err) return cb(err)
      cb(null, body)
    })
  }

  getSearchesNoResults(params, cb) {
    if (typeof params === 'function') {
      cb = params
      params = {}
    }
    this.authedRequest(
      'GET',
      '/algolia/searches-no-results',
      params,
      (err, res, body) => {
        if (err) return cb(err)
        cb(null, body)
      }
    )
  }

  getHitName(hitId, index, cb) {
    this.authedRequest(
      'GET',
      '/algolia/hit-name',
      { hitId, index },
      (err, res, body) => {
        if (err) return cb(err)
        cb(null, body)
      }
    )
  }
}

module.exports = AlgoliaService
