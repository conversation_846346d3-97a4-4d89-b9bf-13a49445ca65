.page-content

  .toolbar
    .centering.js-toolbar
      .toolbar__left
        button.btn.js-btn-action(type='button') No Actions

  .centering

    header.page-header
      h1 Algolia Analytics

    .panel.panel-styled
      .panel-header
        h2 Search Parameters
      .panel-content
        form.js-search-form
          .form-row
            label
              span.form-label-text Index
              select.control.control--choice.form-field(name='index')
                option(value='executive') Executive
                option(value='company', selected) Company
                option(value='article') Article
            .form-row-description.form-copy
              p Choose the Algolia index to analyze

          .form-row
            label
              span.form-label-text Limit
              input.control.control--text.form-field(type='number', name='limit', value='100', min='1', max='1000')
            .form-row-description.form-copy
              p Maximum number of results to return (max 1000)

          .form-row
            label
              span.form-label-text Start Date
              input.control.control--text.form-field(type='date', name='startDate')
            .form-row-description.form-copy
              p Start date for the analytics period (YYYY-MM-DD)

          .form-row
            label
              span.form-label-text End Date
              input.control.control--text.form-field(type='date', name='endDate')
            .form-row-description.form-copy
              p End date for the analytics period (YYYY-MM-DD)

          .form-row.form-row-actions
            button.btn.btn--action(type='submit') Search

    .js-tabs(style='display: none;')
      .panel.panel-styled
        .panel-header
          .tabs
            button.tab-button.js-tab-button.active(data-tab='top-searches') Top Searches
            button.tab-button.js-tab-button(data-tab='top-hits') Top Hits
            button.tab-button.js-tab-button(data-tab='searches-no-results') Searches with No Results
        .panel-content
          .js-tab-content