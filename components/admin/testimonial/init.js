const createController = require('./controllers/controller')
const TestimonialService = require('./services/testimonial')
const createWidgetFactory = require('../widget/lib/base-factory')

const init = (serviceLocator, done) => {
  serviceLocator.testimonialService = new TestimonialService(serviceLocator)
  createController(serviceLocator)

  const widgetFactory = createWidgetFactory()
  serviceLocator.widgetFactories.register('testimonial', widgetFactory)

  done()
}

module.exports = () => ({ testimonialAdmin: ['widget', init] })
