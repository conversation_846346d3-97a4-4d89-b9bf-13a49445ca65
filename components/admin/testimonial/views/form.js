const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const debug = require('../../../../admin/source/js/lib/debug')(
  'testimonial form view'
)
const BaseView = require('cf-base-view')
const modal = require('modal')
const formSaveDelegate = require('../../../../admin/source/js/lib/form-save-delegate')(
  debug
)
const formCancelDelegate = require('cf-form-cancel-delegate')(debug)
const formTitleDelegate = require('../../../../admin/source/js/lib/form-title-delegate')
const AssetCollection = require('../../asset/collections/asset')
const AssetPickerView = require('../../asset/views/picker')
const WidgetAreaView = require('../../widget/views/widget-area')
const imageConfig = require('../../../service/testimonial/image-config.json')
const config = window.config
const InstanceSelect = require('../../instance/views/instance-select')
const TagSelect = require('../../tag/views/tag-select')
const getImageFactory = require('../../asset/lib/image-factory')()

const availableTagTypes = [
  {
    type: 'Testimonial',
    label: 'Testimonial Tags'
  }
]

module.exports = BaseView.extend({
  events: {
    'click .js-save': 'handleSave',
    'click .js-cancel': 'handleCancel',
    submit: (e) => e.preventDefault()
  },

  initialize({ serviceLocator, title, isNew }) {
    this.initialModel = this.model.toJSON()
    this.formTitle = formTitleDelegate(title)
    this.isNew = isNew
    window.testimonialForm = window.testimonialForm || {}
    this.serviceLocator = serviceLocator
    this.tagControls = []

    this.verifiedIntancesSelect = new InstanceSelect(
      this.serviceLocator,
      this.model.get('instances') || [],
      {
        isMultiple: true
      }
    )

    this.on('remove', this.destroy)
    this.$el.on('click', '.js-images-add', this.handleAddImages.bind(this))
    this.setUpTagControls()
    this.render()

    this.listenTo(this, 'afterAppend', this.focusForm)
  },

  handleAddImages(e) {
    e.preventDefault()

    const collection = new AssetCollection()
    const assetListView = new AssetPickerView({
      collection: collection,
      type: 'image',
      serviceLocator: this.serviceLocator
    })

    collection.getByType('image')

    modal({
      title: 'Images',
      className: 'modal-asset-view wide',
      content: assetListView.$el,
      buttons: [
        {
          text: 'Cancel it',
          event: 'Cancel',
          keyCodes: [27],
          className: 'btn'
        },
        {
          text: 'Add selected images',
          event: 'add',
          className: 'btn btn--action',
          keyCodes: [13]
        }
      ]
    })
      .on('add', this.addImages.bind(this, assetListView.selectedCollection))
      .on('close', assetListView.remove.bind(assetListView))
  },

  addImages(images) {
    const ImageModel = getImageFactory('image').model

    images.map((model) => {
      const image = new ImageModel(model.attributes)
      this.model.images.add(image)
      image.setDefaultCrops(imageConfig.crops)
      return image
    })
  },

  focusForm() {
    // Focus on name field if it's empty, otherwise focus on the first empty required field
    const $nameField = this.$(':input[name=name]')
    if (!$nameField.val()) {
      $nameField.focus()
    }
  },

  handleCancel: formCancelDelegate,

  handleSave(e) {
    e.preventDefault()
    this.save()
  },

  save() {
    formSaveDelegate.call(this)
  },

  renderImageWidgetArea() {
    this.imageArea = new WidgetAreaView({
      model: this.model.images,
      receiveDrops: false,
      serviceLocator: this.serviceLocator,
      widgetItemOptions: imageConfig
    })

    this.$el.find('.js-image-area').append(this.imageArea.$el)
  },

  setUpTagControls() {
    availableTagTypes.forEach((tagType) => {
      const readOnly =
        tagType.readOnly || !this.serviceLocator.allowed('tag', 'create')

      const tagControl = new TagSelect(
        this.serviceLocator,
        this.model.get('tags'),
        tagType.type,
        readOnly
      )
      tagControl.loadTags()
      tagControl.on('change', this.updateTags.bind(this))

      this.tagControls.push(tagControl)
      this.attachView(tagControl)
    })
  },

  /*
   * Any time a tag input changes, update the model
   */
  updateTags() {
    let tags = []
    this.tagControls.forEach((tagControl) => {
      tags = tags.concat(tagControl.tags.toJSON())

      for (const key in tags) {
        tags[key].account = this.serviceLocator.session.account
      }
    })
    return this.model.set('tags', tags)
  },

  renderTagControls() {
    this.tagControls.forEach((tagControl) => {
      this.$el
        .find('.js-tags-' + tagControl.tagType.toLowerCase())
        .append(tagControl.render().$el)
    })
  },

  render() {
    const modelData = this.model.toJSON()
    const titleData = this.formTitle(modelData)

    this.$el.empty().append(
      template({
        data: modelData,
        title: titleData,
        config,
        availableTagTypes
      })
    )

    this.$el
      .find('.js-instance-multi-select')
      .append(this.verifiedIntancesSelect.render().$el)

    this.verifiedIntancesSelect.on('change', (instances) => {
      this.model.set('instances', instances)
    })

    this.renderImageWidgetArea()
    this.renderTagControls()

    return this
  }
})
