const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/list.jade'))
const ListItemView = require('./list-item')
const ListFilterView = require('./list-filter')
const GenericListView = require('../../../../admin/source/js/lib/generic/list-view')
const extend = require('lodash.assign')
const config = window.config

module.exports = GenericListView.extend({
  events: extend({}, GenericListView.prototype.events, {
    'click .js-more': 'loadMore'
  }),

  template,

  initialize() {
    GenericListView.prototype.initialize.apply(this, arguments)
    this.renderFilters()
    this.listenTo(this.collection, 'filter', this.maintainOrder)
    this.listenTo(this.collection, 'filter', () => {
      this.trigger('clearSelection')
    })
  },

  renderFilters() {
    this.filters = new ListFilterView()
    this.filters.on('filter', this.applyFilters, this)
    this.$('.js-filters').append(this.filters.$el)
  },

  applyFilters(params) {
    this.collection.applyFilter(params)
  },

  createListItemView(model) {
    return new ListItemView({
      model,
      serviceLocator: this.options.serviceLocator
    })
  },

  loadMore() {
    this.collection.loadMore()
  },

  render() {
    this.$el.empty().append(
      this.template({
        config,
        displayName: this.options.displayName,
        allowed: this.options.serviceLocator.allowed
      })
    )

    return this
  }
})
