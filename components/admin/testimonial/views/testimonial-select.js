const View = require('ventnor')

const DEFAULT_FILTER = {
  $and: [{ slug: { $ne: null } }]
}

const reqOptions = {
  pageSize: 10000,
  projection: ['name', 'slug', 'createdDate']
}

class TestimonialSelect extends View {
  constructor(serviceLocator, selectedTestimonial, options) {
    super(...arguments)
    this.$el = $('<select id="testimonial" />')

    this.options = options || { filter: DEFAULT_FILTER, isMultiple: false }
    this.isMultiple = this.options.isMultiple
    if (this.isMultiple) {
      this.$el = $('<select id="testimonial" multiple/>')
    }

    this.$el.addClass('control control--choice')
    this.el = this.$el[0]
    this.$el.attr(
      'placeholder',
      this.isMultiple
        ? 'Choose one or more testimonials'
        : 'Select a Testimonial'
    )
    this.selectedTestimonial = selectedTestimonial || null
    this.serviceLocator = serviceLocator
  }

  initializeSelectize() {
    this.serviceLocator.testimonialService.find(
      '',
      this.options.filter,
      [],
      reqOptions,
      (err, res) => {
        if (err)
          return this.serviceLocator.logger.error(
            err,
            'Cannot find existing testimonials'
          )

        this.el.selectize.addOption({
          value: null,
          text: '-- Select a testimonial --'
        })

        res.results &&
          res.results
            .sort(
              (a, b) =>
                a.name.localeCompare(b.name, undefined, { numeric: true }) // Sort by name alphanumerically
            )
            .forEach((testimonial) => {
              // The item needs to be added to the list
              // of selectize options in order to be selected
              this.el.selectize.addOption({
                value: testimonial._id,
                text: `${testimonial.name} - (${testimonial.slug})`
              })
              // Select the added option
              this.el.selectize.addItem(this.selectedTestimonial)
            })
        this.el.selectize.on('change', this.updateSelection.bind(this))
      }
    )
  }

  updateSelection() {
    this.x = this.el.selectize.getValue()
    this.emit('change', this.x)
  }

  load(keywords, cb) {
    this.serviceLocator.testimonialService.find(
      keywords,
      this.options.filter,
      [],
      reqOptions,
      (err, res) => {
        if (err)
          return this.serviceLocator.logger.error(
            'Cannot find testimonials',
            err
          )
        cb(
          res.results &&
            res.results.map((testimonial) => {
              return {
                value: testimonial._id,
                text: `${testimonial.name} - (${testimonial.slug})`
              }
            })
        )
      }
    )
  }

  render() {
    setTimeout(() => {
      this.$el.selectize({
        delimiter: ',',
        persist: false,
        create: false,
        onInitialize: this.initializeSelectize.bind(this),
        load: this.load.bind(this),
        preload: true,
        maxItems: this.isMultiple ? null : 1,
        maxOptions: 99999999 // Unlikely high number to show all options
      })
    }, 0)

    return this
  }
}

module.exports = TestimonialSelect
