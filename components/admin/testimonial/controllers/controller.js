const ListView = require('../views/list')
const FormView = require('../views/form')
const Collection = require('../collections/testimonial')
const TestimonialModel = require('../models/testimonial')
const debug = require('../../../../admin/source/js/lib/debug')('testimonial')

module.exports = (serviceLocator) => {
  const router = serviceLocator.router
  const collection = new Collection()
  const displayName = { singular: 'Testimonial', plural: 'Testimonials' }

  // Wait for ACL to load before registering routes
  serviceLocator.hub.once('acl:load', () => {
    router.route('testimonials(/)', 'listTestimonials', () => {
      debug('testimonial url list view route triggered')
      if (!serviceLocator.allow('testimonial', 'read')) return false

      const listView = new ListView({
        collection,
        serviceLocator,
        displayName
      })

      listView.on(
        'new',
        router.navigate.bind(router, 'testimonials/form', { trigger: true })
      )

      listView.on('edit', (model) => {
        router.navigate('testimonials/' + model.id + '/form', {
          trigger: true
        })
      })

      router.render(listView, displayName.plural)
      collection.load()
    })

    const bindSaveAndCancel = (view) => {
      view.on('save', (model) => {
        router.navigate('testimonials', { trigger: true })
      })

      view.on('cancel', (model) => {
        router.navigate('testimonials', { trigger: true })
      })
      return view
    }

    router.route('testimonials/form(/)', 'newTestimonial', () => {
      debug('testimonial create view route triggered')

      if (!serviceLocator.allow('testimonial', 'create')) {
        return false
      }

      const model = new TestimonialModel(
        {},
        {
          collection: collection
        }
      )

      const view = new FormView({
        model,
        serviceLocator,
        title: displayName.singular,
        isNew: true
      })

      router.render(bindSaveAndCancel(view), 'New ' + displayName.singular)
    })

    router.route('testimonials/:id/form(/)', 'editTestimonial', (id) => {
      if (!serviceLocator.allow('testimonial', 'update')) {
        return false
      }
      debug('testimonial edit view route triggered')

      collection.retrieve(id, (err, model) => {
        if (err) {
          router.trigger('notFound', err.message)
          return
        }

        debug('Loading form view', model)

        const view = new FormView({
          model,
          serviceLocator,
          title: displayName.singular
        })

        router.render(bindSaveAndCancel(view), 'Edit ' + displayName.singular)
      })
    })
  }) // Close ACL loading callback
}
