.page-content

  .toolbar
    .centering.js-toolbar
      if allowed('testimonial', 'delete')
        .toolbar__left
          button.btn.js-delete(type='button') Delete Selected

      if allowed('testimonial', 'create')
        button.btn.btn--action.js-new(type='button') New #{displayName.singular}

  .centering

    header.page-header
      h1= displayName.plural
      
      p We have introduced a dedicated Testimonial Content Type designed to make our customer success stories more versatile and easier to manage long-term. Unlike previous hard-coded or static solutions, this new structure is built for flexibility.

      h3 Key Features:

      dl
        div
          dt(style='display: inline-block; margin-right: .5em') Universal Use:
          dd(style='display: inline-block;') By utilising a robust tagging system, a single testimonial can now be assigned to specific business products or categories.

        div
          dt(style='display: inline-block; margin-right: .5em') Cross-Instance Availability: 
          dd(style='display: inline-block;') This content type is architected to work across different instances, ensuring that a testimonial added once can be surfaced wherever it is needed.
        
        div
          dt(style='display: inline-block; margin-right: .5em') Future-Proof Design: 
          dd(style='display: inline-block;') As our product suite grows, this tagging infrastructure allows us to easily associate existing testimonials with new business lines without requiring development updates.
      
    .grid.grid--reverse
      .grid__item.one-quarter
        .js-filters

      .grid__item.three-quarters
        .list-container
          .list-grid.js-items
          .pagination
            p
              | Showing 
              b.js-item-count
              |  of 
              b.js-total-item-count
              |  items
            button.btn.js-more(type='button') Load more
