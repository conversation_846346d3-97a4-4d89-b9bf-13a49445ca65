.panel.panel-styled
  .panel-header
    h2 Basic Details
  .panel-content

    .form-row(id='field--name', data-field='name')
      label
        span.form-label-text Name
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='name', value=data.name, autofocus)
      .js-error

    .form-row(id='field--jobTitle', data-field='jobTitle')
      label
        span.form-label-text Job Title
        input.control.control--text.form-field(type='text', name='jobTitle', value=data.jobTitle)
      .js-error
      
    .form-row(id='field--content', data-field='content')
      label
        span.form-label-text Content
          abbr(title='This field is required') *
        textarea.control.control--text.control--multiline.form-field(name='content', rows='5')= data.content
      .js-error
      .form-row-description.form-copy

    .form-row(id='field--instances', data-field='instances')
      label
        span.form-label-text Instances
          abbr(title='This field is required') *
        .js-instance-multi-select.form-field
      .js-error
        
    .form-row(id='field--tags', data-field='tags')
      label
        span.form-label-text Tags
          abbr(title='This field is required') *
        .form-field.js-tags-testimonial
      .js-error
      
