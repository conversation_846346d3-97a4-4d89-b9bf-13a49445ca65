//-
//- LIST FILTERS
//- ============
//- Appears to the left of the list grid, and scrolls with the page.
//- Contains form to filter list items.
//-

.panel.panel-styled
  .panel-header
    h2 Filter

  .panel-content

    form(action='#', method='get')
      .form-row.form-row-full-width
        label
          span.form-label-text Search
            .form-label-text__right
              span.js-tooltip-trigger.label(
                data-toggle='tooltip'
                title='Use double quotes to search for a phrase and minus to negate a word, e.g.<br />[ "Fashion Week" ] or [ Fashion -Week ]'
                )
                i.icon.icon--question Help

          input.control.control--text.form-field(type='text', name='keywords', placeholder='Keywords')

      .form-row.form-row-full-width
        label
          span.form-label-text Order
          select.control.control--choice.form-field(name='sort')
            option(value='-dateCreated') Newest First
            option(value='dateCreated') Oldest First
            option(value='-score') Relevance

      .form-row.form-row-actions
        input.btn(type='reset', value='Clear')
        input.btn.btn--action(type='submit', value='Filter')
