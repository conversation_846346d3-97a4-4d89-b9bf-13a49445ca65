.list-item

  .list-item-header

    .list-item-actions
      if allowed('testimonial', 'delete')
        label.list-item-select
          input.js-select(type='checkbox', name='name')
      .btn-group

    - var name = data.name + (data.jobTitle ? ' - ' + data.jobTitle : '')

    if allowed('testimonial', 'update')
      h2
        a.js-edit(href='testimonials/#{data._id}/form')= name
    else
      h2= name

  .list-item-content
    dl
      dt Created:
      dd= format(data.createdDate, 'calendar')
