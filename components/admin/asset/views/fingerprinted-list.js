const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(
  join(__dirname, '/../templates/fingerprinted-list.jade')
)
const FingerprintedItemView = require('./fingerprinted-list-item')
const BaseView = require('cf-base-view')
const debug = require('../../../../admin/source/js/lib/debug')(
  'asset in progress view'
)
const bindAll = require('lodash.bindall')

module.exports = BaseView.extend({
  className: 'asset-fingerprinted-list fingerprinted-list--collapsed',

  events: {
    'click .js-clear': 'handleClear',
    'click .js-toggle-content': 'handleToggle'
  },

  initialize({ bCollection, fCollection, unsavedModels, serviceLocator }) {
    bindAll(this)
    this.bCollection = bCollection
    this.fCollection = fCollection
    this.unsavedModels = unsavedModels
    this.serviceLocator = serviceLocator
    this.isCollapsed = true // Start expanded by default
    this.listenTo(this.fCollection, 'add', this.onAdd)
    this.listenTo(this.fCollection, 'clear', this.onClear)

    this.render()
  },

  onAdd(model) {
    debug('Item added to in progress fCollection', model)
    var newItem = new FingerprintedItemView({
      model: model,
      serviceLocator: this.serviceLocator,
      perRow: 4,
      bCollection: this.bCollection,
      fCollection: this.fCollection
    })

    // Add selection handling
    newItem.on('select', (model) => {
      this.selectedCollection.add(model)
    })

    newItem.on('deselect', (model) => {
      this.selectedCollection.remove(model)
    })

    // Pre-select if it's already in selectedCollection
    if (this.selectedCollection && this.selectedCollection.get(model.id)) {
      newItem.select()
    }

    newItem.on('updated', (updatedModel) => {
      this.fCollection._updatedModels = this.fCollection._updatedModels || []
      this.fCollection._updatedModels = {
        ...this.fCollection._updatedModels,
        [updatedModel.cid]: updatedModel
      }
      this.bCollection.syncWithCollection(this.fCollection)
    })

    this.listenTo(model, 'select', (selectedProxyModel) => {
      let targetModel = this.bCollection.get(selectedProxyModel.id)
      if (targetModel) return targetModel.trigger('select', targetModel)
      this.serviceLocator.assetService.read(
        selectedProxyModel.id,
        (err, asset) => {
          if (err) return console.error(err)
          this.bCollection.add(asset)
          targetModel = this.bCollection.get(selectedProxyModel.id)
          targetModel.trigger('select', targetModel)
        }
      )
    })

    this.listenTo(newItem.model, 'endOfUpload', this.onClear)
    this.attachView(newItem)
    this.$('.js-items').append(newItem.$el)
    this.$el.show()
  },

  onClear(model) {
    this.fCollection.remove(model)
    if (this.fCollection.length === 0) this.$el.hide()
    this.remove()
  },

  render() {
    this.$el.append(
      template({
        isEmpty: this.fCollection.length === 0
      })
    )

    this.fCollection.load()

    setTimeout(() => {
      this.unsavedModels.forEach((model) => {
        this.fCollection.add(model)
      })
      if (this.fCollection.length === 0) {
        this.$el.find('.js-no-items').show()
      }
    }, 1000)

    return this
  },

  handleToggle(e) {
    e.preventDefault()
    e.stopPropagation()

    this.isCollapsed = !this.isCollapsed
    const $content = this.$('.js-toggle-target')
    const $toggleText = this.$('.js-toggle-text')
    const $toggleIcon = this.$('.js-toggle-icon')

    if (this.isCollapsed) {
      $content.hide()
      $toggleText.text('Show')
      $toggleIcon.text('▼')
      this.$el.addClass('fingerprinted-list--collapsed')
    } else {
      $content.show()
      $toggleText.text('Hide')
      $toggleIcon.text('▲')
      this.$el.removeClass('fingerprinted-list--collapsed')
    }
  },

  handleClear() {
    this.fCollection
      .filter((model) => {
        return model.isClearable
      })
      .forEach((model) => {
        model.trigger('clear', model)
      })
  }
})
