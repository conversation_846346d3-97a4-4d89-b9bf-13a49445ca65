const View = require('ventnor')
class CategoryAliasSelect extends View {
  constructor(selected, items) {
    super(...arguments)
    this.selected = selected
    if (!items) {
      this.items = []
      // TODO: Change this to the length of the longest instance.categories
      for (let i = 0; i < 20; i++) {
        this.items.push({ value: i, text: `Category ${i + 1}` })
      }
    } else {
      this.items = items
    }
    this.$el = $('<select multiple />')
    this.$el.addClass('control control--choice control--multiline')
    this.el = this.$el[0]
    this.$el.attr('placeholder', 'Select category alias')
    this.regions = []
    this.on('remove', () => this.el.selectize.destroy())
  }

  onChange() {
    const items = this.el.selectize.items
    this.emit('change', items)
  }

  onRemove(value) {
    this.el.selectize.addOption({ value: value, text: value })
    this.el.selectize.refreshOptions()
    this.onChange()
  }

  onAdd(value) {
    this.onChange()
  }

  addSelectizeHandlers() {
    this.el.selectize.on('item_add', this.onAdd.bind(this))
    this.el.selectize.on('item_remove', this.onRemove.bind(this))
  }

  initializeSelectize() {
    this.load()
    this.addSelectizeHandlers()
  }

  load() {
    this.items.forEach((item) => {
      const { value, text } = item
      this.el.selectize.addOption({ value, text })
    })
    this.selected.forEach((item) => {
      this.el.selectize.addItem(item)
    })
  }

  render() {
    setTimeout(() => {
      this.$el.selectize({
        delimiter: ',',
        persist: false,
        create: false,
        sortField: 'asc',
        onInitialize: this.initializeSelectize.bind(this)
      })
    }, 100)
    return this
  }
}

module.exports = CategoryAliasSelect
