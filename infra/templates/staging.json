{"apiVersion": "v1", "spec": {"kind": "Workflow", "spec": {"type": "sequential", "steps": [{"kind": "Workflow", "spec": {"type": "parallel", "steps": [{"kind": "BuildService", "spec": {"name": "build-staging", "billing": {"deploymentPlan": "nf-compute-100-1"}, "vcsData": {"projectUrl": "https://github.com/Bizclik-Media/BizClik", "projectType": "github", "accountLogin": "Bizclik-Media"}, "buildSettings": {"dockerfile": {"buildEngine": "kaniko", "dockerFilePath": "/Dockerfile", "useCache": true, "dockerWorkDir": "/"}}, "buildConfiguration": {"ciIgnoreFlags": ["[skip ci]", "[ci skip]", "[no ci]", "[skip nf]", "[nf skip]", "[northflank skip]", "[skip northflank]"], "ciIgnoreFlagsEnabled": false, "isAllowList": false}, "buildArguments": {"NODE_ENV": "staging"}, "disabledCI": false}}, {"kind": "BuildService", "spec": {"name": "build-varnish-staging", "billing": {"deploymentPlan": "nf-compute-200-1"}, "vcsData": {"projectUrl": "https://github.com/Bizclik-Media/BizClik", "projectType": "github", "accountLogin": "Bizclik-Media"}, "buildSettings": {"dockerfile": {"buildEngine": "kaniko", "dockerFilePath": "/infra/varnish/Dockerfile", "useCache": true, "dockerWorkDir": "/infra/varnish"}}, "buildConfiguration": {"ciIgnoreFlags": ["[skip ci]", "[ci skip]", "[no ci]", "[skip nf]", "[nf skip]", "[northflank skip]", "[skip northflank]"], "ciIgnoreFlagsEnabled": false, "isAllowList": false}, "buildArguments": {"NODE_ENV": "staging"}, "disabledCI": false}}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "spec": {"name": "sitemap-generator-staging", "tags": [], "billing": {"deploymentPlan": "nf-compute-100-1"}, "backoffLimit": 0, "runOnSourceChange": "never", "activeDeadlineSeconds": 300, "deployment": {"docker": {"configType": "customCommand", "customCommand": "dist/worker/app.js components/worker/tasks/generate-sitemap/task.js"}, "buildpack": {"configType": "default"}, "storage": {"ephemeralStorage": {"storageSize": 1024}, "shmSize": 64}, "internal": {"id": "build-staging", "branch": "staging", "buildSHA": "latest"}}, "runtimeEnvironment": {"NODE_ENV": "staging"}, "schedule": "30,0 * * * *", "suspended": false, "concurrencyPolicy": "forbid", "healthChecks": []}}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "spec": {"name": "mongo-wrangler-staging", "billing": {"deploymentPlan": "nf-compute-200"}, "backoffLimit": 0, "runOnSourceChange": "never", "activeDeadlineSeconds": 1800, "deployment": {"docker": {"configType": "default"}, "buildpack": {"configType": "default"}, "storage": {"ephemeralStorage": {"storageSize": 5120}}, "vcs": {"projectUrl": "https://github.com/clocklimited/mongo-wrangler", "projectType": "github", "projectBranch": "northflank"}}, "buildSettings": {"dockerfile": {"buildEngine": "kaniko", "dockerFilePath": "/Dockerfile", "dockerWorkDir": "/"}}, "runtimeEnvironment": {"EXCLUDES": "", "INCLUDES": "", "ONLY": "", "OUTPUT_DB_NAME": "bizclik-site-staging", "INPUT_DB_NAME": "9adfe45b16ba", "INPUT": "mongodb://6b9ed02fbfb6bda3:<EMAIL>:27017/9adfe45b16ba?replicaSet=rs0&authSource=9adfe45b16ba&tls=true", "NF_API_TOKEN": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************.ZlB7O2h3tGZinIp7UEAQfpNGurexkSlSKotJHka5CFo"}, "schedule": "0 3 * * *", "concurrencyPolicy": "forbid", "healthChecks": [], "buildConfiguration": {"pathIgnoreRules": [], "isAllowList": false, "ciIgnoreFlagsEnabled": false}, "buildArguments": {}, "disabledCI": false, "suspended": false}}]}}, {"kind": "Workflow", "spec": {"type": "parallel", "steps": [{"kind": "<PERSON><PERSON><PERSON>", "spec": {"name": "validate-varnish-staging", "billing": {"deploymentPlan": "nf-compute-200-1"}, "backoffLimit": 0, "runOnSourceChange": "never", "activeDeadlineSeconds": 600, "deployment": {"docker": {"configType": "customCommand", "customCommand": "/usr/sbin/varnishd -Cf /etc/varnish/default.vcl"}, "buildpack": {"configType": "default"}, "storage": {"ephemeralStorage": {"storageSize": 1024}}, "internal": {"id": "build-varnish-staging", "branch": "staging", "buildSHA": "latest"}}, "healthChecks": [], "buildConfiguration": {"pathIgnoreRules": [], "isAllowList": false, "ciIgnoreFlagsEnabled": false}, "runtimeEnvironment": {}, "buildArguments": {}, "disabledCI": false}}, {"kind": "DeploymentService", "spec": {"name": "admin-staging", "billing": {"deploymentPlan": "nf-compute-50"}, "deployment": {"instances": 1, "docker": {"configType": "customCommand", "customCommand": "/app/admin/app.js"}, "buildpack": {"configType": "default"}, "storage": {"ephemeralStorage": {"storageSize": 1024}}, "releaseFlowControlled": true}, "ports": [{"name": "p01", "internalPort": 7001, "public": true, "domains": [], "protocol": "HTTP", "security": {"credentials": [], "policies": []}, "disableNfDomain": false}], "runtimeEnvironment": {"NODE_ENV": "staging"}, "healthChecks": [{"protocol": "HTTP", "type": "readinessProbe", "port": 7001, "path": "/_health", "initialDelaySeconds": 5, "periodSeconds": 10, "timeoutSeconds": 5, "failureThreshold": 3, "successThreshold": 1}], "autoscaling": {}}}, {"kind": "DeploymentService", "spec": {"name": "api-staging", "billing": {"deploymentPlan": "nf-compute-50"}, "deployment": {"instances": 1, "docker": {"configType": "customCommand", "customCommand": "/app/dist/api/app.js"}, "buildpack": {"configType": "default"}, "storage": {"ephemeralStorage": {"storageSize": 1024}}, "releaseFlowControlled": true}, "ports": [{"name": "p01", "internalPort": 7002, "public": true, "domains": ["api.staging.cms.bizclikmedia.net"], "protocol": "HTTP", "security": {"credentials": [], "policies": []}, "disableNfDomain": false}], "runtimeEnvironment": {"NODE_ENV": "staging"}, "healthChecks": [{"protocol": "HTTP", "type": "readinessProbe", "port": 7002, "path": "/_health", "initialDelaySeconds": 5, "periodSeconds": 10, "timeoutSeconds": 5, "failureThreshold": 3, "successThreshold": 1}], "autoscaling": {}}}, {"kind": "DeploymentService", "spec": {"name": "site-staging", "billing": {"deploymentPlan": "nf-compute-100-1"}, "deployment": {"instances": 1, "docker": {"configType": "customCommand", "customCommand": "/app/dist/site/server/app.js"}, "buildpack": {"configType": "default"}, "storage": {"ephemeralStorage": {"storageSize": 1024}}, "releaseFlowControlled": true}, "ports": [{"name": "p01", "internalPort": 7000, "public": true, "protocol": "HTTP", "security": {"credentials": [], "policies": []}, "domains": [], "disableNfDomain": false}], "runtimeEnvironment": {"NODE_ENV": "staging"}, "healthChecks": [{"protocol": "HTTP", "type": "readinessProbe", "port": 7000, "path": "/_health", "initialDelaySeconds": 5, "periodSeconds": 10, "timeoutSeconds": 5, "failureThreshold": 3, "successThreshold": 1}], "autoscaling": {}}}, {"kind": "DeploymentService", "spec": {"name": "message-bus-staging", "billing": {"deploymentPlan": "nf-compute-10"}, "deployment": {"instances": 1, "docker": {"configType": "customCommand", "customCommand": "/app/message-bus/app.js"}, "buildpack": {"configType": "default"}, "storage": {"ephemeralStorage": {"storageSize": 1024}}, "releaseFlowControlled": true}, "ports": [{"name": "p01", "internalPort": 7003, "protocol": "TCP", "public": false, "security": {"credentials": [], "policies": []}, "domains": [], "disableNfDomain": false}], "runtimeEnvironment": {"NODE_ENV": "staging"}, "healthChecks": [], "autoscaling": {}}}, {"kind": "DeploymentService", "spec": {"name": "worker-staging", "billing": {"deploymentPlan": "nf-compute-10"}, "deployment": {"instances": 0, "docker": {"configType": "customCommand", "customCommand": "/app/dist/worker/app.js"}, "buildpack": {"configType": "default"}, "storage": {"ephemeralStorage": {"storageSize": 1024}}, "releaseFlowControlled": true}, "ports": [{"name": "p01", "internalPort": 8114, "protocol": "HTTP", "public": false, "security": {"credentials": [], "policies": []}, "domains": [], "disableNfDomain": false}], "runtimeEnvironment": {"NODE_ENV": "staging"}, "healthChecks": [{"protocol": "HTTP", "type": "readinessProbe", "port": 8114, "path": "/_health", "initialDelaySeconds": 5, "periodSeconds": 10, "timeoutSeconds": 5, "failureThreshold": 3, "successThreshold": 1}], "autoscaling": {}}}, {"kind": "DeploymentService", "spec": {"name": "varnish-staging", "billing": {"deploymentPlan": "nf-compute-10"}, "deployment": {"instances": 1, "docker": {"configType": "default"}, "buildpack": {"configType": "default"}, "storage": {"ephemeralStorage": {"storageSize": 10240}}, "releaseFlowControlled": true}, "ports": [{"name": "p01", "internalPort": 8080, "public": true, "domains": ["admin.staging.cms.bizclikmedia.net", "site.staging.cms.bizclikmedia.net", "proxy.staging.cms.bizclikmedia.net", "ai-staging.bizclikmedia.net", "bca-staging.bizclikmedia.net", "bce-staging.bizclikmedia.net", "bcna-staging.bizclikmedia.net", "cg-staging.bizclikmedia.net", "cm-staging.bizclikmedia.net", "dcm-staging.bizclikmedia.net", "ed-staging.bizclikmedia.net", "ev-staging.bizclikmedia.net", "fm-staging.bizclikmedia.net", "food-staging.bizclikmedia.net", "hg-staging.bizclikmedia.net", "im-staging.bizclikmedia.net", "m8-staging.bizclikmedia.net", "mg-staging.bizclikmedia.net", "mining-staging.bizclikmedia.net", "mm-staging.bizclikmedia.net", "pm-staging.bizclikmedia.net", "scd-staging.bizclikmedia.net", "sm-staging.bizclikmedia.net", "tm-staging.bizclikmedia.net"], "protocol": "HTTP", "security": {"credentials": [], "policies": []}, "disableNfDomain": false}], "runtimeEnvironment": {"VARNISH_SIZE": "100M", "VARNISH_HTTP_PORT": "8080"}, "healthChecks": [{"protocol": "HTTP", "type": "readinessProbe", "port": 8080, "path": "/_varnish_health", "initialDelaySeconds": 5, "periodSeconds": 10, "timeoutSeconds": 5, "failureThreshold": 3, "successThreshold": 1}], "autoscaling": {}}}]}}, {"kind": "Workflow", "spec": {"type": "parallel", "steps": [{"kind": "<PERSON><PERSON>", "spec": {"name": "staging-database", "description": "", "type": "mongodb", "version": "5.0.21", "billing": {"deploymentPlan": "nf-compute-50", "storageClass": "ssd", "storage": 4096, "replicas": 1}, "tlsEnabled": true, "externalAccessEnabled": false, "ipPolicies": [], "pitrEnabled": false, "typeSpecificSettings": {}}}]}}, {"kind": "Workflow", "spec": {"type": "parallel", "steps": [{"kind": "SecretGroup", "spec": {"name": "Staging Secrets", "description": "", "secretType": "environment-arguments", "priority": 10, "restrictions": {"restricted": true, "nfObjects": [{"id": "admin-staging", "type": "service"}, {"id": "api-staging", "type": "service"}, {"id": "message-bus-staging", "type": "service"}, {"id": "site-staging", "type": "service"}, {"id": "worker-staging", "type": "service"}, {"id": "sitemap-generator-staging", "type": "job"}], "tags": []}, "addonDependencies": [{"addonId": "staging-database", "keys": [{"keyName": "HOST", "aliases": []}, {"keyName": "MONGO_SRV", "aliases": ["MONGO_URL"]}, {"keyName": "USERNAME", "aliases": []}, {"keyName": "PASSWORD", "aliases": []}, {"keyName": "DATABASE", "aliases": []}]}]}}]}}]}}, "name": "BizClick-staging", "description": "", "project": {"id": "bizclik"}, "$schema": "https://api.northflank.com/v1/schemas/template"}