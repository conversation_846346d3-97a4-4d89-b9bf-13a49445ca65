import React, { useState } from 'react'
import {
  func,
  array,
  number,
  string,
  shape,
  arrayOf,
  bool,
  oneOfType,
  object
} from 'prop-types'
import InfiniteScroll from 'react-infinite-scroll-component'

import styles from './SearchLayout.module.scss'

import Container from '@/component/Primitive/Container'
import TextAlign from '@/component/Primitive/TextAlign'
import Spinner from '@/component/Primitive/Spinner'
import SmartLink from '@/component/Primitive/SmartLink'
import SearchField from './component/SearchField'
import SearchResult from './component/SearchResult'
import ButtonStandard from '@/component/Primitive/ButtonStandard'
import Type from '@/component/Primitive/Type'
import FilterDropdownComponent from './component/FilterDropdown'
import IconButton from '@/component/Primitive/IconButton'
import Icon from '@/component/Primitive/Icon'
import classNames from 'classnames'
import useFeatureFlagHelper from '@/lib/feature-flag-helper'

const formatToOption = (o) => ({ value: o, text: o })

const SearchLayout = ({
  onSearch,
  onLoadMore,
  results,
  searchTerm,
  onCategoryChange,
  onSortChange,
  onContentTypeChange,
  total,
  loading,
  infiniteScrollEnabled,
  selectedFilters,
  filters,
  companiesResults,
  executivesResults,
  instance
}) => {
  const hasMore = results?.length < total
  const [filtersOpen, setFiltersOpen] = useState(false)
  const { isFeatureEnabled } = useFeatureFlagHelper(instance)

  const { sortOptions, contentTypeOptions, categoryOptions } = filters
  const { categories, contentTypes, sortBy } = selectedFilters

  const sortDropdownOptions = sortOptions.map(formatToOption)
  const contentTypeDropdownOptions = ['All', ...contentTypeOptions].map(
    formatToOption
  )
  const categoryDropdownOptions = ['All', ...categoryOptions].map(
    formatToOption
  )

  return (
    <div className={styles.SearchLayout}>
      <Container center gutter size="large">
        <div className={styles.Search}>
          <SearchField
            searchTerm={searchTerm}
            loading={loading}
            onSubmit={onSearch}
            isFeatureEnabled={isFeatureEnabled}
          />
        </div>
      </Container>

      {searchTerm && (
        <div className={styles.Filters}>
          <Container
            center
            gutter
            size="large"
            className={styles.FiltersContainer}
          >
            <Type size="heading8" className={styles.FiltersTitle}>
              <strong>{total}</strong> content results
            </Type>
            <div className={styles.FiltersWrapper}>
              <IconButton
                className={styles.ToggleFiltersButton}
                onClick={() => setFiltersOpen(!filtersOpen)}
                icon={filtersOpen ? 'close' : 'filter'}
                iconWidth={filtersOpen ? 24 : 18}
                iconHeight={filtersOpen ? 24 : 12}
                size="tiny"
                increaseHitArea
                a11ytext="Filters"
              />
              <div
                className={classNames(
                  styles.FiltersList,
                  filtersOpen && styles.filtersOpen
                )}
              >
                <Type className={styles.FiltersLabel} size="heading8">
                  Content Filters
                </Type>
                <FilterDropdown
                  label="Content Type"
                  items={contentTypeDropdownOptions}
                  value={
                    contentTypes.length
                      ? contentTypes.map(formatToOption)
                      : ['All'].map(formatToOption)
                  }
                  onChange={(value) => {
                    onContentTypeChange(value.value)
                  }}
                  checkbox
                />
                <FilterDropdown
                  label="Category"
                  items={categoryDropdownOptions}
                  value={
                    categories.length
                      ? categories.map(formatToOption)
                      : ['All'].map(formatToOption)
                  }
                  onChange={(value) => {
                    onCategoryChange(value.value)
                  }}
                  checkbox
                />
                <FilterDropdown
                  label="Sort by"
                  items={sortDropdownOptions}
                  value={[sortBy].map(formatToOption)}
                  onChange={(value) => {
                    onSortChange(value.value)
                  }}
                />
              </div>
            </div>
          </Container>
        </div>
      )}

      <Container
        className={styles.NoResultsContainer}
        center
        gutter
        size="large"
      >
        {searchTerm && total === 0 && (
          <Container center gutter size="medium" className={styles.NotFound}>
            <TextAlign center>
              No results found. Please try a different search term, or explore
              content from our{' '}
              <SmartLink to="/section" as="/">
                homepage
              </SmartLink>
              .
            </TextAlign>
          </Container>
        )}

        {searchTerm && total > 0 && (
          <div className={styles.ResultsContainer}>
            <div className={styles.LeftColumn}>
              <Title title="Content" bordered="default" />
              <InfiniteScroll
                dataLength={results.length}
                next={onLoadMore}
                hasMore={hasMore && infiniteScrollEnabled && !loading}
                scrollThreshold={0.8}
                loader={
                  <div className={styles.Spinner}>
                    <Spinner />
                  </div>
                }
              >
                {results.map((res, i) => (
                  <SearchResult
                    key={`SearchResult--Results--${res.id}--${i}`}
                    {...res}
                  />
                ))}
              </InfiniteScroll>
            </div>
            {((isFeatureEnabled('companySearch') &&
              companiesResults?.length > 0) ||
              (isFeatureEnabled('executiveSearch') &&
                executivesResults?.length > 0)) && (
              <div className={styles.RightColumn}>
                {isFeatureEnabled('companySearch') &&
                  companiesResults?.length > 0 && (
                    <div classNames={styles.Companies}>
                      <Title title="Companies" bordered="default" />
                      <div className={styles.CompanyResults}>
                        {companiesResults.map((company, key) => (
                          <CompanyCard key={key} company={company} />
                        ))}
                      </div>
                    </div>
                  )}
                {isFeatureEnabled('executiveSearch') &&
                  executivesResults?.length > 0 && (
                    <div classNames={styles.Executives}>
                      <Title title="Executives" bordered="default" />
                      <div className={styles.ExecutiveResults}>
                        {executivesResults.map((executive, key) => (
                          <ExecutiveCard key={key} executive={executive} />
                        ))}
                      </div>
                    </div>
                  )}
              </div>
            )}
          </div>
        )}

        {searchTerm && hasMore && !infiniteScrollEnabled && (
          <div className={styles.LoadMore}>
            <ButtonStandard
              className={styles.LoadBtn}
              size="huge"
              onClick={onLoadMore}
            >
              <Type size="heading8" weight="bold">
                View More
              </Type>
            </ButtonStandard>
          </div>
        )}
      </Container>
    </div>
  )
}

const Title = ({ title }) => (
  <Type className={styles.Title} size="heading6" weight="medium">
    {title}
  </Type>
)

Title.propTypes = {
  title: string
}

const CompanyCard = ({ company }) => {
  const logoUrl =
    company?.images?.logo_free_127?.[0]?.url ||
    company?.images?.thumbnail_1x1_220?.[0]?.url

  return (
    <SmartLink
      className={styles.CompanyCardLink}
      href={`/company/${company?.slug}`}
    >
      <div className={styles.CompanyCard}>
        <div className={styles.CompanyCardImage}>
          {logoUrl && <img src={logoUrl} alt="" />}
        </div>
        <div className={styles.CompanyCardContent}>
          <Type className={styles.CompanyName} size="body3" weight="medium">
            {company?.name}
          </Type>
        </div>
        <div className={styles.CompanyCardCTAContainer}>
          <Icon type="arrow-right" className={styles.CompanyCardCTA} />
        </div>
      </div>
    </SmartLink>
  )
}

const ExecutiveCard = ({ executive }) => {
  const headshotUrl = executive?.images?.headshot_220x347_220?.[0]?.url

  return (
    <SmartLink
      className={styles.ExecutiveCardLink}
      href={`/executive/${executive?.slug}`}
    >
      <div className={styles.ExecutiveCard}>
        <div className={styles.ExecutiveCardImage}>
          {headshotUrl && (
            <>
              <img
                src={headshotUrl}
                alt=""
                className={styles.ExecutiveCardImageBackground}
              />
              <img
                src={headshotUrl}
                alt=""
                className={styles.ExecutiveCardImageForeground}
              />
            </>
          )}
        </div>
        <div className={styles.ExecutiveCardContent}>
          <Type className={styles.ExecutiveName} size="body3" weight="medium">
            {executive?.name}
          </Type>
          <Type
            className={styles.ExecutiveJobTitle}
            size="body4"
            weight="normal"
          >
            {executive?.jobTitle}
          </Type>
        </div>
        <div className={styles.ExecutiveCardCTAContainer}>
          <Icon type="arrow-right" className={styles.ExecutiveCardCTA} />
        </div>
      </div>
    </SmartLink>
  )
}

SearchLayout.propTypes = {
  loading: bool,
  infiniteScrollEnabled: bool,
  onLoadMore: func,
  onSearch: func.isRequired,
  results: array.isRequired,
  searchTerm: string,
  total: number,
  onCategoryChange: func,
  onSortChange: func,
  onContentTypeChange: func,
  selectedFilters: shape({
    categories: array,
    contentTypes: array,
    sortBy: string
  }),
  filters: shape({
    sortOptions: array,
    contentTypeOptions: array,
    categoryOptions: array
  }),
  companiesResults: array,
  executivesResults: array,
  instance: object
}

CompanyCard.propTypes = {
  company: shape({
    _id: string,
    name: string,
    slug: string,
    images: shape({
      logo_free_127: array,
      thumbnail_1x1_220: array
    })
  })
}

ExecutiveCard.propTypes = {
  executive: shape({
    _id: string,
    name: string,
    slug: string,
    jobTitle: string,
    images: shape({
      headshot_220x347_220: array
    })
  })
}

export default SearchLayout

// Filter wrapper
const FilterDropdown = ({ label, value, items, onChange, checkbox }) => {
  const valueLabel = Array.isArray(value)
    ? value.length > 1
      ? 'Multiple'
      : value[0].text
    : value.text
  return (
    <div className={styles.DropdownWrapper}>
      <FilterDropdownComponent
        label={label}
        className={styles.Dropdown}
        value={value}
        valueLabel={valueLabel}
        items={items}
        checkbox={checkbox}
        onChange={onChange}
      />
    </div>
  )
}

FilterDropdown.propTypes = {
  label: string,
  checkbox: bool,
  value: oneOfType([object, array]),
  items: arrayOf(
    shape({
      text: string,
      value: string
    })
  ),
  onChange: func
}
