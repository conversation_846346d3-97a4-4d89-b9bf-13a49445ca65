import React, { useState, useContext } from 'react'
import { func, object, string, bool } from 'prop-types'
import classNames from 'classnames'

import styles from './SearchField.module.scss'

import Icon from '@/component/Primitive/Icon'
import Spinner from '@/component/Primitive/Spinner'
import IconButton from '@/component/Primitive/IconButton'
import { ThemeContext } from '@/component/Context/ThemeContext'

const SearchField = ({
  onSubmit,
  setRef,
  loading,
  searchTerm,
  className,
  isFeatureEnabled
  // __url
}) => {
  const { theme } = useContext(ThemeContext)
  const [value, updateValue] = useState(searchTerm || '')

  const handleChange = (e) => {
    updateValue(e.target.value)
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    onSubmit && onSubmit(value)
  }

  const handleClear = () => {
    updateValue('')
  }

  return (
    <form
      className={classNames(styles.SearchField, className)}
      onSubmit={handleSubmit}
      ref={setRef}
    >
      <div className={styles.SearchFieldControlWrapper}>
        <button className={styles.SearchFieldSubmit} type="submit">
          {loading && <Spinner />}
          {!loading && (
            <Icon
              type="search"
              width={17.5}
              height={17.5}
              a11ytext="SearchButton"
            />
          )}
        </button>
        <input
          className={classNames(styles.SearchFieldControl, styles[theme])}
          type="text"
          name="q"
          onChange={handleChange}
          value={value}
          placeholder={
            !isFeatureEnabled('executiveSearch') &&
            !isFeatureEnabled('companySearch')
              ? 'Lists, Events, Interviews...'
              : 'Lists, Events, Interviews, Executives, Companies...'
          }
          // This is an acceptable autoFocus use-case, as the User will have
          // just manually triggered the search form, so will be expecting the
          // focus jump.
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus
        />
        {value && value.length > 0 && (
          <IconButton
            className={styles.ClearBtn}
            size="tiny"
            onClick={handleClear}
            icon="close"
            a11ytext="Clear"
            increaseHitArea
          />
        )}
      </div>
    </form>
  )
}

SearchField.propTypes = {
  onSubmit: func,
  setRef: object,
  className: string,
  loading: bool,
  searchTerm: string,
  isFeatureEnabled: func
}

export default SearchField
