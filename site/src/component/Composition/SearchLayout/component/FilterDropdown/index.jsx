import React, { useRef, useState } from 'react'
import { array, bool, func, string, object, oneOfType } from 'prop-types'
import classNames from 'classnames'
import useEscapeKey from '@/hook/useEscapeKey'
import useOutsideClick from '@/hook/useOutsideClick'

import styles from './FilterDropdown.module.scss'

import Icon from '@/component/Primitive/Icon'
import Type from '@/component/Primitive/Type'
import CheckDropdownItems from './component/CheckDropdownItems'
import ButtonDropdownItems from './component/ButtonDropdownItems'

const FilterDropdown = ({
  items,
  onChange,
  value,
  valueLabel, // Currently selected value shown on desktop screen
  label,
  checkbox,
  className
}) => {
  const [expanded, setExpanded] = useState(false)
  const ref = useRef()

  const toggleExpanded = () => {
    setExpanded((prevExpanded) => !prevExpanded)
  }

  const handleClose = () => {
    setExpanded(false)
  }

  const handleSelection = (item) => {
    handleClose()
    onChange && onChange(item)
  }

  useEscapeKey(expanded && handleClose)
  useOutsideClick(ref, expanded && handleClose)

  return (
    <div
      className={classNames(
        styles.FilterDropdown,
        expanded && styles.expanded,
        className
      )}
      ref={ref}
    >
      <div className={styles.FilterDropdownList}>
        <button
          type="button"
          className={styles.FilterDropdownListLabel}
          onClick={toggleExpanded}
        >
          <Type className={styles.FilterDropdownListLabelText} size="heading8">
            {label}
          </Type>
          <Type
            className={styles.FilterDropdownListValueText}
            as="span"
            size={['body2', 'body3']}
          >
            {valueLabel}
          </Type>
          <Icon
            width={12}
            height={9}
            type={expanded ? 'arrow-up' : 'arrow-down'}
            a11ytext={expanded ? 'Hide content' : 'Reveal content'}
            className={styles.Icon}
          />
        </button>
      </div>
      {checkbox
        ? expanded && (
            <CheckDropdownItems
              options={items}
              value={value}
              onChange={onChange}
            />
          )
        : expanded && (
            <ButtonDropdownItems
              items={items}
              onChange={handleSelection}
              value={value}
            />
          )}
    </div>
  )
}

FilterDropdown.propTypes = {
  items: array.isRequired,
  onChange: func,
  value: oneOfType([object, array]),
  valueLabel: string,
  label: string,
  className: string,
  checkbox: bool
}

export default FilterDropdown
