.SearchField {
  position: relative;
  width: 100%;
  background: $color-white-primary;
}

.SearchFieldControlWrapper {
  display: flex;
  overflow: hidden;
  height: 100%;
  align-items: center;
}

.SearchFieldControl {
  flex: 1 0 auto;
  border: 0;
  position: relative;
  top: spacing(0.25);
  font-size: $font-heading-eight;
  font-weight: $font-weight-medium;

  @include mq($breakpoint-desktop) {
    font-size: $font-heading-three;
  }

  &:focus {
    outline: 0;
  }

  &::placeholder {
    color: $color-grey-secondary;
  }
}

.SearchFieldSubmit {
  position: relative;
  width: spacing(6);
  svg {
    fill: $color-grey-secondary;
    transition: fill $duration-standard $easing-standard;
  }

  &[disabled] {
    opacity: 0.4;
  }

  @include mq($breakpoint-desktop) {
    width: unset;
    padding-right: spacing(1.5);
  }
}

.SearchFieldHeading {
  padding: spacing(1.5) spacing(4);
  color: $color-grey-primary;
}

.SearchFieldList {
  list-style: none;
}

button.ClearBtn {
  &:hover,
  &:focus {
    svg {
      fill: $color-black-primary;
    }
  }
}
