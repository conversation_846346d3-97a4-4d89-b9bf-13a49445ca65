import React from 'react'
import { storiesOf } from '@storybook/react'
import { action } from '@storybook/addon-actions'

import <PERSON>Field from '.'

const stories = storiesOf('Composition/SearchField', module)

stories.add(
  'Info',
  () => (
    <SearchField onChange={action('onChange')} onSubmit={action('onSubmit')} />
  ),
  {
    info: {
      inline: true,
      text: ``
    }
  }
)

stories.add('Default state', () => (
  <SearchField onChange={action('onChange')} onSubmit={action('onSubmit')} />
))
