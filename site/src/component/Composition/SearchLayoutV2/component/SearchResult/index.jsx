import React from 'react'
import { string, object } from 'prop-types'

import styles from './SearchResult.module.scss'

import Type from '@/component/Primitive/Type'
import SmartLink from '@/component/Primitive/SmartLink'
import Breadcrumbs from '@/component/Primitive/Breadcrumbs'
import Icon from '@/component/Primitive/Icon'

const determineImageUrl = (contentType, images) => {
  const portraitCrop = 'thumbnail_portrait_147'
  const typeToCrop = {
    Video: 'thumbnail_widescreen_307',
    Interview: portraitCrop,
    'Company Report': portraitCrop,
    Whitepaper: portraitCrop
  }
  const crop = typeToCrop[contentType] || 'thumbnail_landscape_307'

  return images[crop][0].url
}

const SearchResult = ({
  headline,
  sell,
  images,
  contentType,
  category,
  fullUrlPath,
  eventBaseSlug
}) => {
  const imageUrl = determineImageUrl(contentType, images)
  const showPlayIcon = contentType === 'Video'
  const breadcrumbs = [{ name: contentType }]

  if (category) {
    breadcrumbs.push({ name: category })
  }

  return (
    <div className={styles.SearchResult}>
      <div className={styles.Content}>
        <Breadcrumbs
          highlight={breadcrumbs.length < 2 ? 'none' : 'last'}
          breadcrumbs={breadcrumbs}
        />
        <Type
          themed
          className={styles.Title}
          size={['body1', 'body1']}
          weight="bold"
          as="h2"
        >
          <SmartLink
            to={eventBaseSlug ? '/eventArticle' : '/article'}
            as={fullUrlPath}
            className={styles.Link}
          >
            {headline}
          </SmartLink>
        </Type>
        {sell && (
          <div className={styles.Description}>
            <Type size="body2" weight="regular">
              {sell}
            </Type>
          </div>
        )}
      </div>
      {imageUrl && (
        <div className={styles.Image}>
          {showPlayIcon && (
            <Icon
              width={50}
              height={50}
              className={styles.PlayIcon}
              a11ytext=""
              type="play"
            />
          )}
          <img loading="lazy" src={imageUrl} alt={headline} />
        </div>
      )}
    </div>
  )
}

SearchResult.propTypes = {
  images: object,
  headline: string,
  sell: string,
  contentType: string,
  category: string,
  fullUrlPath: string,
  eventBaseSlug: string
}

export default SearchResult
