.SearchResult {
  text-decoration: none;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  border-bottom: 1px solid $color-misc-divider;
  padding-block: spacing(2.5);
  position: relative;

  @include mq($breakpoint-tablet) {
    flex-direction: row;
    justify-content: space-between;
  }

  &:first-child {
    padding-top: 0;
  }

  &:hover,
  &:focus {
    .Title {
      color: var(--color-theme--secondary);
    }
  }
}

.Image {
  display: none;
  position: relative;
  color: rgba($color-white-primary, 0.5);
  flex-shrink: 0;
  max-width: 200px;

  @include mq($breakpoint-tablet) {
    display: block;
  }
}

.Content {
  & > * + * {
    margin-top: spacing(1.25);

    @include mq($breakpoint-tablet) {
      //margin-top: spacing(2.5);
    }
  }

  @include mq($breakpoint-tablet) {
    max-width: 750px;
    padding-top: spacing(1.2);
    margin-right: spacing(4);
  }
}

.Title {
  color: $color-black-primary;
}

.Description {
  color: $color-grey-primary;
}

.PlayIcon {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  // transform: translate(-50%, -50%);
  backdrop-filter: blur(20px);
  border-radius: 50%;
  overflow: hidden;
}

.Link {
  &:link,
  &:visited {
    color: inherit;
  }

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: 5;
  }
}
