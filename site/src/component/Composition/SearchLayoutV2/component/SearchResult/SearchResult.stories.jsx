import React from 'react'
import { storiesOf } from '@storybook/react'

import SearchResult from '.'
import Stack from '@/component/Primitive/Stack'

const stories = storiesOf('Composition/SearchResult', module)

const result = {
  id: '1',
  breadcrumbs: [
    {
      name: 'Company'
    },
    {
      name: 'Corporate Finance'
    }
  ],
  title: 'Home Credit Vietnam: Agile to Win Over the Pandemic',
  description:
    '<PERSON>, Chief Operations and Digital Transformation Officer, talks how Home Credit Vietnam adapts and adjusts to drive business during Covid 19.',
  imageUrl: 'https://via.placeholder.com/307x232',
  link: 'https://www.example.com'
}

const defaultProps = {
  ...result
}

stories.add('Info', () => <SearchResult {...defaultProps} />, {
  info: {
    inline: true,
    text: `A component to display search result`
  }
})

stories.add('Default state', () => <SearchResult {...defaultProps} />)
stories.add('Stacked', () => (
  <Stack>
    {Array(5)
      .fill(defaultProps)
      .map((props, index) => (
        <SearchResult {...props} key={index} />
      ))}
  </Stack>
))
