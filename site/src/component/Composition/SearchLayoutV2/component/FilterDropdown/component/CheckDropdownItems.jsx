import React from 'react'
import { array, func, string } from 'prop-types'
import classNames from 'classnames'

import styles from '../FilterDropdown.module.scss'

import Icon from '@/component/Primitive/Icon'
import NativeCheckControl from '@/component/Primitive/CheckControl/NativeCheckControl'

const CheckDropdownItems = ({ options, value, onChange }) => (
  <div
    className={classNames(
      styles.FilterDropdownList,
      styles.FilterDropdownListOverlay
    )}
  >
    {options.map((option, i) => {
      const checked =
        Array.isArray(value) &&
        value.findIndex((v) => v.value === option.value) > -1
      return (
        <div
          className={classNames(
            styles.FilterDropdownCheckBoxWrapper,
            styles.FilterDropdownListItem,
            styles.itemReveal
          )}
          key={`dropdown-item-${i}`}
        >
          <span className={styles.FilterDropdownCheckBoxLabel}>
            {option.text}
          </span>
          <div className={styles.FilterDropdownCheckBox}>
            <NativeCheckControl
              className={styles.FilterDropdownCheckControl}
              value={option.value}
              type="checkbox"
              checked={checked}
              onChange={() => onChange(option)}
            />
            <Icon type="checkmark" className={styles.FilterDropdownCheckMark} />
          </div>
        </div>
      )
    })}
  </div>
)

CheckDropdownItems.propTypes = {
  options: array.isRequired,
  value: string.isRequired,
  onChange: func.isRequired
}

export default CheckDropdownItems
