import React from 'react'
import { array, func, string } from 'prop-types'
import classNames from 'classnames'
import styles from '../FilterDropdown.module.scss'

import Icon from '@/component/Primitive/Icon'

const ButtonDropdownItems = ({ items, onChange, value }) => (
  <ul
    className={classNames(
      styles.FilterDropdownList,
      styles.FilterDropdownListOverlay
    )}
  >
    {items.map((item, i) => {
      const isSelected = item.value === value.value
      return (
        <li className={styles.itemReveal} key={`dropdown-item-${i}`}>
          <button
            className={classNames(
              styles.FilterDropdownListItem,
              isSelected && styles.selected
            )}
            onClick={() => onChange(item)}
          >
            {item.text}
            {isSelected && (
              <Icon
                type="checkmark"
                className={styles.FilterDropdownCheckMark}
              />
            )}
          </button>
        </li>
      )
    })}
  </ul>
)

ButtonDropdownItems.propTypes = {
  items: array.isRequired,
  onChange: func.isRequired,
  value: string.isRequired
}

export default ButtonDropdownItems
