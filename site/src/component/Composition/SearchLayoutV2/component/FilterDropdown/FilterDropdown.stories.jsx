import React from 'react'
import { storiesOf } from '@storybook/react'
import { action } from '@storybook/addon-actions'

import FilterDropdown from '.'

const stories = storiesOf('Composition/FilterDropdown', module)

stories.add(
  'Info',
  () => (
    <FilterDropdown
      active
      items={[
        { text: 'Option 1' },
        { text: 'Option 2' },
        { text: 'Option 3' },
        { text: 'Option 4' },
        { text: 'Option 5 with longer text' }
      ]}
      onChange={(item) => action(item)}
    />
  ),
  {
    info: {
      inline: true,
      text: ``
    }
  }
)

stories.add('Default state', () => (
  <FilterDropdown
    active
    items={[
      { text: 'Option 1' },
      { text: 'Option 2' },
      { text: 'Option 3' },
      { text: 'Option 4' },
      { text: 'Option 5 with longer text' }
    ]}
    onChange={(item) => action(item)}
  />
))

stories.add('with pre-selected option', () => (
  <div>
    <FilterDropdown
      items={[
        { text: 'Option 1', value: 'option-1' },
        { text: 'Option 2', value: 'option-2' },
        { text: 'Option 3', value: 'option-3' },
        { text: 'Option 4', value: 'option-4' },
        { text: 'Option 5', value: 'option-5' }
      ]}
      onChange={(item) => action(item)}
      value="option-4"
    />
  </div>
))

stories.add('As checkbox group', () => (
  <div>
    <FilterDropdown
      checkbox
      items={[
        { text: 'Option 1', value: 'option-1' },
        { text: 'Option 2', value: 'option-2' },
        { text: 'Option 3', value: 'option-3' },
        { text: 'Option 4', value: 'option-4' },
        { text: 'Option 5', value: 'option-5' }
      ]}
      onChange={(item) => action(item)}
      value="option-4"
    />
  </div>
))
