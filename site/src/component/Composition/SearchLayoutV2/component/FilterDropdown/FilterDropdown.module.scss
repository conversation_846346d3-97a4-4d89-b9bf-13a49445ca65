.FilterDropdown {
  position: relative;
  display: inline-block;
  font-size: 16px;
  width: 100%;
  // background-color: $color-black-primary;

  @include mq($breakpoint-desktop) {
    width: auto;
    font-size: 14px;
  }
}

.FilterDropdownList {
  list-style: none;
  width: 100%;
  @include mq($breakpoint-desktop) {
    max-width: 240px;
    width: auto;
  }
}

.FilterDropdownListLabel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 spacing(2.9) 0 spacing(2);
  min-height: spacing(5.5);
  font-family: inherit;
  line-height: inherit;

  &:hover,
  &:focus {
    .FilterDropdownListValueText {
      color: var(--color-theme--secondary);
    }
  }

  @include mq($breakpoint-desktop) {
    padding: 0;
  }
}

.FilterDropdownListItem {
  position: relative;
  padding: 0 spacing(2.5);
  min-height: spacing(5.5);
  font-family: inherit;
  line-height: inherit;
  color: $color-white-primary;
  text-align: left;
  width: 100%;

  @include ellipsis();

  display: flex;
  justify-content: space-between;
  align-items: center;

  @include mq($breakpoint-desktop) {
    min-height: spacing(5);
    padding: 0 spacing(1.5);
    border-bottom: 1px solid $color-misc-divider;
  }

  &:hover,
  &:active,
  &:focus {
    cursor: pointer;
    transition-duration: 0.1s;
    outline: 0;
    background-color: var(--color-theme--secondary);
  }

  &[disabled] {
    pointer-events: none;
  }

  &.selected {
    font-weight: 700;
    transform: translateY(-1px);

    .FilterDropdownCheckMark {
      display: block;
    }
  }

  li:last-child & {
    border-bottom: 0;
  }
}

.FilterDropdownListOverlay {
  z-index: 1;
  background-color: $color-black-secondary;
  box-shadow: 0 4px 28px rgba($color-misc-black, 0.5);

  .itemReveal {
    opacity: 0;
    animation: FilterDropdown-item-reveal 300ms 1 forwards $easing-bounce;
    &:first-child {
      opacity: 1;
    }

    @for $i from 1 through 20 {
      &:nth-child(#{$i}) {
        transform: translateY(#{($i - 1) * -5}px);
        animation-delay: #{($i - 1) * 30}ms;
      }
    }
  }

  @include mq($breakpoint-desktop) {
    position: absolute;
    top: 100%;
    left: 15px;
    min-width: 100%;
  }
}

.FilterDropdownListLabelText {
  @include mq($breakpoint-desktop) {
    display: inline-block;
    opacity: 0.7;
    margin-right: spacing(1);

    &::after {
      content: ':';
    }
  }
}

.FilterDropdownListValueText {
  display: none;
  @include mq($breakpoint-desktop) {
    display: inline-block;
  }
}

.Icon {
  margin-left: spacing(1);
  svg {
    fill: var(--color-theme--secondary);
  }

  @include mq($breakpoint-desktop) {
    margin-left: spacing(1);
    margin-bottom: spacing(0.5);
  }
}

.FilterDropdownCheckBoxWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.FilterDropdownCheckControl {
  position: absolute;
  opacity: 0;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.FilterDropdownCheckMark {
  display: none;
  margin-left: spacing(1.5);
}

.FilterDropdownCheckControl:checked + .FilterDropdownCheckMark {
  display: block;
}

@keyframes FilterDropdown-item-reveal {
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
