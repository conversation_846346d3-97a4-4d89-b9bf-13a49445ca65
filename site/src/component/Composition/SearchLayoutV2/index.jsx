import React, { useState } from 'react'
import {
  func,
  array,
  number,
  string,
  shape,
  arrayOf,
  bool,
  oneOfType,
  object
} from 'prop-types'
import InfiniteScroll from 'react-infinite-scroll-component'

import styles from './SearchLayout.module.scss'

import Container from '@/component/Primitive/Container'
import TextAlign from '@/component/Primitive/TextAlign'
import Spinner from '@/component/Primitive/Spinner'
import SmartLink from '@/component/Primitive/SmartLink'
import Breadcrumbs from '@/component/Primitive/Breadcrumbs'
import SearchField from './component/SearchField'
import SearchResult from './component/SearchResult'
import ButtonStandard from '@/component/Primitive/ButtonStandard'
import Type from '@/component/Primitive/Type'
import FilterDropdownComponent from './component/FilterDropdown'
import IconButton from '@/component/Primitive/IconButton'
import Icon from '@/component/Primitive/Icon'
import classNames from 'classnames'
import useFeatureFlagHelper from '@/lib/feature-flag-helper'

const formatToOption = (o) => ({ value: o, text: o })

// TopPickResult Component
const TopPickResult = ({ result }) => {
  if (!result) return null

  const determineImageUrl = (contentType, images) => {
    const portraitCrop = 'thumbnail_portrait_147'
    const typeToCrop = {
      Video: 'thumbnail_widescreen_307',
      Interview: portraitCrop,
      'Company Report': portraitCrop,
      Whitepaper: portraitCrop
    }
    const crop = typeToCrop[contentType] || 'thumbnail_landscape_307'
    return images?.[crop]?.[0]?.url
  }

  const imageUrl = determineImageUrl(result.contentType, result.images)
  const showPlayIcon = result.contentType === 'Video'
  const breadcrumbs = [{ name: result.contentType }]

  if (result.category) {
    breadcrumbs.push({ name: result.category })
  }

  return (
    <div className={styles.TopPickResult}>
      <div className={styles.TopPickBadge}>
        <Type size="body3" weight="bold">
          TOP PICK
        </Type>
      </div>
      <div className={styles.TopPickContent}>
        <div className={styles.TopPickText}>
          <Breadcrumbs
            highlight={breadcrumbs.length < 2 ? 'none' : 'last'}
            breadcrumbs={breadcrumbs}
          />
          <Type
            themed
            className={styles.TopPickTitle}
            size={['heading6', 'heading5']}
            weight="bold"
            as="h2"
          >
            <SmartLink
              to={result.eventBaseSlug ? '/eventArticle' : '/article'}
              as={result.fullUrlPath}
              className={styles.TopPickLink}
            >
              {result.headline}
            </SmartLink>
          </Type>
          {result.sell && (
            <div className={styles.TopPickDescription}>
              <Type size="body2" weight="regular">
                {result.sell}
              </Type>
            </div>
          )}
        </div>
        {imageUrl && (
          <div className={styles.TopPickImage}>
            {showPlayIcon && (
              <Icon
                width={60}
                height={60}
                className={styles.TopPickPlayIcon}
                a11ytext=""
                type="play"
              />
            )}
            <img loading="lazy" src={imageUrl} alt={result.headline} />
          </div>
        )}
      </div>
    </div>
  )
}

// Skeleton Loading Components
const SkeletonCompanyCard = () => (
  <div className={styles.SkeletonCompanyCard}>
    <div className={styles.SkeletonCompanyImage} />
    <div className={styles.SkeletonCompanyContent}>
      <div className={styles.SkeletonCompanyName} />
      <div className={styles.SkeletonCompanyDetails} />
    </div>
    {/* <div className={styles.SkeletonCompanyCTA} /> */}
  </div>
)

// const SkeletonExecutiveCard = () => (
//   <div className={styles.SkeletonExecutiveCard}>
//     <div className={styles.SkeletonExecutiveImage} />
//     <div className={styles.SkeletonExecutiveContent}>
//       <div className={styles.SkeletonExecutiveName} />
//       <div className={styles.SkeletonExecutiveTitle} />
//     </div>
//     <div className={styles.SkeletonExecutiveCTA} />
//   </div>
// )

// SearchTypeSelector Component
const SearchTypeSelector = ({ searchType, onSearchTypeChange }) => {
  const handleChange = (e) => {
    onSearchTypeChange(e.target.value)
  }

  return (
    <div className={styles.SearchTypeSelector}>
      <Type size="body2" className={styles.SearchTypeSelectorLabel}>
        Search Mode:
      </Type>
      <select
        className={styles.SearchTypeSelectorDropdown}
        value={searchType}
        onChange={handleChange}
        onBlur={handleChange}
        aria-label="Select search mode"
      >
        <option value="simple">Simple Search</option>
        <option value="advanced">Advanced Search</option>
      </select>
    </div>
  )
}

SearchTypeSelector.propTypes = {
  searchType: string,
  onSearchTypeChange: func
}

const SearchLayoutV2 = ({
  onSearch,
  onLoadMore,
  results,
  searchTerm,
  onCategoryChange,
  onSortChange,
  onContentTypeChange,
  onSearchTypeChange,
  total,
  loading,
  infiniteScrollEnabled,
  selectedFilters,
  filters,
  companiesResults,
  executivesResults,
  instance
}) => {
  const hasMore = results?.length < total
  const [filtersOpen, setFiltersOpen] = useState(false)
  const { isFeatureEnabled } = useFeatureFlagHelper(instance)

  const { sortOptions, contentTypeOptions, categoryOptions } = filters
  const { categories, contentTypes, sortBy, searchType } = selectedFilters

  const sortDropdownOptions = sortOptions.map(formatToOption)
  const contentTypeDropdownOptions = ['All', ...contentTypeOptions].map(
    formatToOption
  )
  const categoryDropdownOptions = ['All', ...categoryOptions].map(
    formatToOption
  )

  const pianoLogin = () => {
    window.tp.pianoId.show({
      disableSignUp: false,
      displayMode: 'modal',
      screen: 'login',
      loggedIn: function () {
        userIsLoggedIn()
      }
    })
  }
  const userIsLoggedIn = () => {
    window.location.reload()
  }
  return (
    <div className={styles.SearchLayout}>
      <Container center gutter size="large">
        <div className={styles.SearchV2}>
          <SearchField
            searchTerm={searchTerm}
            loading={loading}
            onSubmit={onSearch}
            isFeatureEnabled={isFeatureEnabled}
          />
        </div>
      </Container>

      {searchTerm && (
        <div className={styles.Filters}>
          <Container
            center
            gutter
            size="large"
            className={styles.FiltersContainer}
          >
            <div className={styles.FiltersTopRow}>
              <Type size="heading8" className={styles.FiltersTitle}>
                <strong>{total === 1 ? '1' : total}</strong>{' '}
                {total === 1 ? 'article found' : 'articles found'}
              </Type>
              <SearchTypeSelector
                searchType={searchType}
                onSearchTypeChange={onSearchTypeChange}
              />
            </div>

            <div className={styles.FiltersBottomRow}>
              <IconButton
                className={styles.ToggleFiltersButton}
                onClick={() => setFiltersOpen(!filtersOpen)}
                icon={filtersOpen ? 'close' : 'filter'}
                iconWidth={filtersOpen ? 24 : 18}
                iconHeight={filtersOpen ? 24 : 12}
                size="tiny"
                increaseHitArea
                a11ytext="Filters"
              />
              <div
                className={classNames(
                  styles.FiltersList,
                  filtersOpen && styles.filtersOpen
                )}
              >
                <Type className={styles.FiltersLabel} size="heading8">
                  Content Filters
                </Type>
                <FilterDropdown
                  label="Content Type"
                  items={contentTypeDropdownOptions}
                  value={
                    contentTypes.length
                      ? contentTypes.map(formatToOption)
                      : ['All'].map(formatToOption)
                  }
                  onChange={(value) => {
                    onContentTypeChange(value.value)
                  }}
                  checkbox
                />
                <FilterDropdown
                  label="Category"
                  items={categoryDropdownOptions}
                  value={
                    categories.length
                      ? categories.map(formatToOption)
                      : ['All'].map(formatToOption)
                  }
                  onChange={(value) => {
                    onCategoryChange(value.value)
                  }}
                  checkbox
                />
                {(!isFeatureEnabled('advancedSearch') ||
                  (isFeatureEnabled('advancedSearch') &&
                    searchType !== 'advanced')) && (
                  <FilterDropdown
                    label="Sort by"
                    items={sortDropdownOptions}
                    value={[sortBy].map(formatToOption)}
                    onChange={(value) => {
                      onSortChange(value.value)
                    }}
                  />
                )}
              </div>
            </div>
          </Container>
        </div>
      )}

      <Container
        className={styles.NoResultsContainer}
        center
        gutter
        size="large"
      >
        {searchTerm &&
          total > 0 &&
          results?.length > 0 &&
          isFeatureEnabled('advancedSearch') &&
          searchType === 'advanced' && <TopPickResult result={results[0]} />}

        {searchTerm && (
          <div className={styles.ResultsContainer}>
            <div className={styles.LeftColumn}>
              <Title title="Content" bordered="default" />
              {searchTerm && total === 0 ? (
                <Container
                  center
                  gutter
                  size="medium"
                  className={styles.NotFound}
                >
                  <TextAlign center>
                    No results found. Please try a different search term, or
                    explore content from our{' '}
                    <SmartLink to="/section" as="/">
                      homepage
                    </SmartLink>
                    .
                  </TextAlign>
                </Container>
              ) : (
                <InfiniteScroll
                  dataLength={results.length}
                  next={onLoadMore}
                  hasMore={hasMore && infiniteScrollEnabled && !loading}
                  scrollThreshold={0.8}
                  loader={
                    <div className={styles.Spinner}>
                      <Spinner />
                    </div>
                  }
                >
                  {(isFeatureEnabled('advancedSearch') &&
                  searchType === 'advanced'
                    ? results.slice(1)
                    : results
                  ).map((res, i) => (
                    <SearchResult
                      key={`SearchResult--Results--${res.id}--${i}`}
                      {...res}
                    />
                  ))}
                </InfiniteScroll>
              )}
            </div>
            {((isFeatureEnabled('advancedSearch') &&
              searchType === 'advanced' &&
              (companiesResults?.length > 0 ||
                executivesResults?.length > 0)) ||
              (searchType === 'simple' && companiesResults?.length > 0)) && (
              <div className={styles.RightColumn}>
                {/* Advanced Search Results */}
                {isFeatureEnabled('advancedSearch') &&
                  searchType === 'advanced' && (
                    <>
                      {companiesResults?.length > 0 && (
                        <div className={styles.Companies}>
                          <Title title="Companies" bordered="default" />
                          <div className={styles.CompanyResults}>
                            {companiesResults.map((company, key) => (
                              <CompanyCard key={key} company={company} />
                            ))}
                          </div>
                        </div>
                      )}
                      {executivesResults?.length > 0 && (
                        <div className={styles.Executives}>
                          <Title title="Executives" bordered="default" />
                          <div className={styles.ExecutiveResults}>
                            {executivesResults.map((executive, key) => (
                              <ExecutiveCard key={key} executive={executive} />
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  )}

                {/* Simple Search Results with Skeleton Loading */}
                {searchType === 'simple' && (
                  <div className={styles.Companies}>
                    <Title title="Companies" bordered="default" />
                    <div className={styles.CompanyResults}>
                      {/* Show the first real company result */}
                      {companiesResults?.length > 0 && (
                        <CompanyCard company={companiesResults[0]} />
                      )}

                      {/* Show skeleton loading results */}
                      <SkeletonCompanyCard />
                      <SkeletonCompanyCard />
                      <SkeletonCompanyCard />
                    </div>

                    {/* Login CTA Button */}
                    <div className={styles.LoginCTA}>
                      <ButtonStandard
                        className={styles.LoginCTAButton}
                        size="small"
                        onClick={pianoLogin}
                        primary
                      >
                        Login for free and search companies and executives
                      </ButtonStandard>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {searchTerm && hasMore && !infiniteScrollEnabled && (
          <div className={styles.LoadMore}>
            <ButtonStandard
              className={styles.LoadBtn}
              size="huge"
              onClick={onLoadMore}
            >
              <Type size="heading8" weight="bold">
                View More
              </Type>
            </ButtonStandard>
          </div>
        )}
      </Container>
    </div>
  )
}

const Title = ({ title }) => (
  <Type className={styles.Title} size="heading6" weight="medium">
    {title}
  </Type>
)

Title.propTypes = {
  title: string
}

const CompanyCard = ({ company }) => {
  const logoUrl =
    company?.images?.logo_free_127?.[0]?.url ||
    company?.images?.thumbnail_1x1_220?.[0]?.url

  return (
    <SmartLink
      className={styles.CompanyCardLink}
      to="/company"
      as={`/company/${company?.slug}`}
    >
      <div className={styles.CompanyCard}>
        <div className={styles.CompanyCardImage}>
          {logoUrl && <img src={logoUrl} alt="" />}
        </div>
        <div className={styles.CompanyCardContent}>
          <Type className={styles.CompanyName} size="body3" weight="medium">
            {company?.name}
          </Type>
        </div>
        <div className={styles.CompanyCardCTAContainer}>
          <Icon type="arrow-right" className={styles.CompanyCardCTA} />
        </div>
      </div>
    </SmartLink>
  )
}

const ExecutiveCard = ({ executive }) => {
  const headshotUrl = executive?.images?.headshot_220x347_220?.[0]?.url

  return (
    <SmartLink
      className={styles.ExecutiveCardLink}
      to="/executive"
      as={`/executive/${executive?.slug}`}
    >
      <div className={styles.ExecutiveCard}>
        <div className={styles.ExecutiveCardImage}>
          {headshotUrl && (
            <>
              <img
                src={headshotUrl}
                alt=""
                className={styles.ExecutiveCardImageBackground}
              />
              <img
                src={headshotUrl}
                alt=""
                className={styles.ExecutiveCardImageForeground}
              />
            </>
          )}
        </div>
        <div className={styles.ExecutiveCardContent}>
          <Type className={styles.ExecutiveName} size="body3" weight="medium">
            {executive?.name}
          </Type>
          <Type
            className={styles.ExecutiveJobTitle}
            size="body3"
            weight="normal"
          >
            {executive?.jobTitle}
          </Type>
        </div>
        <div className={styles.ExecutiveCardCTAContainer}>
          <Icon type="arrow-right" className={styles.ExecutiveCardCTA} />
        </div>
      </div>
    </SmartLink>
  )
}

TopPickResult.propTypes = {
  result: shape({
    _id: string,
    headline: string,
    sell: string,
    contentType: string,
    category: string,
    fullUrlPath: string,
    eventBaseSlug: string,
    images: object
  })
}

SearchLayoutV2.propTypes = {
  loading: bool,
  infiniteScrollEnabled: bool,
  onLoadMore: func,
  onSearch: func.isRequired,
  results: array.isRequired,
  searchTerm: string,
  total: number,
  onCategoryChange: func,
  onSortChange: func,
  onContentTypeChange: func,
  onSearchTypeChange: func,
  selectedFilters: shape({
    categories: array,
    contentTypes: array,
    sortBy: string,
    searchType: string
  }),
  filters: shape({
    sortOptions: array,
    contentTypeOptions: array,
    categoryOptions: array
  }),
  companiesResults: array,
  executivesResults: array,
  instance: object
}

CompanyCard.propTypes = {
  company: shape({
    _id: string,
    name: string,
    slug: string,
    images: shape({
      logo_free_127: array,
      thumbnail_1x1_220: array
    })
  })
}

ExecutiveCard.propTypes = {
  executive: shape({
    _id: string,
    name: string,
    slug: string,
    jobTitle: string,
    images: shape({
      headshot_220x347_220: array
    })
  })
}

export default SearchLayoutV2

// Filter wrapper
const FilterDropdown = ({ label, value, items, onChange, checkbox }) => {
  const valueLabel = Array.isArray(value)
    ? value.length > 1
      ? 'Multiple'
      : value[0].text
    : value.text
  return (
    <div className={styles.DropdownWrapper}>
      <FilterDropdownComponent
        label={label}
        className={styles.Dropdown}
        value={value}
        valueLabel={valueLabel}
        items={items}
        checkbox={checkbox}
        onChange={onChange}
      />
    </div>
  )
}

FilterDropdown.propTypes = {
  label: string,
  checkbox: bool,
  value: oneOfType([object, array]),
  items: arrayOf(
    shape({
      text: string,
      value: string
    })
  ),
  onChange: func
}
