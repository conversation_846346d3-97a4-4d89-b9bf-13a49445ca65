.SearchV2 {
  padding-top: spacing(2);
  padding-bottom: spacing(2);
  position: relative;

  @include mq($breakpoint-desktop) {
    margin-top: spacing(4);
    margin-bottom: spacing(4);
    padding-top: spacing(3);
    padding-bottom: spacing(3);
  }

  &::after {
    background-color: $color-misc-divider;
    content: '';
    display: block;
    height: 2px;
    left: spacing(-2);
    position: relative;
    top: spacing(2);
    width: calc(100% + 32px);

    @include mq($breakpoint-desktop) {
      left: 0;
      top: spacing(3);
      width: 100%;
    }
  }
}

.SearchField {
  position: relative;
}

.Filters {
  background-color: $color-white-secondary;
  // border: 1px solid $color-misc-divider;
  margin-bottom: spacing(3);
  padding: spacing(3);
  position: relative;
  z-index: 10;

  @include mq($breakpoint-desktop) {
    margin-bottom: spacing(4);
    padding: spacing(4);
  }
}

.FiltersContainer {
  display: flex;
  flex-direction: column;
  gap: spacing(2);

  @include mq($breakpoint-desktop) {
    gap: spacing(0);
  }
}

.FiltersTopRow {
  display: flex;
  flex-direction: column;
  gap: spacing(2);
  align-items: flex-start;

  @include mq($breakpoint-desktop) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: spacing(3);
  }
}

.FiltersTitle {
  color: $color-black-primary;
  font-weight: 600;
  margin: 0;
}

.FiltersBottomRow {
  display: flex;
  flex-direction: column;
  gap: spacing(2);

  @include mq($breakpoint-desktop) {
    flex-direction: row;
    align-items: center;
    gap: spacing(3);
  }

  .FiltersLabel {
    border-bottom: 1px solid var(--color-theme--secondary);
    margin-left: spacing(2);
    margin-bottom: spacing(1);
    color: $color-black-primary;
    font-weight: 500;

    @include mq($breakpoint-desktop) {
      margin-right: spacing(2.25);
      margin-bottom: 0;
      margin-left: 0;
    }
  }
}

// SearchTypeSelector Styles
.SearchTypeSelector {
  display: flex;
  flex-direction: column;
  gap: spacing(1);
  align-items: flex-start;

  @include mq($breakpoint-desktop) {
    flex-direction: row;
    align-items: center;
    gap: spacing(1.5);
  }
}

.SearchTypeSelectorLabel {
  color: $color-grey-primary;
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
}

.SearchTypeSelectorDropdown {
  background-color: $color-white-primary;
  border: 2px solid var(--color-theme--secondary);
  color: $color-black-primary;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  padding: spacing(1) spacing(2);
  min-width: 160px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--color-theme--primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:focus {
    outline: none;
    border-color: var(--color-theme--primary);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
  }

  option {
    padding: spacing(1);
    background-color: $color-white-primary;
    color: $color-black-primary;
  }
}

.ToggleFiltersButton:is(button) {
  display: block;
  background-color: var(--color-theme--secondary);
  color: $color-white-primary;
  border: none;
  // border-radius: 6px;
  padding: spacing(1.5);

  &:hover {
    background-color: var(--color-theme--primary);
  }

  @include mq($breakpoint-desktop) {
    display: none;
  }
}

.DropdownWrapper {
  align-items: center;
  display: flex;
  width: 100%;

  @include mq($breakpoint-desktop) {
    width: auto;
    flex-shrink: 0;

    &:not(:last-of-type) {
      margin-right: spacing(2.5);
    }
  }
}

.FiltersList {
  align-items: flex-start;
  background-color: $color-white-primary;
  border: 1px solid $color-misc-divider;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba($color-black-primary, 0.1);
  display: none;
  flex-direction: column;
  gap: spacing(2);
  left: 0;
  margin-top: spacing(2);
  padding: spacing(3);
  position: absolute;
  top: 100%;
  width: 100%;
  z-index: 2;

  @include mq($breakpoint-desktop) {
    align-items: center;
    background-color: transparent;
    border: none;
    box-shadow: none;
    display: flex !important;
    flex-direction: row;
    gap: spacing(3);
    margin-top: 0;
    padding: 0;
    position: relative;
    top: auto;
    width: auto;
  }

  &.filtersOpen {
    display: flex;
  }
}

.Title {
  border-bottom: 2px solid $color-misc-divider;
  padding-bottom: spacing(2);
}

.ResultsContainer {
  display: flex;
  flex-direction: column;
  gap: spacing(2);

  .LeftColumn {
    order: 2;
    display: flex;
    flex-direction: column;
    gap: spacing(2);
    flex-basis: 100%;
  }

  .RightColumn {
    order: 1;
    gap: spacing(2);
    display: flex;
    flex-direction: column;
    flex-basis: 300px;
    flex-grow: 1;
    flex-shrink: 0;
    // border-bottom: 2px solid $color-misc-divider;
  }

  .CompanyResults,
  .ExecutiveResults {
    padding-bottom: spacing(2);
    padding-top: spacing(2);
    gap: spacing(2);
    display: flex;
    flex-direction: column;
  }

  @include mq($breakpoint-desktop) {
    flex-direction: row;
    gap: spacing(4);

    .LeftColumn {
      order: 1;
    }

    .RightColumn {
      order: 2;
    }
  }
}

.CompanyCardLink {
  color: inherit;
  text-decoration: none;

  &:hover {
    .CompanyName {
      color: var(--color-theme--secondary);
      text-decoration: underline;
      text-decoration-color: var(--color-theme--secondary);
    }

    .CompanyCardCTA {
      color: var(--color-theme--secondary);
    }
  }
}

.ExecutiveCardLink {
  color: inherit;
  text-decoration: none;

  &:hover {
    .ExecutiveName {
      color: var(--color-theme--secondary);
      text-decoration: underline;
      text-decoration-color: var(--color-theme--secondary);
    }

    .ExecutiveCardCTA {
      color: var(--color-theme--secondary);
    }
  }
}

.CompanyCard {
  display: flex;
  flex-direction: row;
  gap: spacing(2);

  .CompanyCardImage {
    width: 65px;
    height: 65px;
    background-color: $color-white-secondary;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid $color-misc-divider;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      background-color: grey;
    }
  }

  .CompanyCardContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .CompanyCardCTAContainer {
    width: 15px;
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .CompanyCardCTA {
      height: 100%;
    }
  }
}

.ExecutiveCard {
  display: flex;
  flex-direction: row;
  gap: spacing(2);

  .ExecutiveCardImage {
    width: 65px;
    height: 65px;
    background-color: $color-white-secondary;
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .ExecutiveCardImageBackground {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      filter: blur(8px);
      transform: scale(1.1);
    }

    .ExecutiveCardImageForeground {
      position: relative;
      z-index: 1;
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }

  .ExecutiveCardContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .ExecutiveJobTitle {
      margin-top: spacing(0.5);
      color: $color-grey-primary;
    }
  }

  .ExecutiveCardCTAContainer {
    width: 15px;
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .ExecutiveCardCTA {
      height: 100%;
    }
  }
}

.LoadMore {
  margin-top: spacing(2);
  margin-bottom: spacing(2);
  text-align: center;
  @include mq($breakpoint-tablet) {
    margin-top: spacing(5);
    margin-bottom: spacing(5);
  }
}

button.LoadBtn {
  border: 2px solid var(--color-theme--secondary);
  color: var(--color-theme--secondary);
  display: block;
  width: 100%;

  @include mq($breakpoint-tablet) {
    display: inline-block;
    width: auto;
  }
}

.Spinner {
  margin: spacing(2) auto;
  text-align: center;
}

.NoResultsContainer {
  margin-top: spacing(2);
  margin-bottom: spacing(2);

  @include mq($breakpoint-tablet) {
    margin-top: spacing(5);
    margin-bottom: spacing(5);
  }
}

/* TopPickResult Styles */
.TopPickResult {
  background: linear-gradient(
    135deg,
    var(--color-theme--secondary) 0%,
    var(--color-theme--secondary) 100%
  );
  // border-radius: 12px;
  padding: spacing(3);
  margin-bottom: spacing(3);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
    pointer-events: none;
  }

  @include mq($breakpoint-tablet) {
    margin-bottom: spacing(6);
  }
}

.TopPickBadge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  // border-radius: 20px;
  padding: spacing(0.5) spacing(1.5);
  margin-bottom: spacing(2);

  p {
    color: $color-white-primary;
    letter-spacing: 0.5px;
  }
}

.TopPickContent {
  display: flex;
  flex-direction: column;
  gap: spacing(2);

  @include mq($breakpoint-tablet) {
    flex-direction: row;
    align-items: center;
    gap: spacing(3);
  }
}

.TopPickText {
  flex: 1;
  color: $color-white-primary;

  & > * + * {
    margin-top: spacing(1.5);
  }
}

.TopPickTitle {
  color: $color-white-primary;

  &:hover {
    color: rgba(255, 255, 255, 0.9);
  }
}

.TopPickDescription {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.TopPickImage {
  position: relative;
  flex-shrink: 0;
  // border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  max-width: 280px;

  img {
    width: 100%;
    height: auto;
    display: block;
  }

  @include mq($breakpoint-tablet) {
    max-width: 320px;
  }
}

.TopPickPlayIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: $color-white-primary;
  backdrop-filter: blur(20px);
  // border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  padding: spacing(1);
}

.TopPickLink {
  &:link,
  &:visited {
    color: inherit;
    text-decoration: none;
  }

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: 5;
  }

  &:hover,
  &:focus {
    .TopPickTitle {
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

/* Skeleton Loading Styles */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.SkeletonCompanyCard,
.SkeletonExecutiveCard {
  display: flex;
  align-items: center;
  border: 1px solid $color-misc-divider;
  background: $color-white-primary;
  opacity: 0.7;
}

.SkeletonCompanyImage,
.SkeletonExecutiveImage {
  width: 65px;
  height: 65px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  margin-right: spacing(1.5);
}

.SkeletonCompanyContent,
.SkeletonExecutiveContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: spacing(1);
}

.SkeletonCompanyName,
.SkeletonExecutiveName {
  height: 16px;
  width: 120px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.SkeletonCompanyDetails,
.SkeletonExecutiveTitle {
  height: 12px;
  width: 80px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.SkeletonCompanyCTA,
.SkeletonExecutiveCTA {
  width: 60px;
  height: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Login CTA Styles */
.LoginCTA {
  text-align: center;
}

.LoginCTAButton {
  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}
