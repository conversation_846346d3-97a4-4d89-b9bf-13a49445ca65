import React from 'react'
import {
  arrayOf,
  bool,
  number,
  oneOfType,
  shape,
  string,
  object
} from 'prop-types'
import { NextSeo } from 'next-seo'
import createPlayerUrl from '../../../../../components/worker/tasks/generate-sitemap/lib/player-url-creator'

const ArticleMeta = ({
  authorName,
  authorSlug,
  customTitle,
  description,
  expirationTime,
  images,
  modifiedTime,
  publishedTime,
  publisherLogo,
  publisherName,
  section,
  canonicalUrl,
  tags,
  title,
  seoTitle,
  seoDescription,
  subdomain,
  companies,
  executives,
  partners,
  contentType,
  video
}) => {
  let cleanedUrl
  if (canonicalUrl) {
    const urlObj = new URL(canonicalUrl)
    urlObj.search = ''
    cleanedUrl = urlObj.toString()
  }

  const config = {
    title,
    titleTemplate: customTitle && '%s',
    description,
    canonical: cleanedUrl,
    openGraph: {
      title: seoTitle || title,
      description: seoDescription || description,
      url: cleanedUrl || canonicalUrl,
      images,
      type: 'article',
      article: {
        publishedTime,
        modifiedTime,
        expirationTime,
        section,
        authors: [authorName],
        tags
      }
    }
  }

  // Structured Data Object for Schema.org Schema
  const structuredDataObject = {
    '@context': 'https://schema.org',
    '@type': 'NewsArticle',
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': cleanedUrl
    },
    url: cleanedUrl,
    articleSection: '',
    headline: title,
    description: description,
    image: images,
    datePublished: publishedTime,
    dateModified: modifiedTime,
    keywords: '', // TODO: add tags
    publisher: {
      '@type': 'Organization',
      name: 'Bizclik Media Ltd'
    },
    tags: tags
  }

  // TODO: Author details

  const authorUrl = authorSlug
    ? `https://${subdomain}/author/${authorSlug}`
    : ''
  structuredDataObject.author = [
    {
      '@type': 'Person',
      name: authorName,
      url: authorUrl // TODO: URL if Author Slug exists
    }
  ]

  // TODO: Implement Video
  // Need a conditional to check for YouTube/Vimeo
  // if (contentType === 'Video') {
  //   structuredDataObject.video = {
  //     '@type': 'VideoObject',
  //     name: title,
  //     contentUrl: `https://www.youtube.com/watch?v=${video.videoId}` // TODO: conditional for Vimeo
  //   }
  // }

  // For interviews, company reports... Add details of linked Companies, Partners, Executives
  structuredDataObject.about = []

  if (companies.length > 0) {
    companies.forEach((company) => {
      const companyUrl = company.slug
        ? `https://${subdomain}/company/${company.slug}`
        : ''
      structuredDataObject.about.push({
        '@type': 'Organization',
        name: company.name,
        url: companyUrl
      })
    })
  } else {
    // console.log(`Catfish Schema: No companies`)
  }

  if (executives.length > 0) {
    executives.forEach((executive) => {
      const executiveUrl = executive.slug
        ? `https://${subdomain}/executive/${executive.slug}`
        : ''
      structuredDataObject.about.push({
        '@type': 'Person',
        name: executive.name,
        url: executiveUrl
      })
    })
  } else {
    // console.log(`Catfish Schema: No executives`)
  }

  structuredDataObject.partner = []
  if (partners.length > 0) {
    partners.forEach((partner) => {
      const partnerUrl = partner.slug
        ? `https://${subdomain}/companyr/${partner.slug}`
        : ''
      structuredDataObject.about.push({
        '@type': 'Organization',
        name: partner.name,
        url: partnerUrl
      })
      structuredDataObject.partner.push({
        '@type': 'Organization',
        name: partner.name,
        url: partnerUrl
      })
    })
  } else {
    // console.log(`Catfish Schema: No partners/sponsors`)
  }

  const VideoSchemaScript = () => {
    const { videoProvider, videoId } = video
    if (!videoProvider || !videoId) {
      return <></>
    }

    const thumbnailUrl =
      images?.[0]?.url ||
      (videoProvider === 'youtube' &&
        `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`)

    const schema = {
      '@context': 'https://schema.org',
      '@type': 'VideoObject',
      name: seoTitle || title,
      description: seoDescription || description || seoTitle || title,
      thumbnailUrl,
      uploadDate: publishedTime,
      embedUrl: createPlayerUrl(videoProvider, videoId)
    }

    return (
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(schema)
        }}
      />
    )
  }

  const structuredDataString = JSON.stringify(structuredDataObject)
  return (
    <>
      <NextSeo {...config} />

      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: structuredDataString
        }}
      />

      <VideoSchemaScript />
    </>
  )
}

ArticleMeta.propTypes = {
  authorName: string,
  authorSlug: string,
  customTitle: bool,
  description: string.isRequired,
  expirationTime: string,
  images: arrayOf(
    shape({
      url: string,
      width: oneOfType([string, number]),
      height: oneOfType([string, number]),
      alt: string
    })
  ),
  modifiedTime: string,
  publishedTime: string,
  publisherLogo: string,
  publisherName: string,
  section: string,
  canonicalUrl: string.isRequired,
  tags: arrayOf(string),
  companies: arrayOf(object),
  executives: arrayOf(object),
  partners: arrayOf(object),
  title: string.isRequired,
  seoTitle: string,
  seoDescription: string,
  subdomain: string,
  contentType: string,
  video: object
}

export default ArticleMeta
