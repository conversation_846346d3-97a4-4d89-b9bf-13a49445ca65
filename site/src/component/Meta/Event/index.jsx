import React from 'react'
import {
  arrayOf,
  bool,
  number,
  object,
  oneOfType,
  shape,
  string
} from 'prop-types'
import { NextSeo } from 'next-seo'
import Head from 'next/head'

const EventMeta = ({
  title,
  description,
  seoTitle,
  seoDescription,
  canonicalUrl,
  customTitle,
  images,
  video,
  event,
  instance,
  url
}) => {
  const isEventHomepage =
    new URL(url).pathname === new URL(event._fullUrl).pathname

  let cleanedUrl = ''
  if (canonicalUrl) {
    const urlObj = new URL(canonicalUrl)
    urlObj.search = ''
    cleanedUrl = urlObj.toString()
  }
  const structuredDataObject = {
    '@context': 'https://schema.org',
    '@type': 'Event',
    name: event?.title || title,
    ...(event?.startDate && { startDate: event.startDate }),
    ...(event?.endDate && { endDate: event.endDate }),
    eventStatus: 'https://schema.org/EventScheduled',
    ...(event?.locationName && {
      location: {
        '@type': 'Place',
        name: event.locationName,
        ...(Object.keys(event).some((key) =>
          key.startsWith('locationAddress')
        ) && {
          address: {
            '@type': 'PostalAddress',
            ...(event?.locationStreetAddress && {
              streetAddress: event.locationStreetAddress
            }),
            ...(event?.locationAddressLocality && {
              addressLocality: event.locationAddressLocality
            }),
            ...(event?.locationPostalCode && {
              postalCode: event.locationPostalCode
            }),
            ...(event?.locationAddressRegion && {
              addressRegion: event.locationAddressRegion
            }),
            ...(event?.locationAddressCountry && {
              addressCountry: event.locationAddressCountry
            })
          }
        })
      }
    }),
    image: event?.images
      ? Object.keys(event.images)?.map((image) => {
          return event.images[image]?.[0]?.url
        })
      : null,
    description: description,
    ...(event?.offersUrl && {
      offers: {
        '@type': 'Offer',
        url: event?.offersUrl
      }
    }),
    organizer: {
      '@type': 'Organization',
      name: instance?.name,
      url: event?._fullUrl
    }
  }

  const config = {
    title,
    titleTemplate: customTitle && '%s',
    description,
    canonical: cleanedUrl || canonicalUrl,
    openGraph: {
      title: seoTitle || title,
      description: seoDescription || description,
      url: cleanedUrl,
      images,
      type: 'video.other',
      ...(video?.youtubeId && {
        video: {
          url: `https://www.youtube.com/watch?v=${video.youtubeId}`,
          secureUrl: `https://www.youtube.com/watch?v=${video.youtubeId}`,
          type: 'text/html',
          width: 1280,
          height: 720
        }
      })
    },
    twitter: {
      card: 'player',
      ...(video?.youtubeId && {
        player: `https://www.youtube.com/embed/${video.youtubeId}`,
        playerWidth: 1280,
        playerHeight: 720
      })
    }
  }

  return (
    <>
      <NextSeo {...config} />
      {isEventHomepage && structuredDataObject && (
        <Head>
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(structuredDataObject)
            }}
          />
        </Head>
      )}
    </>
  )
}

EventMeta.propTypes = {
  title: string.isRequired,
  description: string,
  seoTitle: string,
  seoDescription: string,
  canonicalUrl: string.isRequired,
  customTitle: bool,
  images: arrayOf(
    shape({
      url: string.isRequired,
      width: oneOfType([string, number]),
      height: oneOfType([string, number]),
      alt: string
    })
  ),
  video: shape({
    name: string,
    description: string,
    youtubeId: string,
    createdDate: string,
    uploadDate: string,
    duration: string
  }),
  eventName: string,
  eventUrl: string,
  publishedDate: string,
  duration: string,
  event: object,
  instance: object,
  url: string
}

export default EventMeta
