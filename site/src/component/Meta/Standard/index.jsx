import React from 'react'
import { arrayOf, bool, number, oneOfType, shape, string } from 'prop-types'
import { NextSeo } from 'next-seo'

const StandardMeta = ({
  title,
  description,
  seoTitle,
  seoDescription,
  canonicalUrl,
  customTitle,
  images
}) => {
  let cleanedUrl = ''
  if (canonicalUrl) {
    const urlObj = new URL(canonicalUrl)
    urlObj.search = ''
    cleanedUrl = urlObj.toString()
  }

  const config = {
    title,
    titleTemplate: customTitle && '%s',
    description,
    canonical: cleanedUrl || canonicalUrl,
    openGraph: {
      title: seoTitle || title,
      description: seoDescription || description,
      url: cleanedUrl,
      images
    }
  }

  if (cleanedUrl.includes('/thank-you')) {
    config.noindex = true
    config.nofollow = true
  }

  return <NextSeo {...config} />
}

StandardMeta.propTypes = {
  title: string.isRequired,
  description: string,
  seoTitle: string,
  seoDescription: string,
  canonicalUrl: string.isRequired,
  customTitle: bool,
  images: arrayOf(
    shape({
      url: string.isRequired,
      width: oneOfType([string, number]),
      height: oneOfType([string, number]),
      alt: string
    })
  )
}

export default StandardMeta
