import React from 'react'
import { object } from 'prop-types'
import minifyCssString from '@/lib/css-string-minifier'

const RootColorVariables = ({ colors }) => {
  return (
    <style
      dangerouslySetInnerHTML={{
        __html: minifyCssString(`
          :root{
            --color-theme--secondary: ${colors.theme};
            --color-theme--primary: ${colors.primary};
            --color-theme--secondary: ${colors.secondary};
            --color-theme--event: ${colors?.event || colors.secondary};
            --color-theme--event-button-foreground: ${
              colors?.eventButtonForegroundColor || colors?.buttonForeground
            };
            --color-theme-anchor: ${colors?.anchor || 'currentColor'};
            --color-theme-button-foreground: ${
              colors?.buttonForeground || '#000'
            };
            --color-theme-button-background: ${
              colors?.buttonBackground || colors.secondary
            };
          }
        `)
      }}
    />
  )
}

RootColorVariables.propTypes = {
  colors: object.isRequired
}

export default RootColorVariables
