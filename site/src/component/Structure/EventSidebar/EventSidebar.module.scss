.Sidebar {
  font-size: 16px;
  padding: 52px 0 20px 20px;

  ul {
    list-style-type: none;
  }

  a {
    color: $color-black-primary;
    text-decoration: none;
  }
}

.SidebarDark {
  background-color: var(--color-theme--event);
  position: relative;

  &::before {
    background-color: rgba(0, 0, 0, 0.8);
    content: '';
    inset: 0 -2px 0 0;
    position: absolute;
  }
}

.HasOverride {
  background-color: var(--sidebar-color-override);

  &::before {
    all: unset !important;
  }
}

@media (max-width: #{$breakpoint-desktopNav - 1}) {
  .Sidebar {
    display: none;
  }
}

.SidebarInner {
  /* stylelint-disable */
  max-height: calc(100dvh - 105px);
  /* stylelint-enable */
  overflow-y: auto;
  padding-right: 20px;
  position: sticky;
  scrollbar-color: var(--color-theme--event) transparent;
  scrollbar-width: thin;
  top: 82px;
  z-index: 2;
}

.EventNavigationList {
  margin-top: 36px;
}

.List {
  display: grid;
  gap: 12px;
}

.CollapsibleNavItem {
  position: relative;

  &.active {
    .ToggleIcon {
      transform: rotate(180deg);
    }
  }
}

.EventNavigationItemLink {
  align-items: center;
  display: flex;
}

.MainNavItem {
  position: relative;
}

.hasSubItems {
  align-items: center;
  cursor: pointer;
  display: flex;
  justify-content: space-between;

  &:hover {
    .ToggleIcon {
      opacity: 0.7;
    }
  }
}

.NavItemText {
  flex: 1;
  font-weight: 500;
}

.ToggleIcon {
  margin-left: 8px;
  transition: transform 0.3s ease;
  translate: 0 -0.05em;

  &.rotated {
    transform: rotate(180deg);
  }
}

.SubMenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.open {
    max-height: 500px; // Adjust based on your needs
  }
}

.SubMenuContent {
  list-style: none;
  padding: 12px 0 0 20px;
}

.EventNavigationItemLink,
.SubMenuItem {
  text-decoration: none;
  transition: background-color 0.2s ease;

  .SidebarDark & {
    color: $color-white-primary;
  }

  &:active,
  &:hover,
  &.currentPage {
    color: var(--color-theme--event);
    font-weight: 700;
  }
}

:is(a).CtaButton {
  background-color: var(--color-theme--event);
  color: var(--color-theme--event-button-foreground) !important;
  display: flex;
  font-size: 16px;
  justify-content: center;
  margin-top: 20px;
  width: 100%;

  &:first-of-type {
    margin-top: 36px;
  }
}

:is(a).awards {
  background-color: #ffba2f !important;
  color: $color-black-primary !important;
}

:is(a).default {
  background-color: $color-black-primary !important;

  .SidebarDark & {
    background-color: $color-white-primary !important;
    color: $color-black-primary !important;
  }
}

.Share {
  color: $color-black-primary;
  display: flex;
  flex-wrap: wrap;
  gap: 8px 24px;
  margin-top: 32px;

  svg {
    &,
    path {
      fill: currentColor;
    }
  }

  button {
    transition: 250ms ease-in-out color;

    &:hover {
      color: var(--color-theme--event) !important;
    }
  }

  dt {
    flex: 100%;
    font-size: 12px;
    line-height: 1;
  }

  .SidebarDark & {
    color: $color-white-primary;
  }
}
