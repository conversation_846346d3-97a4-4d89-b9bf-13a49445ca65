import moment from 'moment/moment'
import React, { useState, useEffect } from 'react'
import SmartLink from '@/component/Primitive/SmartLink'
import Type from '@/component/Primitive/Type'
import cx from 'classnames'
import Icon from '@/component/Primitive/Icon'
import propTypes from 'prop-types'
import styles from './EventSidebar.module.scss'
import ButtonStandard from '@/component/Primitive/ButtonStandard'
import {
  EmailShareButton,
  FacebookShareButton,
  LinkedinShareButton,
  TwitterShareButton
} from 'react-share'

const LINK_TYPES = {
  LABEL: 'label',
  EVENT_SECTION: 'eventSection',
  EVENT_UMBRELLA_SECTION: 'eventUmbrellaSection',
  EXTERNAL_LINK: 'externalLink'
}

const buildUrl = (link, baseUrl) => {
  const { type, url } = link
  let builtUrl

  switch (type) {
    case LINK_TYPES.LABEL:
      builtUrl = null
      break
    case LINK_TYPES.EVENT_SECTION:
    case LINK_TYPES.EVENT_UMBRELLA_SECTION:
      builtUrl = url ? `${baseUrl}${url}` : baseUrl
      break
    case LINK_TYPES.EXTERNAL_LINK:
      builtUrl = url
      break
    default:
      builtUrl = null
      break
  }

  return builtUrl?.replace(/\/$/, '') || builtUrl
}

const buildCtaUrl = (cta, baseUrl) => {
  if (cta.eventLayoutSlug) {
    return `${baseUrl}/${cta.eventLayoutSlug}`
  }
  return cta.link || ''
}

const buildLogoUrl = (logoAction, proxyEventHomeUrl, baseUrl) => {
  if (logoAction?.eventLayoutSlug) {
    return `${baseUrl}/${logoAction.eventLayoutSlug}`
  }
  if (logoAction?.link) {
    return logoAction.link
  }
  return proxyEventHomeUrl || baseUrl
}

const getExternalLinkProps = (item) => {
  if (item.type === LINK_TYPES.EXTERNAL_LINK) {
    return {
      href: item.url,
      target: '_blank',
      rel: 'noopener noreferrer'
    }
  }
  return {}
}

const CollapsibleNavigationItem = ({
  item,
  index,
  baseUrl,
  activeMenuIndex,
  setActiveMenuIndex,
  currentUrl
}) => {
  const hasSubItems = item.subItems?.length > 0
  const isActive = activeMenuIndex === index
  const itemUrl = buildUrl(item, baseUrl)
  const isCurrentPage = currentUrl === itemUrl

  const hasActiveSubItem =
    hasSubItems &&
    item.subItems.some((subItem) => currentUrl === buildUrl(subItem, baseUrl))

  useEffect(() => {
    if ((isCurrentPage || hasActiveSubItem) && hasSubItems) {
      setActiveMenuIndex(index)
    }
  }, [
    currentUrl,
    isCurrentPage,
    hasActiveSubItem,
    hasSubItems,
    index,
    setActiveMenuIndex
  ])

  const handleToggle = () => {
    if (hasSubItems) {
      setActiveMenuIndex(isActive ? null : index)
    }
  }

  const handleMainLinkClick = (e) => {
    if (hasSubItems) {
      e.preventDefault()
      handleToggle()
    }
  }

  const renderSubMenu = () => {
    if (!hasSubItems) return null

    return (
      <div className={cx(styles.SubMenu, isActive && styles.open)}>
        <ul className={cx(styles.SubMenuContent, styles.List)}>
          {item.subItems.map((subItem, subIndex) => {
            const subItemUrl = buildUrl(subItem, baseUrl)
            const isSubItemActive = currentUrl === subItemUrl

            return (
              <li key={`sub-${index}-${subIndex}`}>
                <SmartLink
                  to={subItem.type.includes('event') ? '/events' : ''}
                  as={subItemUrl}
                  className={cx(
                    styles.SubMenuItem,
                    isSubItemActive && styles.currentPage
                  )}
                  {...getExternalLinkProps(subItem)}
                >
                  {subItem.title}
                </SmartLink>
              </li>
            )
          })}
        </ul>
      </div>
    )
  }

  return (
    <li className={cx(styles.CollapsibleNavItem, isActive && styles.active)}>
      <div className={styles.MainNavItem}>
        <SmartLink
          to={item.type.includes('event') ? '/events' : ''}
          as={itemUrl}
          className={cx(
            styles.EventNavigationItemLink,
            isCurrentPage && styles.currentPage,
            hasSubItems && styles.hasSubItems
          )}
          onClick={handleMainLinkClick}
          {...getExternalLinkProps(item)}
        >
          <span className={styles.NavItemText}>{item.title}</span>
          {hasSubItems && (
            <Icon
              type="arrow-down"
              className={cx(styles.ToggleIcon, isActive && styles.rotated)}
              width={12}
              height={7}
            />
          )}
        </SmartLink>
      </div>
      {renderSubMenu()}
    </li>
  )
}

CollapsibleNavigationItem.propTypes = {
  item: propTypes.shape({
    title: propTypes.string,
    type: propTypes.string,
    url: propTypes.string,
    subItems: propTypes.array
  }),
  index: propTypes.number,
  baseUrl: propTypes.string,
  activeMenuIndex: propTypes.number,
  setActiveMenuIndex: propTypes.func,
  currentUrl: propTypes.string
}

const NavigationLogo = ({
  logoUrl,
  logoAction,
  light,
  dark,
  useNavigationDarkTheme,
  isEventPage
}) => {
  return (
    <SmartLink
      as={logoUrl}
      to="/events"
      {...(logoAction?.openInNewTab && {
        target: '_blank',
        rel: 'noopener noreferrer'
      })}
    >
      <img alt="" src={useNavigationDarkTheme ? dark : light} />
    </SmartLink>
  )
}

NavigationLogo.propTypes = {
  logoUrl: propTypes.string,
  logoAction: propTypes.shape({
    eventLayoutSlug: propTypes.string,
    link: propTypes.string,
    openInNewTab: propTypes.bool
  }),
  light: propTypes.string,
  dark: propTypes.string,
  useNavigationDarkTheme: propTypes.bool,
  isEventPage: propTypes.bool
}

const BackLink = ({ eventUmbrellaUrl }) => (
  <SmartLink as={eventUmbrellaUrl} to="/events">
    <Type as="p" weight={['bold', 'medium']} size={['body4', 'body3']}>
      <Icon
        width={9}
        height={12}
        type="arrow-left"
        a11ytext="Back to event Portfolio"
      />
      Back to Portfolio
    </Type>
  </SmartLink>
)

BackLink.propTypes = {
  eventUmbrellaUrl: propTypes.string
}

const Share = () => {
  const url = typeof window !== 'undefined' ? window.location.href : ''

  return (
    <dl className={cx(styles.Share)}>
      <dt>Share:</dt>

      <dd>
        <LinkedinShareButton className={cx(styles.ShareItem)} url={url}>
          <Icon width="15" type="linkedin" />
        </LinkedinShareButton>
      </dd>

      <dd>
        <TwitterShareButton className={cx(styles.ShareItem)} url={url}>
          <Icon width="15" type="twitter" />
        </TwitterShareButton>
      </dd>

      <dd>
        <FacebookShareButton className={cx(styles.ShareItem)} url={url}>
          <Icon width="18" type="facebook" />
        </FacebookShareButton>
      </dd>

      <dd>
        <EmailShareButton
          subject="I wanted you to see this event"
          body="Check out this event"
          className={cx(styles.ShareItem)}
          url={url}
        >
          <Icon width="18" type="email" />
        </EmailShareButton>
      </dd>
    </dl>
  )
}

const EventNavigation = ({
  baseUrl,
  baseUrls,
  eventNavItems,
  logos,
  isEventPage,
  showLogoInNavigation,
  proxyEventHomeUrl,
  logoAction,
  navigationCtas,
  useNavigationDarkTheme
}) => {
  const [activeMenuIndex, setActiveMenuIndex] = useState(0)
  const [currentUrl, setCurrentUrl] = useState('')

  useEffect(() => {
    setCurrentUrl(window.location.pathname)

    for (const index in eventNavItems) {
      if (eventNavItems[index].subItems.length) {
        setActiveMenuIndex(parseInt(index))
        break
      }
    }
  }, [eventNavItems])

  const logoUrl = buildLogoUrl(logoAction, proxyEventHomeUrl, baseUrl)

  const renderNavigation = () => {
    return (
      <NavigationLogo
        logoUrl={logoUrl}
        logoAction={logoAction}
        light={logos.light}
        isEventPage={isEventPage}
        dark={logos.dark}
        useNavigationDarkTheme={useNavigationDarkTheme}
      />
    )
  }

  return (
    <>
      {renderNavigation()}
      <nav
        className={cx(styles.EventNavigationList, isEventPage && styles.event)}
      >
        {Array.isArray(eventNavItems) && (
          <ul className={styles.List}>
            {eventNavItems.map((item, index) => {
              return (
                <CollapsibleNavigationItem
                  key={`event-${index}`}
                  item={item}
                  index={index}
                  baseUrl={baseUrl}
                  activeMenuIndex={activeMenuIndex}
                  setActiveMenuIndex={setActiveMenuIndex}
                  currentUrl={currentUrl}
                />
              )
            })}
          </ul>
        )}
        {Array.isArray(navigationCtas) &&
          navigationCtas.map((button, index) => {
            return (
              <ButtonStandard
                inverse
                size="small"
                icon="arrow-right"
                key={index}
                to={button.eventLayoutSlug ? '/events' : ''}
                as={button.eventLayoutSlug ? buildCtaUrl(button, baseUrl) : ''}
                className={cx(
                  styles.CtaButton,
                  button.buttonClassName,
                  styles[button.theme]
                )}
                {...(button.link && {
                  href: button.link,
                  target: button.openInNewTab ? '_blank' : undefined,
                  rel: 'noopener noreferrer'
                })}
              >
                {button.label}
              </ButtonStandard>
            )
          })}

        <Share />
      </nav>
    </>
  )
}

EventNavigation.propTypes = {
  baseUrl: propTypes.string,
  eventNavItems: propTypes.array,
  logos: propTypes.shape({
    light: propTypes.string,
    dark: propTypes.string
  }),
  isEventPage: propTypes.bool,
  baseUrls: propTypes.shape({
    eventUmbrella: propTypes.string,
    event: propTypes.string
  }),
  showLogoInNavigation: propTypes.bool,
  ctaButton: propTypes.shape({
    label: propTypes.string,
    buttonId: propTypes.string,
    buttonClassName: propTypes.string,
    link: propTypes.string,
    eventLayoutSlug: propTypes.string,
    openInNewTab: propTypes.bool
  }),
  proxyEventHomeUrl: propTypes.string,
  logoAction: propTypes.shape({
    eventLayoutSlug: propTypes.string,
    link: propTypes.string,
    openInNewTab: propTypes.bool
  }),
  navigationCtas: propTypes.array,
  useNavigationDarkTheme: propTypes.bool
}

const buildCtaButton = (eventUmbrella, isEventPage) => {
  if (!isEventPage) return null

  const specialActions = eventUmbrella?.event?.specialActions
  const navigationCta = specialActions?.navigation_cta
  const fallbackCta = eventUmbrella?.event?.navigationCtaButton

  const cta = navigationCta || fallbackCta
  if (!cta) return null

  return {
    label: cta.labelOverride || cta.label,
    buttonId: cta.buttonId,
    buttonClassName: cta.buttonClassName,
    eventLayoutSlug: cta.eventLayoutSlug,
    link: cta.link,
    openInNewTab: cta.openInNewTab
  }
}

const buildAwardsCta = (eventUmbrella, isEventPage) => {
  if (!isEventPage) return null

  const specialActions = eventUmbrella?.event?.specialActions
  const navigationAwardsCta = specialActions?.navigation_awards_cta

  if (!navigationAwardsCta) return null

  return {
    label: navigationAwardsCta.labelOverride || navigationAwardsCta.label,
    buttonId: navigationAwardsCta.buttonId,
    buttonClassName: 'awards',
    eventLayoutSlug: navigationAwardsCta.eventLayoutSlug,
    link: navigationAwardsCta.link,
    openInNewTab: navigationAwardsCta.openInNewTab
  }
}

const buildLogoAction = (eventUmbrella, isEventPage) => {
  if (!isEventPage) return null
  return eventUmbrella?.event?.specialActions?.navigation_logo || null
}

const buildLogos = (eventUmbrella, isEventPage) => {
  const { portfolioLogoOverrideUrl } = eventUmbrella?.event || null
  if (isEventPage && portfolioLogoOverrideUrl) {
    return {
      light: portfolioLogoOverrideUrl,
      dark: portfolioLogoOverrideUrl
    }
  }

  return {
    light: eventUmbrella?.lightLogoUrl,
    dark: eventUmbrella?.darkLogoUrl
  }
}

const buildLiveAction = (eventUmbrella, isEventPage) => {
  if (!isEventPage) return null

  const specialActions = eventUmbrella?.event?.specialActions
  return specialActions?.live || eventUmbrella?.event?._liveAction || null
}

const EventSidebar = (props) => {
  const { eventUmbrella, baseUrls } = props
  const now = moment()
  const startDate = moment(eventUmbrella?.event?.startDate)
  const endDate = moment(eventUmbrella?.event?.endDate)
  const isEventLive = now.isBetween(startDate, endDate)
  const { sidebarColorOverride } = eventUmbrella?.event || null
  const isEventPage = !!baseUrls?.event
  const showLogoInNavigation = isEventPage
    ? eventUmbrella?.event?.showLogoInNavigation
    : eventUmbrella?.showLogoInNavigation

  const eventNavigationProps = {
    inverse: false,
    ctaButton: buildCtaButton(eventUmbrella, isEventPage),
    awardsCta: buildAwardsCta(eventUmbrella, isEventPage),
    logoAction: buildLogoAction(eventUmbrella, isEventPage),
    eventNavItems: isEventPage
      ? eventUmbrella?.event?.navItems
      : eventUmbrella?.navItems,
    baseUrl: isEventPage ? baseUrls?.event : baseUrls?.eventUmbrella,
    baseUrls,
    logos: buildLogos(eventUmbrella, isEventPage),
    isEventPage,
    isEventLive,
    showLogoInNavigation,
    proxyEventHomeUrl: isEventPage
      ? eventUmbrella?.event?.proxyEventHomeUrl
      : null,
    liveAction: buildLiveAction(eventUmbrella, isEventPage),
    useNavigationDarkTheme: eventUmbrella?.event?.useNavigationDarkTheme,
    navigationCtas: eventUmbrella?.event?.navigationCtas
  }

  return (
    <div
      className={cx(
        styles.Sidebar,
        eventNavigationProps.useNavigationDarkTheme && styles.SidebarDark,
        sidebarColorOverride && styles.HasOverride
      )}
      style={
        sidebarColorOverride && {
          '--sidebar-color-override': sidebarColorOverride
        }
      }
    >
      <div className={styles.SidebarInner}>
        <EventNavigation {...eventNavigationProps} />
      </div>
    </div>
  )
}

EventSidebar.propTypes = {
  eventUmbrella: propTypes.object,
  baseUrls: propTypes.shape({
    eventUmbrella: propTypes.string,
    event: propTypes.string
  })
}

export default EventSidebar
