.Row {
  margin-top: unset !important;
  padding-block: var(--stack-gap);

  [class='company-portal'] & {
    &, // Hide all rows by default
    [class*='WidgetWrapper']:has([class*='_Anchor_']:only-child) {
      // Hide any widget wrappers that are empty
      display: none;
    }

    // If the row has an anchor that is not an only child, show the row.
    &:has([class*='WidgetWrapper'] [class*='_Anchor_']:not(:only-child)) {
      display: block;
    }
  }
}

.tight {
  padding-block: unset;
}

.full:first-child {
  margin-top: spacing(-4);
  padding-top: unset;
}

.black {
  --row-background-color: #{$color-white-primary};

  background-color: $color-black-primary;
  color: var(--row-background-color);

  &:not(.tight) + .black {
    padding-top: unset;
  }
}

.grey-dark {
  --row-background-color: #{$color-white-primary};

  background-color: $color-black-alt;
  color: var(--row-background-color);

  &:not(.tight) + .grey-dark {
    padding-top: unset;
  }
}

.grey {
  background-color: #f5f5f5;

  &:not(.tight) + .grey {
    padding-top: unset;
  }
}

.white {
  &:not(.tight) + .white {
    padding-top: unset;
  }
}
