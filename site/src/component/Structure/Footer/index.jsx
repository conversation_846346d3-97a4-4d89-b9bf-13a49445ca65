import React from 'react'
import { array, string, object, bool } from 'prop-types'

import styles from './Footer.module.scss'

import Container from '@/component/Primitive/Container'
import Grid from '@/component/Primitive/Grid'

import PublicationInfo from './component/PublicationInfo'
import Navigation from './component/Navigation'
// import QuickLinks from './component/QuickLinks'
import SmartLink from '../../Primitive/SmartLink'
import IconButton from '../../Primitive/IconButton'

const Footer = ({
  logoUrl,
  latestMagazineIssue,
  navItems,
  navInstanceLinks,
  instanceEnabled,
  instanceStrapline,
  socialLinks
}) => {
  return (
    <footer className={styles.Footer}>
      <Container center size="wide" gutter>
        <div className={styles.FooterContent}>
          {instanceEnabled && (
            <Grid gutter="default">
              <Grid.Item hidden="mobile" width={{ d: 1 / 2 }}>
                <PublicationInfo
                  logoUrl={logoUrl}
                  latestMagazineIssue={latestMagazineIssue}
                  strapline={instanceStrapline}
                />
              </Grid.Item>
              <Grid.Item width={{ d: 1 / 2 }}>
                <Navigation navItems={navItems} socialLinks={socialLinks} />
              </Grid.Item>
            </Grid>
          )}
        </div>
        {/* {instanceEnabled && <QuickLinks navInstanceLinks={navInstanceLinks} />} */}
        {!instanceEnabled && (
          <div className={styles.Socials}>
            {socialLinks?.map(({ type, url }, i) => {
              return (
                <li key={i}>
                  <SmartLink href={url} target="_blank">
                    <IconButton
                      icon={type}
                      a11ytext={`${type} icon`}
                      iconHeight={20}
                      className={styles.SocialsIconButton}
                    />
                  </SmartLink>
                </li>
              )
            })}
          </div>
        )}
      </Container>
    </footer>
  )
}

Footer.propTypes = {
  logoUrl: string,
  latestMagazineIssue: object,
  navItems: array,
  navInstanceLinks: array,
  instanceEnabled: bool,
  instanceStrapline: string,
  socialLinks: array
}

export default Footer
