import React from 'react'
import { array } from 'prop-types'
import classNames from 'classnames'

import Hide from '@/component/Primitive/Hide'
import List from '@/component/Primitive/List'
import SmartLink from '@/component/Primitive/SmartLink'
import Type from '@/component/Primitive/Type'
import Icon from '@/component/Primitive/Icon'

import styles from '../../Footer.module.scss'

const SocialLinks = ({ socialLinks }) => (
  <div
    className={classNames(
      styles.FooterNavColumn,
      styles.FooterNavColumnSocials
    )}
  >
    <Hide below="desktop">
      <Type
        themed
        size="heading8"
        weight="bold"
        className={styles.FooterNavColumnHeader}
      >
        Socials
      </Type>
    </Hide>
    <List unstyled className={styles.FooterNavListSocials}>
      {socialLinks.map(({ type, width, title, url }, index) => {
        return (
          <li
            className={styles.FooterLinksListItem}
            key={`FooterLinksList--${index}`}
          >
            <SmartLink
              href={url}
              target="_blank"
              className={classNames(
                styles.FooterNavLink,
                styles.FooterNavLinkSocial
              )}
            >
              <span className={styles.FooterNavLinkSocialIcon}>
                <Icon type={type} width={width} a11ytext={title} />
              </span>
              <Type
                as="span"
                size={['body2', 'body3']}
                weight="medium"
                className={styles.FooterNavLinkSocialLabel}
              >
                {title}
              </Type>
            </SmartLink>
          </li>
        )
      })}
    </List>
  </div>
)

SocialLinks.propTypes = {
  socialLinks: array
}

export default SocialLinks
