import React from 'react'
import { string, object } from 'prop-types'
import styles from '../../Footer.module.scss'

import Type from '@/component/Primitive/Type'

import SmartLink from '@/component/Primitive/SmartLink'

import ResponsiveImage from '@/component/Primitive/ResponsiveImage'

const PublicationInfo = ({ logoUrl, latestMagazineIssue, strapline }) => {
  return (
    <div className={styles.FooterContentAbout}>
      {latestMagazineIssue && (
        <div className={styles.FooterContentAboutImageWrapper}>
          <SmartLink
            to="/magazine"
            as={`/magazine/${latestMagazineIssue.slug}`}
          >
            <ResponsiveImage
              width={150}
              height={208}
              alt="Latest publication"
              loading="lazy"
              src={latestMagazineIssue.images.cover_321x446_150[0].url}
              srcSet={[
                {
                  src: latestMagazineIssue.images.cover_321x446_150[0].url,
                  width: 150
                },
                {
                  src: latestMagazineIssue.images.cover_321x446_300[0].url,
                  width: 300
                }
              ]}
              className={styles.FooterContentAboutImage}
            />
          </SmartLink>
        </div>
      )}
      <div className={styles.FooterContentAboutInfo}>
        <img
          loading="lazy"
          src={logoUrl}
          alt="Publication Logo"
          className={styles.FooterContentAboutImage}
        />
        <Type
          as="p"
          size={['body2', 'body3']}
          className={styles.FooterContentAboutText}
        >
          {strapline}
        </Type>
      </div>
    </div>
  )
}

PublicationInfo.propTypes = {
  logoUrl: string,
  latestMagazineIssue: object,
  strapline: string
}

export default PublicationInfo
