import React, { useState, useEffect } from 'react'
import classNames from 'classnames'
import { arrayOf, shape, string, bool } from 'prop-types'

import styles from '../../Footer.module.scss'

import List from '@/component/Primitive/List'
import SmartLink from '@/component/Primitive/SmartLink'
import Type from '@/component/Primitive/Type'
import Icon from '@/component/Primitive/Icon'
import ShrinkWrap from '@/component/Primitive/ShrinkWrap'
import Hide from '@/component/Primitive/Hide'
import TextAlign from '@/component/Primitive/TextAlign'

const FooterNavigation = ({ title, content }) => {
  const [expanded, setExpanded] = useState(
    typeof window === 'undefined'
      ? true
      : window.matchMedia('(min-width: 960px)').matches
  )

  useEffect(() => {
    const abortController = new AbortController()

    typeof window !== 'undefined' &&
      window.addEventListener(
        'resize',
        () => {
          const { matches } = window.matchMedia('(min-width: 960px)')
          setExpanded(matches)
        },
        abortController
      )

    return () => {
      abortController.abort()
    }
  }, [])

  const handleToggle = () => setExpanded((state) => !state)

  return (
    <>
      <Hide at="desktop">
        <button className={styles.FooterNavColumnHeader} onClick={handleToggle}>
          <ShrinkWrap fullWidth vAlign="middle">
            <ShrinkWrap.Item>
              <TextAlign left>
                <Type themed size="heading6" weight="bold">
                  {title}
                </Type>
              </TextAlign>
            </ShrinkWrap.Item>
            <ShrinkWrap.Item shrink>
              <Icon
                className={classNames(styles.Icon, expanded && styles.rotated)}
                type="arrow-down"
                width={12}
              />
            </ShrinkWrap.Item>
          </ShrinkWrap>
        </button>
      </Hide>
      <Hide below="desktop">
        <Type
          className={styles.FooterNavColumnHeader}
          size={['heading6', 'heading8']}
          weight="bold"
          themed
        >
          {title}
        </Type>
      </Hide>
      {expanded && (
        <List unstyled className={styles.FooterNavList}>
          {content.map(({ url, type, title }, index) => (
            <li
              className={styles.FooterLinksListItem}
              key={`FooterLinksList--${index}`}
            >
              <SmartLink
                href={url}
                target={type === 'externalLink' ? '_blank' : undefined}
                className={styles.FooterNavLink}
              >
                <Type as="span" size={['heading6', 'body3']} weight="medium">
                  {title}
                </Type>
              </SmartLink>
            </li>
          ))}
        </List>
      )}
    </>
  )
}

FooterNavigation.propTypes = {
  title: string,
  content: arrayOf(
    shape({
      text: string,
      href: string,
      isExternal: bool
    })
  )
}

export default FooterNavigation
