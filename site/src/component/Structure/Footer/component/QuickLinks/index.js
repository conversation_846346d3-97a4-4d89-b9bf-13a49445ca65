import React from 'react'

import Icon from '@/component/Primitive/Icon'
import List from '@/component/Primitive/List'
import SmartLink from '@/component/Primitive/SmartLink'
import Type from '@/component/Primitive/Type'

import styles from '../../Footer.module.scss'
import links from '../../links'
import cx from 'classnames'
import { array } from 'prop-types'

const QuickLinks = ({ navInstanceLinks }) => (
  <div className={styles.FooterQuickLinksWrapper}>
    <a
      href="https://bizclikmedia.com/"
      target="_blank"
      rel="noreferrer noopener"
      className={styles.FooterQuickLinksLogo}
    >
      <Icon type="BMG" width={72} a11ytext="BMG Logo" />
    </a>

    <List unstyled className={styles.FooterQuickLinks}>
      {links?.map(({ content }) => {
        return content?.map(({ text, href, external }, index) => (
          <li className={styles.FooterQuickLink} key={index}>
            <SmartLink
              href={href}
              target={external ? '_blank' : ''}
              className={styles.FooterQuickLinkAnchor}
            >
              <Type size={['body2', 'body4']} as="span">
                {text}
              </Type>
            </SmartLink>
          </li>
        ))
      })}
      <li className={styles.FooterQuickLink} key="cookie-manager">
        <button
          onClick={() => window?.OneTrust?.ToggleInfoDisplay()}
          className={styles.FooterQuickLinkAnchor}
        >
          <Type size={['body2', 'body4']} as="span">
            Cookie Settings
          </Type>
        </button>
      </li>
    </List>
    <List unstyled className={cx(styles.FooterQuickLinks, styles.right)}>
      {navInstanceLinks?.map(({ title, url }, index) => (
        <li className={styles.FooterQuickLink} key={index}>
          <SmartLink
            href={url}
            className={styles.FooterQuickLinkAnchor}
            target="_blank"
          >
            <Type size={['body2', 'body4']}>{title}</Type>
          </SmartLink>
        </li>
      ))}
    </List>
  </div>
)

QuickLinks.propTypes = {
  navInstanceLinks: array
}

export default QuickLinks
