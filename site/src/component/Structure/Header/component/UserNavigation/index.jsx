/* eslint-disable camelcase */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useContext, useEffect } from 'react'
import classNames from 'classnames'
import { bool } from 'prop-types'

import SmartLink from '@/component/Primitive/SmartLink'
import Icon from '@/component/Primitive/Icon'
import Type from '@/component/Primitive/Type'
import { UserContext } from '@/component/Context/UserContext'
import styles from './UserNavigation.module.scss'

const UserNavigation = ({ desktopOnly, mobileOnly, noFullname }) => {
  const [loading, setLoading] = React.useState(true)
  // Initialise connection with Piano object (client-side)
  // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  // The "window.tp" object is the external Piano "Tinypass" object that handles
  // user registration, user logins, checking if user is logged in or not etc.
  // We need access to the window.tp object, so this must run client side.
  // As it's external to React an Effect would be the appropriate place to put it.
  // We pass an empty array as an argument as we only want it to run once.
  useEffect(() => {
    window.tp = window.tp || []
    // Passing tp an initialisation function which detects whether or not the user is logged in
    // and sets the userName and userLoggedIn property accordingly
    window.tp.push([
      'init',
      function () {
        setLoading(false)
        if (window.tp.pianoId.isUserValid()) {
          userIsLoggedIn()
        } else {
          userIsLoggedOut()
        }
      }
    ])
  }, [])

  const { currentUser, setCurrentUser } = useContext(UserContext)

  // Connect with the API to fetch the user access list from Piano.
  // This list is an array of Piano resource IDs that the user has access to.
  // Once received it saves to Session Storage (so it doesn't persist after the user leaves the site)
  // and updates the User Context with the User Name, email, User ID and Access List
  // to make this info available for other components.
  const checkUserAccess = (userObj) => {
    var myHeaders = new Headers()
    myHeaders.append('Content-Type', 'application/json')

    var raw = JSON.stringify({
      pianoUserId: userObj.uid
    })

    var requestOptions = {
      method: 'POST',
      headers: myHeaders,
      body: raw,
      redirect: 'follow'
    }

    // TODO: Prevent this from firing multiple times. As I believe piano is just blocking some of the requests causing Sentry errors
    fetch('/api/user/getaccess', requestOptions)
      .then((response) => response.text())
      .then((result) => {
        const data = JSON.parse(result)
        if (data.success === true) {
          sessionStorage.setItem('pianoAccess', data.accessList)
          setCurrentUser({
            ...userObj,
            accessList: data.accessList
          })
        } else {
          console.error(data.message)
          sessionStorage.setItem('pianoAccess', `Error: ${data.message}`)
          setCurrentUser({
            ...userObj,
            accessList: [`Error: ${data.message}`]
          })
        }
      })
      .catch((err) => {
        console.error(err)
        sessionStorage.setItem('pianoAccess', `Error: ${err}`)
        setCurrentUser({
          ...userObj,
          accessList: [`Error: ${err}`]
        })
      })
  }

  const userIsLoggedIn = () => {
    const { given_name, family_name, email, uid } = window.tp.pianoId.getUser()
    const userObj = {
      name: `${given_name} ${family_name}`,
      email: `${email}`,
      uid: `${uid}`,
      loggedIn: true
    }
    checkUserAccess(userObj)
  }

  const userIsLoggedOut = () => {
    setCurrentUser({
      name: null,
      email: null,
      uid: null,
      loggedIn: false,
      accessList: null
    })
    sessionStorage.removeItem('pianoAccess')
  }

  const pianoLogin = () => {
    window.tp.pianoId.show({
      disableSignUp: false,
      displayMode: 'modal',
      screen: 'login',
      loggedIn: function () {
        userIsLoggedIn()
      }
    })
  }

  const pianoLogout = () => {
    window.tp.pianoId.logout()
    userIsLoggedOut()
  }

  const pianoRegister = () => {
    window.tp.pianoId.show({
      screen: 'register',
      loggedIn: function () {
        userIsLoggedIn()
      },
      loggedOut: function () {
        userIsLoggedOut()
      }
    })
  }

  return (
    <div
      className={classNames(
        styles.UserNavigation,
        desktopOnly && styles.desktopOnly,
        mobileOnly && styles.mobileOnly
      )}
    >
      <div
        className={classNames(
          styles.Links,
          loading && styles.loading,
          !!currentUser && styles.dynamicWidth
        )}
      >
        {currentUser.loggedIn === false ? (
          <div className={styles.LinksInner}>
            <SmartLink
              className={classNames(styles.Link, styles.login)}
              onClick={pianoLogin}
            >
              <Type themed size={['body2', 'body4']}>
                Login
              </Type>
            </SmartLink>
            <SmartLink
              className={classNames(styles.Link, styles.register)}
              onClick={pianoRegister}
            >
              <Type themed size={['body2', 'body4']}>
                Register
              </Type>
            </SmartLink>
          </div>
        ) : null}
        {currentUser.loggedIn === true ? (
          <>
            <SmartLink className={classNames(styles.Link)} to="/my-account">
              <Type themed size={['body2', 'body4']} className={styles.Profile}>
                {!noFullname && currentUser.name}
                <Icon type="person" />
              </Type>
            </SmartLink>
            <SmartLink
              className={classNames(styles.Link, styles.logout)}
              onClick={pianoLogout}
            >
              <Type themed size={['body2', 'body4']}>
                Logout
              </Type>
            </SmartLink>
          </>
        ) : null}
      </div>
    </div>
  )
}

UserNavigation.propTypes = {
  desktopOnly: bool,
  mobileOnly: bool,
  noFullname: bool
}

export default UserNavigation
