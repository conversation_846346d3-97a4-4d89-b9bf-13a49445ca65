import React from 'react'
import classNames from 'classnames'
import { string, bool, func } from 'prop-types'

import SmartLink from '@/component/Primitive/SmartLink'
import Type from '@/component/Primitive/Type'
import Hide from '@/component/Primitive/Hide'
import UserNavigation from '../UserNavigation'
import Icon from '@/component/Primitive/Icon'

import styles from './UtilityNavigation.module.scss'

const UtilityNavigation = ({
  currentPath,
  inverse,
  navigationVisible,
  userNavigationVisible,
  toggleSearch
}) => {
  return (
    <div
      className={classNames(
        styles.UtilityNavigation,
        inverse && styles.inverse
      )}
    >
      <div className={styles.LinksWrapper}>
        <div className={styles.Links}>
          <Hide below="desktopnav" className={styles.Link}>
            {!currentPath.includes('/search') && (
              <button
                className={styles.SearchToggle}
                onClick={toggleSearch}
                aria-controls="header-search"
              >
                <Type themed size={['body2', 'body4']}>
                  Find
                </Type>
                <Icon
                  type="search"
                  width={16}
                  className={styles.SearchIcon}
                  aria-hidden="true"
                />
              </button>
            )}
          </Hide>
          <SmartLink
            className={classNames(
              styles.Link,
              currentPath === '/advertise' && styles.active
            )}
            href="/advertise"
          >
            <Type themed size={['body2', 'body4']}>
              Advertise
            </Type>
          </SmartLink>
          <SmartLink
            className={classNames(
              styles.Link,
              currentPath === '/editorial' && styles.active
            )}
            href="/editorial"
          >
            <Type themed size={['body2', 'body4']}>
              Editorial
            </Type>
          </SmartLink>
        </div>

        <Hide below="desktopnav">
          {userNavigationVisible && <UserNavigation desktopOnly />}
        </Hide>
      </div>
    </div>
  )
}

UtilityNavigation.propTypes = {
  currentPath: string,
  inverse: bool,
  navigationVisible: bool,
  userNavigationVisible: bool,
  toggleSearch: func
}

export default UtilityNavigation
