import React, { useRef, useState, useEffect, useContext } from 'react'
import { arrayOf, shape, string, array, bool, object, func } from 'prop-types'
import classNames from 'classnames'

import useEscapeKey from '@/hook/useEscapeKey'
import useOutsideClick from '@/hook/useOutsideClick'

import { ThemeContext } from '@/component/Context/ThemeContext'

import Navigation from '../Navigation'
import NavigationDropdown from '../Navigation/component/NavigationDropdown'
import Container from '@/component/Primitive/Container'
import SmartLink from '@/component/Primitive/SmartLink'
import IconButton from '@/component/Primitive/IconButton'
import SearchOverlay from '@/component/Primitive/SearchOverlay'
import Hide from '@/component/Primitive/Hide'

import useMedia from '@/hook/useMedia'

import UtilityNavigation from './component/UtilityNavigation'
import EventNavigation from '../Navigation/component/EventNavigation'
import UserNavigation from './component/UserNavigation'

import moment from 'moment'

import styles from './Header.module.scss'
import CompanyNavigation from '@/component/Primitive/CompanyPortal/CompanyNavigation'

const Header = ({
  currentPath,
  navItems,
  lightLogoUrl,
  socialLinks,
  userNavigationVisible,
  eventProps,
  company,
  isFeatureEnabled
}) => {
  const isDesktop = useMedia('(min-width: 1080px)')
  const [navigationVisible, setNavigationVisible] = useState(false)
  const [searchVisible, setSearchVisibility] = useState(false)
  const headerType = useContext(ThemeContext)
  const [smallNav, setSmallNav] = useState(false)
  const initialThreshold = 25
  const [scrollThreshold, setScrollThreshold] = useState(initialThreshold)
  const [smallNavThreshold, setSmallNavThreshold] = useState(initialThreshold)
  const [disableSticky, setDisableSticky] = useState(false)
  const navListRef = useRef()
  const [isTopStuck, setIsTopStuck] = useState(false)
  const headerRef = useRef(null)

  useEffect(() => {
    if (!headerRef.current) {
      return null
    }

    const abortController = new AbortController()
    const headerHeight = () => {
      const height = headerRef.current.offsetHeight

      document
        .querySelector(':root')
        .style.setProperty('--headerHeight', `${height}px`)
    }

    headerHeight()
    window.addEventListener('resize', headerHeight, abortController)

    return abortController.abort()
  }, [headerRef])

  useEffect(() => {
    const handleScroll = () => {
      window.requestAnimationFrame(() => {
        const { current } = navListRef
        if (!current) {
          return
        }

        const { top } = current?.getBoundingClientRect()

        if (top < 5) {
          setIsTopStuck(true)
        } else {
          setIsTopStuck(false)
        }
      })
    }

    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [isDesktop]) // eslint-disable-line react-hooks/exhaustive-deps

  const openNavigation = () => setNavigationVisible(true)

  const closeNavigation = () => setNavigationVisible(false)

  const toggleSearch = (e) => {
    e.preventDefault()
    setSearchVisibility(!searchVisible)
    setDisableSticky(!disableSticky)
  }

  const searchOverlayRef = useRef()
  useEscapeKey(searchVisible && toggleSearch)
  useOutsideClick(searchOverlayRef, searchVisible && toggleSearch)

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY

      if (scrollY > smallNavThreshold) {
        setSmallNav(true)
        setSmallNavThreshold(scrollY + 1) // Increase threshold to avoid jitter
      } else if (scrollY <= smallNavThreshold - 1) {
        setSmallNav(false)
        setSmallNavThreshold(scrollY - 1) // Decrease threshold to avoid jitter
      }

      if (scrollY > scrollThreshold) {
        setScrollThreshold(scrollY + 1) // Increase threshold to avoid jitter
      } else if (scrollY <= scrollThreshold - 1) {
        setScrollThreshold(scrollY - 1) // Decrease threshold to avoid jitter
      }
    }

    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [isDesktop]) // eslint-disable-line react-hooks/exhaustive-deps

  const inverseHeader = headerType === 'Inverse'

  const now = moment()
  const startDate = moment(eventProps?.eventUmbrella?.event?.startDate)
  const endDate = moment(eventProps?.eventUmbrella?.event?.endDate)
  const isEventLive = now.isBetween(startDate, endDate)
  const isEventPage = () => !!eventProps?.baseUrls?.event
  const isEventUmbrellaPage = () => !!eventProps?.baseUrls?.eventUmbrella
  const showLogoInNavigation = isEventPage()
    ? eventProps?.eventUmbrella?.event?.showLogoInNavigation
    : eventProps?.eventUmbrella?.showLogoInNavigation

  const eventNavigationProps = {
    inverse: inverseHeader,
    ctaButton:
      isEventPage() &&
      eventProps?.eventUmbrella?.event?.specialActions?.navigation_cta
        ? {
            label:
              eventProps?.eventUmbrella?.event?.specialActions?.navigation_cta
                .labelOverride ||
              eventProps?.eventUmbrella?.event?.specialActions?.navigation_cta
                .label,
            buttonId:
              eventProps?.eventUmbrella?.event?.specialActions?.navigation_cta
                .buttonId,
            buttonClassName:
              eventProps?.eventUmbrella?.event?.specialActions?.navigation_cta
                .buttonClassName,
            eventLayoutSlug:
              eventProps?.eventUmbrella?.event?.specialActions?.navigation_cta
                .eventLayoutSlug,
            link:
              eventProps?.eventUmbrella?.event?.specialActions?.navigation_cta
                .link,
            openInNewTab:
              eventProps?.eventUmbrella?.event?.specialActions?.navigation_cta
                .openInNewTab
          }
        : isEventPage() && eventProps?.eventUmbrella?.event?.navigationCtaButton
        ? {
            label:
              eventProps?.eventUmbrella?.event?.navigationCtaButton
                .labelOverride ||
              eventProps?.eventUmbrella?.event?.navigationCtaButton.label,
            buttonId:
              eventProps?.eventUmbrella?.event?.navigationCtaButton.buttonId,
            buttonClassName:
              eventProps?.eventUmbrella?.event?.navigationCtaButton
                .buttonClassName,
            eventLayoutSlug:
              eventProps?.eventUmbrella?.event?.navigationCtaButton
                .eventLayoutSlug,
            link: eventProps?.eventUmbrella?.event?.navigationCtaButton.link,
            openInNewTab:
              eventProps?.eventUmbrella?.event?.navigationCtaButton.openInNewTab
          }
        : null,
    awardsCta:
      isEventPage() &&
      eventProps?.eventUmbrella?.event?.specialActions?.navigation_awards_cta
        ? {
            label:
              eventProps?.eventUmbrella?.event?.specialActions
                ?.navigation_awards_cta.labelOverride ||
              eventProps?.eventUmbrella?.event?.specialActions
                ?.navigation_awards_cta.label,
            buttonId:
              eventProps?.eventUmbrella?.event?.specialActions
                ?.navigation_awards_cta.buttonId,
            buttonClassName: 'awards',
            eventLayoutSlug:
              eventProps?.eventUmbrella?.event?.specialActions
                ?.navigation_awards_cta.eventLayoutSlug,
            link:
              eventProps?.eventUmbrella?.event?.specialActions
                ?.navigation_awards_cta.link,
            openInNewTab:
              eventProps?.eventUmbrella?.event?.specialActions
                ?.navigation_awards_cta.openInNewTab
          }
        : null,
    logoAction:
      isEventPage() &&
      eventProps?.eventUmbrella?.event?.specialActions?.navigation_logo
        ? eventProps?.eventUmbrella?.event?.specialActions?.navigation_logo
        : null,
    eventNavItems: isEventPage()
      ? eventProps?.eventUmbrella?.event?.navItems
      : eventProps?.eventUmbrella?.navItems,
    baseUrl: isEventPage()
      ? eventProps?.baseUrls?.event
      : eventProps?.baseUrls?.eventUmbrella,
    baseUrls: eventProps?.baseUrls,
    logos: isEventPage()
      ? {
          light: eventProps?.eventUmbrella?.event.lightLogoUrl,
          dark: eventProps?.eventUmbrella?.event.darkLogoUrl
        }
      : {
          light: eventProps?.eventUmbrella?.lightLogoUrl,
          dark: eventProps?.eventUmbrella?.darkLogoUrl
        },
    isEventPage: isEventPage(),
    isEventLive,
    showLogoInNavigation,
    proxyEventHomeUrl: isEventPage()
      ? eventProps?.eventUmbrella?.event?.proxyEventHomeUrl
      : null,
    liveAction:
      isEventPage() && eventProps?.eventUmbrella?.event?.specialActions?.live
        ? eventProps?.eventUmbrella?.event?.specialActions?.live
        : isEventPage() && eventProps?.eventUmbrella?.event?._liveAction
        ? eventProps?.eventUmbrella?.event?._liveAction
        : null
  }

  return (
    <div
      className={classNames(
        styles.HeaderContainer,
        styles.sticky,
        disableSticky && styles.disableSticky,
        (isEventPage() ||
          (isEventUmbrellaPage() &&
            eventNavigationProps?.eventNavItems?.length > 0)) &&
          styles.event,
        isTopStuck && 'HeaderIsStuck'
      )}
      ref={headerRef}
    >
      <header
        className={classNames(
          styles.Header,
          inverseHeader && styles.inverse,
          styles.HeaderNavigationWrapper,
          navigationVisible && styles.HeaderNavigationWrapperActive
        )}
      >
        <div className={styles.Top}>
          <Hide at="desktopnav">
            <SmartLink
              href="/"
              className={classNames(styles.Logo)}
              title="Home"
            >
              <img alt="" src={lightLogoUrl} />
            </SmartLink>
          </Hide>
          <Container gutter center size="wide">
            <div className={styles.Inner}>
              <Hide at="desktopnav">
                <button
                  onClick={navigationVisible ? closeNavigation : openNavigation}
                  title={`${navigationVisible ? 'Close' : 'Open'} navigation`}
                  className={classNames(
                    styles.HeaderButton,
                    styles.HeaderNavToggle,
                    navigationVisible && styles.HeaderNavToggleActive
                  )}
                  type="button"
                >
                  <span />
                </button>
              </Hide>
              <Hide at="desktopnav">
                <IconButton
                  icon="search"
                  onClick={toggleSearch}
                  a11ytext="Toggle search"
                  className={styles.HeaderButton}
                  aria-controls="header-search"
                />
              </Hide>
            </div>
          </Container>
        </div>
        <div
          className={classNames(
            styles.Bottom,
            smallNav && styles.small,
            styles && styles.event
          )}
        >
          <Navigation
            lightLogoUrl={lightLogoUrl}
            showLogoInNavigation={showLogoInNavigation}
            small={smallNav}
          >
            <UtilityNavigation
              currentPath={currentPath}
              inverse={inverseHeader}
              userNavigationVisible={userNavigationVisible}
              toggleSearch={toggleSearch}
            />
            <Hide below="desktopnav">
              <hr />
            </Hide>
            <ul ref={navListRef} className={styles.NavigationListInner}>
              {userNavigationVisible && <UserNavigation mobileOnly />}
              <Hide at="desktopnav">
                {company && <CompanyNavigation {...company} mobileOnly />}
              </Hide>
              <Hide at="desktopnav">
                {eventProps?.baseUrls?.eventUmbrella &&
                  eventNavigationProps?.eventNavItems?.length > 0 && (
                    <EventNavigation {...eventNavigationProps} mobileOnly />
                  )}
              </Hide>
              {navItems &&
                navItems.length > 0 &&
                navItems.map((item, i) => (
                  <Navigation.Item
                    key={i}
                    title={item.title}
                    target={item.type === 'externalLink' ? '_blank' : null}
                    href={item.type === 'externalLink' ? item.url : null}
                    to={item.type === 'section' ? '/section' : null}
                    as={item.type === 'section' ? item.url : null}
                    onClick={!isDesktop ? closeNavigation : null}
                    active={currentPath === item.url}
                    inverse={inverseHeader}
                    className={i === navItems.length - 1 ? styles.last : null}
                  >
                    {item.subItems.length > 0 && (
                      <NavigationDropdown
                        closeNav={!isDesktop ? closeNavigation : null}
                        items={item.subItems}
                      />
                    )}
                  </Navigation.Item>
                ))}
              <Navigation.Item
                title="Advertise"
                mobileOnly
                href="/advertise"
                onClick={!isDesktop ? closeNavigation : null}
              />
              <Navigation.Item
                title="Editorial"
                mobileOnly
                href="/editorial"
                onClick={!isDesktop ? closeNavigation : null}
              />
              <Hide at="desktopnav">
                {userNavigationVisible && <UserNavigation desktopOnly />}
              </Hide>
              <Navigation.Item title="Socials" mobileOnly>
                <NavigationDropdown items={socialLinks} />
              </Navigation.Item>

              <Hide
                below="desktopnav"
                className={classNames(
                  styles.UserNav,
                  isTopStuck && styles.UserNavShow
                )}
                as="li"
              >
                {userNavigationVisible && (
                  <UserNavigation desktopOnly noFullname />
                )}
              </Hide>
            </ul>
          </Navigation>

          {!eventProps?.useNewNavigationSidebar && (
            <Hide below="desktopnav">
              {eventProps?.baseUrls?.eventUmbrella &&
                eventNavigationProps?.eventNavItems?.length > 0 && (
                  <EventNavigation {...eventNavigationProps} />
                )}
            </Hide>
          )}
        </div>

        {searchVisible && (
          <SearchOverlay
            setRef={searchOverlayRef}
            toggleSearch={toggleSearch}
            inverse={inverseHeader}
            isFeatureEnabled={isFeatureEnabled}
          />
        )}
      </header>
    </div>
  )
}

Header.defaultProps = {
  navItems: []
}

Header.propTypes = {
  currentPath: string,
  lightLogoUrl: string,
  userNavigationVisible: bool,
  socialLinks: array,
  navItems: arrayOf(
    shape({
      name: string,
      link: string
    })
  ),
  eventProps: object,
  company: object,
  instance: object,
  isFeatureEnabled: func
}

export default Header
