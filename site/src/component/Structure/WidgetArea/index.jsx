import React, { Fragment } from 'react'
import classNames from 'classnames'
import { array, object, bool, number } from 'prop-types'
import widgetFactory from '../../Widgets'

import WidgetWrapper from '@/component/Primitive/WidgetWrapper'
import Stack from '@/component/Primitive/Stack'

import styles from './WidgetArea.module.scss'

const WidgetArea = ({
  widgets,
  article,
  instance,
  section,
  pageData,
  lastWidgetIsSticky,
  latestMagazineIssue,
  cappedWidth,
  rowIndex,
  noStack,
  eventUmbrella
}) => {
  if (!widgets || widgets.length === 0) return null
  const lastWidget = widgets[widgets.length - 1]
  if (!lastWidget) return null
  const LastComponent = widgetFactory[lastWidget.type]
  let remainingWidgets = widgets
  if (lastWidgetIsSticky) {
    remainingWidgets = widgets.slice(0, -1)
  }

  const wrapperExemptions = [
    'articleLayoutHeader',
    'articleLayoutBody',
    'articleLayoutTags',
    'eventSponsorLayoutHeader',
    'eventSponsorLayoutBody',
    'eventSponsorLayoutFooter',
    'banner'
  ]

  let Wrapper
  if (widgets.length <= 1) {
    Wrapper = Fragment
  } else if (noStack) {
    Wrapper = 'div'
  } else {
    Wrapper = Stack
  }

  return (
    <div
      className={classNames(
        styles.WidgetArea,
        cappedWidth && styles['capped-width']
      )}
    >
      {(widgets.length > 1 || !lastWidgetIsSticky) && (
        <Wrapper>
          {remainingWidgets?.map((widget) => {
            if (!widget) return null
            const Component = widgetFactory[widget.type]
            // if (!Component) console.log('Widget type not found:', widget.type)
            if (!Component) return null
            const widgetProps = {
              ...widget
            }
            const componentProps = {
              ...widgetProps,
              article,
              section,
              instance,
              pageData,
              latestMagazineIssue,
              rowIndex,
              eventUmbrella: eventUmbrella || pageData?.eventUmbrella
            }

            if (wrapperExemptions.includes(widget.type)) {
              return <Component key={widget.id} {...componentProps} />
            }

            return (
              <WidgetWrapper
                id={widget.id}
                key={`Widget-${widget.id}`}
                displayOptions={widget.displayOptions}
                essential={widget.essential}
                showWhenEventIs={widget.showWhenEventIs}
                pageData={pageData}
                eventUmbrella={eventUmbrella}
              >
                <Component {...componentProps} />
              </WidgetWrapper>
            )
          })}
        </Wrapper>
      )}
      {lastWidgetIsSticky && (
        <WidgetWrapper
          key={lastWidget.id}
          displayOptions={lastWidget.displayOptions}
          essential={lastWidget.essential}
          showWhenEventIs={lastWidget.showWhenEventIs}
          pageData={pageData}
          eventUmbrella={eventUmbrella}
          sticky
        >
          <LastComponent
            {...{
              ...{ ...lastWidget },
              article,
              section,
              instance,
              pageData,
              latestMagazineIssue,
              rowIndex,
              eventUmbrella
            }}
          />
        </WidgetWrapper>
      )}
    </div>
  )
}

WidgetArea.propTypes = {
  widgets: array,
  article: object,
  section: object,
  instance: object,
  pageData: object,
  lastWidgetIsSticky: bool,
  cappedWidth: bool,
  latestMagazineIssue: object,
  rowIndex: number,
  noStack: bool,
  eventUmbrella: object
}

export default WidgetArea
