import React, { useState } from 'react'
import propTypes from 'prop-types'
import Navigation from '../..'
import NavigationDropdown from '../NavigationDropdown'
import Container from '@/component/Primitive/Container'
import SmartLink from '@/component/Primitive/SmartLink'
import Icon from '@/component/Primitive/Icon'
import Type from '@/component/Primitive/Type'
import styles from './EventNavigation.module.scss'
import cx from 'classnames'

const EventNavigation = ({
  baseUrl,
  baseUrls,
  eventNavItems,
  inverseHeader,
  mobileOnly,
  logos: { light },
  isEventPage,
  showLogoInNavigation,
  ctaButton,
  awardsCta,
  isEventLive,
  proxyEventHomeUrl,
  liveAction,
  logoAction
}) => {
  const [menuActive, setMenuActive] = useState(false)

  const retrieveUrl = (link) => {
    switch (link.type) {
      case 'label':
        return null
      case 'eventSection':
      case 'eventUmbrellaSection':
        if (!link.url) return baseUrl
        return `${baseUrl}${link.url}`
      case 'externalLink':
        return link.url
      default:
        break
    }
  }

  const retrieveCtaUrl = (cta) => {
    if (cta.eventLayoutSlug) {
      return `${baseUrl}/${cta.eventLayoutSlug}`
    } else if (cta.link) {
      return cta.link
    } else {
      return ''
    }
  }

  const retrieveLogoUrl = () => {
    if (logoAction) {
      if (logoAction.eventLayoutSlug) {
        return `${baseUrl}/${logoAction.eventLayoutSlug}`
      } else if (logoAction.link) {
        return logoAction.link
      }
    }
    return proxyEventHomeUrl || baseUrl
  }

  const handleClick = () => setMenuActive((prevState) => !prevState)

  if (mobileOnly) {
    return (
      eventNavItems && (
        <>
          <div className={styles.LogoWrapper}>
            {isEventPage && !showLogoInNavigation && (
              <SmartLink
                as={baseUrls.eventUmbrella}
                to="/events"
                className={styles.BackLink}
              >
                <Type
                  as="p"
                  weight={['bold', 'medium']}
                  size={['body4', 'body3']}
                  className={cx(styles.Back)}
                >
                  <Icon
                    width={9}
                    height={12}
                    type="arrow-left"
                    a11ytext="Back to event Portfolio"
                    className={styles.BackIcon}
                  />
                  Portfolio
                </Type>
              </SmartLink>
            )}
            {showLogoInNavigation && (
              <button
                type="button"
                onClick={handleClick}
                className={cx(
                  styles.LogoButton,
                  menuActive && styles.LogoButtonActive
                )}
              >
                <img alt="" src={light} />

                <Icon type="arrow-down" width={18} className={styles.Icon} />
              </button>
            )}
          </div>
          {menuActive &&
            eventNavItems.map((link, i) => (
              <Navigation.Item
                title={link.title}
                to={link.type.includes('event') ? '/events' : ''}
                as={retrieveUrl(link)}
                key={`event-${i}`}
                inverse={inverseHeader}
                classNames={{
                  NavigationItemLink: styles.EventNavigationItemLink,
                  Icon: styles.EventNavigationDropdownIcon
                }}
              >
                {!!link.subItems.length && (
                  <NavigationDropdown
                    items={link.subItems.map((i) => {
                      // Hack to remove forward slash
                      if (i.type !== 'externalLink' && i.url)
                        i.url = i.url.replace('/', '')
                      return i
                    })}
                    baseUrl={baseUrl}
                  />
                )}
              </Navigation.Item>
            ))}
          {ctaButton && (
            <Navigation.Item
              title={ctaButton.label}
              inverse={inverseHeader}
              id={ctaButton.buttonId}
              {...(ctaButton.link && {
                rel: 'noopener noreferrer',
                target: ctaButton.openInNewTab ? '_blank' : undefined,
                href: ctaButton.link
              })}
              {...(ctaButton.eventLayoutSlug && {
                to: '/events',
                as: retrieveCtaUrl(ctaButton)
              })}
              classNames={{
                NavigationItemLink: cx(
                  styles.EventNavigationItemLink,
                  styles.cta,
                  ctaButton.buttonClassName
                )
              }}
            />
          )}
          {awardsCta && (
            <Navigation.Item
              title={awardsCta.label}
              inverse={inverseHeader}
              id={awardsCta.buttonId}
              {...(awardsCta.link && {
                rel: 'noopener noreferrer',
                target: awardsCta.openInNewTab ? '_blank' : undefined,
                href: awardsCta.link
              })}
              {...(awardsCta.eventLayoutSlug && {
                to: '/events',
                as: retrieveCtaUrl(awardsCta)
              })}
              classNames={{
                NavigationItemLink: cx(
                  styles.EventNavigationItemLink,
                  styles.awards,
                  awardsCta.buttonClassName
                )
              }}
            />
          )}
          {/* - DISABLED AS DOESN'T ACCOUNT FOR TIMEZONE RN, SAME AS EVENTCONTENTGRID & TIMELINE
          {isEventPage && isEventLive && (
            <Navigation.Item
              title={liveAction?.labelOverride || 'LIVE'}
              to="/events"
              as={
                liveAction?.link ||
                (liveAction?.eventLayoutSlug
                  ? `${baseUrl}/${liveAction.eventLayoutSlug}`
                  : `${baseUrl}`)
              }
              inverse={inverseHeader}
              {...(liveAction?.openInNewTab && {
                target: '_blank',
                rel: 'noopener noreferrer'
              })}
              {...(liveAction?.buttonId && {
                id: liveAction.buttonId
              })}
              classNames={{
                NavigationItemLink: cx(
                  styles.EventNavigationItemLink,
                  styles.live,
                  liveAction?.buttonClassName
                )
              }}
            />
          )} */}
        </>
      )
    )
  }

  return (
    <div className={styles.EventNavigation}>
      <Container
        className={cx(
          styles.EventNavigationContainer,
          !showLogoInNavigation && styles.noLogo
        )}
        center
        size="wide"
      >
        {!isEventPage && showLogoInNavigation && (
          <SmartLink
            as={retrieveLogoUrl()}
            to="/events"
            className={cx(styles.Logo)}
            {...(logoAction?.openInNewTab && {
              target: '_blank',
              rel: 'noopener noreferrer'
            })}
          >
            <img alt="" src={light} />
          </SmartLink>
        )}
        {isEventPage && !showLogoInNavigation && (
          <SmartLink
            as={baseUrls.eventUmbrella}
            to="/events"
            className={styles.BackLink}
          >
            <Type
              as="p"
              weight={['bold', 'medium']}
              size={['body4', 'body3']}
              className={cx(styles.Back)}
            >
              <Icon
                width={9}
                height={12}
                type="arrow-left"
                a11ytext="Back to event Portfolio"
                className={styles.BackIcon}
              />
              Back to Portfolio
            </Type>
          </SmartLink>
        )}
        {isEventPage && showLogoInNavigation && (
          <SmartLink
            as={retrieveLogoUrl()}
            to="/events"
            className={cx(styles.Logo, isEventPage && styles.event)}
            {...(logoAction?.openInNewTab && {
              target: '_blank',
              rel: 'noopener noreferrer'
            })}
          >
            <img alt="" src={light} />
          </SmartLink>
        )}
        <nav
          className={cx(
            styles.EventNavigationList,
            isEventPage && styles.event
          )}
        >
          {eventNavItems &&
            eventNavItems.map((link, i) => (
              <Navigation.Item
                title={link.title}
                to={link.type.includes('event') ? '/events' : ''}
                as={retrieveUrl(link)}
                {...(link.type === 'externalLink' && {
                  href: link.url,
                  target: '_blank',
                  rel: 'noopener noreferrer'
                })}
                key={`event-${i}`}
                inverse={inverseHeader}
                classNames={{
                  NavigationItemLink: styles.EventNavigationItemLink,
                  Icon: styles.EventNavigationDropdownIcon
                }}
              >
                {!!link.subItems.length && (
                  <NavigationDropdown
                    items={link.subItems.map((i) => {
                      // Hack to remove forward slash
                      if (i.type !== 'externalLink' && i.url)
                        i.url = i.url.replace('/', '')
                      return i
                    })}
                    baseUrl={i.type !== 'externalLink' && baseUrl}
                  />
                )}
              </Navigation.Item>
            ))}
          {ctaButton && (
            <Navigation.Item
              title={ctaButton.label}
              inverse={inverseHeader}
              id={ctaButton.buttonId}
              {...(ctaButton.link && {
                rel: 'noopener noreferrer',
                target: ctaButton.openInNewTab ? '_blank' : undefined,
                href: ctaButton.link
              })}
              {...(ctaButton.eventLayoutSlug && {
                to: '/events',
                as: retrieveCtaUrl(ctaButton)
              })}
              classNames={{
                NavigationItemLink: cx(
                  styles.EventNavigationItemLink,
                  styles.cta,
                  ctaButton.buttonClassName
                )
              }}
            />
          )}
          {awardsCta && (
            <Navigation.Item
              title={awardsCta.label}
              inverse={inverseHeader}
              id={awardsCta.buttonId}
              {...(awardsCta.link && {
                rel: 'noopener noreferrer',
                target: awardsCta.openInNewTab ? '_blank' : undefined,
                href: awardsCta.link
              })}
              {...(awardsCta.eventLayoutSlug && {
                to: '/events',
                as: retrieveCtaUrl(awardsCta)
              })}
              classNames={{
                NavigationItemLink: cx(
                  styles.EventNavigationItemLink,
                  styles.awards,
                  awardsCta.buttonClassName
                )
              }}
            />
          )}
          {/* - DISABLED AS DOESN'T ACCOUNT FOR TIMEZONE RN, SAME AS EVENTCONTENTGRID & TIMELINE
          {isEventPage && isEventLive && (
            <Navigation.Item
              title={liveAction?.labelOverride || 'LIVE'}
              to="/events"
              as={
                liveAction?.link ||
                (liveAction?.eventLayoutSlug
                  ? `${baseUrl}/${liveAction.eventLayoutSlug}`
                  : `${baseUrl}`)
              }
              inverse={inverseHeader}
              {...(liveAction?.openInNewTab && {
                target: '_blank',
                rel: 'noopener noreferrer'
              })}
              {...(liveAction?.buttonId && {
                id: liveAction.buttonId
              })}
              classNames={{
                NavigationItemLink: cx(
                  styles.EventNavigationItemLink,
                  styles.live,
                  liveAction?.buttonClassName
                )
              }}
            />
          )} */}
        </nav>
      </Container>
    </div>
  )
}

EventNavigation.propTypes = {
  baseUrl: propTypes.string,
  eventNavItems: propTypes.array,
  inverseHeader: propTypes.bool,
  mobileOnly: propTypes.bool,
  logos: propTypes.shape({
    light: propTypes.string,
    dark: propTypes.string
  }),
  isEventPage: propTypes.bool,
  baseUrls: propTypes.shape({
    eventUmbrella: propTypes.string,
    event: propTypes.string
  }),
  showLogoInNavigation: propTypes.bool,
  isEventLive: propTypes.bool,
  proxyEventHomeUrl: propTypes.string,
  liveAction: propTypes.shape({
    labelOverride: propTypes.string,
    link: propTypes.string,
    eventLayoutSlug: propTypes.string,
    openInNewTab: propTypes.bool,
    buttonId: propTypes.string,
    buttonClassName: propTypes.string
  }),
  ctaButton: propTypes.shape({
    label: propTypes.string,
    buttonId: propTypes.string,
    buttonClassName: propTypes.string,
    link: propTypes.string,
    eventLayoutSlug: propTypes.string,
    openInNewTab: propTypes.bool
  }),
  logoAction: propTypes.shape({
    key: propTypes.string,
    type: propTypes.string,
    buttonId: propTypes.string,
    buttonClassName: propTypes.string,
    eventLayoutKey: propTypes.string,
    eventLayoutSlug: propTypes.string,
    baseUrl: propTypes.string,
    link: propTypes.string,
    openInNewTab: propTypes.bool,
    labelOverride: propTypes.string
  }),
  awardsCta: propTypes.shape({
    label: propTypes.string,
    buttonId: propTypes.string,
    buttonClassName: propTypes.string,
    link: propTypes.string,
    eventLayoutSlug: propTypes.string,
    openInNewTab: propTypes.bool
  })
}

export default EventNavigation
