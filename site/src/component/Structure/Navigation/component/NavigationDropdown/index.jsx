import React from 'react'
import { array, bool, func, string } from 'prop-types'
import cx from 'classnames'
import SmartLink from '@/component/Primitive/SmartLink'
import Type from '@/component/Primitive/Type'
import Icon from '@/component/Primitive/Icon'
import styles from './NavigationDropdown.module.scss'

const NavigationDropdown = ({
  items,
  onClick,
  closeNav,
  baseUrl,
  isTwoColumn
}) => {
  const itemTypeMap = {
    section: 'section',
    eventSection: 'events'
  }

  const getHref = (item) => {
    if (item.type === 'externalLink') return item.url
    if (baseUrl) {
      return `${baseUrl}/${item.url}`
    }
    return item.url
  }

  return (
    <div className={styles.NavigationDropdown}>
      <div
        className={cx(
          styles.NavigationDropdownInner,
          isTwoColumn && styles.NavigationDropdownInnerTwoColumn
        )}
      >
        {items.map((item, i) => (
          <SmartLink
            target={!item.type ? '_blank' : null}
            href={item.type ? getHref(item) : null}
            to={item.type ? `/${itemTypeMap[item.type]}` : null}
            onClick={(e) => {
              if (closeNav) {
                closeNav()
              }
              if (onClick) {
                e.preventDefault()
                onClick(item)
              }
            }}
            className={styles.NavigationDropdownItem}
            key={i}
          >
            <Type
              themed
              weight={['medium', 'regular']}
              size={['heading7', 'body4']}
            >
              {item.icon && (
                <Icon type={item.icon} width={18} className={styles.Icon} />
              )}
              {item.title || item.text}
            </Type>
          </SmartLink>
        ))}
      </div>
    </div>
  )
}

NavigationDropdown.propTypes = {
  items: array,
  onClick: func,
  closeNav: func,
  baseUrl: string,
  isTwoColumn: bool
}

export default NavigationDropdown
