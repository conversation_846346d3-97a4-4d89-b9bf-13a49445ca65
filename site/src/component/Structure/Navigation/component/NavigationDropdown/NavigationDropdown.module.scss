.NavigationDropdown {
  color: $color-white-primary;
}

@include mq($breakpoint-desktopNav) {
  .NavigationDropdown {
    clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
    margin-left: spacing(2);
    max-width: 300px;
    min-width: 150px;
    padding-top: spacing(1.25);
    position: absolute;
    right: 0;
    transform: translate3d(0, 0, 0);
    transition: 250ms ease-in-out clip-path;
    width: max-content;
    z-index: 2;

    [class^='NavigationItem']:hover &,
    [class^='NavigationItem']:focus-within & {
      clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }

    &:has(.NavigationDropdownInnerTwoColumn) {
      left: 0;
      max-width: 400px;
      right: auto;
    }
  }
}

.NavigationDropdownInner {
  background-color: $color-black-secondary;
}

@media (min-width: $breakpoint-desktopNav) {
  .NavigationDropdownInnerTwoColumn {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
}

.NavigationDropdownItem {
  border-bottom: 1px solid $color-misc-divider;
  color: $color-white-primary;
  display: block;
  padding: spacing(2.25);
  text-align: left;
  text-decoration: none;
  width: 100%;

  .NavigationDropdownInnerTwoColumn & {
    &:nth-child(odd) {
      border-right: 1px solid $color-misc-divider;
    }
  }

  &:last-child {
    border-bottom: 0;
  }

  &:hover,
  &:focus {
    background-color: var(
      --color-theme-button-background,
      --color-theme--secondary
    );
    color: var(--color-theme-button-foreground);
  }

  @include mq($breakpoint-desktopNav) {
    padding: spacing(1.5);
  }
}

@media (min-width: $breakpoint-desktopNav) {
  .NavigationDropdownItem {
    .NavigationDropdownInnerTwoColumn & {
      &:nth-child(odd) {
        border-right: 1px solid $color-misc-divider;
      }
    }
  }
}

.Icon {
  display: inline-block;
  margin-bottom: 3px;
  margin-right: spacing(1);
  vertical-align: middle;
}
