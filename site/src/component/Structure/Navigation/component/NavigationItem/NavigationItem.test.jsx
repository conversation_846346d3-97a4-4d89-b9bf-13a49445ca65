import React from 'react'
import validateRequiredProps from '@/lib/validate-required-props'
import { shallow } from 'enzyme'
import NavigationItem from '.'

const requiredProps = () => ({})

describe('Component: NavigationItem', function () {
  validateRequiredProps(NavigationItem, requiredProps())

  test('should output the expected markup with default props', function () {
    const wrapper = shallow(<NavigationItem {...requiredProps()} />)
    expect(wrapper.prop('className')).toEqual('NavigationItem')
  })

  test('should output additional className when `active` prop passed', function () {
    const wrapper = shallow(<NavigationItem {...requiredProps()} active />)
    expect(wrapper.find('.NavigationItemLink').prop('className')).toEqual(
      'NavigationItemLink active'
    )
  })
})
