import React, { useState } from 'react'
import cx from 'classnames'
import { bool, node, object, string } from 'prop-types'

import SmartLink from '@/component/Primitive/SmartLink'
import Icon from '@/component/Primitive/Icon'
import Type from '@/component/Primitive/Type'
import Hide from '@/component/Primitive/Hide'

import styles from './NavigationItem.module.scss'

const NavigationItem = ({
  title,
  children,
  active,
  desktopOnly,
  mobileOnly,
  primary,
  saveSpace,
  inverse,
  className,
  classNames,
  ...other
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false)
  return (
    <li
      className={cx(
        styles.NavigationItem,
        primary && styles.primary,
        desktopOnly && styles.desktopOnly,
        mobileOnly && styles.mobileOnly,
        dropdownOpen && styles['is-open'],
        inverse && styles.inverse,
        className
      )}
    >
      <SmartLink
        className={cx(
          styles.NavigationItemLink,
          active && styles.active,
          saveSpace && styles.saveSpace,
          classNames?.NavigationItemLink
        )}
        {...other}
      >
        <Type
          themed
          as="span"
          weight={['bold', 'medium']}
          size={['heading7', 'body4']}
          className={styles.Title}
        >
          {title}
        </Type>
        {children && (
          <Icon
            className={cx(styles.Icon, classNames?.Icon)}
            type="arrow-down"
          />
        )}
      </SmartLink>

      {children && (
        <>
          <Hide at="desktopnav">
            <button
              className={styles.DropdownToggle}
              onClick={() => setDropdownOpen(!dropdownOpen)}
            >
              <Icon
                className={cx(
                  styles.DropdownIcon,
                  dropdownOpen && styles.flipped,
                  classNames?.Icon
                )}
                type="arrow-down"
              />
            </button>
          </Hide>
          <div className={styles.Dropdown}>{children}</div>
        </>
      )}
    </li>
  )
}

NavigationItem.propTypes = {
  active: bool,
  desktopOnly: bool,
  mobileOnly: bool,
  children: node,
  title: string,
  primary: bool,
  saveSpace: bool,
  inverse: bool,
  className: string,
  classNames: object
}

export default NavigationItem
