import React, { useState } from 'react'
import classNames from 'classnames'

import SmartLink from '@/component/Primitive/SmartLink'
import Icon from '@/component/Primitive/Icon'
import IconButton from '@/component/Primitive/IconButton'
import Type from '@/component/Primitive/Type'
import NavigationDropdown from '../NavigationDropdown'
// import { ThemeContext } from '@/component/Context/ThemeContext'

import styles from './LiveEventsNavigationItem.module.scss'
// import { useVersionPath } from '@/component/Context/VersionPathContext'

const items = [
  {
    title: 'url 1',
    url: '#'
  },
  {
    title: 'url 2',
    url: '#'
  },
  {
    title: 'url 3',
    url: '#'
  }
]

const LiveEventsNavigationItem = () => {
  const [dropdownOpen, setDropdownOpen] = useState(false)
  // const versionPath = useVersionPath()
  // const { theme } = useContext(ThemeContext)
  return (
    <li
      className={classNames(
        styles.LiveEventsNavigationItem,
        dropdownOpen && styles['is-open']
      )}
    >
      <SmartLink className={styles.LiveEventsNavigationItemLink}>
        <div>
          {/* TODO: to be reinstated along with live event feature */}
          {/* <img
            alt=""
            className={styles.LiveIcon}
            src={versionPath(`/public/image/brand/${theme}--live.png`)}
          /> */}
          <Type as="span" size="body2" italic weight="bold">
            LIVE
          </Type>
          <Icon className={styles.Icon} type="arrow-down" />
        </div>
      </SmartLink>
      {items && (
        <>
          <IconButton
            icon="arrow-down"
            className={classNames(
              styles.DropdownToggle,
              dropdownOpen && styles.flipped
            )}
            onClick={() => setDropdownOpen(!dropdownOpen)}
            a11ytext="Toggle dropdown"
          />
          <div className={styles.Dropdown}>
            <NavigationDropdown items={items} />
          </div>
        </>
      )}
    </li>
  )
}

LiveEventsNavigationItem.propTypes = {}

export default LiveEventsNavigationItem
