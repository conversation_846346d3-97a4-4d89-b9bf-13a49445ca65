import React from 'react'
import NavigationWrapper from './component/NavigationWrapper'
import NavigationItem from './component/NavigationItem'
import { bool, node, string } from 'prop-types'

const Navigation = ({ children, lightLogoUrl, small }) => {
  return (
    <NavigationWrapper lightLogoUrl={lightLogoUrl} small={small}>
      {children}
    </NavigationWrapper>
  )
}
Navigation.displayName = 'Navigation'

Navigation.Item = NavigationItem
Navigation.Item.displayName = 'Navigation.Item'

Navigation.propTypes = {
  children: node,
  lightLogoUrl: string,
  small: bool
}

export default Navigation
