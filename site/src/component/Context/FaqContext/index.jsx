import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useMemo
} from 'react'
import { shape } from 'prop-types'

const FaqContext = createContext()

/**
 * FaqProvider acts as a context provider for managing FAQ entries and generating Schema.org JSON-LD.
 *
 * @component
 * @example
 * // Wrap your component tree with FaqProvider
 * return (
 *   <FaqProvider>
 *     <App />
 *   </FaqProvider>
 * )
 *
 * @example
 * // Use the hook within a child component to add FAQs or retrieve the schema
 * const { addFaq, getFaqSchema, faqs } = useFaqs()
 *
 * // Add a new FAQ entry
 * addFaq('What is React?', 'A JavaScript library for building user interfaces')
 *
 * // Get the schema for SEO
 * const schema = getFaqSchema()
 */
export const FaqProvider = ({ children }) => {
  const [faqs, setFaqs] = useState([])

  const addFaq = useCallback((question, answer) => {
    setFaqs((prevFaqs) => [...prevFaqs, { question, answer }])
  }, [])

  const getFaqSchema = useCallback(
    () => ({
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: faqs.map(({ question, answer }) => ({
        '@type': 'Question',
        name: question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: answer
        }
      }))
    }),
    [faqs]
  )

  const value = useMemo(() => ({ faqs, addFaq, getFaqSchema }), [
    faqs,
    addFaq,
    getFaqSchema
  ])

  return <FaqContext.Provider value={value}>{children}</FaqContext.Provider>
}

FaqProvider.propTypes = {
  children: shape({}).isRequired
}

export const useFaqs = () => {
  const context = useContext(FaqContext)
  if (!context) {
    throw new Error('useFaqs must be used within a FaqProvider')
  }
  return context
}
