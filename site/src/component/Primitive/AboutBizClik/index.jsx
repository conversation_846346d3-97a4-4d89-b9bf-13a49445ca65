import classNames from 'classnames'
import React from 'react'
import Container from '../Container'
import Icon from '../Icon'
import Type from '../Type'

import styles from './AboutBizClik.module.scss'

const logos = ['Business', 'Events', 'Intelligence', 'Lifestyle', 'Networks']

const AboutBizClik = () => (
  <div className={styles.AboutBizClik}>
    <Container center size="wide" gutter className={styles.Container}>
      <div className={styles.Content}>
        <Type
          themed
          as="h2"
          size={['heading3', 'heading0']}
          weight="medium"
          className={styles.Title}
        >
          BizClik Media Group, One Global Partner
        </Type>
        <Type as="p" size={['body2', 'body1']}>
          BizClik Media Group is one of the UK’s fastest growing media
          companies, with a global portfolio of leading digital communities.
        </Type>
        <Type as="p" size={['body2', 'body1']}>
          Established in 2007, BizClik Media Group is the corporate storyteller
          for the world’s biggest companies and the executives who run them.
        </Type>
        <Type as="p" size={['body2', 'body1']}>
          Our One Global Partner program positions BizClik as the digital media
          company of choice for delivering world-class multimedia content.
        </Type>
        <Type as="p" size={['body2', 'body1']}>
          We serve our digital community of 14 million executives through
          digital magazines, websites, newsletters, digital branding, content
          syndication, executive insights, white papers, webinars, virtual
          conferences, promo videos, podcasts, and our fast-growing demand
          generation division.
        </Type>
      </div>
      <div className={styles.Services}>
        {logos.map((logo) => (
          <Icon
            className={classNames(styles.Logo, styles[logo.toLowerCase()])}
            key={logo}
            height={87}
            type={`BMG${logo}`}
            a11ytext={`BMG ${logo}`}
          />
        ))}
        <Icon
          className={styles.BMGLogo}
          height={96}
          type="BMG"
          a11ytext="BMG"
        />
      </div>
    </Container>
  </div>
)

AboutBizClik.propTypes = {}

export default AboutBizClik
