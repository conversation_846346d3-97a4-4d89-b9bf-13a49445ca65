import React from 'react'
import { string } from 'prop-types'

import Icon from '../Icon'
import ShrinkWrap from '../ShrinkWrap'
import ButtonStandard from '../ButtonStandard'
import styles from './ArticleEventButton.module.scss'

const ArticleEventButton = ({
  eventRegistrationLink,
  startDate,
  onDemandLink
}) => {
  const isFutureEvent = new Date(startDate) > new Date()
  if (!isFutureEvent) {
    if (!onDemandLink)
      return (
        <div className={styles.ButtonWrapper}>
          <ButtonStandard
            className={styles.ArticleEventButton}
            href={eventRegistrationLink}
          >
            <ShrinkWrap>
              <ShrinkWrap.Item>Find out more</ShrinkWrap.Item>
              <ShrinkWrap.Item shrink>
                <Icon type="arrow-right" />
              </ShrinkWrap.Item>
            </ShrinkWrap>
          </ButtonStandard>
        </div>
      )

    return (
      <div className={styles.ButtonWrapper}>
        <ButtonStandard
          outline
          className={styles.ArticleEventButton}
          href={onDemandLink}
          target="_blank"
        >
          <ShrinkWrap>
            <ShrinkWrap.Item>Watch on Demand</ShrinkWrap.Item>
            <ShrinkWrap.Item shrink>
              <Icon type="arrow-right" />
            </ShrinkWrap.Item>
          </ShrinkWrap>
        </ButtonStandard>
      </div>
    )
  }

  return (
    <div className={styles.ButtonWrapper}>
      <ButtonStandard
        primary
        className={styles.ArticleEventButton}
        href={eventRegistrationLink}
        target="_blank"
      >
        <ShrinkWrap>
          <ShrinkWrap.Item>Register now</ShrinkWrap.Item>
          <ShrinkWrap.Item shrink>
            <Icon type="arrow-right" />
          </ShrinkWrap.Item>
        </ShrinkWrap>
      </ButtonStandard>
    </div>
  )
}

ArticleEventButton.propTypes = {
  eventRegistrationLink: string.isRequired,
  startDate: string.isRequired,
  onDemandLink: string
}

export default ArticleEventButton
