.ButtonWrapper {
  border-top: 2px solid $color-misc-divider;
  padding-top: spacing(4);
}

.ArticleEventButton {
  color: var(--color-theme-button-foreground);
  background-color: var(--color-theme-button-background);

  &.noAction {
    background-color: $color-black-brand;
  }

  &::before {
    background: color-mix(
      in sRGB,
      var(--color-theme-button-background) 80%,
      var(--color-theme-button-foreground)
    );
  }

  &:hover,
  &:focus {
    svg path {
      fill: $color-white-primary;
    }
  }
}
