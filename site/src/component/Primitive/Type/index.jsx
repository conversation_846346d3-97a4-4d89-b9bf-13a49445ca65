import React, { useContext } from 'react'
import { bool, node, oneOfType, string, array } from 'prop-types'
import classNames from 'classnames'
import { ThemeContext } from '@/component/Context/ThemeContext'
import styles from './Type.module.scss'

import Element from '../Element'

export const sizes = [
  'display0',
  'display1',
  'display2',
  'heading0',
  'heading1',
  'heading2',
  'heading3',
  'heading4',
  'heading5',
  'heading6',
  'heading7',
  'heading8',
  'body1',
  'body2',
  'body3',
  'body4',
  'body5',
  'body6',
  'navigation',
  'tag',
  'misc1',
  'misc2',
  'misc3',
  'misc4',
  'base'
]

export const weights = ['extrabold', 'bold', 'medium', 'regular', 'italic']

const getSizeClasses = (sizes) => [
  styles[`m-${sizes[0]}`],
  styles[`d-${sizes[1]}`]
]

const getWeightClasses = (weights) => [
  styles[`m-${weights[0]}`],
  styles[`d-${weights[1]}`]
]

const Type = ({
  as,
  size,
  weight,
  italic,
  uppercase,
  className,
  children,
  color,
  themed,
  useDangerouslySetInnerHTML
}) => {
  const { theme } = useContext(ThemeContext)
  return (
    <Element
      as={as}
      className={classNames(
        styles.Type,
        typeof size === 'string' && styles[size],
        typeof size === 'object' && getSizeClasses(size),
        typeof weight === 'string' && styles[weight],
        typeof weight === 'object' && getWeightClasses(weight),
        italic && styles.italic,
        uppercase && styles.uppercase,
        themed && styles[theme],
        className
      )}
      {...(useDangerouslySetInnerHTML
        ? { dangerouslySetInnerHTML: { __html: children } }
        : { children })}
      style={{ color: color }}
    />
  )
}

Type.displayName = 'Type'

Type.defaultProps = {
  as: 'div',
  size: 'base',
  weight: 'regular'
}

Type.propTypes = {
  size: oneOfType([array, string]),
  weight: oneOfType([array, string]),
  as: string,
  children: node,
  className: string,
  color: string,
  italic: bool,
  uppercase: bool,
  themed: bool,
  useDangerouslySetInnerHTML: bool
}

export default Type
