import React, { useMemo } from 'react'
import SmartLink from '@/component/Primitive/SmartLink'
import moment from 'moment/moment'
import styles from './EventLatestCTA.module.scss'
import { object, string } from 'prop-types'
import cx from 'classnames'
import Icon from '@/component/Primitive/Icon'
import YouTubeEmbed from '@/component/Primitive/YouTubeEmbed'

const PRE_TEXT = 'Global Events Program'
const BUTTON_TEXT = 'Attend Now'

const EventLatestCTA = ({ event, backgroundURL, textOverride, videoId }) => {
  const { brandColor, backgroundVideoId } = event
  const youTubeId = videoId || backgroundVideoId
  const hasBackground = !!(backgroundURL || youTubeId)

  const fromToDate = useMemo(() => {
    const { startDate, endDate } = event
    const startDateMoment = moment(startDate)
    const endDateMoment = moment(endDate)
    const isSameMonth = startDateMoment.month() === endDateMoment.month()
    const isSameDay =
      startDateMoment.format('Y MMMM D') === endDateMoment.format('Y MMMM D')

    if (isSameDay) {
      return startDateMoment.format('D MMMM')
    } else if (isSameMonth) {
      // Same month format: "28-29 May"
      return `${startDateMoment.format('D')}-${endDateMoment.format('D MMMM')}`
    } else {
      // Different month format: "28 May - 2 June"
      return `${startDateMoment.format('D MMMM')} - ${endDateMoment.format(
        'D MMMM'
      )}`
    }
  }, [event])

  const title = (
    <>
      Join us {event?.city ? `in ${event.city}` : null}
      <br />
      on {fromToDate}
    </>
  )

  return (
    <div
      className={styles.EventLatestCTA}
      style={brandColor && { '--color-theme--event': brandColor }}
    >
      <div
        className={cx(
          styles.EventLatestCTABackground,
          hasBackground && styles.EventLatestCTABackgroundMultiply
        )}
      >
        {!backgroundURL && youTubeId && (
          <div className={cx(styles.YoutubeEmbed)}>
            <YouTubeEmbed hideControls autoplay loop videoId={youTubeId} />
          </div>
        )}

        {backgroundURL && <img src={backgroundURL} alt="" />}
      </div>
      <span className={styles.EventLatestCTAAlert}>{PRE_TEXT}</span>
      <div className={styles.EventLatestCTAImage}>
        <img src={event?.darkLogoUrl || event?.lightLogoUrl} alt="" />
      </div>
      <SmartLink
        to={event?._fullUrl}
        className={cx(
          styles.EventLatestCTALink,
          hasBackground && styles.EventLatestCTALinkWhite
        )}
      >
        {textOverride || title}
      </SmartLink>
      <span className={styles.EventLatestCTAButton}>
        {BUTTON_TEXT}
        <Icon width={7} type="arrow-right" />
      </span>
    </div>
  )
}

EventLatestCTA.propTypes = {
  event: object,
  backgroundURL: string,
  textOverride: string,
  videoId: string
}

export default EventLatestCTA
