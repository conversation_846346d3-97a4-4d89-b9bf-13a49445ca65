import React from 'react'
import { object } from 'prop-types'
import EntitiesContainer from './component/EntitiesContainer'
import CompanyList from '@/component/Primitive/CompanyPortal/CompanyList'

const RelatedEntities = ({ article }) => {
  const { companies, executives } = article

  const companiesArray = []
  for (const company of companies) {
    const obj = {
      title: company.name,
      imgSrc: company?.images?.logo_free_127?.[0]?.url
    }
    if (company.showProfilePage) {
      obj.link = `/company/${company.slug}`
    }
    companiesArray.push(obj)
  }
  companiesArray.sort((a, b) => a.title.localeCompare(b.title))

  const executivesArray = []
  for (const executive of executives) {
    executivesArray.push({
      title: executive.name,
      subTitle: executive.jobTitle,
      link: `/executive/${executive.slug}`,
      imgSrc: executive?.images?.headshot_220x347_220?.[0]?.url
    })
  }
  executivesArray.sort((a, b) => a.title.localeCompare(b.title))

  return (
    <>
      {companiesArray.length > 0 && (
        <EntitiesContainer title="Company portals">
          <CompanyList listItems={companiesArray} hasMargin={false} />
        </EntitiesContainer>
      )}

      {executives && executivesArray.length > 0 && (
        <EntitiesContainer title="Executives">
          <CompanyList
            listItems={executivesArray}
            hasMargin={false}
            hasBackgroundImage
          />
        </EntitiesContainer>
      )}
    </>
  )
}

RelatedEntities.propTypes = {
  article: object
}

export default RelatedEntities
