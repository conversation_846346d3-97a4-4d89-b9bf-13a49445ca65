import React, { useState, useContext } from 'react'
import classNames from 'classnames'
import { string, arrayOf, shape } from 'prop-types'
import changeFileExtension from '@/lib/filename-extension-modifier'
import IconButton from '../IconButton'

import Inline from '../Inline'

import { ThemeContext } from '@/component/Context/ThemeContext'
import styles from './PerspectiveCarousel.module.scss'
import SmartLink from '../SmartLink'
import ResponsiveImage from '../ResponsiveImage'

const PerspectiveCarousel = ({ items }) => {
  const { theme, brandType } = useContext(ThemeContext)
  const [index, setIndex] = useState(0)
  const next = () => {
    items.length > 0 && setIndex((index + 1) % items.length)
  }
  const prev = () => {
    items.length > 0 && setIndex((items.length + index - 1) % items.length)
  }

  return (
    <>
      <div className={styles.PerspectiveCarousel}>
        {brandType === 'B2B' && items.length > 0 && (
          <>
            <SmartLink
              to="/magazine"
              as={`/magazine/${items[0].slug}`}
              className={classNames(
                styles.Item,
                index === 1 && styles.left,
                index === 2 && styles.right
              )}
            >
              <ResponsiveImage
                width={270}
                height={374}
                loading="lazy"
                alt=""
                src={items[0].imgUrl}
                srcSet={[
                  {
                    src: changeFileExtension(items[0].imgUrl, 'webp'),
                    width: 270
                  },
                  {
                    src: changeFileExtension(items[0].retinaImgUrl, 'webp'),
                    width: 540
                  }
                ]}
              />
            </SmartLink>

            {items.length > 1 && (
              <SmartLink
                to="/magazine"
                as={`/magazine/${items[1].slug}`}
                className={classNames(
                  styles.Item,
                  index === 2 && styles.left,
                  index === 0 && styles.right
                )}
              >
                <ResponsiveImage
                  width={270}
                  height={374}
                  loading="lazy"
                  alt=""
                  src={items[1].imgUrl}
                  srcSet={[
                    {
                      src: changeFileExtension(items[1].imgUrl, 'webp'),
                      width: 270
                    },
                    {
                      src: changeFileExtension(items[1].retinaImgUrl, 'webp'),
                      width: 540
                    }
                  ]}
                />
              </SmartLink>
            )}
            {items.length > 2 && (
              <SmartLink
                to="/magazine"
                as={`/magazine/${items[2].slug}`}
                className={classNames(
                  styles.Item,
                  index === 0 && styles.left,
                  index === 1 && styles.right
                )}
              >
                <ResponsiveImage
                  width={270}
                  height={374}
                  loading="lazy"
                  alt=""
                  src={items[2].imgUrl}
                  srcSet={[
                    {
                      src: changeFileExtension(items[2].imgUrl, 'webp'),
                      width: 270
                    },
                    {
                      src: changeFileExtension(items[2].retinaImgUrl, 'webp'),
                      width: 540
                    }
                  ]}
                />
              </SmartLink>
            )}
          </>
        )}
        {brandType === 'Lifestyle' && items[0] && (
          <SmartLink
            to="/magazine"
            as={`/magazine/${items[0].slug}`}
            className={classNames(styles.Item, styles.ItemSolo)}
          >
            <ResponsiveImage
              width={270}
              height={374}
              loading="lazy"
              alt=""
              src={items[0].imgUrl}
              srcSet={[
                {
                  src: items[0].imgUrl,
                  width: 270
                },
                {
                  src: items[0].retinaImgUrl,
                  width: 540
                }
              ]}
            />
          </SmartLink>
        )}
        {brandType === 'B2B' && (
          <img
            loading="lazy"
            src={`/image/texture/texture-${theme}.png`}
            alt=""
            width={934}
            height={800}
            className={styles.Texture}
          />
        )}
      </div>
      {brandType !== 'Lifestyle' && items.length > 1 && (
        <div className={styles.Navigation}>
          <Inline middle center>
            <IconButton
              a11ytext="previous"
              rounded
              themed
              onClick={prev}
              icon="arrow-left"
            />
            <IconButton
              a11ytext="next"
              rounded
              themed
              onClick={next}
              icon="arrow-right"
            />
          </Inline>
        </div>
      )}
    </>
  )
}

PerspectiveCarousel.propTypes = {
  items: arrayOf(
    shape({
      imgUrl: string.isRequired,
      retinaImgUrl: string.isRequired,
      slug: string.isRequired
    })
  ).isRequired
}

export default PerspectiveCarousel
