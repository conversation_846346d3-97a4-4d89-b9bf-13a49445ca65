import React, { useRef, useState } from 'react'
import { array, bool, func, string } from 'prop-types'
import classNames from 'classnames'
import useEscapeKey from '@/hook/useEscapeKey'
import useOutsideClick from '@/hook/useOutsideClick'

import styles from './Dropdown.module.scss'

import Icon from '@/component/Primitive/Icon'
import Type from '@/component/Primitive/Type'

const Dropdown = ({
  alignRight,
  block,
  items,
  onChange,
  value,
  label,
  className
}) => {
  const [expanded, setExpanded] = useState(false)
  const ref = useRef()

  const toggleExpanded = () => {
    setExpanded((prevExpanded) => !prevExpanded)
  }

  const handleClose = () => {
    setExpanded(false)
  }

  const handleSelection = (item) => {
    handleClose()
    onChange && onChange(item)
  }

  useEscapeKey(expanded && handleClose)
  useOutsideClick(ref, expanded && handleClose)

  const hasItems = items.length > 0

  return (
    <div
      className={classNames(
        styles.Dropdown,
        alignRight && styles.alignRight,
        block && styles.block,
        expanded && styles.expanded,
        className
      )}
      ref={ref}
    >
      <div className={styles.DropdownList}>
        <button
          type="button"
          className={styles.DropdownListLabel}
          {...(hasItems && { onClick: toggleExpanded })}
          {...(!hasItems && { disabled: true })}
        >
          <Type as="span" size={['body2', 'body3']}>
            {label}
          </Type>

          {hasItems && (
            <Icon
              width={12}
              height={9}
              type={expanded ? 'arrow-up' : 'arrow-down'}
              a11ytext={expanded ? 'Hide content' : 'Reveal content'}
              className={styles.Icon}
            />
          )}
        </button>
      </div>

      {expanded && (
        <ul
          className={classNames(
            styles.DropdownList,
            styles.DropdownListOverlay
          )}
        >
          {items.map((item, i) => {
            return (
              <li key={`dropdown-item-${i}`}>
                <button
                  className={classNames(
                    styles.DropdownListItem,
                    item.value === value && styles.selected
                  )}
                  onClick={() => handleSelection(item)}
                >
                  {item.text}
                </button>
              </li>
            )
          })}
        </ul>
      )}
    </div>
  )
}

Dropdown.defaultProps = {
  currentIndex: 0
}

Dropdown.propTypes = {
  alignRight: bool,
  block: bool,
  items: array.isRequired,
  onChange: func,
  parentRef: func,
  value: string,
  label: string,
  className: string
}

export default Dropdown
