import React from 'react'
import validateRequiredProps from '@/lib/validate-required-props'
import { render } from '@testing-library/react'
import Element from '.'

const requiredProps = () => ({})

describe('Component: Element', function () {
  validateRequiredProps(Element, requiredProps())

  test('should output the expected markup with default props', function () {
    const { container } = render(<Element {...requiredProps()} />)
    expect(container.querySelector('div')).toBeTruthy()
  })

  test('should output as specified element if set', function () {
    const { container } = render(<Element {...requiredProps()} as="h1" />)
    expect(container.querySelector('h1')).toBeTruthy()
  })

  test('should output all additional props as expected', function () {
    const { container } = render(
      <Element
        {...requiredProps()}
        className="example-class"
        title="Example title"
      />
    )
    expect(container.firstChild).toHaveClass('example-class')
  })
})
