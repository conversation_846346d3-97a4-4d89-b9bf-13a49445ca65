import React from 'react'
import classNames from 'classnames'
import {
  arrayOf,
  bool,
  node,
  number,
  object,
  oneOf,
  oneOfType,
  string
} from 'prop-types'
// import sassKeysToArray from '@/lib/sass-keys-to-array'

import styles from './GridItem.module.scss'

// const columns = sassKeysToArray(styles.columns)
const columns = ['m', 't', 'd', 'dm']
const trailingGaps = ['mobile', 'desktop', 'default']

const getWidthClasses = (widths) => {
  if (Array.isArray(widths)) {
    return widths.map(
      (width, i) => styles[`${columns[i]}-${Math.round(width * 100)}`]
    )
  }
  return Object.entries(widths).map(
    (width) =>
      columns.includes(width[0]) &&
      styles[`${width[0]}-${Math.round(width[1] * 100)}`]
  )
}

const GridItem = ({
  children,
  width,
  grow,
  trailingGap,
  hidden,
  className
}) => (
  <div
    className={classNames(
      styles.GridItem,
      grow && styles.grow,
      width && getWidthClasses(width),
      trailingGap && styles[`trailingGap-${trailingGap}`],
      hidden && styles[`hidden-${hidden}`],
      className
    )}
  >
    {children}
  </div>
)

GridItem.displayName = 'GridItem'

GridItem.propTypes = {
  children: node,
  trailingGap: oneOf(trailingGaps),
  grow: bool,
  width: oneOfType([arrayOf(number), object]),
  hidden: string,
  className: string
}

export default GridItem
