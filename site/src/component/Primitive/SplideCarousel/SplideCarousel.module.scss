.CarouselButtons {
  --button-inset: 10px;

  &:has([disabled] + [disabled]) {
    display: none;
  }
}

@media (min-width: $breakpoint-desktopMedium) {
  .CarouselButtons {
    --button-inset: -44px;
  }
}

.CarouselButton {
  background-color: var(--color-theme--event, var(--color-theme--secondary));
  border-radius: 50%;
  color: var(--color-theme--event-button-foreground);
  display: grid;
  height: 24px;
  inset: calc(var(--button-top, 50%) - 12px) auto auto var(--button-inset);
  place-items: center;
  position: absolute;
  transition: 250ms ease-in-out;
  width: 24px;
  z-index: 10;

  &[disabled] {
    opacity: 0.25;
    pointer-events: none;
  }

  &:active,
  &:hover {
    transform: scale(1.25);
  }

  &:nth-of-type(2) {
    inset: calc(var(--button-top, 50%) - 12px) var(--button-inset) auto auto;
  }
}
