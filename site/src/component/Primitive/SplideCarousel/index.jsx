import { Splide, SplideTrack } from '@splidejs/react-splide'
import cx from 'classnames'
import styles from './SplideCarousel.module.scss'
import Icon from '@/component/Primitive/Icon'
import React, { useEffect, useRef, useState } from 'react'
import { array, object, string } from 'prop-types'

const SplideCarousel = ({ children, options, className = {} }) => {
  const splideWrapperRef = useRef(null)
  const [buttonTop, setButtonTop] = useState('50%')
  const combinedOptions = {
    gap: '20px',
    pagination: false,
    ...options
  }

  useEffect(() => {
    const controller = new AbortController()
    const handleResize = () => {
      const imgs = splideWrapperRef?.current?.getElementsByTagName('img')
      if (imgs && imgs.length) {
        const img = [...imgs].filter((img) => img.checkVisibility())?.[0]

        if (img) {
          const top = img.scrollHeight / 2
          setButtonTop(`${top}px`)
        }
      }
    }

    window.addEventListener('load', handleResize, controller)
    window.addEventListener('resize', handleResize, controller)

    return () => {
      controller.abort()
    }
  }, [])

  return (
    <div
      ref={splideWrapperRef}
      style={{ '--button-top': buttonTop }}
      className={className}
    >
      <Splide hasTrack={false} options={combinedOptions}>
        <SplideTrack>{children}</SplideTrack>

        <div className={cx('splide__arrows', styles.CarouselButtons)}>
          <button
            className={cx(
              'splide__arrow',
              'splide__arrow--prev',
              styles.CarouselButton
            )}
          >
            <Icon width="7" type="arrow-left" />
          </button>
          <button
            className={cx(
              'splide__arrow',
              'splide__arrow--next',
              styles.CarouselButton
            )}
          >
            <Icon width="7" type="arrow-right" />
          </button>
        </div>
      </Splide>
    </div>
  )
}

SplideCarousel.propTypes = {
  children: array,
  options: object,
  className: string
}

export default SplideCarousel
