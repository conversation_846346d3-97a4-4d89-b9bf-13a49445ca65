import React from 'react'
import { arrayOf, number, shape, string } from 'prop-types'
import SplideCarousel from '@/component/Primitive/SplideCarousel'
import { SplideSlide } from '@splidejs/react-splide'
import CompanyTitle from '@/component/Primitive/CompanyPortal/CompanyTitle'
import Stack from '@/component/Primitive/Stack'

export const propTypes = {
  galleryImages: shape({
    image_768: arrayOf(
      shape({
        alt: string,
        caption: string,
        height: number,
        link: string,
        ratio: string,
        url: string,
        width: number
      })
    )
  })
}

const CompanyGallery = ({ galleryImages }) => {
  const carouselOptions = {
    perPage: 3,
    breakpoints: {
      640: {
        perPage: 1
      },
      768: {
        perPage: 2
      }
    },
    gap: '24px',
    pagination: false
  }

  if (!galleryImages?.image_768?.length) {
    return <></>
  }

  return (
    <Stack>
      <CompanyTitle title="Gallery" />

      <SplideCarousel options={carouselOptions}>
        {galleryImages?.image_768?.map((widget, index) => (
          <SplideSlide key={index}>
            <img src={widget.url} alt={widget?.caption || widget?.alt || ''} />
          </SplideSlide>
        ))}
      </SplideCarousel>
    </Stack>
  )
}

CompanyGallery.propTypes = propTypes

export default CompanyGallery
