import React from 'react'
import { shape, string, object } from 'prop-types'

import styles from './CompanyHero.module.scss'
import cx from 'classnames'
import YouTubeEmbed from '@/component/Primitive/YouTubeEmbed'
import Type from '@/component/Primitive/Type'
import Container from '@/component/Primitive/Container'
import Verified from '@/component/Primitive/CompanyPortal/Verified'

const CompanyHero = ({ company, instance }) => {
  if (!company) {
    return <></>
  }
  const {
    images,
    heroImages,
    name,
    heroVideoId,
    isVerified,
    city,
    state,
    country
  } = company
  const logo = images?.thumbnail_1x1_576?.[0]?.url
  const {
    hero_72x17_1440: desktop,
    hero_72x17_720: tablet,
    hero_72x17_640: mobile
  } = heroImages

  const hasSrcSet = desktop?.[0]?.url && tablet?.[0]?.url && mobile?.[0]?.url

  const addressArr = []
  if (city) {
    addressArr.push(city)
  }

  if (state) {
    addressArr.push(state)
  }

  if (country) {
    addressArr.push(country)
  }

  const address = addressArr.length ? addressArr.join(', ') : null

  return (
    <div className={styles.Hero}>
      {company.isVerified && heroVideoId && (
        <div className={cx(styles.YoutubeEmbed)}>
          <YouTubeEmbed hideControls autoplay loop videoId={heroVideoId} />
        </div>
      )}

      {!heroVideoId && !hasSrcSet && (
        <>
          <div
            className={cx(
              styles.HeroImageCircle,
              styles.HeroImageCircleTopLeft
            )}
          />

          <div
            className={cx(
              styles.HeroImageCircle,
              styles.HeroImageCircleBottomRight
            )}
          />
        </>
      )}

      {company.isVerified && !heroVideoId && hasSrcSet && (
        <div className={cx(styles.HeroImageWrapper)}>
          <picture>
            <source
              media="(min-width: 1200px)"
              srcSet={desktop[0].url}
              width="1440"
            />

            <source
              media="(min-width: 640px)"
              srcSet={tablet[0].url}
              width="720"
            />

            <img
              src={mobile[0].url}
              alt=""
              width="640"
              className={styles.HeroImage}
            />
          </picture>
        </div>
      )}

      <div className={styles.Content}>
        <Container size="wide" center gutter>
          <div className={styles.ContentInner}>
            <div className={styles.Logo}>
              <img src={logo} alt="" />
            </div>

            <hgroup>
              <div className={styles.HeadingFlex}>
                <Type
                  themed
                  as="h1"
                  size={['heading3', 'heading2']}
                  weight="medium"
                  className={styles.Heading}
                >
                  {name}
                </Type>

                {isVerified && <Verified />}
              </div>

              {address && (
                <Type
                  themed
                  as="p"
                  size={['body1', 'body2']}
                  weight="medium"
                  className={styles.SubHeading}
                >
                  {address}
                </Type>
              )}
            </hgroup>
          </div>
        </Container>
      </div>
    </div>
  )
}

CompanyHero.propTypes = {
  instance: shape({
    _id: string
  }),
  company: shape({
    images: object,
    oneLiner: string,
    name: string,
    heroVideoId: string
  })
}

export default CompanyHero
