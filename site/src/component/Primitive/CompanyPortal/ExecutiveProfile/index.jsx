import React from 'react'
import CompanyTitle from '@/component/Primitive/CompanyPortal/CompanyTitle'
import Prose from '@/component/Primitive/Prose'
import { object } from 'prop-types'
import styles from './ExecutiveProfile.module.scss'
import ContainerQuery from '@/component/Primitive/ContainerQuery'
import Verified from '@/component/Primitive/CompanyPortal/Verified'

const ExecutiveProfile = ({ executive }) => {
  const { name, jobTitle, bio, isVerified, city, state, country } = executive
  const src =
    executive?.images?.headshotv2_1x1_720?.[0]?.url ||
    executive?.images?.headshot_220x347_720?.[0]?.url
  const addressArr = []

  if (city) {
    addressArr.push(city)
  }

  if (state) {
    addressArr.push(state)
  }

  if (country) {
    addressArr.push(country)
  }

  const address = addressArr.length ? addressArr.join(', ') : null

  return (
    <ContainerQuery containerName="ExecutiveProfile">
      <div className={styles.Grid}>
        <div>
          {src && (
            <div className={styles.Image}>
              <div className={styles.ImageBackgroundWrapper}>
                <img src={src} alt="" />
              </div>

              <img src={src} alt="" className={styles.ImageForeground} />

              {isVerified && <Verified absolute />}
            </div>
          )}
        </div>

        <div>
          <hgroup className={styles.HeadingGroup}>
            {name && <CompanyTitle title={name} isVerified={isVerified} />}

            {jobTitle && <p className={styles.JobTitle}>{jobTitle}</p>}

            {address && (
              <p className={styles.Address}>
                <svg
                  width="15"
                  height="20"
                  viewBox="0 0 15 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M7.50002 0.916672C3.79127 0.916672 0.791687 3.91626 0.791687 7.62501C0.791687 11.6213 5.02752 17.1317 6.77169 19.2304C7.15502 19.6904 7.8546 19.6904 8.23794 19.2304C9.97252 17.1317 14.2084 11.6213 14.2084 7.62501C14.2084 3.91626 11.2088 0.916672 7.50002 0.916672ZM7.50002 10.0208C6.17752 10.0208 5.10419 8.94751 5.10419 7.62501C5.10419 6.30251 6.17752 5.22917 7.50002 5.22917C8.82252 5.22917 9.89585 6.30251 9.89585 7.62501C9.89585 8.94751 8.82252 10.0208 7.50002 10.0208Z" />
                </svg>

                {address}
              </p>
            )}
          </hgroup>

          {bio && <Prose dangerousHtml={bio} />}
        </div>
      </div>
    </ContainerQuery>
  )
}

ExecutiveProfile.propTypes = {
  executive: object.isRequired
}

export default ExecutiveProfile

/**
 * https://assets.bizclikmedia.net/220/a833878f6be706284807b6caacab7817:b05ccd2095fa9b5866aabf2bb7c27ef7/1741550352635-e-1761782400-v-beta-t-zrfkiow8bvvz-egmk-olkkgxbsxhtc8uub2xujxsj54 220w, https://assets.bizclikmedia.net/576/a833878f6be706284807b6caacab7817:bbb7d0de588044f7c520255ae34cc984/1741550352635-e-1761782400-v-beta-t-zrfkiow8bvvz-egmk-olkkgxbsxhtc8uub2xujxsj54 576w, https://assets.bizclikmedia.net/720/a833878f6be706284807b6caacab7817:14a91c9781e8af57c4c83ca5cfd94d9b/1741550352635-e-1761782400-v-beta-t-zrfkiow8bvvz-egmk-olkkgxbsxhtc8uub2xujxsj54 720w, https://assets.bizclikmedia.net/900/a833878f6be706284807b6caacab7817:f0617cb693f8d48ee8f89a1fd3daccb3/1741550352635-e-1761782400-v-beta-t-zrfkiow8bvvz-egmk-olkkgxbsxhtc8uub2xujxsj54 900w
 */
