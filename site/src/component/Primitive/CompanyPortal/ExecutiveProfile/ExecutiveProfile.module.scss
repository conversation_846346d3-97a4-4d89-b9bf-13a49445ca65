.Grid {
  display: grid;
  gap: 24px;
}

@container ExecutiveProfile (min-width: 768px) {
  .Grid {
    grid-template-columns: 15fr 19fr;
  }
}

.HeadingGroup {
  margin-bottom: 32px;
}

@container ExecutiveProfile (min-width: 768px) {
  .HeadingGroup {
    margin-bottom: 54px;
  }
}

.JobTitle {
  color: #646464;
  font-size: 16px;
  font-weight: 500;
  margin-top: 8px;
}

.Address {
  align-items: start;
  color: #646464;
  display: flex;
  font-size: 16px;
  gap: 8px;
  margin-top: 6px;

  svg {
    fill: var(--color-theme--secondary);
    flex-shrink: 0;
  }
}

.BottomGrid {
  align-items: start;
  display: grid;
  gap: 24px;
  margin-top: 32px;
}

@container ExecutiveProfile (min-width: 640px) {
  .BottomGrid {
    margin-top: 60px;
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
}

.Milestones {
  display: grid;
  font-size: 16px;
  gap: 10px;

  dt {
    margin-bottom: 14px;
  }

  dd {
    color: $color-grey-primary;

    &::before {
      color: var(--color-theme--secondary);
      content: '\2022';
      display: inline-block;
      margin-right: 0.5em;
    }
  }

  span {
    font-weight: 700;
  }
}

.History {
  display: grid;
  font-size: 16px;
  gap: 24px;
  line-height: 1.4;

  dd {
    color: $color-grey-primary;
    display: grid;
    gap: 24px;
    grid-template-columns: 80px 1fr;
  }

  h2 {
    color: $color-black-primary;
    font-size: 18px;
    font-weight: 500;
  }
}

.HistoryLogo {
  border: 3px solid #d1d1d1;

  img {
    display: block;
  }
}

.HistoryJobTitle {
  font-weight: 500;
}

.Image {
  aspect-ratio: 1;
  overflow: hidden;
  position: relative;
}

.ImageForeground {
  height: 100%;
  object-fit: contain;
  position: relative;
  width: 100%;
}

.ImageBackgroundWrapper {
  filter: blur(10px);
  inset: 0;
  position: absolute;

  img {
    height: 100%;
    object-fit: cover;
    width: 100%;
  }
}
