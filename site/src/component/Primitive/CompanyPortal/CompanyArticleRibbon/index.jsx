import React from 'react'
import cx from 'classnames'
import styles from './CompanyArticleRibbon.module.scss'
import Icon from '@/component/Primitive/Icon'
import { string } from 'prop-types'
import { getIcon } from '@/component/Primitive/CompanyPortal/lib/helpers'

const CompanyArticleRibbon = ({ contentType }) => {
  const icon = getIcon(contentType)

  return (
    <span className={cx(styles.CardContentType)}>
      {icon && (
        <Icon type={icon} width="12" height="auto" className={styles.Icon} />
      )}

      {contentType.toLowerCase() === 'list' ? 'Top 10' : contentType}
    </span>
  )
}

CompanyArticleRibbon.propTypes = {
  contentType: string
}

export default CompanyArticleRibbon
