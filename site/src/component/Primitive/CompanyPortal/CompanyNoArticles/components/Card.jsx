import styles from '@/component/Primitive/CompanyPortal/CompanyNoArticles/CompanyNoArticles.module.scss'
import cx from 'classnames'
import React from 'react'
import Dots from '@/component/Primitive/CompanyPortal/CompanyNoArticles/components/Dots'
import { string } from 'prop-types'

const FullWidthCard = ({ articleType, companyName }) => {
  const isTall = ['Whitepaper', 'Company Report'].includes(articleType)

  return (
    <div className={styles.Card}>
      <div
        className={cx(
          styles.NoArticlesFullWidthImage,
          isTall && styles.NoArticlesFullWidthImageTall
        )}
      >
        <Dots />
      </div>

      <div>
        We're busy making new {articleType.toLowerCase()}s about {companyName}.
        More will be coming your way soon.
      </div>
    </div>
  )
}

FullWidthCard.propTypes = {
  articleType: string.isRequired,
  companyName: string.isRequired
}
export default FullWidthCard
