.LimitedExecutives {
  color: $color-grey-primary;
}

.LimitedExecutivesImage {
  aspect-ratio: 1/1;
  background-color: #efefef;
  display: grid;
  margin-bottom: 20px;
  place-items: center;
  position: relative;
}

.LimitedExecutivesContent {
  color: $color-grey-primary;
  font-size: 12px;

  h3 {
    font-size: 18px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 8px;
  }
}

.NoArticles {
  color: $color-grey-primary;
  margin-inline: auto;
  max-width: 740px;
  text-align: center;
  width: 100%;
}

.NoArticlesFullWidth {
  color: $color-grey-primary;
  display: grid;
  gap: 20px;
  padding-block: 24px;
  position: relative;

  &:not(:last-child) {
    border-bottom: #d1d1d1 1px solid;
  }
}

@container latest-posts (min-width: 640px) {
  .NoArticlesFullWidth {
    align-items: start;
    grid-template-columns: auto 287px;
    padding-block: 32px;
  }
}

.NoArticlesFullWidthImage {
  aspect-ratio: 287/165;
  background-color: #efefef;
  display: grid;
  place-items: center;
  position: relative;

  &::after {
    background-color: var(--color-theme--primary);
    content: '';
    display: block;
    height: 4px;
    inset: auto 0 0;
    opacity: 0.5;
    position: absolute;
  }
}

@container latest-posts (min-width: 640px) {
  .NoArticlesFullWidthContent {
    order: -1;
  }
}

.DotsAnimation {
  display: flex;
  gap: 6px;
  justify-content: center;
  padding-block: 32px;
}

.Dot {
  animation: bounce 3000ms infinite ease-in-out;
  aspect-ratio: 1;
  border: 10px solid var(--color-theme--secondary);
  border-radius: 50%;
  opacity: 0.2;
  position: relative;
  width: 36px;

  &:nth-child(2) {
    animation-delay: 400ms;
    border-color: var(--color-theme--primary);
  }

  &:nth-child(3) {
    animation-delay: 800ms;
  }
}

@keyframes bounce {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.2;
  }
}

.Card {
  color: $color-grey-primary;
  display: grid;
  font-size: 18px;
  font-weight: 700;
  gap: 24px;
  line-height: 1.4;

  .NoArticlesFullWidthImage {
    aspect-ratio: 16/9;
  }

  .NoArticlesFullWidthImageTall {
    aspect-ratio: 325/463;
  }
}
