import React, { useState } from 'react'
import { arrayOf, bool, number, shape, string } from 'prop-types'
import ContainerQuery from '@/component/Primitive/ContainerQuery'
import CompanyTitle from '@/component/Primitive/CompanyPortal/CompanyTitle'
import styles from './CompanyList.module.scss'
import CompanyButton from '@/component/Primitive/CompanyPortal/CompanyButton'
import SmartLink from '@/component/Primitive/SmartLink'
import Icon from '@/component/Primitive/Icon'
import cx from 'classnames'

const CompanyList = ({
  title,
  listItems,
  link,
  hasMargin = true,
  noMarginBottom,
  hasBackgroundImage = false,
  clickToShowMoreCount
}) => {
  const [showListItems, setShowListItems] = useState(false)
  const limitListItemsHeight =
    clickToShowMoreCount &&
    listItems.length > clickToShowMoreCount &&
    !showListItems

  return (
    <ContainerQuery containerName="CompanyList">
      {title && <CompanyTitle title={title} />}

      {Array.isArray(listItems) && (
        <div
          className={cx(
            styles.Wrapper,
            limitListItemsHeight && styles.LimitHeight
          )}
        >
          <ul
            className={cx(
              styles.List,
              hasMargin && styles.ListMargin,
              noMarginBottom && styles.NoListMarginBottom
            )}
          >
            {listItems.map(
              (
                {
                  imgSrc,
                  link,
                  title,
                  subTitle,
                  hasBackgroundImage: imageHasBackgroundImage
                },
                index
              ) => (
                <li key={index} className={styles.ListItem}>
                  {imgSrc && (
                    <div className={styles.ListImage}>
                      <img
                        src={imgSrc}
                        alt=""
                        className={styles.ListImageForeground}
                      />

                      {(hasBackgroundImage || imageHasBackgroundImage) && (
                        <img
                          src={imgSrc}
                          alt=""
                          className={styles.ListImageBackground}
                        />
                      )}
                    </div>
                  )}

                  <hgroup className={styles.ListItemContent}>
                    {link ? (
                      <h3>
                        <SmartLink to={link}>{title}</SmartLink>
                      </h3>
                    ) : (
                      <h3>{title}</h3>
                    )}
                    {subTitle && <p>{subTitle}</p>}

                    <Icon
                      type="arrow-right"
                      width="7"
                      height="12"
                      className={styles.Arrow}
                    />
                  </hgroup>
                </li>
              )
            )}
          </ul>

          {limitListItemsHeight && (
            <button
              className={styles.RemoveHeightLimit}
              onClick={() => setShowListItems((prevState) => !prevState)}
            >
              Show more{' '}
              <Icon
                type="arrow-down"
                width="12"
                height="7"
                className={styles.ArrowDown}
              />
            </button>
          )}
        </div>
      )}

      {link && <CompanyButton to={link.url}>{link.title}</CompanyButton>}
    </ContainerQuery>
  )
}

CompanyList.propTypes = {
  title: string,
  listItems: arrayOf(
    shape({
      imgSrc: string,
      title: string,
      subTitle: string,
      link: string
    })
  ),
  link: shape({
    title: string,
    link: string
  }),
  hasMargin: bool,
  noMarginBottom: bool,
  hasBackgroundImage: bool,
  clickToShowMoreCount: number
}
export default CompanyList
