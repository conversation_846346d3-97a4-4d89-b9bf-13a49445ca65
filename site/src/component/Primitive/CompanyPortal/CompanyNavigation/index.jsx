import React, { useMemo, useState } from 'react'
import Navigation from '../../../Structure/Navigation'
import NavigationDropdown from '../../../Structure/Navigation/component/NavigationDropdown'
import Container from '@/component/Primitive/Container'
import styles from './CompanyNavigation.module.scss'
import cx from 'classnames'
import { string, object, bool } from 'prop-types'
import CompanyButton from '@/component/Primitive/CompanyPortal/CompanyButton'
import Icon from '@/component/Primitive/Icon'
import Type from '@/component/Primitive/Type'
import Hide from '@/component/Primitive/Hide'

const CompanyNavigation = ({
  name,
  platformsWithUrls = [],
  slug,
  mobileOnly = false,
  isVerified = false,
  portalPermissions
}) => {
  const [menuActive, setMenuActive] = useState(false)

  const handleClick = () => setMenuActive((prevState) => !prevState)

  const platformsLinks = useMemo(
    () =>
      platformsWithUrls.map((item, _index) => ({
        title: item.name.replace(/magazine|digital/gi, '').trim(),
        url: item.fullUrl,
        type: 'externalLink'
      })),
    [platformsWithUrls]
  )

  if (!slug) {
    return null
  }

  const navItems = [
    {
      title: 'Home',
      url: `/company/${slug}`,
      display: true
    },
    {
      title: 'Content Hub',
      url: `/company/${slug}/content-hub`,
      display: portalPermissions?.enableContentHubNavigation && isVerified
    },
    {
      title: 'Executives',
      url: `/company/${slug}/executives`,
      display: portalPermissions?.enableExecutivesNavigation && isVerified
    },
    {
      title: 'Partnerships',
      url: `/company/${slug}/partnerships`,
      display: portalPermissions?.enablePartnershipsNavigation && isVerified
    }
  ]

  // For companies with no articles linked to them yet
  if (platformsLinks.length > 0) {
    navItems.splice(1, 0, {
      title: 'Platform',
      subItems: platformsLinks,
      display: true,
      isTwoColumn: platformsLinks.length > 6
    })
  }

  const Nav = () => (
    <Container center size="wide">
      {mobileOnly && (
        <button
          type="button"
          onClick={handleClick}
          className={cx(styles.Button, menuActive && styles.ButtonActive)}
        >
          <Type
            themed
            as="span"
            weight={['bold', 'medium']}
            size={['heading7', 'body4']}
          >
            {name}'s Portal
          </Type>

          <Icon type="arrow-down" width={18} />
        </button>
      )}

      {((mobileOnly && menuActive) || !mobileOnly) && (
        <nav className={cx(styles.CompanyNavigationList)}>
          <ul>
            {navItems &&
              navItems.map(
                (link, i) =>
                  link.display && (
                    <Navigation.Item
                      title={link.title}
                      to={link.url}
                      as={link.url}
                      {...(link.type === 'externalLink' && {
                        href: link.url,
                        target: '_blank',
                        rel: 'noopener noreferrer'
                      })}
                      key={`company-${i}`}
                      classNames={{
                        NavigationItemLink: styles.CompanyNavigationItemLink,
                        Icon: styles.CompanyNavigationDropdownIcon
                      }}
                    >
                      {!!link?.subItems?.length && (
                        <NavigationDropdown
                          isTwoColumn={link?.isTwoColumn}
                          title={link.title}
                          items={link.subItems.map((i) => {
                            // Hack to remove forward slash
                            if (i.type !== 'externalLink' && i.url)
                              i.url = i.url.replace('/', '')
                            return i
                          })}
                          // baseUrl={i.type !== 'externalLink' && baseUrl}
                        />
                      )}
                    </Navigation.Item>
                  )
              )}

            {portalPermissions?.enableGetInTouchNavigation &&
              isVerified &&
              mobileOnly && (
                <Navigation.Item
                  title="Contact Us"
                  to="/company/[slug]/contact-us"
                  as={`/company/${slug}/contact-us`}
                  classNames={{
                    NavigationItemLink: styles.CompanyNavigationItemLink,
                    Icon: styles.CompanyNavigationDropdownIcon
                  }}
                />
              )}
          </ul>

          {portalPermissions?.enableGetInTouchNavigation &&
            isVerified &&
            !mobileOnly && (
              <div className={styles.CompanyNavigationButton}>
                <CompanyButton
                  to="/company/[slug]/contact-us"
                  as={`/company/${slug}/contact-us`}
                  outline
                >
                  Contact Us
                </CompanyButton>
              </div>
            )}
        </nav>
      )}
    </Container>
  )

  if (mobileOnly) {
    return (
      <div className={styles.CompanyNavigation}>
        <Nav />
      </div>
    )
  }

  return (
    <Hide below="desktopnav" className={styles.CompanyNavigation}>
      <Nav />
    </Hide>
  )
}

CompanyNavigation.propTypes = {
  platformsWithUrls: object,
  slug: string,
  mobileOnly: bool,
  name: string,
  isVerified: bool,
  portalPermissions: object
}

export default CompanyNavigation
