.CompanyNavigation {
  background-color: $color-white-primary;
  border-bottom: 2px solid var(--color-theme--secondary);
  border-top: 2px solid var(--color-theme--secondary);
}

@media (min-width: #{$breakpoint-desktopNav}) {
  .CompanyNavigation {
    background-color: $color-white-primary;
    border-bottom: unset;
    border-top: 4px solid var(--color-theme--secondary);
    box-shadow: 0 4px 25px 10px rgba($color-black-primary, 0.25);
    display: flex;
    padding: spacing(1) 0;
    position: sticky;
    top: 55px;
    z-index: 11;
  }
}

@media (min-width: #{$breakpoint-desktopNav}) {
  .CompanyNavigationContainer {
    display: flex;
    justify-content: space-between;
    max-width: spacing(174 + 4);
    padding: 0 spacing(4);
  }
}

@media (min-width: #{$breakpoint-desktopNav}) {
  .CompanyNavigationList {
    padding-right: 16px;

    &,
    ul {
      align-items: center;
      display: flex;
    }

    > ul > *:last-child > a,
    > ul > *:last-child > button {
      padding-right: 0;
    }
  }
}

.CompanyNavigationItemLink {
  border-color: var(--color-theme--secondary);
  color: $color-black-primary;
}

@media (min-width: #{$breakpoint-desktopNav}) {
  .CompanyNavigationItemLink {
    background-color: $color-white-primary;
    border-color: unset;
    transition: 250ms ease-in-out color;

    @include mq($breakpoint-desktopNav) {
      background-color: unset;
    }

    &.cta {
      background-color: $color-black-primary;
      color: $color-white-primary;

      @include mq($breakpoint-desktopNav) {
        margin-left: spacing(2.25);
        padding: 0 spacing(2.25) !important;
      }

      &:hover {
        background-color: rgba($color-black-secondary, 0.75);
        color: $color-white-primary;
      }
    }
  }
}

.CompanyNavigationDropdownIcon {
  color: $color-black-primary;

  svg {
    fill: $color-black-primary;
  }
}

.Button {
  align-items: center;
  border-bottom: 1px solid var(--color-theme--secondary);
  display: flex;
  flex-grow: 1;
  gap: 10px;
  justify-content: space-between;
  margin-bottom: -1px;
  padding: 18px;
  position: relative;
  width: 100%;

  svg {
    flex-shrink: 0;
    height: auto !important;
    margin-right: -2px;
    translate: 4px 0;
    width: 12px !important;
  }
}

.ButtonActive {
  svg {
    transform: scaleY(-1);
  }
}

.CompanyNavigationButton {
  margin-left: auto;
}
