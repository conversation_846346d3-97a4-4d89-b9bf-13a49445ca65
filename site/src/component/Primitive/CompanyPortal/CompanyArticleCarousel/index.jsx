import { object } from 'prop-types'
import React, { useState } from 'react'
import Splide<PERSON>arousel from '@/component/Primitive/SplideCarousel'
import { SplideSlide } from '@splidejs/react-splide'
import styles from './CompanyArticleCarousel.module.scss'
import ButtonStandard from '@/component/Primitive/ButtonStandard'
import cx from 'classnames'
import Container from '@/component/Primitive/Container'
import CompanyArticleRibbon from '@/component/Primitive/CompanyPortal/CompanyArticleRibbon'

const getImage = (images = {}, context, ratio, size) =>
  images?.[`${context}_${ratio}_${size}`]?.[0]

const chunkArrayMap = (arr, size = 2) => {
  return Array.from({ length: Math.ceil(arr.length / size) }, (_, i) =>
    arr.slice(i * size, i * size + size)
  )
}

const CompanyArticleCarousel = ({ company }) => {
  const CONTENT_TYPES = ['blog', 'news', 'article', 'interview', 'list']
  const articleTypes = company?.companyArticles?.filter((articleType) =>
    CONTENT_TYPES.includes(articleType.type.toLowerCase())
  )
  const createSlug = (type) => type.toLowerCase().replace(/\s+/g, '-')

  const [currentArticleType, setCurrentArticleType] = useState(
    articleTypes[0]?.type
  )

  const articleIndex = articleTypes.findIndex(
    (articleType) => articleType.type === currentArticleType
  )
  const articleChunks = chunkArrayMap(
    articleTypes[articleIndex]?.articles || []
  )

  if (articleTypes && !articleTypes.length) {
    return <></>
  }

  const getContentTypePlural = (contentType) => {
    switch (contentType.toLowerCase()) {
      case 'blog':
        return 'Blogs'
      case 'news':
        return 'News'
      case 'article':
        return 'Articles'
      case 'interview':
        return 'Interviews'
      case 'list':
        return 'Top 10'
      default:
        return null
    }
  }

  const carouselOptions = {
    perPage: 3,
    breakpoints: {
      992: {
        perPage: 2
      },
      640: {
        perPage: 1
      }
    }
  }

  return (
    <div className={styles.Holder}>
      <Container size="wide" center gutter>
        <menu className={styles.Menu}>
          <ul className={styles.MenuList}>
            {articleTypes &&
              articleTypes?.map((contentType, index) => (
                <li key={index}>
                  <button
                    onClick={() => setCurrentArticleType(contentType.type)}
                    className={cx(
                      styles.MenuButton,
                      contentType.type === currentArticleType &&
                        styles.MenuButtonActive
                    )}
                  >
                    {getContentTypePlural(contentType.type)}
                  </button>
                </li>
              ))}
          </ul>
        </menu>

        <SplideCarousel options={carouselOptions} className={styles.Carousel}>
          {articleChunks?.map((articleChunk, index) => {
            return (
              <SplideSlide key={index}>
                <ul className={styles.CompanyArticles}>
                  {articleChunk?.map((article) => {
                    const imgUrl = getImage(
                      article.images,
                      'thumbnail',
                      'widescreen',
                      '553'
                    )?.url

                    return (
                      <li key={article._id} className={styles.CardContainer}>
                        <div className={styles.Card}>
                          {imgUrl && (
                            <div className={styles.ImageHolder}>
                              <img src={imgUrl} alt="" />

                              <div className={styles.HoverGradient} />
                            </div>
                          )}

                          <div>
                            <CompanyArticleRibbon
                              contentType={article.contentType}
                            />

                            <a href={article.fullUrlPath}>{article.headline}</a>
                          </div>
                        </div>
                      </li>
                    )
                  })}
                </ul>
              </SplideSlide>
            )
          })}
        </SplideCarousel>

        {company?.isVerified && (
          <div className={styles.ButtonGroup}>
            {company?.portalPermissions?.enableContentHubNavigation && (
              <ButtonStandard
                to={`/company/${company.slug}/content-hub/${createSlug(
                  currentArticleType
                )}`}
                icon="arrow-right"
                className={styles.Button}
                size="small"
                primary
              >
                View {getContentTypePlural(currentArticleType)}
              </ButtonStandard>
            )}

            {company?.portalPermissions?.enableExecutivesNavigation && (
              <ButtonStandard
                to={`/company/${company.slug}/executives`}
                icon="arrow-right"
                className={styles.ButtonOutline}
                size="small"
                primary
                outline
              >
                View All Executives
              </ButtonStandard>
            )}
          </div>
        )}
      </Container>
    </div>
  )
}

CompanyArticleCarousel.propTypes = {
  company: object
}

export default CompanyArticleCarousel
