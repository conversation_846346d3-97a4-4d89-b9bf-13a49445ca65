import React from 'react'
import { array, bool, number, object, shape, string } from 'prop-types'
import styles from './CompanyArticleSection.module.scss'
import SmartLink from '@/component/Primitive/SmartLink'
import ContainerQuery from '@/component/Primitive/ContainerQuery'
import CompanyButton from '@/component/Primitive/CompanyPortal/CompanyButton'
import cx from 'classnames'
import moment from 'moment/moment'
import CompanyNoArticles from '@/component/Primitive/CompanyPortal/CompanyNoArticles'

const CardImage = ({ images, orientation = 'landscape', isVideo }) => (
  <div
    className={cx(
      styles.CardImageHolder,
      orientation === 'portrait' && styles.CardImageHolderPortrait
    )}
  >
    {orientation === 'portrait' ? (
      <picture>
        {'thumbnail_portrait_144' in images && (
          <img
            src={
              images.thumbnail_portrait_576?.[0]?.url ||
              images.thumbnail_portrait_144?.[0]?.url
            }
            alt={images.thumbnail_portrait_144?.[0]?.caption || ''}
          />
        )}
      </picture>
    ) : (
      <picture>
        {'thumbnail_landscape_900' in images && (
          <source
            media="(min-width: 580px)"
            srcSet={images.thumbnail_landscape_900?.[0].url}
          />
        )}
        {'thumbnail_landscape_580' in images && (
          <source
            media="(min-width: 412px)"
            srcSet={images.thumbnail_landscape_580?.[0].url}
          />
        )}
        {'thumbnail_landscape_412' in images && (
          <source
            media="(min-width: 330px)"
            srcSet={images.thumbnail_landscape_412?.[0].url}
          />
        )}
        {'thumbnail_landscape_330' in images && (
          <img
            src={images.thumbnail_landscape_330?.[0].url}
            alt={images.thumbnail_landscape_330?.[0]?.caption || ''}
          />
        )}
      </picture>
    )}

    {!isVideo && <div className={styles.HoverGradient} />}

    {isVideo && (
      <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={styles.VideoIcon}
      >
        <path d="M24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24C0 10.7452 10.7452 0 24 0ZM17.1426 34.2852L34.2852 24L17.1426 13.7139V34.2852Z" />
      </svg>
    )}
  </div>
)

CardImage.propTypes = {
  images: object,
  orientation: string,
  isVideo: bool
}

const CardTitle = ({ headline, fullUrlPath, preTitle }) => (
  <div>
    {preTitle && <span className={styles.PreTitle}>{preTitle}</span>}
    <h3 className={styles.CardContent}>
      <SmartLink to={fullUrlPath}>{headline}</SmartLink>
    </h3>
  </div>
)

CardTitle.propTypes = {
  headline: string,
  fullUrlPath: string,
  preTitle: string
}

const Card = ({
  headline,
  fullUrlPath,
  images,
  orientation = 'landscape',
  isVideo = false,
  preTitle = null
}) => {
  return (
    <div className={styles.Card}>
      <CardImage images={images} orientation={orientation} isVideo={isVideo} />

      <CardTitle
        headline={headline}
        fullUrlPath={fullUrlPath}
        preTitle={preTitle}
      />
    </div>
  )
}

Card.propTypes = {
  headline: string,
  fullUrlPath: string,
  images: shape({
    thumbnail_landscape_900: array,
    thumbnail_landscape_580: array,
    thumbnail_landscape_412: array,
    thumbnail_landscape_330: array
  }),
  orientation: string,
  isVideo: bool,
  preTitle: string
}

const TallCard = (props) => <Card {...props} orientation="portrait" />
const VideoCard = (props) => <Card {...props} isVideo />

const CompanyArticleSection = ({
  items,
  articleType,
  display,
  company,
  isVerified,
  showPlaceholder = true
}) => {
  const articleTypeSlug = articleType.toLowerCase().replace(/\s+/g, '-')
  const link =
    display && items.length === display
      ? `/company/${company.slug}/content-hub/${articleTypeSlug}`
      : ''

  return (
    <>
      <ContainerQuery containerName="CompanyArticleSection">
        <div className={styles.Grid}>
          {items.map((article, index) => {
            const props = article

            if (
              ['Whitepaper', 'Company Report'].includes(article.contentType)
            ) {
              props.preTitle = 'Download'
            } else if (article.contentType === 'Event') {
              const isFuture =
                moment(article.startDate).format('YYYYMMDD') >
                moment(moment.now()).format('YYYYMMDD')
              props.preTitle = isFuture ? 'Register' : 'Watch On Demand'
            }

            switch (articleType) {
              case 'Video':
                return <VideoCard key={index} {...props} />
              case 'Whitepaper':
              case 'Company Report':
                return <TallCard key={index} {...props} />
              default:
                return <Card key={index} {...props} />
            }
          })}

          {showPlaceholder && items.length < display && (
            <CompanyNoArticles
              type="card"
              companyName={company?.name}
              articleType={articleType}
            />
          )}
        </div>

        {company?.portalPermissions?.enableContentHubNavigation &&
          link &&
          isVerified && (
            <div className={styles.ButtonGroup}>
              <CompanyButton to={link}>View all {articleType}s</CompanyButton>
            </div>
          )}
      </ContainerQuery>
    </>
  )
}

CompanyArticleSection.propTypes = {
  items: array,
  articleType: string,
  display: number,
  company: object,
  isVerified: bool,
  showPlaceholder: bool
}

export default CompanyArticleSection
