import React from 'react'
import CompanyTitle from '@/component/Primitive/CompanyPortal/CompanyTitle'
import SmartLink from '@/component/Primitive/SmartLink'
import styles from './CompanyLatestPosts.module.scss'
import Type from '@/component/Primitive/Type'
import cx from 'classnames'
import { string, array, object, bool } from 'prop-types'
import CompanyArticleRibbon from '@/component/Primitive/CompanyPortal/CompanyArticleRibbon'
import CompanyButton from '@/component/Primitive/CompanyPortal/CompanyButton'
import CompanyNoArticles from '@/component/Primitive/CompanyPortal/CompanyNoArticles'

const CompanyLatestPosts = ({
  title,
  latestPosts,
  company,
  hideMoreArticlesText = false
}) => {
  const isArticle = (contentType) => contentType.toLowerCase() === 'article'
  const hasArticles = !!latestPosts.length

  if (!hasArticles && !hideMoreArticlesText) {
    return (
      <>
        {title && <CompanyTitle title={title} />}

        {<CompanyNoArticles companyName={company?.name} />}
      </>
    )
  }

  return (
    <>
      {title && <CompanyTitle title={title} />}

      <ul className={cx(styles.List, !title && styles.ListNoTitle)}>
        {latestPosts.map((post) => {
          const imageSrc = post.images.thumbnail_widescreen_553?.[0]?.url

          return (
            <li className={styles.Card} key={post._id}>
              <div
                className={cx(
                  styles.CardImage,
                  isArticle(post.contentType) && styles.CardImageArticle
                )}
              >
                {imageSrc && <img src={imageSrc} alt="" />}

                <div className={styles.HoverGradient} />
              </div>

              <hgroup className={styles.CardContent}>
                <CompanyArticleRibbon contentType={post.contentType} />

                <Type
                  as="h3"
                  weight="500"
                  size="heading5"
                  className={styles.CardContentTitle}
                >
                  <SmartLink href={post.fullUrlPath}>{post.headline}</SmartLink>
                </Type>

                <Type as="p" className={styles.CardContentSell} size="body2">
                  {post.sell}
                </Type>
              </hgroup>
            </li>
          )
        })}

        {!hideMoreArticlesText && latestPosts.length < 3 && (
          <li>
            <CompanyNoArticles type="fullWidth" companyName={company?.name} />
          </li>
        )}
      </ul>

      {company?.portalPermissions?.enableContentHubNavigation &&
        company?.isVerified && (
          <div>
            <CompanyButton to={`/company/${company.slug}/content-hub`}>
              Content Hub
            </CompanyButton>
          </div>
        )}
    </>
  )
}

CompanyLatestPosts.propTypes = {
  title: string,
  latestPosts: array,
  company: object,
  hideMoreArticlesText: bool
}

export default CompanyLatestPosts
