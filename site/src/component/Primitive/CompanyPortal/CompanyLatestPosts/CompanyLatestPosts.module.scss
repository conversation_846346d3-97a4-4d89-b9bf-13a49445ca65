.List {
  container-name: latest-posts;
  container-type: inline-size;
  list-style-type: none;
}

.Card {
  display: grid;
  gap: 20px;
  padding-block: 24px;
  position: relative;

  &:not(:last-child) {
    border-bottom: #d1d1d1 1px solid;
  }

  &:only-child {
    .ListNoTitle & {
      border-bottom: #d1d1d1 1px solid;
      border-top: #d1d1d1 1px solid;
    }
  }
}

@container latest-posts (min-width: 640px) {
  .Card {
    align-items: start;
    grid-template-columns: auto 287px;
    padding-block: 32px;
  }
}

.CardImage {
  position: relative;

  &::after {
    background-color: var(--color-theme--secondary);
    content: '';
    display: block;
    height: 4px;
    opacity: 0.5;
  }
}

.CardImageArticle {
  &::after {
    opacity: 1;
  }
}

.CardContent {
  img {
    width: 100%;
  }
}

@container latest-posts (min-width: 640px) {
  .CardContent {
    order: -1;
  }
}

.CardContentType {
  align-items: center;
  color: $color-white-primary;
  display: inline-flex;
  font-size: 12px;
  font-weight: 500;
  gap: 8px;
  margin-bottom: 16px;
  padding: 5px 8px;
  position: relative;
  transform: perspective(1px) translateZ(0);
  transition: 250ms ease-in-out;

  .Card:has(a:active, a:hover) & {
    padding-left: 24px;
  }

  &::before {
    background-color: var(--color-theme--primary);
    content: '';
    inset: 0;
    opacity: 0.5;
    position: absolute;
    z-index: -1;
  }
}

.CardContentTypeArticle {
  &::before {
    opacity: 1;
  }
}

.CardContentTitle {
  color: $color-black-primary;
  transition: 250ms ease-in-out;

  .Card:has(a:active, a:hover) & {
    color: var(--color-theme--secondary);
  }

  a {
    color: currentColor;

    &:link,
    &:visited {
      text-decoration: none;
    }

    &::before {
      content: '';
      inset: 0;
      position: absolute;
      z-index: 2;
    }
  }
}

.CardContentSell {
  color: $color-grey-primary;
  margin-top: 16px;
}

.Icon {
  display: block !important;
  height: 12px !important;
  translate: 0 -1px;
  width: 12px !important;

  svg {
    display: block;
    height: 100%;
    object-fit: contain;
    width: 100%;
  }
}

.HoverGradient {
  background-color: var(--color-theme--secondary);
  inset: 0 0 4px;
  mask-image: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.5) 0%,
    rgba(0, 0, 0, 0) 66.6%,
    rgba(0, 0, 0, 0) 100%
  );
  opacity: 0;
  position: absolute;
  transition: 250ms ease-in-out;

  .Card:has(a:active, a:hover) & {
    opacity: 1;
  }
}
