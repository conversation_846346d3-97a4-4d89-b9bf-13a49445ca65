.Milestones {
  padding-top: 32px;
}

@container ExecutiveMeta (min-width: 640px) {
  .History {
    padding-top: 32px;

    & + .Milestones {
      border-top: 1px solid #bebebe;
    }
  }

  .Milestones {
    padding-top: 60px;
  }
}

.MilestonesGrid {
  display: grid;
  gap: 16px 24px;

  dd {
    font-size: 16px;
    line-height: 1.25;
    list-style: circle;
    padding-left: 15px;
    position: relative;

    &::before {
      background: var(--color-theme--secondary);
      border-radius: 50%;
      content: '';
      height: 6px;
      left: 0;
      position: absolute;
      top: 5px;
      width: 6px;
    }

    span {
      color: var(--color-theme--primary);
      display: inline-block;
      font-size: inherit;
      font-weight: 700;
    }
  }
}

@container ExecutiveMeta (min-width: 640px) {
  .MilestonesGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@container ExecutiveMeta (min-width: 768px) {
  .MilestonesGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.CompanyGroup {
  display: grid;
  gap: 24px;
  grid-template-columns: 80px 1fr;

  &:not(:last-child) {
    border-bottom: 1px solid #bebebe;
    margin-bottom: 24px;
    padding-bottom: 24px;
  }
}

@container ExecutiveMeta (min-width: 768px) {
  .CompanyGroup {
    grid-template-columns: 80px 182px 1fr;
  }
}

.HistoryLogo {
  aspect-ratio: 1;
  border: 3px solid #bebebe;

  img {
    height: 100%;
    object-fit: contain;
    width: 100%;
  }
}

.CompanyNameColumn,
.CompanyJobsList {
  padding-top: 16px;
}

.CompanyNameRow {
  align-items: start;
  display: flex;
  gap: 10px;

  > div {
    flex-shrink: 0;
    transform: translateY(-0.2em);
  }
}

.CompanyName {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.25;
}

.CompanyJobsList {
  font-size: 16px;
}

@container ExecutiveMeta (max-width: 767px) {
  .CompanyJobsList {
    grid-column: 1 / 3;
  }
}

.CompanyJobsListItem {
  color: #646464;
  display: grid;
  grid-template-columns: 1fr 183px;
}

.CompanyJobsListTitle {
  font-weight: 500;
  padding-left: 15px;
  position: relative;

  &::before {
    background: var(--color-theme--secondary);
    border-radius: 50%;
    content: '';
    height: 6px;
    left: 0;
    position: absolute;
    top: 0.5em;
    width: 6px;
  }
}

.CompanyJobsListDate {
  text-align: right;

  span {
    color: var(--color-theme--secondary);
  }
}

.CompanyDuration {
  color: #646464;
  font-size: 16px;
  line-height: 1.25;
  margin-top: 0.4em;
}
