import React from 'react'
import ContainerQuery from '@/component/Primitive/ContainerQuery'
import Stack from '@/component/Primitive/Stack'
import styles from './ExecutiveMeta.module.scss'
import CompanyTitle from '@/component/Primitive/CompanyPortal/CompanyTitle'
import { object } from 'prop-types'
import moment from 'moment'
import Verified from '@/component/Primitive/CompanyPortal/Verified'

const ExecutiveMeta = ({ executive }) => {
  const { milestones, history } = executive

  const getMonth = (monthNumber) => {
    switch (monthNumber) {
      case 1:
        return 'Jan'
      case 2:
        return 'Feb'
      case 3:
        return 'Mar'
      case 4:
        return 'Apr'
      case 5:
        return 'May'
      case 6:
        return 'Jun'
      case 7:
        return 'Jul'
      case 8:
        return 'Aug'
      case 9:
        return 'Sept'
      case 10:
        return 'Oct'
      case 11:
        return 'Nov'
      case 12:
        return 'Dec'
      default:
        return null
    }
  }

  const groupAndSortWorkHistory = () => {
    if (!history?.length) return []
    const groupedByCompany = history.reduce((acc, job) => {
      const companyName = job.company?.name || job.fallbackCompanyName
      if (!companyName) {
        return acc
      }
      if (!acc[companyName]) {
        acc[companyName] = {
          companyName,
          companyLogo: job.company?.images?.logo_free_127?.[0]?.url,
          jobs: [],
          earliestStartDate: null,
          latestEndDate: null,
          totalDuration: null,
          isVerified: job.company?.isVerified || false
        }
      }
      const startMonthName = job.startMonth ? getMonth(job.startMonth) : null
      const endMonthName = job.endMonth ? getMonth(job.endMonth) : null
      // Create date string with month names if available
      let dateString = ''
      if (startMonthName) {
        dateString += `${startMonthName} `
      }
      dateString += `${job.startYear}`
      dateString += ' - '
      if (job.endYear) {
        if (endMonthName) {
          dateString += `${endMonthName} `
        }
        dateString += job.endYear
      } else {
        dateString += '<span>Present</span>'
      }

      // Create moment.js dates for calculations - subtract 1 from month because moment uses 0-indexed months
      const startMonth = job.startMonth ? job.startMonth - 1 : 0
      const endMonth = job.endMonth ? job.endMonth - 1 : 11
      const endYear = job.endYear || new Date().getFullYear()

      const startDate = moment([job.startYear, startMonth, 1])
      const endDate = job.endYear
        ? moment([endYear, endMonth, 1]).endOf('month')
        : moment() // Use current date for present positions

      // Update earliest start and latest end dates for the company
      if (
        !acc[companyName].earliestStartDate ||
        startDate.isBefore(acc[companyName].earliestStartDate)
      ) {
        acc[companyName].earliestStartDate = startDate
      }

      if (
        !acc[companyName].latestEndDate ||
        endDate.isAfter(acc[companyName].latestEndDate)
      ) {
        acc[companyName].latestEndDate = endDate
      }

      acc[companyName].jobs.push({
        jobTitle: job.jobTitle,
        startYear: job.startYear,
        endYear: job.endYear,
        startMonth: job.startMonth,
        endMonth: job.endMonth,
        dateString,
        startDate,
        endDate
      })
      return acc
    }, {})

    // Calculate total duration for each company
    Object.values(groupedByCompany).forEach((company) => {
      if (company.earliestStartDate && company.latestEndDate) {
        // Calculate the difference in months
        const start = company.earliestStartDate
        const end = company.latestEndDate

        // Get difference in months
        const diffMonths = end.diff(start, 'months')
        const years = Math.floor(diffMonths / 12)
        const months = diffMonths % 12

        company.totalDuration = `${years} ${years === 1 ? 'year' : 'years'}${
          months > 0 ? `, ${months} ${months === 1 ? 'month' : 'months'}` : ''
        }`
      }

      company.jobs.sort((a, b) => {
        const aEndYear = a.endYear || new Date().getFullYear()
        const bEndYear = b.endYear || new Date().getFullYear()
        // If end years are the same, compare end months
        if (bEndYear === aEndYear) {
          const aEndMonth = a.endMonth || 12
          const bEndMonth = b.endMonth || 12
          if (bEndMonth !== aEndMonth) {
            return bEndMonth - aEndMonth
          }
        }
        // If end years and months are the same, compare start years
        if (bEndYear - aEndYear === 0) {
          // If start years are the same, compare start months
          if (b.startYear === a.startYear && a.startMonth && b.startMonth) {
            return b.startMonth - a.startMonth
          }
          return b.startYear - a.startYear
        }
        return bEndYear - aEndYear
      })
    })

    return Object.values(groupedByCompany).map((company, companyIndex) => (
      <dd key={companyIndex} className={styles.CompanyGroup}>
        <div className={styles.CompanyLogoColumn}>
          {company.companyLogo && (
            <div className={styles.HistoryLogo}>
              <img src={company.companyLogo} alt="" />
            </div>
          )}
        </div>
        <div className={styles.CompanyNameColumn}>
          {company.companyName && (
            <div className={styles.CompanyNameRow}>
              <h3 className={styles.CompanyName}>{company.companyName}</h3>
              {company.isVerified && <Verified />}
            </div>
          )}
          {company.totalDuration && (
            <div className={styles.CompanyDuration}>
              {company.totalDuration}
            </div>
          )}
        </div>
        <dl className={styles.CompanyJobsList}>
          {company.jobs.map((job, jobIndex) => (
            <div key={jobIndex} className={styles.CompanyJobsListItem}>
              {job.jobTitle && (
                <dt className={styles.CompanyJobsListTitle}>{job.jobTitle}</dt>
              )}
              <dd
                className={styles.CompanyJobsListDate}
                dangerouslySetInnerHTML={{ __html: job.dateString }}
              />
            </div>
          ))}
        </dl>
      </dd>
    ))
  }

  return (
    <ContainerQuery containerName="ExecutiveMeta">
      {(!!history?.length || !!milestones?.length) && (
        <Stack gap="large">
          {history?.length ? (
            <Stack as="dl" className={styles.History}>
              <dt>
                <CompanyTitle title="Work History" />
              </dt>

              {groupAndSortWorkHistory()}
            </Stack>
          ) : null}

          {milestones?.length ? (
            <Stack as="dl" className={styles.Milestones}>
              <dt>
                <CompanyTitle title="Milestones" />
              </dt>

              <div className={styles.MilestonesGrid}>
                {milestones?.map((milestone, index) => (
                  <dd key={index}>
                    <span>{milestone.year}</span> - {milestone.event}
                  </dd>
                ))}
              </div>
            </Stack>
          ) : null}
        </Stack>
      )}
    </ContainerQuery>
  )
}

ExecutiveMeta.propTypes = {
  executive: object.isRequired
}

export default ExecutiveMeta
