.Button {
  &::before {
    background: rgba(255, 255, 255, 0.1) !important;
  }

  svg {
    color: var(--color-theme-button-foreground) !important;
  }
}

.ButtonOutline {
  background-color: transparent !important;
  box-shadow: inset 0 0 0 1px var(--color-theme--primary) !important;
  color: var(--color-theme--primary) !important;

  svg {
    color: var(--color-theme--secondary) !important;
  }

  &::before {
    background: var(--color-theme--primary) !important;
  }

  &:active,
  &:hover {
    color: $color-white-primary !important;
  }
}

.IsWhite {
  &:not(.ButtonOutline) {
    background-color: $color-white-primary !important;
    color: var(--color-theme--primary) !important;

    &::before {
      background: rgba(0, 0, 0, 0.1) !important;
    }
  }

  &.ButtonOutline {
    box-shadow: inset 0 0 0 1px $color-white-primary !important;
    color: $color-white-primary !important;

    &::before {
      background: rgba(255, 255, 255, 0.1) !important;
    }
  }
}
