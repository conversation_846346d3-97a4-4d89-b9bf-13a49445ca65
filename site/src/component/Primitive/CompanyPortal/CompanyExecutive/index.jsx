import styles from './CompanyExecutive.module.scss'
import Type from '@/component/Primitive/Type'
import SmartLink from '@/component/Primitive/SmartLink'
import Icon from '@/component/Primitive/Icon'
import React from 'react'
import { array, bool, object, string } from 'prop-types'
import Verified from '@/component/Primitive/CompanyPortal/Verified'

const CompanyExecutive = ({
  images,
  slug,
  name,
  jobTitle,
  company,
  isVerified = false
}) => {
  const src =
    images?.headshotv2_1x1_720?.[0]?.url ||
    images?.headshot_220x347_720?.[0]?.url
  const props = {
    to: '/executive',
    as: `/executive/${slug}`
  }

  if (company) {
    props.to = '/company/[slug]/executives/[exeSlug]'
    props.as = `/company/${company.slug}/executives/${slug}`
  }

  return (
    <div className={styles.Card}>
      {src && (
        <div className={styles.CardImage}>
          <div className={styles.CardImageBackgroundWrapper}>
            <img src={src} alt="" />
          </div>

          <img src={src} alt="" className={styles.CardImageForeground} />

          {isVerified && <Verified absolute />}
        </div>
      )}

      <div className={styles.CardBottom}>
        <hgroup className={styles.CardHGroup}>
          <Type
            size="body2"
            as="h3"
            weight="700"
            className={styles.CardHeading}
          >
            <SmartLink {...props} className={styles.CardAnchor}>
              {name}
            </SmartLink>

            <Icon
              type="arrow-right"
              width="7"
              height="12"
              className={styles.CardIcon}
            />
          </Type>

          {jobTitle && (
            <Type size="body4" as="p" weight="500">
              {jobTitle}
            </Type>
          )}
        </hgroup>
      </div>
    </div>
  )
}

CompanyExecutive.propTypes = {
  images: array,
  name: string,
  jobTitle: string,
  slug: string,
  company: object,
  isVerified: bool
}

export default CompanyExecutive
