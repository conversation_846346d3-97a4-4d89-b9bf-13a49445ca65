import React, { useEffect, useRef } from 'react'
import { string } from 'prop-types'
import styles from './LinkedInFollowCompany.module.scss'
import linkedinIds from './linkedinIds.json'

const LinkedInFollowCompany = ({ subdomain, counter = 'bottom' }) => {
  const containerRef = useRef(null)
  const scriptLoadedRef = useRef(false)

  // Look up the LinkedIn company ID from the subdomain, fallback to default
  const companyId = subdomain
    ? linkedinIds[subdomain] || linkedinIds.default
    : linkedinIds.default

  useEffect(() => {
    if (!companyId || typeof window === 'undefined') return

    // Only load the LinkedIn script once globally
    if (!window.IN && !scriptLoadedRef.current) {
      const script = document.createElement('script')
      script.src = 'https://platform.linkedin.com/in.js'
      script.type = 'text/javascript'
      script.async = true
      script.text = 'lang: en_US'

      document.body.appendChild(script)
      scriptLoadedRef.current = true

      // Parse the plugin after script loads
      script.onload = () => {
        if (window.IN && window.IN.parse) {
          window.IN.parse()
        }
      }
    } else if (window.IN && window.IN.parse) {
      // If script already loaded, just parse the new widget
      window.IN.parse()
    }
  }, [companyId])

  if (!companyId) return null

  return (
    <div ref={containerRef} className={styles.LinkedInFollowCompany}>
      <script
        type="IN/FollowCompany"
        data-id={companyId}
        data-counter={counter}
      />
    </div>
  )
}

LinkedInFollowCompany.propTypes = {
  subdomain: string,
  counter: string
}

export default LinkedInFollowCompany
