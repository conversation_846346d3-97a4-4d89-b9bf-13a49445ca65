import React, { useState, useEffect } from 'react'
import { func, string, bool, shape } from 'prop-types'
import cx from 'classnames'
import { createPortal } from 'react-dom'
import useMedia from '@/hook/useMedia'

import styles from './MagazineCTA.module.scss'
import SmartLink from '../SmartLink'
import Icon from '../Icon'
import Type from '../Type'
import IconButton from '../IconButton'

const MagazineCTA = ({
  imageUrl,
  title,
  to,
  as,
  variation,
  className,
  classNames
}) => {
  const isTablet = useMedia('(max-width: 599px)')
  const [visible, setVisible] = useState(false)

  const handleDismiss = () => {
    window.localStorage.setItem('dismissedMagazineCTA', true)
    setVisible(false)
  }

  useEffect(() => {
    const isServer = typeof window === 'undefined'
    if (isServer) return
    const dismissed = JSON.parse(
      window.localStorage.getItem('dismissedMagazineCTA')
    )
    if (dismissed) {
      setVisible(false)
    } else {
      setVisible(true)
    }
  }, [])

  if (isTablet) {
    return createPortal(
      <MagazineCTAComponent
        imageUrl={imageUrl}
        title={title}
        to={to}
        as={as}
        visible={visible}
        onDismiss={handleDismiss}
        className={className}
        classNames={classNames}
      />,
      document.getElementById('magazineCTA')
    )
  } else
    return (
      <MagazineCTAComponent
        imageUrl={imageUrl}
        title={title}
        to={to}
        as={as}
        variation={variation}
        className={className}
        classNames={classNames}
      />
    )
}

export default MagazineCTA

MagazineCTA.propTypes = {
  imageUrl: string.isRequired,
  title: string.isRequired,
  to: string.isRequired,
  as: string.isRequired,
  variation: string,
  className: string,
  classNames: shape({
    title: string,
    button: string
  })
}

const MagazineCTAComponent = ({
  imageUrl,
  title,
  to,
  as,
  onDismiss,
  visible,
  variation,
  className,
  classNames
}) => {
  if (variation === 'large')
    return (
      <div
        className={cx(
          styles.MagazineCTA,
          !visible && styles.hidden,
          styles.large,
          className
        )}
      >
        <SmartLink to={to} as={as} className={styles.MagazineCTAWrapper}>
          <div className={styles.Content}>
            <div className={styles.ContentTop}>
              <div className={styles.Tag}>
                <Type size="body3" weight="medium">
                  Latest
                </Type>
              </div>
              <div className={styles.Title}>
                <Type
                  size="body1"
                  weight="medium"
                  as="h3"
                  className={classNames?.title}
                >
                  Digital Magazine
                </Type>
              </div>
            </div>
            <div className={cx(styles.CTA, classNames?.button)}>
              <Type size="body3" weight="medium">
                Read Now{' '}
              </Type>
              <Icon
                className={styles.ArrowIcon}
                type="arrow-right"
                a11ytext="Read more"
                width={7.5}
                height={12}
              />
            </div>
          </div>
          <img loading="lazy" src={imageUrl} alt={title} />
        </SmartLink>
        {onDismiss && (
          <IconButton
            small
            className={styles.Dismiss}
            onClick={onDismiss}
            icon="close"
            a11ytext="Dismiss"
            width={37}
            height={37}
          />
        )}
      </div>
    )

  return (
    <div
      className={cx(styles.MagazineCTA, !visible && styles.hidden, className)}
    >
      <SmartLink to={to} as={as} className={styles.MagazineCTAWrapper}>
        <img loading="lazy" src={imageUrl} alt={title} />
        <div className={styles.Content}>
          <div className={styles.Title}>
            <Type size="body2" weight="medium">
              {title}
            </Type>
          </div>
          <div className={cx(styles.CTA, classNames?.button)}>
            <Type size="body2" weight="medium">
              Click here{' '}
            </Type>
            <Icon
              className={styles.ArrowIcon}
              type="arrow-right"
              a11ytext="Read more"
              width={7.5}
              height={12}
            />
          </div>
        </div>
      </SmartLink>
      {onDismiss && (
        <IconButton
          small
          className={styles.Dismiss}
          onClick={onDismiss}
          icon="close"
          a11ytext="Dismiss"
          width={37}
          height={37}
        />
      )}
    </div>
  )
}

MagazineCTAComponent.propTypes = {
  imageUrl: string.isRequired,
  title: string.isRequired,
  to: string.isRequired,
  as: string.isRequired,
  onDismiss: func,
  visible: bool,
  variation: string,
  className: string,
  classNames: shape({
    title: string,
    button: string
  })
}

export const MagazineCTAContainer = () => (
  <div id="magazineCTA" className={styles.MagazineCTAContainer} />
)
