.ButtonStandard {
  background-color: $color-white-primary;
  color: $color-black-primary;
  padding: spacing(1.75) spacing(5);
  position: relative;
  text-align: center;
  text-decoration: none;
  transform: perspective(1px) translateZ(0);
  transition: color 250ms ease-in-out;
  @include font-smoothing();

  &::before {
    background: rgba($color-white-primary, 0.1);
    content: '';
    inset: 0;
    position: absolute;
    transform: scaleX(0);
    transform-origin: 0 50%;
    transition-duration: 0.3s;
    transition-property: transform;
    transition-timing-function: ease-out;
    z-index: -1;
  }

  &:active,
  &:hover,
  &:focus {
    &::before {
      transform: scaleX(1);
    }
  }
}

.ButtonStandardContent {
  align-items: center;
  display: flex;
  justify-content: center;

  &.hasIcon {
    flex-wrap: nowrap;
    justify-content: space-between;
    margin-top: 2px;

    &.reversed {
      flex-direction: row-reverse;
    }
  }
}

.ButtonStandardSpinner {
  left: 50%;
  opacity: 0;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
}

.primary {
  background-color: var(
    --color-theme-button-background,
    --color-theme--secondary
  );
  color: var(--color-theme-button-foreground);

  //&:hover,
  //&:focus,
  //&:active {
  //  background-color: darken($color-grey-secondary, 10);
  //}

  &.outline {
    background-color: var(--color-theme-button-foreground);
    box-shadow: inset 0 0 0 1px
      var(--color-theme-button-background, --color-theme--secondary);
    color: var(--color-theme-button-background, --color-theme--secondary);

    &::before {
      background-color: var(
        --color-theme-button-background,
        --color-theme--secondary
      );
    }
  }
}

.outline {
  background: transparent;
  box-shadow: inset 0 0 0 1px $color-misc-divider;
  color: $color-black-primary;

  &.primary {
    background: transparent;
    box-shadow: inset 0 0 0 1px
      var(--color-theme-button-background, --color-theme--secondary);
  }

  &.inverse {
    [class*='Row_black'] &,
    [class*='VideoGrid_VideoGrid'] & {
      color: $color-white-primary;
    }
  }

  &::before {
    background-color: var(
      --color-theme-button-background,
      --color-theme--secondary
    );
  }

  &:hover,
  &:focus,
  &:active {
    box-shadow: inset 0 0 0 1px
      var(--color-theme-button-background, --color-theme--secondary);
    color: var(--color-theme-button-foreground);
  }
}

.small {
  padding: spacing(1.25) spacing(2);
}

.large {
  padding: spacing(2) spacing(5);
}

.bloated {
  padding: spacing(2.5) spacing(5);
}

.huge {
  padding: spacing(2.1) spacing(8.375);
}

.disabled {
  background-color: $color-misc-divider;
  border-color: $color-misc-divider;
}

.loading {
  pointer-events: none;

  .ButtonStandardContent {
    opacity: 0;
    transition-duration: $duration-shortest;
  }

  .ButtonStandardSpinner {
    opacity: 1;
  }
}

@include mq($max: $breakpoint-desktop - 1) {
  .Text {
    .ButtonStandardContent.hasIcon & {
      margin-right: spacing(2);
    }
  }
}

.Icon {
  @include mq($breakpoint-desktop) {
    margin-bottom: 3px;
    margin-left: spacing(2);

    .reversed & {
      margin-left: 0;
      margin-right: spacing(2);
    }
  }
}
