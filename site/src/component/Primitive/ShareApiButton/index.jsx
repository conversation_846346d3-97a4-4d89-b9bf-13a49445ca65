import React, { useState } from 'react'
import { string } from 'prop-types'

import ButtonStandard from '../ButtonStandard'
import Icon from '../Icon'

const ShareButton = (props) => {
  const [shared, setShared] = useState(false)

  if (!navigator.share) return null

  const { label, labelShared, text, title, url } = props

  const handleShare = () => {
    navigator.share({ title, text, url }).then(() => setShared(true))
    // .catch(error => console.log('Error sharing', error))
  }

  return (
    <ButtonStandard onClick={handleShare}>
      {!shared && label}
      {(shared && labelShared) || label}
      {shared && <Icon type="check" a11ytext="" width={20} />}
    </ButtonStandard>
  )
}

ShareButton.propTypes = {
  label: string.isRequired,
  labelShared: string,
  text: string,
  title: string,
  url: string.isRequired
}

export default ShareButton
