import React, { useEffect, useState, useContext } from 'react'
import classNames from 'classnames'
import { func, object, bool } from 'prop-types'
import { useRouter } from 'next/router'

import styles from './SearchOverlay.module.scss'

import Icon from '../Icon'
import IconButton from '../IconButton'
import Spinner from '../Spinner'
import { ThemeContext } from '@/component/Context/ThemeContext'

const SearchOverlay = ({
  onChange,
  onSubmit,
  setRef,
  toggleSearch,
  inverse,
  isFeatureEnabled
}) => {
  const router = useRouter()
  const { theme } = useContext(ThemeContext)
  const [value, updateValue] = useState('')
  const [submitted, setSubmitted] = useState(false)

  const handleChange = (e) => {
    updateValue(e.target.value)
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    setSubmitted(true)
    onSubmit && onSubmit(e)
    router.push({
      pathname: '/search',
      query: { q: value }
    })
  }

  useEffect(() => {
    onChange && onChange(value)

    if (submitted) {
      onSubmit && onSubmit()
      setSubmitted(true)
    }
  }, [onChange, onSubmit, submitted, value])
  if (submitted) return null
  return (
    <form
      action="/search"
      className={classNames(styles.SearchOverlay, inverse && styles.inverse)}
      onSubmit={handleSubmit}
      ref={setRef}
      id="header-search"
    >
      <div className={styles.SearchOverlayControlWrapper}>
        <button className={styles.SearchOverlaySubmit} type="submit">
          {submitted && <Spinner />}
          {!submitted && <Icon type="search" a11ytext="SearchButton" />}
        </button>

        <label className={styles.SearchOverlayLabel} htmlFor="search-query">
          Search
        </label>

        <input
          id="search-query"
          className={classNames(styles.SearchOverlayControl, styles[theme])}
          type="text"
          name="q"
          onChange={handleChange}
          value={value}
          placeholder={
            !isFeatureEnabled('executiveSearch') &&
            !isFeatureEnabled('companySearch')
              ? 'Lists, Events, Interviews...'
              : 'Lists, Events, Interviews, Executives, Companies...'
          }
          // This is an acceptable autoFocus use-case, as the User will have
          // just manually triggered the search form, so will be expecting the
          // focus jump.
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus
        />
        <IconButton
          onClick={toggleSearch}
          icon="close"
          a11ytext="Toggle search"
          className={styles.CloseIcon}
        />
      </div>
    </form>
  )
}

SearchOverlay.defaultProps = {
  autocompletions: [],
  suggestions: []
}

SearchOverlay.propTypes = {
  onChange: func,
  onSubmit: func,
  setRef: object,
  toggleSearch: func,
  inverse: bool,
  isFeatureEnabled: func
  // __url: func.isRequired
}

export default SearchOverlay
