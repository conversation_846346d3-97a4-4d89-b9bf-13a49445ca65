.LogoDark {
  display: none;

  [class*='Row_grey-dark'] &,
  [class*='Row_black'] & {
    display: block;
  }
}

.LogoLight {
  [class*='Row_grey-dark'] &,
  [class*='Row_black'] & {
    display: none;
  }
}

.EventHero {
  overflow: hidden;
  padding-bottom: spacing(4);
  padding-top: spacing(4);
  position: relative;

  @include mq($breakpoint-desktop) {
    height: 0;
    padding-bottom: 45%;
    position: relative;
  }

  @include mq($breakpoint-desktopMedium) {
    padding-bottom: 35%;
  }

  @include mq($breakpoint-desktopLarge) {
    padding-bottom: 29%;
  }

  &.hideEventLogo {
    @include mq($breakpoint-desktop) {
      padding-bottom: 37.5%;
    }

    @include mq($breakpoint-desktopMedium) {
      padding-bottom: 30%;
    }

    @include mq($breakpoint-desktopLarge) {
      padding-bottom: 22%;
    }

    .Logo {
      display: none !important;
    }
  }

  &.useMaxHeight {
    .Logo {
      max-height: 200px;
      max-width: 100%;
    }
  }

  &.alignLeft {
    .TitleText,
    .Description {
      margin-left: unset;
    }

    .Stack {
      align-items: flex-start !important;
      text-align: left !important;
    }

    .ContentWrapper {
      @include mq($breakpoint-desktop) {
        justify-content: flex-start !important;
        text-align: left !important;
      }
    }

    .ButtonGroup {
      @include mq($breakpoint-desktop) {
        justify-content: flex-start;
      }
    }

    .Title,
    .Subtitle,
    .Description {
      margin-left: unset;
    }
  }

  &.useAsHeading {
    height: auto;
    padding-bottom: spacing(1);
    padding-top: spacing(1);

    @include mq($breakpoint-desktop) {
      padding-bottom: spacing(4);
      padding-top: spacing(4);
    }

    .EventHeroInner {
      @include mq($breakpoint-desktop) {
        left: unset;
        position: relative;
        top: unset;
        transform: unset;
        width: unset;
      }
    }

    .ContentWrapper {
      padding-bottom: spacing(0) !important;
      padding-top: spacing(0) !important;

      @include mq($breakpoint-desktop) {
        padding-bottom: spacing(0) !important;
        padding-top: spacing(0) !important;
      }
    }
  }

  &.useAsHeading.sponsor {
    height: auto;
    padding-bottom: spacing(4);
    padding-top: spacing(4);

    @include mq($breakpoint-desktop) {
      padding-bottom: spacing(4);
      padding-top: spacing(4);
    }
  }

  &.useAsHeading.sponsor.hideEventLogo {
    padding-bottom: spacing(4);
    padding-top: spacing(6);
  }

  &.sponsor {
    .TitleWrapper {
      gap: spacing(2);
    }

    .Subtitle {
      background-color: rgba(0, 0, 0, 0.8);
      color: $color-white-primary;
      padding: spacing(0.5) spacing(1);
    }

    .Highlight {
      color: var(--row-background-color, $color-black-primary) !important;
      font-weight: 900;
    }
  }

  &.sponsor.dark {
    .Highlight {
      color: $color-white-primary !important;
      font-weight: 900;
    }
  }

  .EventHeroInner {
    position: relative;
    z-index: 2;

    @include mq($breakpoint-desktop) {
      display: flex;
      left: 50%;
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    .ImagesWrapper {
      & > * {
        height: 100%;
      }

      @include mq($breakpoint-desktop) {
        flex: 1;
        margin-bottom: unset;
      }

      .ImageWrapper {
        position: relative;

        .Image {
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .Stack {
      align-items: center;
      display: flex;
      flex-direction: column;
      text-align: center;
    }

    .ContentWrapper {
      padding-bottom: spacing(8);
      padding-top: spacing(8);

      @include mq($breakpoint-desktop) {
        align-items: center;
        display: flex;
        flex: 1.5;
        justify-content: center;
        text-align: center;
      }

      @include mq($breakpoint-desktop) {
        flex: 1;
      }
    }

    &.inverse {
      .ImagesWrapper {
        @include mq($breakpoint-desktop) {
          order: 2;
        }
      }

      .ContentWrapper {
        @include mq($breakpoint-desktop) {
          order: 1;
        }
      }
    }
  }

  [class*='Row_grey-dark'] &,
  [class*='Row_black'] & {
    @include darkMode;

    background-color: $color-black-brand;

    .YoutubeEmbedOverlay {
      background-color: rgba(0, 0, 0, 0.2);
      background-image: radial-gradient(rgba(0, 0, 0, 0.4), transparent);
    }
  }

  [class*='Row_grey-dark'] & {
    background-color: $color-black-alt;
  }
}

.EventHeroImage {
  display: block;
  height: 100%;
  left: 0;
  opacity: 0.5;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1;

  img {
    height: 100%;
    object-fit: cover;
    width: 100%;
  }

  &::before {
    background-color: rgba(0, 0, 0, 0.21);
    content: '';
    inset: 0;
    position: absolute;
    z-index: 2;
  }

  @include mq($breakpoint-desktop) {
    &.parallax {
      background-attachment: fixed;
    }
  }
}

.SponsorLogo {
  /* stylelint-disable-next-line property-no-unknown */
  aspect-ratio: 1 / 1;
  background: $color-white-primary;
  max-width: 75px;

  @include mq($breakpoint-tablet) {
    max-width: 100px;
  }
}

.Logo {
  max-height: 60px;

  @include mq($breakpoint-tablet) {
    max-height: 80px;
  }
}

.TitleWrapper {
  display: flex;
  flex-direction: column;

  @include mq($breakpoint-tablet) {
    align-items: flex-end;
    flex-direction: row;
    gap: spacing(2);
    margin-bottom: spacing(2);
  }

  @include mq($breakpoint-desktop) {
    align-items: flex-start;
  }
}

.TitleText {
  display: flex;
  flex-direction: column;
  gap: spacing(1);
  margin-left: auto;
  margin-right: auto;
}

.Title {
  @include mq($breakpoint-desktop) {
    margin-left: auto;
    margin-right: auto;
    max-width: unset;
  }
}

.Subtitle {
  margin-left: auto;
  margin-right: auto;
  margin-top: spacing(2);
  max-width: 400px;
  font-weight: 500;
  font-size: 16px;
}

@media (min-width: 768px) {
  .Subtitle {
    font-size: 18px;
  }
}

.Description {
  margin-left: auto;
  margin-right: auto;
  max-width: 500px;

  .DescriptionInner {
    p {
      font-size: inherit !important;
    }

    li {
      font-size: 0.8em !important;
    }
  }
}

.ButtonGroup {
  @include mq($breakpoint-desktop) {
    display: flex;
    justify-content: center;
  }
}

.YoutubeEmbed {
  height: 185vh;
  left: 50%;
  pointer-events: none;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 400vw;

  @include mq($breakpoint-tablet) {
    width: 200vw;
  }

  @include mq($breakpoint-desktop) {
    height: 135vh;
  }

  .YoutubeEmbedOverlay {
    background-color: rgba(255, 255, 255, 0.9);
    height: 100%;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 100%;
  }

  iframe {
    height: 100%;
    width: 100%;
  }

  &.show {
    opacity: 1;
  }
}

.HighlightWrapper {
  display: grid;
  font-weight: 300;
  gap: spacing(2);

  .Highlight {
    font-weight: 500;
  }
}

.EventVideoOverlay {
  background-color: rgba(0, 0, 0, 0.21);
  inset: 0;
  position: absolute;

  &::before {
    background-color: var(--color-theme--event);
    content: '';
    inset: 0;
    mask-image: linear-gradient(to right, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
    opacity: 0.225;
    position: absolute;
  }
}

.SrOnly {
  position: absolute;
  overflow: hidden;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
  clip: rect(0 0 0 0);
  clip-path: polygon(0 0, 0 0, 0 0);
  white-space: nowrap;
}
