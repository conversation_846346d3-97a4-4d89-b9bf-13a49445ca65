import React from 'react'
import styles from './Highlight.module.scss'
import { any, string, shape, bool } from 'prop-types'
import { createNodes } from './lib/node-creator'
import cx from 'classnames'

export const Highlight = ({ children, className, classNames, options }) => {
  const { preventDefaultFontSize = false, fixLineReturns = false } =
    options || {}
  let isOpenQuote = true

  const getFontSize = () => {
    if (preventDefaultFontSize) return ''
    if (children.includes('<h1>')) return 'large'
    if (children.includes('<h2>')) return 'medium'
    return 'small'
  }

  let html = children
    .replaceAll('h1', 'p')
    .replaceAll('h2', 'p')
    .replaceAll('h3', 'p')
    .replaceAll('h4', 'p')
    .replaceAll('h5', 'p')
    .replaceAll('h6', 'p')

  if (fixLineReturns) {
    html = html.replaceAll(/\r\n|\n|\r/g, '<br>')
  }

  if (typeof html === 'string' && !html.includes('<')) {
    return (
      <span dangerouslySetInnerHTML={{ __html: html }} className={className} />
    )
  }
  const nodes = createNodes(html, 'p')

  const paintTree = (node) => {
    if (!node.children && node.value) {
      return <span dangerouslySetInnerHTML={{ __html: node.value }} />
    }
    return node.children.map((child, index) => {
      if (!child.tag) {
        return (
          <span key={index} dangerouslySetInnerHTML={{ __html: child.value }} />
        )
      }
      if (child.tag === 'em') {
        return (
          <em
            key={index}
            dangerouslySetInnerHTML={{ __html: child.value }}
            className={cx(styles.Highlight, styles.thin, classNames?.em)}
          />
        )
      }
      if (child.tag === 'strong') {
        let __html = child.value
        if (['&quot;', '"'].includes(__html)) {
          if (isOpenQuote) {
            __html = '“'
            isOpenQuote = false
          } else {
            __html = '”'
          }
        }

        return (
          <strong
            key={index}
            dangerouslySetInnerHTML={{ __html }}
            className={cx(styles.Highlight, classNames?.strong)}
          />
        )
      }
      return null
    })
  }

  return (
    <span
      className={cx(className, styles.HighlightWrapper, styles[getFontSize()])}
      data-font-size={getFontSize()}
    >
      {nodes.map((node, index) => (
        <span className={styles.HighlightInner} key={index}>
          {paintTree(node)}
        </span>
      ))}
    </span>
  )
}

Highlight.propTypes = {
  children: any,
  className: string,
  classNames: shape({
    strong: string
  }),
  options: shape({
    preventDefaultFontSize: bool
  })
}
