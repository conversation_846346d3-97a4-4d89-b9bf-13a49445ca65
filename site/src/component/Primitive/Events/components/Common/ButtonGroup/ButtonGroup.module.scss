.ButtonGroup {
  align-items: flex-start;
  background-color: inherit;
  display: flex;
  flex-direction: column;
  gap: spacing(1);

  &.center {
    align-items: center;
    justify-content: center;
  }

  @include mq($breakpoint-desktop) {
    flex-direction: row;
  }

  a {
    text-decoration: none;
  }
}

.Button {
  background-color: var(--color-theme--event) !important;
  color: var(--color-theme--event-button-foreground) !important;

  * {
    color: inherit !important;
  }
}

.Button.primary--black-text {
  background-color: var(--color-theme--event) !important;
  color: $color-black-primary !important;

  * {
    color: $color-black-primary !important;
  }

  &:hover,
  &:focus,
  &:active {
    &::before {
      background-color: rgba($color-black-primary, 0.1);
    }
  }
}

.Button.primary--awards {
  background-color: #ffba2f !important;
  color: $color-black-primary !important;

  * {
    color: $color-black-primary !important;
  }

  &:hover,
  &:focus,
  &:active {
    &::before {
      background-color: rgba($color-black-primary, 0.1);
    }
  }
}

.Button.secondary {
  background: 0 0 !important;
  box-shadow: inset 0 0 0 1px var(--color-theme--event) !important;
  color: var(--row-background-color, $color-black-primary) !important;

  &::before {
    background-color: var(--color-theme--event);
  }

  [class*='UpcomingEventsCarousel'] & {
    color: $color-white !important;
  }

  &:hover,
  &:focus,
  &:active {
    color: var(--color-theme--event-button-foreground) !important;
  }
}

.Button.tertiary {
  background-color: $color-black-primary !important;

  * {
    color: $color-white-primary !important;
  }

  &::before {
    background-color: rgba($color-white-primary, 0.1);
  }

  [class*='Row_grey-dark'] &,
  [class*='Row_black'] & {
    background-color: $color-white-primary !important;

    &::before {
      background-color: $color-black-primary;
    }

    * {
      color: $color-black-primary !important;
      transition: color 250ms ease-in-out;
    }

    &:hover,
    &:focus,
    &:active {
      * {
        color: $color-white-primary !important;
      }
    }
  }

  [class*='Row_grey-dark'] & {
    &::before {
      background-color: $color-black-alt;
    }

    * {
      color: $color-black-alt !important;
    }
  }
}

@media (min-width: $breakpoint-desktop) {
  .HasBigButton {
    align-items: start !important;
    display: grid !important;
    grid-template-columns: repeat(2, max-content);

    > a:first-of-type {
      grid-area: 1 / 1 / 3 / 3;

      > span {
        justify-content: center !important;
      }
    }
  }
}
