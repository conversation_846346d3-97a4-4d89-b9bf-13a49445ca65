import React, { useMemo } from 'react'
import { any, arrayOf, bool, oneOf, shape, string } from 'prop-types'
import ButtonStandard from '@/component/Primitive/ButtonStandard'
import styles from './ButtonGroup.module.scss'
import cx from 'classnames'

const determineNewTabBehaviour = (button) => {
  if (button.type !== 'link') return button
  const link = button.link
  if (!link) return button
  if (link.includes('http:') || link.includes('https:') || link.includes('www'))
    return button
  if (link.includes('#')) {
    if (link.startsWith('#')) return { ...button, openInNewTab: false }
    if (typeof window === 'undefined') return button
    const currentPathWithoutHash = window.location.pathname
    const linkWithoutHash = link.split('#')[0]
    if (!currentPathWithoutHash) return button
    if (!linkWithoutHash) return button
    if (currentPathWithoutHash.includes(linkWithoutHash)) {
      return { ...button, link: '#' + link.split('#')[1], openInNewTab: false }
    }
    return button
  }
  return button
}

export const ButtonGroup = ({
  group,
  className,
  classNames,
  center,
  theme,
  hasBigButton = false
}) => {
  const resolvedButtons = useMemo(() => {
    return group.map((button) => determineNewTabBehaviour(button))
  }, [group])

  return (
    <div
      className={cx(
        styles.ButtonGroup,
        center && styles.center,
        className,
        hasBigButton && styles.HasBigButton
      )}
    >
      {resolvedButtons?.map((button, i) => {
        if (button.hidden) return null
        const commonProps = {
          key: button.eventLayoutSlug
            ? `${button.eventLayoutSlug}-${i}`
            : `${button.link}-${i}`,
          primary: true,
          outline: button.variant === 'secondary',
          size: 'small',
          icon: !button.hasNoArrow && 'arrow-right',
          id: button.buttonId,
          className: cx(
            styles.Button,
            button.buttonClassName,
            classNames?.button,
            styles[button.variant],
            styles[theme],
            button.hasNoHover && styles.noHover
          ),
          ...(button.openInNewTab && {
            target: '_blank',
            rel: 'noopener noreferrer'
          })
        }

        const buttonContent = button.labelOverride || button.label

        if (button.eventLayoutSlug || button.eventLayoutSlug === '') {
          return (
            <ButtonStandard
              {...commonProps}
              to={
                button.eventId
                  ? '/events/[slug]/[oslug]'
                  : '/events/[...section]'
              }
              href={`${button.baseUrl}${
                button.eventLayoutSlug ? `/${button.eventLayoutSlug}` : ''
              }`}
            >
              {buttonContent}
            </ButtonStandard>
          )
        }

        return (
          // eslint-disable-next-line react/jsx-key
          <ButtonStandard {...commonProps} href={button.link}>
            {buttonContent}
          </ButtonStandard>
        )
      })}
    </div>
  )
}

ButtonGroup.propTypes = {
  center: bool,
  className: string,
  classNames: shape({
    button: string
  }),
  theme: oneOf(['light', 'dark', 'grey']),
  group: arrayOf(
    shape({
      label: string,
      link: string,
      action: string,
      buttonId: any,
      buttonClassName: any
    })
  ),
  hasBigButton: bool
}
