import React, { useMemo, useState } from 'react'
import { array, object, string } from 'prop-types'
import ListView from '@/component/Primitive/Events/components/EventAgendaNavigator/components/List/ListView'
import ButtonStandard from '@/component/Primitive/ButtonStandard'
import cx from 'classnames'
import styles from './EventAgendaNavigator.module.scss'
import Container from '@/component/Primitive/Container'
import { TimelineView } from '@/component/Primitive/Events/components/EventAgendaNavigator/components/Timeline/TimelineView'
import Stack from '@/component/Primitive/Stack'
import Type from '@/component/Primitive/Type'
import { ButtonGroup, Highlight } from '@/component/Primitive/Events'
import moment from 'moment/moment'
import { getImage } from '@/component/Primitive/Events/components/EventAgendaNavigator/utils/getImage'
import { getTimezoneAbbreviation } from '@/lib/timezoneAbbreviationMiddleware'

const VIEWS = ['list', 'timeline']

const EventAgendaNavigator = ({
  pageData,
  baseURL,
  buttonGroup,
  locationFilters,
  eventUmbrella,
  ...other
}) => {
  const [view, setView] = useState(VIEWS[0])
  const [selectedLocation, setSelectedLocation] = useState('All')
  const { event } = eventUmbrella
  const { startDate, endDate, agenda, locations, images } = event
  const formattedStartDate = moment(startDate).format('dddd Do MMMM')
  const formattedEndDate = moment(endDate).format('dddd Do MMMM')
  const dateRange =
    formattedStartDate === formattedEndDate
      ? `📅 ${formattedStartDate}`
      : `📅 ${formattedStartDate} - ${formattedEndDate}`

  const timezoneAbbreviation = useMemo(() => {
    const zone = eventUmbrella?.event?.timezone
    const firstEventEndDate = eventUmbrella?.event?.agenda?.[0]?.endDate
    if (zone && firstEventEndDate) {
      return getTimezoneAbbreviation(firstEventEndDate, zone)
    }

    return 'unset'
  }, [eventUmbrella])

  const formatDate = (dateString) => moment(dateString).format('D.M.YYYY')

  const backgroundImage = getImage(images, 'desktop_background', '72x17', 720)
    ?.url

  const embellishedEvent = {
    ...event,
    agenda: agenda?.map((item) => {
      const speakers = item?.speakers
      return {
        ...item,
        speakers: speakers?.map((speaker) => {
          const speakerImage = getImage(speaker?.images, 'speaker', '1x1', 320)
            ?.url
          return {
            ...speaker,
            image: {
              alt: 'Event Agenda Speaker Placeholder',
              sizes: ['min-width(960px) 50vw', '100vw'],
              src: speakerImage
            }
          }
        })
      }
    })
  }

  const filterableLocations = useMemo(() => {
    const arr = [{ key: 'all', name: 'All' }]

    if (typeof locationFilters !== 'object' || locationFilters?.length <= 1) {
      return arr
    }

    for (const location of locations) {
      if (locationFilters?.includes(location.key)) {
        arr.push(location)
      }
    }

    return arr
  }, [locationFilters, locations])

  const dayLabels = useMemo(
    () =>
      Array.from(new Set(agenda.map((item) => formatDate(item.startDate)))).map(
        (_, index) => 'Day ' + (index + 1)
      ),
    [agenda]
  )

  const sortedAgenda = useMemo(() => {
    return agenda.sort((a, b) => new Date(a.startDate) - new Date(b.startDate))
  }, [agenda])

  const agendaItemDayMap = useMemo(() => {
    const formattedStartDates = Array.from(
      new Set(agenda.map((item) => formatDate(item.startDate)))
    )
    return agenda.reduce((acc, curr) => {
      const formattedStartDate = formatDate(curr.startDate)
      if (!acc[formattedStartDate]) {
        acc[formattedStartDate] = {
          dayIndex:
            formattedStartDates.findIndex(
              (date) => formattedStartDate === date
            ) + 1,
          stages: []
        }
      } else {
        acc[formattedStartDate].dayIndex =
          formattedStartDates.findIndex((date) => formattedStartDate === date) +
          1
      }

      acc[formattedStartDate].startDate = curr.startDate
      acc[
        formattedStartDate
      ].dayString = `Day ${acc[formattedStartDate].dayIndex}`

      acc[formattedStartDate].stages.push(curr.location.name)
      acc[formattedStartDate].stages = Array.from(
        new Set(acc[formattedStartDate].stages)
      ).sort((a, b) => {
        const aI = locations.findIndex((loc) => loc.name === a)
        const bI = locations.findIndex((loc) => loc.name === b)
        return aI - bI
      })
      return acc
    }, {})
  }, [agenda, locations])

  const [selectedDay, setSelectedDay] = useState(() => {
    // If today is not between the first and last day, select the first day
    // Else select today
    const now = moment(new Date())
    if (
      now.isBetween(
        moment(sortedAgenda[0].startDate),
        moment(sortedAgenda[sortedAgenda?.length - 1].endDate),
        'day',
        '[]'
      )
    ) {
      const formattedDate = formatDate(now)
      if (Object.keys(agendaItemDayMap).includes(formattedDate))
        return 'Day ' + agendaItemDayMap[formattedDate].dayIndex
      else return 'Day 1'
    } else {
      return (
        'Day ' +
        agendaItemDayMap[formatDate(sortedAgenda[0].startDate)].dayIndex
      )
    }
  })

  const currentDateItem = useMemo(() => {
    return Object.values(agendaItemDayMap).find(
      (item) => item.dayString === selectedDay
    )
  }, [agendaItemDayMap, selectedDay])

  const ViewFilters = () => {
    return (
      <div className={styles.EventAgendaNavigatorTop}>
        <dl className={styles.EventAgendaNavigatorDl}>
          <Type as="dt" size="body3" weight="bold">
            View:
          </Type>
          <div className={styles.ButtonGroup}>
            <dd>
              <ButtonStandard
                primary
                size="small"
                onClick={() => setView(VIEWS[0])}
                className={cx(
                  styles.Button,
                  view === VIEWS[0] && styles.active
                )}
                disabled={view === VIEWS[0]}
              >
                List
              </ButtonStandard>
            </dd>

            <dd>
              <ButtonStandard
                primary
                size="small"
                onClick={() => setView(VIEWS[1])}
                className={cx(
                  styles.Button,
                  view === VIEWS[1] && styles.active
                )}
                disabled={view === VIEWS[1]}
              >
                Timeline
              </ButtonStandard>
            </dd>
          </div>
        </dl>

        {dayLabels.length > 1 && (
          <dl className={styles.EventAgendaNavigatorDl}>
            <Type as="dt" size="body3" weight="bold">
              Day:
            </Type>
            <div className={styles.ButtonGroup}>
              {dayLabels.map((label) => (
                <dd key={label}>
                  <ButtonStandard
                    primary
                    size="small"
                    onClick={() => setSelectedDay(label)}
                    className={cx(
                      styles.Button,
                      selectedDay === label && styles.active
                    )}
                  >
                    {label}
                  </ButtonStandard>
                </dd>
              ))}
            </div>
          </dl>
        )}

        {filterableLocations.length > 2 && view === VIEWS[0] && (
          <dl className={styles.EventAgendaNavigatorDl}>
            <Type as="dt" size="body3" weight="bold">
              Stage:
            </Type>
            <div className={styles.ButtonGroup}>
              {filterableLocations.map((location) => (
                <dd key={location.key}>
                  <ButtonStandard
                    primary
                    size="small"
                    onClick={() => setSelectedLocation(location.name)}
                    className={cx(
                      styles.Button,
                      selectedLocation === location.name && styles.active
                    )}
                  >
                    {location.name}
                  </ButtonStandard>
                </dd>
              ))}
            </div>
          </dl>
        )}
      </div>
    )
  }

  return (
    <div className={styles.EventAgendaNavigator}>
      <Container center gutter size="wide">
        <Stack gap="small">
          <Type
            as="h2"
            size={['heading5', 'heading2']}
            weight="extrabold"
            className={styles.Title}
          >
            <Highlight>{`<h2>Event <strong>Agenda</strong></h2>`}</Highlight>
          </Type>
          <Type
            as="p"
            size={['body2', 'body3']}
            weight="medium"
            className={styles.Subtitle}
          >
            {dateRange}
          </Type>
        </Stack>

        <div>
          {view === VIEWS[1] && <ViewFilters />}

          <div>
            {view === VIEWS[0] ? (
              <ListView
                eventUmbrella={eventUmbrella}
                baseURL={baseURL}
                selectedDay={selectedDay}
                agendaItemDayMap={agendaItemDayMap}
                currentDateItem={currentDateItem}
                timezoneAbbreviation={timezoneAbbreviation}
                selectedLocation={selectedLocation}
                ViewFilters={ViewFilters}
              />
            ) : (
              <TimelineView
                currentDateItem={currentDateItem}
                dayLabels={dayLabels}
                backgroundImage={backgroundImage}
                event={embellishedEvent}
                sortedAgenda={sortedAgenda}
                agendaItemDayMap={agendaItemDayMap}
                selectedDay={selectedDay}
                timezoneAbbreviation={timezoneAbbreviation}
                {...other}
              />
            )}
          </div>
        </div>
        {!!buttonGroup?.length && (
          <ButtonGroup
            group={buttonGroup}
            className={cx(styles.ButtonGroup, styles.ButtonGroupBottom)}
            center
          />
        )}
      </Container>
    </div>
  )
}

EventAgendaNavigator.propTypes = {
  theme: string,
  pageData: object,
  buttonGroup: array,
  baseURL: string,
  locationFilters: array,
  eventUmbrella: object
}

export default EventAgendaNavigator
