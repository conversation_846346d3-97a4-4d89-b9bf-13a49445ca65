import React, { useContext } from 'react'
import { EventAgendaContext } from '@/component/Context/EventAgendaContext'
import { arrayOf, bool, number, object, oneOf, shape, string } from 'prop-types'
import Type from '@/component/Primitive/Type'
import moment from 'moment'
import styles from '@/component/Primitive/Events/components/EventAgendaNavigator/components/List/ListView.module.scss'
import cx from 'classnames'

const ListViewRow = (props) => {
  const {
    title,
    speakers,
    sessionType,
    time,
    startDate,
    endDate,
    timezone,
    registerButtonClassName,
    registerAction,
    location,
    hideStageLabel
  } = props
  const { handleOpenModal, setEventAgendaModalItem } = useContext(
    EventAgendaContext
  )

  const handleClick = () => {
    setEventAgendaModalItem({
      ...props,
      location: props.location.name,
      sessionType: props.sessionType.name,
      timezone,
      registerButtonClassName,
      registerAction
    })
    handleOpenModal()
  }

  const isNetworking = () =>
    sessionType?.name?.toLowerCase()?.includes('network')

  const isRemarks = () => sessionType?.name?.toLowerCase()?.includes('remark')

  const isWorkshop = () =>
    sessionType?.name?.toLowerCase()?.includes('workshop')

  const isLive = () => {
    const now = moment()
    const start = moment(startDate)
    const end = moment(endDate)
    return now.isBetween(start, end)
  }

  const Component = (
    <tr
      role="button"
      tabIndex={0}
      onKeyDown={() => handleClick()}
      onClick={() => handleClick()}
      className={cx(
        isNetworking() || isRemarks() ? styles.IsNetworking : null,
        isLive() ? styles.isLive : null
      )}
    >
      <td data-title="Time:">
        <Type as="span" weight="bold">
          {time}
        </Type>
      </td>
      {!hideStageLabel && (
        <td data-title="Location:">
          <Type as="span" weight="regular">
            {location.name}
          </Type>
        </td>
      )}
      <td data-title="Name:">
        <Type as="span" weight="regular">
          {title}
        </Type>
      </td>
      <td data-title="Speaker:">
        <SpeakersColumn
          speakers={speakers}
          isNetworking={isNetworking()}
          isWorkshop={isWorkshop()}
        />
      </td>
      <td data-title="Type:">
        <Type as="span" weight="regular">
          {sessionType.shortName}
        </Type>
      </td>
    </tr>
  )
  return Component
}

const SpeakersColumn = ({ speakers, isNetworking, isWorkshop }) => {
  if (speakers.length === 0)
    return <Type as="span">{isNetworking || isWorkshop ? '' : 'TBC'}</Type>
  else if (speakers.length === 1) {
    return (
      <Type as="span" weight="regular">
        <Type as="span" weight="bold">
          {speakers[0].name}
        </Type>
        <br />
        {speakers[0].companyName}
      </Type>
    )
  } else {
    return (
      <span>
        {speakers.map((speaker, index) => (
          <p key={index}>
            <Type as="span" weight="bold">
              {speaker.name}
            </Type>
            <span>{' - '}</span>
            <Type as="span" weight="regular">
              {speaker.companyName}
            </Type>
          </p>
        ))}
      </span>
    )
  }
}

SpeakersColumn.propTypes = {
  isNetworking: bool,
  speakers: arrayOf(
    shape({
      name: string,
      companyName: string
    })
  )
}

ListViewRow.propTypes = {
  title: string,
  speakers: arrayOf(
    shape({
      name: string,
      companyName: string,
      role: string
    })
  ),
  align: oneOf(['left', 'center']),
  openInNewTab: bool,
  showDatePrefix: bool,
  startDate: string,
  endDate: string,
  sessionType: object,
  time: string,
  day: number,
  theme: string,
  shaded: bool,
  agendaItem: object,
  timezone: string,
  registerButtonClassName: string,
  registerAction: object,
  baseUrl: string,
  location: object,
  hideStageLabel: bool
}

export default ListViewRow
