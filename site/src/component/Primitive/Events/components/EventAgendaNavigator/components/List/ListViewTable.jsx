import React, { useMemo } from 'react'
import moment from 'moment'
import ListViewRow from '@/component/Primitive/Events/components/EventAgendaNavigator/components/List/ListViewRow'
import styles from '@/component/Primitive/Events/components/EventAgendaNavigator/components/List/ListView.module.scss'
import Type from '@/component/Primitive/Type'
import { getImage } from '@/component/Primitive/Events/components/EventAgendaNavigator/utils/getImage'
import { array, bool, object, string } from 'prop-types'

const ListViewTable = ({
  list,
  dayMap,
  inverseDayMap,
  baseURL,
  hideStageLabel,
  filteredCompanies,
  filteredEventTypes,
  currentDateItem,
  timezoneAbbreviation,
  selectedLocation
}) => {
  const filteredList = useMemo(() => {
    return list.reduce((arr, content, index) => {
      // Filter by companies and event types
      const companyMatch =
        filteredCompanies.length === 0 ||
        content.speakers?.some((speaker) =>
          filteredCompanies.includes(speaker.companyName)
        )

      const typeMatch =
        filteredEventTypes.length === 0 ||
        filteredEventTypes.includes(content?.sessionType?.shortName)

      // Filter by date
      const dateMatch =
        moment(content?.startDate).format('YYYYMMDD') ===
        moment(currentDateItem?.startDate).format('YYYYMMDD')

      const locationMatch =
        selectedLocation === 'All' ||
        (selectedLocation !== 'All' &&
          selectedLocation === content.location?.name)

      // Only continue if all filters pass
      if (!(companyMatch && typeMatch && dateMatch && locationMatch)) return arr

      // Determine if date prefix should be shown
      const previousStartDate = list[index - 1]?.startDate
      const showDatePrefix =
        !moment(previousStartDate).isSame(content.startDate, 'day') ||
        !previousStartDate

      arr.push({
        ...content,
        tag: 'Event Agenda',
        key: index,
        title: content.title || content.name,
        subtitle: content.subtitle || content.sessionType.name,
        description: content.description || '',
        speakers: content.speakers?.map((speaker) => {
          const speakerImage = getImage(speaker?.images, 'speaker', '1x1', 320)
            ?.url
          return {
            ...speaker,
            image: {
              alt: 'Event Agenda Speaker Placeholder',
              sizes: ['min-width(960px) 50vw', '100vw'],
              src: speakerImage
            }
          }
        }),
        sessionType: content.sessionType,
        time: `${content.localeSafeStartTime} - ${content.localeSafeEndTime} (${timezoneAbbreviation})`,
        ctaLabel: content.ctaLabel || 'Read about topic',
        theme: 'light',
        align: 'center',
        numItemsInRow: { m: 1, t: 1, d: 1 },
        shaded: index % 2 === 0,
        showDatePrefix,
        day: Object.keys(dayMap).length > 1 && inverseDayMap[content._id],
        baseURL,
        location: content.location,
        hideStageLabel,
        openInNewTab: false
      })

      return arr
    }, [])
  }, [
    baseURL,
    currentDateItem.startDate,
    dayMap,
    filteredCompanies,
    filteredEventTypes,
    hideStageLabel,
    inverseDayMap,
    list,
    selectedLocation,
    timezoneAbbreviation
  ])

  if (filteredList.length === 0) {
    return (
      <Type as="p" size="body2">
        No events found for this day. Try selecting a different day or adjusting
        your filters to find events.
      </Type>
    )
  }

  return (
    <table className={styles.ListViewTable}>
      <thead>
        <tr>
          <Type as="th" size="body3" weight="strong">
            Time
          </Type>

          {!hideStageLabel && (
            <Type as="th" size="body3" weight="strong">
              Stage
            </Type>
          )}

          <Type as="th" size="body3" weight="strong">
            Name
          </Type>

          <Type as="th" size="body3" weight="strong">
            Speaker
          </Type>

          <Type as="th" size="body3" weight="strong">
            Type
          </Type>
        </tr>
      </thead>

      <tbody>
        {filteredList.map((content) => (
          <ListViewRow key={content.key} {...content} />
        ))}
      </tbody>
    </table>
  )
}

ListViewTable.propTypes = {
  list: array,
  dayMap: object,
  inverseDayMap: object,
  baseURL: string,
  hideStageLabel: bool,
  filteredCompanies: array,
  filteredEventTypes: array,
  currentDateItem: object,
  timezoneAbbreviation: string,
  selectedLocation: string
}

export default ListViewTable
