.EventAgendaNavigator {
  padding-block: spacing(4) spacing(6);
}

@include mq($breakpoint-tablet) {
  .EventAgendaNavigator {
    padding-block: spacing(6) spacing(10);
  }
}

.EventAgendaNavigatorTop {
  display: flex;
  flex-wrap: wrap;
  gap: spacing(3);
  margin-bottom: spacing(3);
}

.EventAgendaNavigatorDl {
  dt {
    margin-bottom: 0.25em;
  }
}

.Subtitle {
  margin-bottom: spacing(3);
}

.ButtonGroup {
  display: flex;
  flex-wrap: wrap;
  gap: spacing(1);

  .Button {
    background-color: transparent;
    border: 1px solid var(--color-theme--event);
    color: $color-black-primary;

    [class*='Row_grey-dark'] &,
    [class*='Row_black'] & {
      color: $color-white-primary;
    }

    &.active {
      background-color: var(--color-theme--event);
      color: var(--color-theme--event-button-foreground) !important;
    }
  }
}

.ButtonGroupBottom {
  margin-top: 32px;
}
