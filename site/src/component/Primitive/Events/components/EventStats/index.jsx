import React from 'react'
import styles from './EventStats.module.scss'
import { Highlight, ButtonGroup } from '@/component/Primitive/Events'
import Type from '@/component/Primitive/Type'
import Container from '@/component/Primitive/Container'
import Stack from '@/component/Primitive/Stack'
import cx from 'classnames'
import { array, string } from 'prop-types'

export const EventStats = ({
  stats,
  backgroundImage,
  theme,
  buttonGroup,
  title,
  subtitle,
  description
}) => {
  return (
    <div className={cx(styles.EventStats)}>
      <Container
        className={styles.EventStatsContainer}
        gutter
        center
        size="wide"
      >
        <Stack className={styles.EventStatsInner} gap="large">
          {(title || subtitle) && (
            <Stack gap="tiny">
              {title && (
                <Type
                  as="h2"
                  size={['heading5', 'heading2']}
                  weight="bold"
                  className={styles.Title}
                >
                  <Highlight>{title}</Highlight>
                </Type>
              )}
              {subtitle && (
                <Type
                  as="h2"
                  size={['body2', 'body3']}
                  weight="medium"
                  className={styles.Subtitle}
                >
                  {subtitle}
                </Type>
              )}
            </Stack>
          )}
          <div className={styles.Stats}>
            {stats?.length &&
              stats.map((stat, i) => (
                <Stat
                  label={stat.label}
                  value={stat.statistic}
                  key={`EventStats--Statistic--${i}`}
                />
              ))}
          </div>
          {description && (
            <Type size={['body2', 'body2']} className={styles.Description}>
              {description}
            </Type>
          )}
          {!!buttonGroup?.length && <ButtonGroup group={buttonGroup} />}
        </Stack>
      </Container>
      {backgroundImage && (
        <div
          className={styles.EventStatsImage}
          style={{ backgroundImage: `url(${backgroundImage})` }}
        />
      )}
    </div>
  )
}

EventStats.propTypes = {
  stats: array,
  backgroundImage: string,
  theme: string,
  buttonGroup: array,
  title: string,
  subtitle: string,
  description: string
}

const Stat = ({ value, label }) => (
  <div className={styles.StatWrapper}>
    <div className={styles.Stat}>
      <Type themed size={['heading3', 'display2']} weight="bold">
        {value}
      </Type>
    </div>
    <Type size="body2" weight="medium" className={styles.Label}>
      {label}
    </Type>
  </div>
)

Stat.propTypes = {
  value: string.isRequired,
  label: string.isRequired
}
