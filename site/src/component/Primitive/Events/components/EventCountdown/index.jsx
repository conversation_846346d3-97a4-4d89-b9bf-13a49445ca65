import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { string, shape, oneOfType, number } from 'prop-types'
import { Highlight } from '@/component/Primitive/Events'
import styles from './EventCountdown.module.scss'
import Type from '@/component/Primitive/Type'
import Container from '@/component/Primitive/Container'
import Icon from '@/component/Primitive/Icon'
import moment from 'moment'

export const EventCountdown = ({ event }) => {
  const { name, buildingName, startDate, endDate } = event

  const formatDate = (start, end) => {
    const s = moment(start)
    const e = moment(end)

    if (s.isSame(e, 'day')) return s.format('D MMMM YYYY')
    return `${s.format('D MMMM')} - ${e.format('D MMMM YYYY')}`
  }

  const calculateTimeLeft = useCallback(() => {
    const now = moment()
    const start = moment(startDate)
    const weeks = Math.floor(start.diff(now, 'weeks'))
    const days = Math.floor(start.diff(now, 'days') % 7)
    const hours = Math.floor(start.diff(now, 'hours') % 24)
    const minutes = Math.floor(start.diff(now, 'minutes') % 60)
    const seconds = Math.floor(start.diff(now, 'seconds') % 60)
    return { weeks, days, hours, minutes, seconds }
  }, [startDate])

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft())

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)
    return () => clearInterval(timer)
  }, [calculateTimeLeft])

  const { weeks, days, hours, minutes, seconds } = timeLeft

  const highlightedName = useMemo(() => {
    const splitName = name.split(' ')
    const lastWord = splitName.pop()
    const rest = splitName.join(' ')
    return `<h2>${rest} <strong>${lastWord}</strong></h2>`
  }, [name])

  return (
    <div className={styles.EventCountdown}>
      <Container
        gutter
        size="wide"
        center
        className={styles.EventCountdownInner}
      >
        <div className={styles.Content}>
          <div className={styles.Divider}>
            <Type
              as="p"
              size={['body5', 'body4']}
              weight="regular"
              className={styles.DividerText}
            >
              EVENT
            </Type>
            <hr />
          </div>
          <Type
            as="h1"
            size={['heading5', 'heading3']}
            weight="extrabold"
            className={styles.Title}
          >
            <Highlight>{highlightedName}</Highlight>
          </Type>
          <div className={styles.InfoWrapper}>
            <div className={styles.Date}>
              <Icon
                width={20}
                height={20}
                className={styles.Calendar}
                type="calendar"
              />
              <Type
                as="p"
                size="body3"
                weight="regular"
                className={styles.Info}
              >
                {formatDate(startDate, endDate)}
              </Type>
            </div>
            <Type as="p" size="body3" weight="regular" className={styles.Dot} />
            <div className={styles.Location}>
              <Icon
                width={12}
                height={12}
                className={styles.Pin}
                type="map-pin"
              />
              <Type
                as="p"
                size="body3"
                weight="regular"
                className={styles.Info}
              >
                {buildingName}
              </Type>
            </div>
          </div>
        </div>
        <div className={styles.BoxWrapper}>
          <Box number={weeks} unit="Weeks" />
          <Box number={days} unit="Days" />
          <Box number={hours} unit="Hrs" />
          <Box number={minutes} unit="Mins" />
          <Box number={seconds} unit="Secs" />
        </div>
      </Container>
    </div>
  )
}

EventCountdown.propTypes = {
  event: shape({
    name: string
  })
}

const Box = ({ number, unit }) => {
  return (
    <div className={styles.Box}>
      <Type
        as="p"
        size={['heading6', 'heading4']}
        weight="bold"
        className={styles.Number}
      >
        {number}
      </Type>
      <Type
        as="p"
        size={['body5', 'body4']}
        weight="regular"
        className={styles.Unit}
      >
        {unit}
      </Type>
    </div>
  )
}

Box.propTypes = {
  number: oneOfType([number, string]),
  unit: oneOfType([number, string])
}
