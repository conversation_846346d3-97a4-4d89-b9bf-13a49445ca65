import React from 'react'
import styles from './EventFeatureBoxes.module.scss'
import { object, bool, shape, string, arrayOf } from 'prop-types'

export const EventFeatureBoxes = ({ featureBoxes }) => {
  if (typeof featureBoxes !== 'object' || featureBoxes.length === 0) {
    return <></>
  }

  return (
    <ul className={styles.Grid}>
      {featureBoxes.map((item, index) => (
        <li key={index} className={styles.Item}>
          {item.image?.url && (
            <div className={styles.ItemBackgroundHolder}>
              <img
                className={styles.ItemBackgroundImage}
                src={item.image.url}
                alt=""
                width={item.image?.width}
                height={item.image?.height}
              />
            </div>
          )}

          <div className={styles.ItemInnerText}>
            {item.url ? (
              <a
                href={item.url}
                target={item.openInNewTab ? '_blank' : '_self'}
                rel="noopener noreferrer"
              >
                {item.title}
              </a>
            ) : (
              item.title
            )}
          </div>
        </li>
      ))}
    </ul>
  )
}

EventFeatureBoxes.propTypes = {
  featureBoxes: arrayOf(
    shape({
      title: string,
      url: string,
      openInNewTab: bool,
      image: object
    })
  )
}
