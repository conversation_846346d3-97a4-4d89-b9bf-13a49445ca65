.Grid {
  display: grid;
  gap: 32px 24px;
  list-style: none;
  margin-top: 32px;
}

@media (min-width: 640px) {
  .Grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .Grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 992px) {
  .GridFourColumn {
    grid-template-columns: repeat(4, 1fr);
  }
}

.Description {
  margin-top: 16px;
}

.ImageHolder {
  border-radius: 10px;
  display: grid;
  margin-bottom: 16px;
  overflow: hidden;

  img {
    transition: 250ms ease-in-out;
  }

  .Item:has(a:active, a:hover) & {
    img {
      opacity: 0.8;
      transform: scale(1.1);
    }
  }

  > * {
    grid-area: 1 / 1 / 2 / 2;
  }
}

.Item {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.ItemTitle {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 4px;

  a {
    color: currentColor;

    &::after {
      content: '';
      inset: 0;
      position: absolute;
    }

    &:link,
    &:visited {
      text-decoration: none;
    }

    &:active,
    &:hover {
      text-decoration: underline;
      text-decoration-color: var(
        --color-theme--event,
        var(--color-theme--secondary)
      );
      text-decoration-thickness: 2px;
      text-underline-offset: 2px;
    }
  }
}

.ItemDescription {
  color: $color-grey50;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}

.ItemButton {
  align-items: center;
  background-color: var(--color-theme--event, var(--color-theme--secondary));
  color: var(--color-theme--event-button-foreground);
  display: inline-flex;
  font-size: 12px;
  gap: 8px;
  margin-top: auto;
  padding: 8px 16px;
}

.ItemButtonNoLink {
  background-color: $color-black-primary;
}

.ItemButtonIcon {
  margin-top: -2px;
}
