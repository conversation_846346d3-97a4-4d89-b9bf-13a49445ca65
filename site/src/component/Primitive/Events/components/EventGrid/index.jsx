import React from 'react'
import styles from './EventGrid.module.scss'
import { array, bool, object, shape, string } from 'prop-types'
import { Highlight } from '@/component/Primitive/Events'
import Prose from '@/component/Primitive/Prose'
import SmartLink from '@/component/Primitive/SmartLink'
import Icon from '@/component/Primitive/Icon'
import classNames from 'classnames'

const EventItemImage = ({ images }) => {
  const getImage = (name, size) => images?.[`${name}_${size}`]?.[0]

  const backgroundImage = getImage('background', 640)?.url

  return (
    <div className={styles.ImageHolder}>
      <img src={backgroundImage} alt="" />
    </div>
  )
}

EventItemImage.propTypes = {
  images: object
}

const EventItem = ({
  title,
  description,
  ctaText,
  images,
  event,
  urlOverride,
  externalLink
}) => {
  const eventTitle = title || event?.name
  const eventDescription = description
  const hasLink = !!urlOverride?.length || !!event?._fullUrl?.length
  const eventCtaLinkText = ctaText || 'Find out more'
  const eventCtaLink = urlOverride || event?._fullUrl
  const eventCtaText = hasLink ? eventCtaLinkText : ctaText

  return (
    <div className={styles.Item}>
      <EventItemImage images={images} />
      {eventTitle && (
        <h3 className={styles.ItemTitle}>
          {hasLink ? (
            <SmartLink
              href={eventCtaLink}
              target={externalLink ? '_blank' : null}
            >
              {eventTitle}
            </SmartLink>
          ) : (
            eventTitle
          )}
        </h3>
      )}

      {eventDescription && (
        <p className={styles.ItemDescription}>{eventDescription}</p>
      )}

      {eventCtaText && (
        <span
          className={classNames(
            styles.ItemButton,
            !hasLink && styles.ItemButtonNoLink
          )}
        >
          {eventCtaText}
          {hasLink && (
            <Icon
              width={7}
              className={styles.ItemButtonIcon}
              type="arrow-right"
            />
          )}
        </span>
      )}
    </div>
  )
}

EventItem.propTypes = {
  title: string,
  description: string,
  ctaText: string,
  images: object,
  event: shape({ name: string, _fullUrl: string }),
  urlOverride: string,
  externalLink: bool
}

export const EventGrid = ({ title, description, items }) => {
  return (
    <div className={styles.EventGrid}>
      {title && (
        <Prose>
          <h2>
            <Highlight>{title}</Highlight>
          </h2>
        </Prose>
      )}

      {description && (
        <Prose dangerousHtml={description} className={styles.Description} />
      )}

      <ul
        className={classNames(
          styles.Grid,
          items.length > 3 && styles.GridFourColumn
        )}
      >
        {items?.map((item, index) => (
          <li key={index}>
            <EventItem {...item} />
          </li>
        ))}
      </ul>
    </div>
  )
}

EventGrid.propTypes = {
  title: string,
  description: string,
  items: array
}
