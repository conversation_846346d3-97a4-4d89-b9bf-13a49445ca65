import React from 'react'
import { string, bool, array, object } from 'prop-types'
import styles from './EventContentBlock.module.scss'
import Carousel from '@/component/Primitive/Carousel'
import Block from './components/Block'
import cx from 'classnames'
import moment from 'moment'
import AgendaItemBlock from './components/AgendaItemBlock'

export const EventContentBlock = ({ list, theme, inverse, widgetId }) => {
  const mapContentPropsToProps = (content) => {
    switch (content.type) {
      case 'video':
        return {
          ...content,
          tag: 'On Demand Video',
          title: content.title || content.video.name,
          subtitle:
            content.subtitle ||
            // Will be set to the date of the agenda item???
            moment(content.video.createdDate).format('dddd, MMMM Do YYYY') ||
            'video stats',
          description:
            content.description ||
            content.video.description ||
            'description...',
          ctaLabel: content.ctaLabel || 'Watch now',
          theme: content.theme || theme,
          inverse: content.inverse || inverse
        }
      case 'article':
        return {
          ...content,
          tag: 'Event Article',
          title: content.title || content.article.headline,
          subtitle: content.subtitle || content.article.category || 'subtitle',
          description:
            content.description || content.article.sell || '...description',
          ctaLabel: content.ctaLabel || 'Read full article',
          theme: content.theme || theme,
          inverse: content.inverse || inverse
        }
      case 'sponsor':
        return {
          ...content,
          tag: 'Event Sponsor',
          title: content.title || content.sponsor.displayName,
          subtitle: content.sponsor.tier.name
            ? content.sponsor.tier.name
            : 'Partner',
          description: content.sponsor.description,
          ctaLabel: content.ctaLabel || 'Visit sponsor booth',
          logo: content.logo,
          theme: content.theme || theme,
          inverse: content.inverse || inverse
        }
      case 'speaker':
        return {
          ...content,
          tag: 'Event Speaker',
          title: content.title || content.speaker.name,
          subtitle: content.subtitle || content.speaker.jobTitle,
          description: content.description,
          ctaLabel: content.ctaLabel || 'Read speaker bio',
          theme: content.theme || theme,
          inverse: content.inverse || inverse
        }
      case 'agendaItem':
        return {
          ...content,
          tag: 'Event Agenda',
          title: content.title || content.agendaItem.name,
          subtitle: content.subtitle || content.agendaItem.sessionType.name,
          description:
            content.description ||
            content.agendaItem.description ||
            '...description',
          ctaLabel: content.ctaLabel || 'Read about topic',
          theme: content.theme || theme,
          inverse: content.inverse || inverse,
          logo: content.logo,
          timeRange: `${content.localeSafeStartTime} - ${content.localeSafeEndTime} (${content.timezoneAbbreviation})`,
          id:
            widgetId ||
            `agenda-item-${
              content.agendaItemId || content.agendaItem._id || ''
            }`
        }
      case 'event': {
        const formattedStartDate = moment(content.event.startDate).format(
          'ddd D MMMM'
        )
        const formattedEndDate = moment(content.event.endDate).format(
          'ddd D MMMM'
        )
        const dateRange = `${formattedStartDate} - ${formattedEndDate}`

        return {
          ...content,
          tag: 'Event',
          title: content.title || content.event.name,
          subtitle: content.subtitle || dateRange,
          description: content.description || content.event.description,
          ctaLabel: content.ctaLabel || 'View event',
          theme: content.theme || theme
        }
      }
      case 'eventUmbrella':
        return {
          ...content,
          tag: 'Event Umbrella',
          title: content.title || content.eventUmbrella.name,
          subtitle: content.subtitle,
          description: content.description || content.eventUmbrella.description,
          ctaLabel: content.ctaLabel,
          theme: content.theme || theme
        }
      default:
        break
    }
    return null
  }

  const ComponentMap = {
    agendaItem: AgendaItemBlock
  }

  if (list.length === 1)
    return (
      <div className={cx(styles.EventContentBlock)}>
        {list?.map((content, i) => {
          const Component = ComponentMap[content.type] || Block
          return <Component key={i} {...mapContentPropsToProps(content)} />
        })}
      </div>
    )

  return (
    <div className={cx(styles.EventContentBlock)}>
      <Carousel
        paginationClassName={styles.CarouselPagination}
        options={{
          nextDisabledOffsetFromEnd: 0
        }}
      >
        {list?.map((content, i) => {
          const Component = ComponentMap[content.type] || Block
          return <Component key={i} {...mapContentPropsToProps(content)} />
        })}
      </Carousel>
    </div>
  )
}

EventContentBlock.propTypes = {
  list: array,
  theme: string,
  inverse: bool,
  image: object,
  widgetId: string
}
