import React, { useEffect, useState } from 'react'
import { string } from 'prop-types'
import ButtonStandard from '@/component/Primitive/ButtonStandard'
import Icon from '@/component/Primitive/Icon'
import Type from '@/component/Primitive/Type'

const ShareButton = ({ label, labelShared, url }) => {
  const [shared, setShared] = useState(false)
  const isClient = typeof window !== 'undefined'

  useEffect(() => {
    if (shared) {
      const timeout = setTimeout(() => setShared(false), 2000)

      return () => clearTimeout(timeout)
    }
  }, [shared])

  if (!isClient) {
    return <></>
  }

  const handleShare = () => {
    // Copy URL to clipboard
    try {
      navigator.clipboard.writeText(url).then(() => {
        setShared(true)
      })
    } catch (err) {
      // Fallback for older browsers
      const textarea = document.createElement('textarea')
      textarea.value = url
      textarea.style.position = 'fixed' // Prevent scrolling to bottom
      document.body.appendChild(textarea)
      textarea.focus()
      textarea.select()

      try {
        document.execCommand('copy')
        setShared(true)
      } catch (err) {
        console.error('Failed to copy URL: ', err)
      }

      document.body.removeChild(textarea)
    }
  }

  return (
    <ButtonStandard onClick={handleShare} size="small">
      <Type size="body4" weight="medium" as="span">
        {shared ? (
          <>
            {labelShared || 'Copied!'}{' '}
            <Icon type="check" a11ytext="" width={14} />
          </>
        ) : (
          <>
            <Icon type="share" a11ytext="" width={14} /> {label}
          </>
        )}
      </Type>
    </ButtonStandard>
  )
}

ShareButton.propTypes = {
  label: string.isRequired,
  labelShared: string,
  url: string.isRequired
}

export default ShareButton
