import React from 'react'
import PropTypes from 'prop-types'
import { Highlight, ButtonGroup } from '@/component/Primitive/Events'
import styles from './EventIconSplitHero.module.scss'
import cx from 'classnames'
import Stack from '@/component/Primitive/Stack'
import Type from '@/component/Primitive/Type'
import Prose from '@/component/Primitive/Prose'
import Icon from '@/component/Primitive/Icon'
import Container from '@/component/Primitive/Container'

export const EventIconSplitHero = ({
  title,
  subtitle,
  description,
  theme,
  buttonGroup,
  inverse,
  direction,
  icon,
  iconSize,
  image
}) => {
  if (image) {
    // Image is available for rendering
  }

  return (
    <div
      className={cx(
        styles.EventIconSplitHero,
        direction === 'column' && styles.column
      )}
    >
      <Container
        center
        size="wide"
        className={cx(
          styles.EventIconSplitHeroInner,
          inverse && styles.inverse
        )}
      >
        <div className={styles.ContentWrapper}>
          <div className={styles.Content}>
            <Stack gap="medium">
              <Stack gap="small">
                {title && (
                  <Type
                    as="h1"
                    size={['heading5', 'heading2']}
                    weight="medium"
                    className={styles.Title}
                  >
                    <Highlight classNames={{ strong: styles.Highlight }}>
                      {title}
                    </Highlight>
                  </Type>
                )}
                {subtitle && (
                  <Type
                    as="h2"
                    size={['body2', 'body3']}
                    weight="medium"
                    className={styles.Subtitle}
                  >
                    <Prose
                      className={styles.Subtitle}
                      dangerousHtml={subtitle}
                    />
                  </Type>
                )}
                {description && direction !== 'column' && (
                  <Type size={['body2', 'body2']}>
                    <Prose
                      className={styles.Description}
                      dangerousHtml={description}
                    />
                  </Type>
                )}
              </Stack>
              {direction !== 'column' && !!buttonGroup?.length && (
                <ButtonGroup group={buttonGroup} theme={theme} />
              )}
            </Stack>
          </div>
        </div>
        <div className={styles.IconWrapper}>
          {image ? (
            <img
              src={image.desktop.src}
              alt="Icon"
              className={styles.ImageOverride}
              style={{ width: iconSize || 200, height: iconSize || 200 }}
            />
          ) : (
            <Icon
              type={icon || 'events-lanyard'}
              width={iconSize || 200}
              height={iconSize || 200}
              className={styles.Icon}
              kit="bzk"
              a11ytext=""
            />
          )}
        </div>
        {direction === 'column' && (
          <Stack gap="small" className={styles.OverflowContent}>
            {description && direction === 'column' && (
              <Type size={['body2', 'body2']}>
                <Prose
                  className={styles.Description}
                  dangerousHtml={description}
                />
              </Type>
            )}
            {direction === 'column' && !!buttonGroup?.length && (
              <ButtonGroup
                group={buttonGroup}
                theme={theme}
                className={styles.ButtonGroup}
              />
            )}
          </Stack>
        )}
      </Container>
    </div>
  )
}

EventIconSplitHero.propTypes = {
  title: PropTypes.string,
  subtitle: PropTypes.string,
  description: PropTypes.string,
  theme: PropTypes.string,
  buttonGroup: PropTypes.array,
  inverse: PropTypes.bool,
  direction: PropTypes.oneOf(['row', 'column']),
  icon: PropTypes.string,
  iconSize: PropTypes.number,
  image: PropTypes.object
}

export default EventIconSplitHero
