import React from 'react'
import { arrayOf, number, shape, string, oneOf, object, bool } from 'prop-types'
import { Highlight } from '@/component/Primitive/Events'
import styles from './EventWOFSplitHero.module.scss'
import ResponsiveImage from '@/component/Primitive/ResponsiveImage'
import cx from 'classnames'
import Stack from '@/component/Primitive/Stack'
import Icon from '@/component/Primitive/Icon'
import Hide from '@/component/Primitive/Hide'
// import ButtonStandard from '@/component/Primitive/ButtonStandard'
import Type from '@/component/Primitive/Type'

export const EventWOFSplitHero = ({
  title,
  subtitle,
  description,
  theme,
  ctaLabel,
  ctaLink,
  image,
  inverse
}) => {
  const { mobile: mobileImage, desktop: desktopImage } = image
  return (
    <div className={cx(styles.EventWOFSplitHero)}>
      <div
        className={cx(styles.EventWOFSplitHeroInner, inverse && styles.inverse)}
      >
        {image?.desktop?.src ? (
          <>
            <Image device="desktop" image={desktopImage} />
            <Image device="mobile" image={mobileImage} />
          </>
        ) : (
          <div className={styles.ImageWrapper}>
            <img
              loading="lazy"
              src="/image/event/wall-of-fame.jpeg"
              alt="Wall of Fame"
              width={1600 / 2}
              height={900 / 2}
              className={styles.Image}
            />
          </div>
        )}
        <div className={styles.ContentWrapper}>
          <div className={styles.Content}>
            <Stack gap="small">
              <Stack gap="tiny">
                {title && <Title title={title} />}
                {/* {subtitle && (
                  <Type
                    as="h2"
                    size={['body2', 'body3']}
                    weight="medium"
                    className={styles.Subtitle}
                  >
                    {subtitle}
                  </Type>
                )} */}
              </Stack>
              {/* {description && (
                <Type size={['body2', 'body2']}>{description}</Type>
              )} */}
              <div>
                <div className={styles.Socials}>
                  <ul className={styles.SocialsList}>
                    <Social
                      type="linkedin"
                      url="https://www.linkedin.com/company/bizclik"
                    />
                    <Social type="twitter" url="https://twitter.com/BizClik_" />
                    <Social
                      type="facebook"
                      url="https://www.facebook.com/BizClikMediaGroup/"
                    />
                    <Social
                      type="youtube"
                      url="https://www.youtube.com/@BizClikMedia"
                    />
                    <Social
                      type="instagram"
                      url="https://www.instagram.com/bizclik_/"
                    />
                  </ul>
                </div>
              </div>
              {/* {ctaLabel && (
                <ButtonStandard
                  primary
                  outline
                  size="small"
                  icon="arrow-right"
                  href={ctaLink}
                  className={styles.Button}
                >
                  {ctaLabel}
                </ButtonStandard>
              )} */}
            </Stack>
          </div>
        </div>
      </div>
    </div>
  )
}

EventWOFSplitHero.propTypes = {
  title: string,
  subtitle: string,
  description: string,
  theme: string,
  ctaLabel: string,
  ctaLink: string,
  inverse: bool,
  image: shape({
    mobile: shape({
      src: string,
      srcSet: arrayOf(shape({ width: number, src: string })),
      sizes: arrayOf(string)
    }),
    desktop: shape({
      src: string,
      srcSet: arrayOf(shape({ width: number, src: string })),
      sizes: arrayOf(string)
    })
  })
}

const Title = ({ title }) => {
  return (
    <Type
      as="h1"
      size={['heading5', 'heading2']}
      weight="medium"
      className={styles.Title}
    >
      <div>
        <Highlight classNames={{ strong: styles.Highlight }}>{title}</Highlight>
        <div className={styles.CircleWrapper}>
          <Icon
            type="circle-hollow"
            width={15}
            height={15}
            className={cx(styles.Circle, styles.blue)}
          />
          <Icon
            type="circle-hollow"
            width={15}
            height={15}
            className={cx(styles.Circle, styles.yellow)}
          />
          <Icon
            type="circle-hollow"
            width={15}
            height={15}
            className={cx(styles.Circle, styles.red)}
          />
        </div>
      </div>
    </Type>
  )
}

Title.propTypes = {
  title: string
}

const Social = ({ type, url }) => {
  return (
    <li>
      <a href={url} target="_blank" rel="noreferrer noopener">
        <Icon
          type={type}
          a11ytext={`${type} icon`}
          className={styles.SocialsIconButton}
          width={16}
          height={16}
        />
      </a>
    </li>
  )
}

Social.propTypes = {
  type: string,
  url: string
}

const Image = ({ device, image }) => {
  const hideProps =
    device === 'mobile' ? { at: 'desktop' } : { below: 'desktop' }
  const className =
    device === 'mobile' ? styles.mobileImage : styles.desktopImage
  return (
    <Hide {...hideProps} className={styles.ImageWrapper}>
      <ResponsiveImage className={cx(styles.Image, className)} {...image} />
    </Hide>
  )
}

Image.propTypes = {
  device: oneOf(['mobile', 'desktop']),
  image: object
}
