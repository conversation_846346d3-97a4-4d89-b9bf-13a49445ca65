.EventFeaturedTestimonial {
  padding-bottom: spacing(4);
  padding-top: spacing(4);
  position: relative;

  cite {
    font-style: normal;
  }

  .EventFeaturedTestimonialInner {
    position: relative;
    @include mq($breakpoint-tablet) {
      display: flex;
      justify-content: center;
    }

    .ImagesWrapper {
      & > * {
        height: 100%;
      }

      @include mq($breakpoint-tablet) {
        flex: 1;
        margin-bottom: unset;
      }

      .ImageWrapper {
        position: relative;

        .Image {
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .ContentWrapper {
      padding-bottom: spacing(8);
      padding-top: spacing(8);
      text-align: center;

      .Content {
        align-items: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
    }

    &.inverse {
      .ImagesWrapper {
        @include mq($breakpoint-tablet) {
          order: 2;
        }
      }

      .ContentWrapper {
        @include mq($breakpoint-tablet) {
          order: 1;
        }
      }
    }
  }

  [class*='Row_grey-dark'] & {
    @include darkMode;

    background-color: $color-black-alt;
  }

  [class*='Row_black'] & {
    @include darkMode;

    background-color: $color-black-primary;
  }
}

.EventFeaturedTestimonialImage {
  background-color: red;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  display: block;
  height: 100%;
  left: 0;
  opacity: 0.03;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1;
}

.Headline {
  max-width: spacing(60);
  @include mq($breakpoint-desktop) {
    max-width: unset;
  }
}

.QuoteeOverride {
  text-decoration: underline;
  text-decoration-color: var(--color-theme--event);
}
