import React from 'react'
import { string, array, object } from 'prop-types'
import {
  Highlight,
  ButtonGroup,
  SpeakerThumbnail
} from '@/component/Primitive/Events'
import styles from './EventFeaturedTestimonial.module.scss'
import cx from 'classnames'
import Stack from '@/component/Primitive/Stack'
import Type from '@/component/Primitive/Type'
import Container from '@/component/Primitive/Container'

export const EventFeaturedTestimonial = ({
  headline,
  quote,
  backgroundImage,
  theme,
  buttonGroup,
  speaker,
  quoteeOverride
}) => {
  const quoteify = (text) =>
    `<h2><strong>&ldquo;</strong>${text}<strong>&rdquo;</strong></h2>`

  const getDisplayedQuote = () => {
    if (headline) return headline
    if (speaker?.testimonial) return quoteify(speaker.testimonial)
    return ''
  }

  return (
    <div className={cx(styles.EventFeaturedTestimonial)}>
      <Container
        center
        size="mediumLarge"
        gutter
        className={cx(styles.EventFeaturedTestimonialInner)}
      >
        <div className={styles.ContentWrapper}>
          <Stack className={styles.Content} gap="medium" as="blockquote">
            {speaker && !quoteeOverride && <SpeakerThumbnail {...speaker} />}
            {quoteeOverride && (
              <footer>
                <Type
                  className={styles.QuoteeOverride}
                  weight="bold"
                  size={['body2', 'body2']}
                  as="cite"
                >
                  {quoteeOverride}
                </Type>
              </footer>
            )}
            <Stack gap="tiny">
              {(headline || speaker?.testimonial) && (
                <Type
                  as="p"
                  size={['heading5', 'heading4']}
                  className={styles.Headline}
                >
                  <Highlight options={{ fixLineReturns: true }}>
                    {getDisplayedQuote()}
                  </Highlight>
                </Type>
              )}
            </Stack>
            {!quote && speaker && (
              <Type size={['body2', 'body2']}>
                {speaker?.eventName && <strong>{speaker.eventName}</strong>}
                {speaker?.roles?.length
                  ? ' - ' + speaker.roles.join(' | ')
                  : ''}
              </Type>
            )}
            {quote && (
              <Type size={['body2', 'body2']} weight="bold">
                {quote}
              </Type>
            )}
            {!!buttonGroup?.length && (
              <ButtonGroup group={buttonGroup} center />
            )}
          </Stack>
        </div>
      </Container>
      {backgroundImage && (
        <div
          className={styles.EventFeaturedTestimonialImage}
          style={{ backgroundImage: `url(${backgroundImage})` }}
        />
      )}
    </div>
  )
}

EventFeaturedTestimonial.propTypes = {
  speaker: object,
  headline: string,
  quote: string,
  theme: string,
  buttonGroup: array,
  backgroundImage: string,
  quoteeOverride: string
}
