import React, { useEffect, useState } from 'react'
import cx from 'classnames'
import { object, string, bool, func, number, array, shape } from 'prop-types'
import Truncate from 'react-truncate'
import SmartLink from '@/component/Primitive/SmartLink'

import Type from '../../Type'
import Hide from '../../Hide'
import Inline from '../../Inline'

import styles from '../Card.module.scss'

const CardContent = ({
  description,
  title,
  subtitle,
  shouldShowSubtitle,
  headerComponents,
  footerComponents,
  contentBorder,
  cappedHeight,
  typeConfig,
  truncateTitleAt,
  truncateDescriptionAt,
  clickableFooter,
  hideDescriptionOnMobile,
  classNames,
  link,
  isExternalLink,
  siblingInstance,
  to,
  eventBaseSlug,
  hasShade
}) => {
  const Header = headerComponents
  const Footer = footerComponents

  const [fontsReady, setFontsReady] = useState(false)

  useEffect(() => {
    if (fontsReady) return
    document.fonts.ready.then(() => setFontsReady(true))
  })

  const smartLinkProps = {}
  if (isExternalLink) {
    smartLinkProps.target = '_blank'
    smartLinkProps.rel = 'noopener noreferrer'
    smartLinkProps.href = link
  } else if (siblingInstance) {
    smartLinkProps.target = '_blank'
    smartLinkProps.rel = ''
    smartLinkProps.href = `https://${siblingInstance.subdomain}${link}`
  } else {
    smartLinkProps.to = to || (eventBaseSlug ? '/eventArticle' : '/article')
    smartLinkProps.as = link
  }

  return (
    <div
      className={cx(
        styles.CardContent,
        contentBorder?.includes('bottom') && styles['border-bottom'],
        contentBorder?.includes('top') && styles['border-top'],
        cappedHeight && styles.cappedHeight,
        classNames?.root
      )}
    >
      <div className={cx(styles.CardInner, classNames?.inner)}>
        {headerComponents && (
          <div className={styles.CardHeader}>
            <Inline middle gap="small">
              <Header />
            </Inline>
          </div>
        )}
        <div className={cx(styles.CardTitle, classNames?.title)}>
          <Type
            as="h3"
            weight={typeConfig?.title?.weight || 'bold'}
            size={typeConfig?.title?.size || 'heading7'}
            themed={typeConfig?.title?.themed}
          >
            <SmartLink
              {...smartLinkProps}
              className={cx(styles.CardLink, classNames?.link)}
            >
              {truncateTitleAt && fontsReady ? (
                <Truncate lines={truncateTitleAt}>{title}</Truncate>
              ) : (
                title
              )}
            </SmartLink>
          </Type>
        </div>
        {shouldShowSubtitle && subtitle && (
          <div className={cx(styles.CardSubTitle)}>
            <Type
              as="h3"
              weight={typeConfig?.subtitle?.weight || 'medium'}
              size={typeConfig?.subtitle?.size || ['body2', 'body3']}
              color={
                typeof typeConfig?.subtitle?.color === 'string'
                  ? typeConfig?.subtitle?.color
                  : null
              }
              themed={typeConfig?.subtitle?.themed}
            >
              <SmartLink
                {...smartLinkProps}
                className={cx(styles.CardLink, classNames?.link)}
              >
                {subtitle}
              </SmartLink>
            </Type>
          </div>
        )}
        {description && (
          <Hide below={hideDescriptionOnMobile && 'desktop'}>
            <div
              className={cx(styles.CardDescription, classNames?.description)}
            >
              <Type
                as="p"
                size={typeConfig?.description?.size || ['body2', 'body3']}
              >
                <Truncate lines={truncateDescriptionAt}>{description}</Truncate>
              </Type>
            </div>
          </Hide>
        )}
      </div>
      {footerComponents && (
        <div className={cx(styles.CardFooter, clickableFooter && styles.onTop)}>
          <Footer />
        </div>
      )}
      {hasShade && <div className={cx(styles.CardContentShade)} />}
    </div>
  )
}

CardContent.defaultProps = {
  truncateTitleAt: null,
  truncateDescriptionAt: 4,
  hasShade: false
}

CardContent.propTypes = {
  description: string,
  title: string.isRequired,
  subtitle: string,
  shouldShowSubtitle: bool,
  headerComponents: func,
  footerComponents: func,
  contentBorder: array,
  clickableFooter: bool,
  cappedHeight: bool,
  typeConfig: object,
  truncateTitleAt: number,
  truncateDescriptionAt: number,
  hideDescriptionOnMobile: bool,
  classNames: shape({
    root: string,
    inner: string,
    description: string,
    title: string
  }),
  link: string.isRequired,
  siblingInstance: shape({
    logoUrl: string,
    subdomain: string
  }),
  to: string,
  eventBaseSlug: string,
  hasShade: bool,
  isExternalLink: bool
}

export default CardContent
