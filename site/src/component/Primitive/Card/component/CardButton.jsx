import React from 'react'
import classNames from 'classnames'
import { node, bool, string } from 'prop-types'

import styles from '../Card.module.scss'
import Inline from '../../Inline'
import Icon from '../../Icon'

const CardButton = ({ children, inverse, theme = 'default' }) => {
  return (
    <div
      className={classNames(
        styles.CardButton,
        inverse && styles.inverse,
        theme && styles[theme]
      )}
    >
      <Inline center middle>
        {children}
        <Icon a11ytext="" type="arrow-right" />
      </Inline>
    </div>
  )
}

CardButton.propTypes = {
  children: node,
  inverse: bool,
  theme: string
}

export default CardButton
