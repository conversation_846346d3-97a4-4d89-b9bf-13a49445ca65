import React, { useContext, useEffect } from 'react'
import { array, string, number, bool, shape } from 'prop-types'
import useMeasure from '@/hook/useMeasure'

import ResponsiveImage from '../../ResponsiveImage'
import ResponsiveMedia from '../../ResponsiveMedia'
import Icon from '../../Icon'

import styles from '../Card.module.scss'
import { ImageHeightContext } from '@/component/Context/ImageHeightContext'
import cx from 'classnames'

const CardImage = ({
  src,
  srcSet,
  sizes,
  imageIcon,
  imageRatio,
  imageLoad,
  width,
  rowIndex,
  articleIndex,
  isDesktop,
  classNames
}) => {
  const [bind, { height }] = useMeasure()
  const ImageHeight = useContext(ImageHeightContext)

  useEffect(() => ImageHeight && ImageHeight.setImageHeight(height), [
    ImageHeight,
    height
  ])

  const determineLoading = (imageLoad) => {
    if (imageLoad) return imageLoad
    switch (isDesktop) {
      case true:
        return rowIndex <= 1 ? 'eager' : 'lazy'
      case false:
        return rowIndex <= 1 && articleIndex <= 1 ? 'eager' : 'lazy'
      default:
        return 'lazy'
    }
  }

  return (
    <div className={cx(styles.CardImage, classNames?.root)} {...bind}>
      <div className={styles.CardImageInner}>
        <ResponsiveMedia ratio={imageRatio}>
          <ResponsiveImage
            loading={determineLoading(imageLoad)}
            alt=""
            src={src}
            srcSet={srcSet}
            sizes={sizes}
            width={width}
            height={width && imageRatio ? Math.ceil(width * imageRatio) : width}
          />
        </ResponsiveMedia>
        {imageIcon && (
          <Icon
            width={50}
            height={50}
            a11ytext=""
            className={styles.CardImageIcon}
            type={imageIcon}
          />
        )}

        <div className={styles.HoverGradient} />
      </div>
    </div>
  )
}

CardImage.propTypes = {
  src: string,
  srcSet: array,
  sizes: array,
  imageRatio: number,
  width: number,
  imageIcon: string,
  imageLoad: string,
  rowIndex: number,
  articleIndex: number,
  isDesktop: bool,
  classNames: shape({
    root: string
  })
}

export default CardImage
