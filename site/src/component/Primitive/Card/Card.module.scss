.CardWrapper {
  height: 100%;
  position: relative;
}

.CardLink {
  color: currentColor;
  text-decoration: none;

  &::before {
    content: '';
    inset: 0;
    position: absolute;
    z-index: 2;
  }
}

.Card {
  color: inherit;
  display: flex;
  flex: 1 0 0;
  flex-direction: column;
  height: 100%;
  position: relative;
  text-decoration: none;

  &:focus-within a:focus {
    outline: none;
    text-decoration: none;
  }

  .CardTitle {
    transition: 250ms ease-in-out color;
  }

  &:has(.CardLink:active, .CardLink:hover) {
    .CardTitle {
      color: var(--color-theme--secondary) !important;
    }

    .CardButton {
      border-color: var(
        --color-theme-button-background,
        --color-theme--secondary
      );
      color: var(--color-theme-button-foreground);
    }
  }
}

.FloatyLogo {
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: spacing(1);
  position: absolute;
  top: spacing(1.5);
  left: spacing(1.5);
  z-index: 3;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  img {
    display: block;
    height: 32px;
    width: auto;
    max-width: 80px;
    object-fit: contain;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }

  &::before {
    content: none !important;
  }
}

@include mq($max: $breakpoint-tablet - 1) {
  .Card:not(.horizontal, :has([class*='CardButton'])) {
    .CardDescription {
      display: none;
    }
  }
}

@include mq($max: $breakpoint-desktop - 1) {
  .Card:not(.horizontal, :has([class*='CardButton'])) {
    align-items: start;
    display: grid;
    gap: 16px;
    grid-template-columns: 44.5% 1fr;

    .CardImage {
      margin-bottom: spacing(1);
    }

    &.inverse .CardImage {
      margin-bottom: 0;
    }

    .CardContent {
      height: auto;
      justify-content: start;
    }
  }
}

@media (min-width: #{$breakpoint-desktop}) {
  .NewFeatureStyle {
    box-shadow: unset !important;
  }
}

.CardImage {
  margin-bottom: spacing(2);
  position: relative;
  width: 100%;
  z-index: 1;

  > div {
    background-color: var(--color-theme--primary);
    overflow: hidden;

    img {
      box-sizing: border-box;
    }
  }

  .horizontal & {
    margin-bottom: 0;
    margin-right: spacing(3);
  }

  @include mq($breakpoint-desktop) {
    margin-bottom: spacing(1.5);
  }

  &::before {
    bottom: 0;
    box-shadow: inset 0 0 0 1px $color-misc-divider;
    content: '';
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1;

    .inverse & {
      display: none;
    }
  }
}

.CardImageIcon {
  backdrop-filter: blur(20px);
  border-radius: 50%;
  left: 50%;
  overflow: hidden;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);

  path {
    fill: rgba($color-white-primary, 0.5);
  }

  @include mq($max: $breakpoint-desktop - 1) {
    height: spacing(4.5) !important;
    line-height: spacing(4.5) !important;
    width: spacing(4.5) !important;
  }
}

.CardHeader {
  margin-bottom: spacing(1.5);
  position: relative;
  z-index: 1;
}

.CardContent {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;

  &.border-bottom {
    border-bottom: 1px solid $color-misc-divider;
    padding-bottom: spacing(2);
  }

  &.cappedHeight {
    height: 70%;
  }
}

.CardContentShade {
  position: relative;

  &::after {
    background: $color-black-secondary;
    bottom: spacing(-3);
    content: '';
    left: -2000px;
    position: absolute;
    right: -2000px;
    top: -600px;
    z-index: 0;

    @include mq($max: $breakpoint-desktop - 1) {
      display: none;
    }
  }
}

.CardInner {
  z-index: 1;

  .border-top & {
    border-top: 2px solid var(--color-theme--secondary);
    padding-top: spacing(2);
  }

  .inverse:not(.horizontal) .border-top & {
    margin-top: spacing(1.5);
  }

  > * + * {
    margin-top: spacing(0.5);
    z-index: 1;

    @include mq($breakpoint-desktop) {
      margin-top: spacing(1);
    }
  }
}

.CardDescription {
  color: var(--row-background-color, $color-grey-primary);

  .inverse & {
    color: $color-white-primary;
  }
}

.CardFooter {
  display: inherit;
  margin-top: spacing(2);

  &.onTop {
    position: relative;
    z-index: 2;
  }

  @include mq($breakpoint-desktop) {
    align-self: flex-start;
  }
}

.CardButton {
  background-color: $color-white-primary;
  border: 1px solid $color-misc-divider;
  color: var(--row-background-color, currentColor);
  font-size: $font-body-12;
  font-weight: 500;
  line-height: 1;
  padding: spacing(1.75) spacing(5);
  position: relative;
  text-align: center;
  text-decoration: none;
  transform: perspective(1px) translateZ(0);
  transition: color 250ms ease-in-out;
  @include font-smoothing();

  @include mq($max: $breakpoint-desktop - 1) {
    font-size: $font-body-16;
    width: 100%;
  }

  .inverse & {
    background-color: transparent;
  }

  &::before {
    background: var(
      --color-theme-button-background,
      var(--color-theme--secondary)
    );
    content: '';
    inset: 0;
    position: absolute;
    transform: scaleX(0);
    transform-origin: 0 50%;
    transition-duration: 0.3s;
    transition-property: transform;
    transition-timing-function: ease-out;
    z-index: -1;
  }

  &.primary {
    background-color: var(--color-theme-button-background);
    border-color: var(--color-theme-button-background);
    color: var(--color-theme-button-foreground);
  }

  &.primary::before {
    background: color-mix(
      in sRGB,
      var(--color-theme-button-background) 90%,
      black
    );
  }

  &.secondary {
    background-color: transparent;
    border-color: var(--color-theme-button-background);
    color: var(--color-theme-button-foreground);
  }

  &.secondary::before {
    background: var(--color-theme-button-background);
  }

  &.tertiary {
    background-color: transparent;
    border-color: $color-misc-divider;
    color: var(--color-theme-button-foreground);
  }

  &.tertiary::before {
    background: var(--color-theme-button-background);
  }

  .Card:has(a:active) &,
  .Card:has(a:hover) &,
  .Card:has(a:focus) & {
    &::before {
      transform: scaleX(1);
    }
  }
}

.GridWrapper {
  width: 100%;
}

.horizontal {
  flex-direction: row;
  height: 100%;
}

.inverse {
  background: $color-black-primary;
  color: $color-white-primary;
}

.inset {
  box-shadow: 0 4px 28px rgba(0, 0, 0, 0.5);
  padding: spacing(2);

  &:hover {
    box-shadow: 0 4px 40px $color-misc-black;
  }
}

.HoverGradient {
  background-color: var(--color-theme--secondary);
  inset: 0;
  mask-image: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.5) 0%,
    rgba(0, 0, 0, 0) 66.6%,
    rgba(0, 0, 0, 0) 100%
  );
  opacity: 0;
  position: absolute;
  transition: 250ms ease-in-out;

  .Card:has(a:active, a:hover) & {
    opacity: 1;
  }
}
