import React from 'react'
import cx from 'classnames'
import { array, string, bool, number, shape, oneOfType } from 'prop-types'

import styles from './Card.module.scss'

import Grid from '../Grid'
import CardImage from './component/CardImage'
import CardContent from './component/CardContent'

const Card = (props) => {
  const {
    inverse,
    inset,
    hasShade,
    horizontal,
    gridWidths,
    gridGutter,
    rowIndex,
    articleIndex,
    isDesktop,
    className,
    classNames,
    hideStandfirst = false,
    newFeatureStyle = false
  } = props
  const imageProps = (({
    src,
    srcSet,
    sizes,
    imageIcon,
    imageRatio,
    imageLoad,
    width
  }) => ({
    src,
    srcSet,
    sizes,
    imageIcon,
    imageRatio,
    imageLoad,
    width,
    rowIndex,
    articleIndex,
    isDesktop
  }))(props)

  const contentProps = (({
    description,
    link,
    isExternalLink,
    siblingInstance,
    to,
    eventBaseSlug,
    title,
    subtitle,
    shouldShowSubtitle,
    headerComponents,
    footerComponents,
    contentBorder,
    clickableFooter,
    typeConfig,
    truncateTitleAt,
    truncateDescriptionAt,
    hideDescriptionOnMobile,
    hasShade
  }) => ({
    description,
    link,
    isExternalLink,
    siblingInstance,
    to,
    eventBaseSlug,
    title,
    subtitle,
    shouldShowSubtitle,
    headerComponents,
    footerComponents,
    contentBorder,
    clickableFooter,
    typeConfig,
    truncateTitleAt,
    truncateDescriptionAt,
    hideDescriptionOnMobile,
    hasShade
  }))(props)

  if (hideStandfirst) {
    contentProps.description = null
  }

  return (
    <div className={cx(styles.CardWrapper, className)}>
      <div
        className={cx(
          styles.Card,
          inverse && styles.inverse,
          inset && styles.inset,
          horizontal && styles.horizontal,
          classNames?.card,
          newFeatureStyle && styles.NewFeatureStyle
        )}
      >
        {props.siblingInstance?.logoUrl && (
          <a
            href={`https://${props.siblingInstance.subdomain}`}
            className={styles.FloatyLogo}
            // eslint-disable-next-line react/jsx-no-target-blank
            target="_blank"
          >
            <img src={props.siblingInstance.logoUrl} alt="Logo" />
          </a>
        )}
        {horizontal && (
          <div className={styles.GridWrapper}>
            <Grid flex gutter={gridGutter}>
              <Grid.Item
                trailingGap={horizontal === 'desktop' ? 'mobile' : null}
                width={{
                  m: horizontal !== 'desktop' && gridWidths[0],
                  d: gridWidths[0]
                }}
              >
                <CardImage classNames={classNames?.cardImage} {...imageProps} />
              </Grid.Item>
              <Grid.Item
                width={{
                  m: horizontal !== 'desktop' && gridWidths[1],
                  d: gridWidths[1]
                }}
              >
                <CardContent
                  cappedHeight={hasShade}
                  classNames={classNames?.cardContent}
                  {...contentProps}
                />
              </Grid.Item>
            </Grid>
          </div>
        )}

        {!horizontal && (
          <>
            <CardImage classNames={classNames?.cardImage} {...imageProps} />
            <CardContent
              classNames={classNames?.cardContent}
              {...contentProps}
            />
          </>
        )}
      </div>
    </div>
  )
}

Card.defaultProps = {
  gridWidths: [5 / 11, 6 / 11],
  gridGutter: 'small'
}

Card.propTypes = {
  link: string.isRequired,
  siblingInstance: shape({
    logoUrl: string,
    subdomain: string
  }),
  to: string,
  gridWidths: array,
  gridGutter: string,
  inverse: bool,
  inset: bool,
  hasShade: bool,
  horizontal: oneOfType([bool, string]),
  eventBaseSlug: string,
  rowIndex: number,
  articleIndex: number,
  isDesktop: bool,
  className: string,
  hideStandfirst: bool,
  classNames: shape({
    link: string,
    card: string,
    cardImage: shape({
      root: string
    }),
    cardContent: shape({
      root: string,
      inner: string,
      description: string,
      title: string
    })
  }),
  newFeatureStyle: bool
}
export default Card
