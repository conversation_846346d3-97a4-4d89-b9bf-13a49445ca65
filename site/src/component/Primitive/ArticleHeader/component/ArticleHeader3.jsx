import React from 'react'
import { format } from 'date-fns'
import { bool, shape, string } from 'prop-types'
import styles from './ArticleHeader.module.scss'
import Breadcrumbs from '../../Breadcrumbs'
import Type from '../../Type'
import ArticleShare from '../../ArticleShare'
import SmartLink from '../../SmartLink'
import { getTimezoneAbbreviation } from '@/lib/timezoneAbbreviationMiddleware'

const ArticleHeader3 = ({
  headline,
  contentType,
  startDate,
  endDate,
  address,
  author,
  __shareUrl,
  localeSafeStartTime,
  timezone,
  hideOnDesktop
}) => {
  const timezoneAbbreviation = getTimezoneAbbreviation(endDate, timezone)

  const formattedDate = `${format(new Date(startDate), 'E dd MMM')} - ${format(
    new Date(endDate),
    'E dd MMM, yyyy'
  )}`

  // Use timezone-safe time if available, otherwise fallback to regular formatting
  const displayStartTime =
    localeSafeStartTime && localeSafeStartTime !== 'unset'
      ? localeSafeStartTime
      : format(new Date(startDate), 'HH:mm')

  const displayTimezone =
    timezoneAbbreviation && timezoneAbbreviation !== 'unset'
      ? ` (${timezoneAbbreviation})`
      : ''

  const formattedTime = `${displayStartTime}${displayTimezone}`
  const formattedAddress = address.split(', ')[0]
  const crumbs = [contentType, formattedDate, formattedAddress]
  if (timezone) crumbs.push(formattedTime)
  const finalCrums = crumbs.map((item) => ({
    name: item
  }))

  return (
    <div className={styles.ArticleHeader3}>
      <div className={styles.Headline}>
        <Type themed as="h1" size={['heading3', 'display1']} weight="bold">
          {headline}
        </Type>
      </div>
      <Breadcrumbs
        size={['body2', 'heading6']}
        highlight="all-but-first"
        breadcrumbs={finalCrums}
      />
      <ArticleShare url={__shareUrl} hideOnDesktop={hideOnDesktop} />
      {author && author?.slug && (
        <Type size={['body2', 'body3']} weight="medium">
          By{' '}
          <SmartLink
            to="/author"
            as={`/author/${author?.slug}`}
            className={styles.Author}
          >
            {author?.name}
          </SmartLink>
        </Type>
      )}
      {author && !author?.slug && (
        <Type
          size={['body2', 'body3']}
          weight="medium"
          className={styles.AuthorLink}
        >
          By {author?.name}
        </Type>
      )}
    </div>
  )
}

ArticleHeader3.propTypes = {
  headline: string.isRequired,
  contentType: string.isRequired,
  startDate: string,
  endDate: string,
  address: string,
  author: shape({
    name: string.isRequired,
    slug: string
  }),
  __shareUrl: string,
  localeSafeStartTime: string,
  timezone: string,
  hideOnDesktop: bool
}

export default ArticleHeader3
