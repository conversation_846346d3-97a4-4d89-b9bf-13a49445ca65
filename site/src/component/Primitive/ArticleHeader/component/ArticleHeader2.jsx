import React from 'react'
import classNames from 'classnames'
import { format } from 'date-fns'
import { string, bool, shape, object, arrayOf } from 'prop-types'
import Head from 'next/head'
import changeFileExtension from '@/lib/filename-extension-modifier'

import styles from './ArticleHeader.module.scss'
import Breadcrumbs from '../../Breadcrumbs'
import Type from '../../Type'
import ResponsiveImage from '../../ResponsiveImage'
import ResponsiveMedia from '../../ResponsiveMedia'
import SmartLink from '@/component/Primitive/SmartLink'
import ButtonStandard from '@/component/Primitive/ButtonStandard'
import Hide from '@/component/Primitive/Hide'
import ArticleShare from '../../ArticleShare'
import Stack from '@/component/Primitive/Stack'

const ArticleHeader2 = ({
  headline,
  subAttribution,
  contentType,
  displayDate,
  images,
  companies,
  hideShortHeadline,
  issuPublicationId,
  issuIssueId,
  slug,
  magazineOrigin,
  author,
  __shareUrl,
  hideOnDesktop
}) => {
  const formattedDisplayDate = format(new Date(displayDate), 'MMMM dd, yyyy')

  const getShortHeadline = (contentType, companies) => {
    const headlineMap = {
      'Company Report': companies[0]?.shortDescription
    }

    return headlineMap[contentType]
  }

  const ConditionalMediaWrapper = ({ condition, wrapper, children }) =>
    condition ? wrapper(children) : children

  const isMediaClickable =
    (issuPublicationId && issuIssueId) ||
    (magazineOrigin === 'joomag' && issuIssueId)

  const srcSetFormatter = (srcSet) => {
    return srcSet.map((image) => `${image.src} ${image.width}w`).join(', ')
  }

  const imgSrcSet = [
    {
      src: changeFileExtension(images.thumbnail_portrait_144[0].url, 'webp'),
      width: 144
    },
    {
      src: changeFileExtension(images.thumbnail_portrait_286[0].url, 'webp'),
      width: 286
    },
    {
      src: changeFileExtension(images.thumbnail_portrait_576[0].url, 'webp'),
      width: 576
    }
  ]

  return (
    <Stack className={styles.ArticleHeader2Wrapper}>
      <div className={styles.ArticleHeader2}>
        <Head>
          <link
            rel="preload"
            as="image"
            href={imgSrcSet[0].src}
            imagesrcset={srcSetFormatter(imgSrcSet)}
            imagesizes={[
              '(min-width: 800px) 286px',
              '(min-width: 600px) 35vw',
              'calc((100vw - 80px) * 0.5)'
            ]}
          />
        </Head>
        {contentType === 'Company Report' && isMediaClickable && (
          <Hide at="tablet">
            <div className={styles.ButtonWrapper}>
              <ButtonStandard
                to="/brochure/[slug]"
                as={'/brochure/' + slug}
                className={styles.Button}
                primary
              >
                View Full Report
              </ButtonStandard>
            </div>
          </Hide>
        )}
        {contentType === 'Whitepaper' && isMediaClickable && (
          <Hide at="tablet">
            <div className={styles.ButtonWrapper}>
              <ButtonStandard
                to="/brochure/[slug]"
                as={'/brochure/' + slug}
                className={styles.Button}
                primary
              >
                View Whitepaper
              </ButtonStandard>
            </div>
          </Hide>
        )}
        <div className={styles.Image}>
          <ConditionalMediaWrapper
            condition={isMediaClickable}
            wrapper={(children) => (
              <SmartLink to="/brochure/[slug]" as={'/brochure/' + slug}>
                {children}
              </SmartLink>
            )}
          >
            <ResponsiveMedia ratio={455 / 322}>
              <ResponsiveImage
                alt={headline}
                src={images.thumbnail_portrait_286[0].url}
                srcSet={imgSrcSet}
                sizes={[
                  '(min-width: 800px) 286px',
                  '(min-width: 600px) 35vw',
                  'calc((100vw - 80px) * 0.5)'
                ]}
                loading="eager"
              />
            </ResponsiveMedia>
          </ConditionalMediaWrapper>
        </div>
        <div className={styles.Content}>
          <Breadcrumbs
            highlight="none"
            breadcrumbs={[contentType, formattedDisplayDate].map((item) => ({
              name: item
            }))}
          />
          <div className={styles.Headline}>
            <Type themed as="h1" size={['heading3', 'display1']} weight="bold">
              {headline}
            </Type>
          </div>
          {!hideShortHeadline && (
            <div
              className={classNames(
                styles.ShortHeadline,
                contentType !== 'Company Report' && styles.themed
              )}
            >
              <Type themed as="h2" size={['body2', 'heading6']} weight="medium">
                <div
                  dangerouslySetInnerHTML={{
                    __html:
                      getShortHeadline(contentType, companies) || subAttribution
                  }}
                />
              </Type>
            </div>
          )}
          {companies[0] &&
            contentType !== 'Company Report' &&
            contentType !== 'Whitepaper' && (
              <div className={styles.Company}>
                <img
                  loading="lazy"
                  src={companies[0].images?.logo_free_127[0].url}
                  alt={companies[0].name}
                />
              </div>
            )}
          {contentType === 'Company Report' && isMediaClickable && (
            <Hide below="tablet">
              <div className={styles.ButtonWrapper}>
                <ButtonStandard
                  to="/brochure/[slug]"
                  as={'/brochure/' + slug}
                  className={styles.Button}
                  primary
                >
                  View Full Report
                </ButtonStandard>
              </div>
            </Hide>
          )}
          {contentType === 'Whitepaper' && isMediaClickable && (
            <Hide below="tablet">
              <div className={styles.ButtonWrapper}>
                <ButtonStandard
                  to="/brochure/[slug]"
                  as={'/brochure/' + slug}
                  className={styles.Button}
                  primary
                >
                  View Whitepaper
                </ButtonStandard>
              </div>
            </Hide>
          )}
        </div>
      </div>
      <ArticleShare url={__shareUrl} hideOnDesktop={hideOnDesktop} />
      {author && author?.slug && (
        <Type size={['body2', 'body3']} weight="medium">
          By{' '}
          <SmartLink
            to="/author"
            as={`/author/${author?.slug}`}
            className={styles.Author}
          >
            {author?.name}
          </SmartLink>
        </Type>
      )}
      {author && !author?.slug && (
        <Type
          size={['body2', 'body3']}
          weight="medium"
          className={styles.AuthorLink}
        >
          By {author?.name}
        </Type>
      )}
    </Stack>
  )
}

ArticleHeader2.propTypes = {
  headline: string.isRequired,
  subAttribution: string,
  contentType: string.isRequired,
  images: object,
  displayDate: string,
  hideShortHeadline: bool,
  companies: arrayOf(
    shape({
      logo: string,
      name: string
    })
  ),
  issuPublicationId: string,
  issuIssueId: string,
  slug: string,
  author: shape({
    name: string.isRequired,
    slug: string
  }),
  magazineOrigin: string,
  __shareUrl: string,
  hideOnDesktop: bool
}

export default ArticleHeader2
