import React from 'react'
import { array, arrayOf, shape, string } from 'prop-types'
import styles from './UpcomingEventsCarousel.module.scss'
import moment from 'moment'
import LogoScroller from '@/component/Primitive/LogoScroller'
import cx from 'classnames'
import YouTubeEmbed from '@/component/Primitive/YouTubeEmbed'
import { Splide, SplideSlide, SplideTrack } from '@splidejs/react-splide'
import { ButtonGroup } from '@/component/Primitive/Events'

export const EVENT = {
  name: string.isRequired,
  darkLogoUrl: string,
  lightLogoUrl: string,
  startDate: string.isRequired,
  endDate: string,
  buildingName: string,
  sponsors: array,
  backgroundVideoId: string,
  brandColor: string,
  buttonGroup: array,
  shortDescription: string,
  portfolioLogoOverrideUrl: string
}

const getLowestTierSponsors = (sponsors) => {
  if (!sponsors || sponsors.length === 0) {
    return []
  }

  const lowestRank = Math.min(...sponsors.map((sponsor) => sponsor.tier.rank))

  return sponsors.filter((sponsor) => sponsor.tier.rank === lowestRank)
}

const DateRange = ({ startDate, endDate }) => {
  const renderDateRange = () => {
    const start = moment(startDate)
    const end = endDate ? moment(endDate) : moment(startDate)

    if (!endDate || start.isSame(end, 'day')) {
      return (
        <time dateTime={start.format('YYYY-MM-DD')}>
          {start.format('D MMMM YYYY')}
        </time>
      )
    }

    if (start.isSame(end, 'month')) {
      return (
        <>
          <time dateTime={start.format('YYYY-MM-DD')}>{start.format('D')}</time>
          {' - '}
          <time dateTime={end.format('YYYY-MM-DD')}>
            {end.format('D MMMM YYYY')}
          </time>
        </>
      )
    }

    if (start.isSame(end, 'year')) {
      return (
        <>
          <time dateTime={start.format('YYYY-MM-DD')}>
            {start.format('D MMMM')}
          </time>
          {' - '}
          <time dateTime={end.format('YYYY-MM-DD')}>
            {end.format('D MMMM YYYY')}
          </time>
        </>
      )
    }

    return (
      <>
        <time dateTime={start.format('YYYY-MM-DD')}>
          {start.format('D MMMM YYYY')}
        </time>
        {' - '}
        <time dateTime={end.format('YYYY-MM-DD')}>
          {end.format('D MMMM YYYY')}
        </time>
      </>
    )
  }

  return <span className={styles.DateRange}>{renderDateRange()}</span>
}

DateRange.propTypes = {
  startDate: EVENT.startDate,
  endDate: EVENT.endDate
}

const UpcomingEventsSlide = ({
  name,
  darkLogoUrl,
  lightLogoUrl,
  startDate,
  endDate,
  buildingName,
  sponsors,
  backgroundVideoId,
  brandColor,
  buttonGroup,
  shortDescription,
  overArchingLogoUrl,
  portfolioLogoOverrideUrl,
  eventButtonForegroundColor
}) => {
  const logoUrl = darkLogoUrl || lightLogoUrl
  const lowestTierSponsors = getLowestTierSponsors(sponsors)?.map(
    (sponsor) => ({
      url: sponsor.logoSponsorUrl
    })
  )
  const finalOverArchingLogoUrl = portfolioLogoOverrideUrl || overArchingLogoUrl

  return (
    <div
      className={styles.Holder}
      style={{
        '--color-theme--event': brandColor,
        '--color-theme--event-button-foreground': eventButtonForegroundColor
      }}
    >
      <div className={cx(styles.YoutubeEmbed)}>
        {backgroundVideoId && (
          <YouTubeEmbed
            hideControls
            autoplay
            loop
            videoId={backgroundVideoId}
          />
        )}

        <div className={styles.YoutubeEmbedOverlay} />
      </div>

      <div className={styles.Content}>
        <div className={styles.Logo}>
          <h2 className={styles.HideTitle}>{name}</h2>

          {finalOverArchingLogoUrl && (
            <img src={finalOverArchingLogoUrl} alt="" />
          )}
        </div>

        <div className={styles.Logo}>
          {logoUrl && <img src={logoUrl} alt="" />}
        </div>

        {shortDescription && (
          <h3 className={styles.ShortDescription}>{shortDescription}</h3>
        )}

        <div className={styles.Text}>
          {startDate && <DateRange startDate={startDate} endDate={endDate} />}

          {buildingName && <p>{buildingName}</p>}
        </div>

        {!!buttonGroup?.length && (
          <ButtonGroup group={buttonGroup} className={styles.ButtonGroup} />
        )}
      </div>

      {lowestTierSponsors.length && (
        <div className={styles.Sponsors}>
          <LogoScroller logos={lowestTierSponsors} />
        </div>
      )}
    </div>
  )
}

UpcomingEventsSlide.propTypes = {
  ...EVENT,
  overArchingLogoUrl: string
}

const UpcomingEventsCarousel = ({
  events,
  overArchingEventLightLogoUrl,
  overArchingEventDarkLogoUrl
}) => {
  /**
   * @type {import('@splidejs/splide').Options}
   */
  const OPTIONS = {
    pagination: true,
    arrows: false,
    autoplay: true,
    interval: 8000,
    perPage: 1,
    type: 'loop',
    pauseOnHover: false,
    pauseOnFocus: false,
    autoHeight: true
  }
  const overArchingLogoUrl =
    overArchingEventDarkLogoUrl || overArchingEventLightLogoUrl

  return (
    <div className={styles.UpcomingEventsCarousel}>
      <Splide options={OPTIONS} hasTrack={false}>
        <div className={cx(styles.Progress, 'splide__progress')}>
          <div className="splide__progress__bar" />
        </div>

        <div className={cx(styles.Pagination, 'splide__pagination')} />
        <SplideTrack>
          {events?.map((event, key) => (
            <SplideSlide key={key}>
              <UpcomingEventsSlide
                {...event}
                overArchingLogoUrl={overArchingLogoUrl}
              />
            </SplideSlide>
          ))}
        </SplideTrack>
      </Splide>
    </div>
  )
}

UpcomingEventsCarousel.propTypes = {
  events: arrayOf(shape(EVENT)),
  overArchingEventLightLogoUrl: string,
  overArchingEventDarkLogoUrl: string
}

export default UpcomingEventsCarousel
