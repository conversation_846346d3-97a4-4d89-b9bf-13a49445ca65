import React from 'react'
import { shape, string, array } from 'prop-types'

import styles from './AdvertiseHeader.module.scss'
import Type from '../Type'
import ResponsiveImage from '../ResponsiveImage'
import classNames from 'classnames'
import ButtonStandard from '../ButtonStandard'

const AdvertiseHeader = ({ brand, image }) => {
  return (
    <div className={styles.AdvertiseHeader}>
      {image && (
        <>
          <ResponsiveImage
            src={image.desktop.src}
            srcSet={image.desktop.srcSet}
            sizes={image.desktop.sizes}
            className={classNames(styles.HeaderImage, styles.headerDesktop)}
            loading="eager"
            alt=""
          />
          <ResponsiveImage
            alt=""
            src={image.mobile.src}
            srcSet={image.mobile.srcSet}
            sizes={image.mobile.sizes}
            className={classNames(styles.HeaderImage, styles.headerMobile)}
            loading="eager"
          />
        </>
      )}
      <div className={styles.Content}>
        <Type
          themed
          as="h1"
          size={['heading5', 'display1']}
          weight="medium"
          className={styles.Title}
        >
          Advertise with <span hidden>{brand.name}</span>
        </Type>
        {brand && (
          <img
            src={brand.logo}
            alt={brand.name || ''}
            className={styles.Logo}
            loading="lazy"
          />
        )}
        <ButtonStandard
          primary
          size="large"
          className={styles.CTA}
          href="#media-kit-download"
        >
          <Type size={['body2', 'heading8']} weight="bold">
            Request Media Kit
          </Type>
        </ButtonStandard>
      </div>
    </div>
  )
}

AdvertiseHeader.propTypes = {
  brand: shape({
    logo: string.isRequired,
    name: string.isRequired
  }).isRequired,
  image: shape({
    desktop: shape({
      src: string.isRequired,
      srcSet: array,
      sizes: array
    }).isRequired,
    mobile: shape({
      src: string.isRequired,
      srcSet: array,
      sizes: array
    }).isRequired
  })
}

export default AdvertiseHeader
