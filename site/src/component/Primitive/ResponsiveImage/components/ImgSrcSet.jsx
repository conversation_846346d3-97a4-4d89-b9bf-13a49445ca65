import React from 'react'
import { arrayOf, bool, number, oneOf, shape, string } from 'prop-types'
import { useInView } from 'react-intersection-observer'

const srcSetFormatter = (srcSet) => {
  return srcSet.map((image) => `${image.src} ${image.width}w`).join(', ')
}

const ResponsiveImage = ({
  alt,
  height,
  loading,
  sizes,
  src,
  srcSet,
  width,
  className,
  isDesktop
}) => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: isDesktop ? 1 : 0.1,
    rootMargin: isDesktop ? '0px 0px' : '600px 0px',
    initialInView: false
  })

  if (loading === 'eager')
    return (
      <img
        alt={alt}
        width={width}
        height={height}
        loading={loading}
        src={src}
        sizes={sizes && sizes.join(',')}
        srcSet={srcSet && srcSetFormatter(srcSet)}
        className={className}
        style={{ background: 'grey' }}
      />
    )

  return (
    <>
      <div ref={ref} style={{ background: 'black' }} />
      {inView && (
        <img
          alt={alt}
          width={width}
          height={height}
          loading="eager"
          src={src}
          sizes={sizes && sizes.join(',')}
          srcSet={srcSet && srcSetFormatter(srcSet)}
          className={className}
        />
      )}
    </>
  )
}

ResponsiveImage.propTypes = {
  alt: string.isRequired,
  height: number,
  loading: oneOf(['auto', 'eager', 'lazy']),
  sizes: arrayOf(string),
  src: string.isRequired,
  srcSet: arrayOf(shape({ width: number.isRequired, src: string.isRequired })),
  width: number,
  className: string,
  isDesktop: bool
}

export default ResponsiveImage
