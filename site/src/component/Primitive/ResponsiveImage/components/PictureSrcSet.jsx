import React from 'react'
import { arrayOf, number, oneOf, shape, string } from 'prop-types'

const Sources = ({ srcSet }) => {
  const sortedSizes = srcSet.sort((a, b) => b.width - a.width)

  return (
    <>
      {sortedSizes?.map((image, index) => (
        <source
          key={index}
          srcSet={image.src}
          media={`(width >= ${image.width}px)`}
        />
      ))}
    </>
  )
}

Sources.propTypes = {
  srcSet: arrayOf(shape({ width: number.isRequired, src: string.isRequired }))
    .isRequired
}

const ResponsiveImage = ({
  alt,
  height,
  loading,
  src,
  srcSet,
  width,
  className
}) => {
  const Image = () => (
    <img
      alt={alt || ''}
      width={width}
      height={height}
      loading={loading}
      src={src}
      className={className}
    />
  )

  return Array.isArray(srcSet) && srcSet.length > 0 ? (
    <picture>
      <Sources srcSet={srcSet} />
      <Image />
    </picture>
  ) : (
    <Image />
  )
}

ResponsiveImage.propTypes = {
  alt: string.isRequired,
  height: number,
  loading: oneOf(['auto', 'eager', 'lazy']),
  src: string.isRequired,
  srcSet: arrayOf(shape({ width: number.isRequired, src: string.isRequired })),
  width: number,
  className: string
}

export default ResponsiveImage
