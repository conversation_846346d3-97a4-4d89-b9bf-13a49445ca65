import React from 'react'
import { arrayOf, bool, number, oneOf, shape, string } from 'prop-types'
import ImgSrcSet from './components/ImgSrcSet'
import PictureSrcSet from './components/PictureSrcSet'
import { useFeatureFlags } from '@/component/Context/FeatureFlagContext'

const ResponsiveImage = (props) => {
  const { isFeatureEnabled } = useFeatureFlags() || {}
  if (
    typeof isFeatureEnabled === 'function' &&
    isFeatureEnabled('pictureSrcSet')
  ) {
    return <PictureSrcSet {...props} />
  }

  return <ImgSrcSet {...props} />
}

ResponsiveImage.propTypes = {
  alt: string.isRequired,
  height: number,
  loading: oneOf(['auto', 'eager', 'lazy']),
  sizes: arrayOf(string),
  src: string.isRequired,
  srcSet: arrayOf(shape({ width: number.isRequired, src: string.isRequired })),
  width: number,
  className: string,
  isDesktop: bool
}

export default ResponsiveImage
