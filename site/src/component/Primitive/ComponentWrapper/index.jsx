import React from 'react'
import { node } from 'prop-types'
import { useFaqs } from '@/component/Context/FaqContext'
const ComponentWrapper = ({ children }) => {
  const { getFaqSchema, faqs } = useFaqs()
  const hasFaqs = faqs.length > 0
  return (
    <>
      {children}
      {hasFaqs && (
        <script type="application/ld+json">
          {JSON.stringify(getFaqSchema())}
        </script>
      )}
    </>
  )
}

ComponentWrapper.propTypes = {
  children: node
}

export default ComponentWrapper
