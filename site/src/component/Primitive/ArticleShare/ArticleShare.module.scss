.ArticleShare {
  padding-bottom: spacing(2);

  @include mq($breakpoint-desktop) {
    padding-bottom: spacing(0);
  }
}

.ArticleShareWithFollow {
  display: grid;
  gap: 32px;

  @media (min-width: 350px) {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
  }
}

.ShareContent {
  flex: 1;
}

.FollowButton {
  display: flex;
  align-items: flex-end;
  position: relative;

  @include mq($breakpoint-tablet) {
    right: 0;
    bottom: 0;
  }

  @include mq($breakpoint-desktop) {
    bottom: 4px;
  }
}

@media (min-width: 1331px) {
  .ArticleShareNoDesktop {
    display: none !important;
  }
}

.Title {
  color: $color-misc-red;
  margin-bottom: spacing(1);
  text-transform: uppercase;

  @include mq($breakpoint-desktop) {
    margin-bottom: 8px;
  }
}

.Share {
  display: grid;
  grid-auto-flow: column;
  gap: 24px;

  @include mq($breakpoint-desktop) {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
  }
}

.ShareItem {
  color: var(--row-background-color, $color-black-primary);
  display: block;

  svg {
    height: auto;

    &,
    path {
      fill: currentColor;
    }
  }

  &:hover,
  &:focus {
    svg,
    path {
      fill: var(--color-theme--secondary);
    }
  }
}
