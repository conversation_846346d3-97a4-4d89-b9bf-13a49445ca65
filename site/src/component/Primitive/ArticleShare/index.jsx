import React from 'react'
import { bool, string } from 'prop-types'
import cx from 'classnames'

import {
  EmailShareButton,
  FacebookShareButton,
  LinkedinShareButton,
  TwitterShareButton
} from 'react-share'

import styles from './ArticleShare.module.scss'
import stickyStyles from './StickyArticleShare.module.scss'

import Icon from '../Icon'
import Type from '../Type'
import LinkedInFollowCompany from '../LinkedInFollowCompany'

const ArticleShare = ({
  url,
  sticky,
  hideOnDesktop,
  linkedinFollowSubdomain
}) => (
  <div
    className={cx(
      styles.ArticleShare,
      hideOnDesktop && styles.ArticleShareNoDesktop,
      sticky && stickyStyles.StickyArticleShare,
      linkedinFollowSubdomain && !sticky && styles.ArticleShareWithFollow
    )}
  >
    <div
      className={cx(!sticky && linkedinFollowSubdomain && styles.ShareContent)}
    >
      <Type
        size={['body2', 'body4']}
        weight="medium"
        className={cx(
          styles.Title,
          sticky && stickyStyles.StickyArticleShareTitle
        )}
      >
        Share
      </Type>

      <div
        className={cx(
          styles.Share,
          sticky && stickyStyles.StickyArticleShareRow
        )}
      >
        <LinkedinShareButton
          className={cx(
            styles.ShareItem,
            sticky && stickyStyles.StickyArticleShareItem
          )}
          url={url}
        >
          <Icon
            width="20"
            type="linkedin"
            className={cx(sticky && stickyStyles.StickyArticleShareIcon)}
          />
        </LinkedinShareButton>

        <TwitterShareButton
          className={cx(
            styles.ShareItem,
            sticky && stickyStyles.StickyArticleShareItem
          )}
          url={url}
        >
          <Icon
            width="20"
            type="twitter"
            className={cx(sticky && stickyStyles.StickyArticleShareIcon)}
          />
        </TwitterShareButton>

        <FacebookShareButton
          className={cx(
            styles.ShareItem,
            sticky && stickyStyles.StickyArticleShareItem
          )}
          url={url}
        >
          <Icon
            width="20"
            type="facebook"
            className={cx(sticky && stickyStyles.StickyArticleShareIcon)}
          />
        </FacebookShareButton>

        <EmailShareButton
          subject="I wanted you to see this article"
          body="Check out this article"
          className={cx(
            styles.ShareItem,
            sticky && stickyStyles.StickyArticleShareItem
          )}
          url={url}
        >
          <Icon
            width="24"
            type="email"
            className={cx(sticky && stickyStyles.StickyArticleShareIcon)}
          />
        </EmailShareButton>
      </div>
    </div>
    {!sticky && linkedinFollowSubdomain && (
      <div className={styles.FollowButton}>
        <LinkedInFollowCompany
          subdomain={linkedinFollowSubdomain}
          counter="right"
        />
      </div>
    )}
  </div>
)

ArticleShare.propTypes = {
  url: string.isRequired,
  sticky: bool,
  hideOnDesktop: bool,
  linkedinFollowSubdomain: string
}

export default ArticleShare
