import React, { useContext } from 'react'
import { array, bool } from 'prop-types'

import Grid from '../../Grid'
import Card from '../../Card'
import Carousel from '../../Carousel'
import Hide from '../../Hide'
import Surround from '../../Surround'
import Tag from '../../Tag'
import CardButton from '../../Card/component/CardButton'
import { ThemeContext } from '@/component/Context/ThemeContext'

import brandToColor from '@/lib/brand-to-color'
import Blockquote from '../../Blockquote'
import { RowContext } from '@/component/Context/RowContext'

const CardGrid2 = ({ articles }) => {
  const { theme } = useContext(ThemeContext)
  const inverseRow = useContext(RowContext)
  if (!articles.length) return null
  const themecolor = brandToColor(theme)
  const gridArticles = articles.slice(2)
  const hasFeaturedArticle = articles[0].featured
  const hasQuote = articles[1] && articles[1].quote
  const imageRatio = 455 / 322

  const getButton = (article) => {
    if (!article.startDate || article.contentType !== 'Event')
      return <CardButton inverse={inverseRow}>Read more</CardButton>
    const startDate = new Date(article.startDate)
    if (startDate < new Date()) {
      if (article.onDemandLink)
        return (
          <CardButton inverse theme="secondary">
            Watch on Demand
          </CardButton>
        )
      return (
        <CardButton inverse theme="tertiary">
          Find out more
        </CardButton>
      )
    }
    return (
      <CardButton inverse={inverseRow} theme="primary">
        Register now
      </CardButton>
    )
  }

  return (
    <>
      <Hide below="desktop">
        <Grid flex gutter="default">
          <Grid.Item
            trailingGap={inverseRow ? 'desktop' : null}
            width={{ d: 1 / 3 }}
          >
            <Card
              typeConfig={{
                title: {
                  size: 'heading3',
                  themed: true
                },
                subtitle: {
                  size: 'heading6'
                },
                description: { size: 'body2' }
              }}
              inverse={hasFeaturedArticle || inverseRow}
              inset={hasFeaturedArticle && !inverseRow}
              headerComponents={
                hasFeaturedArticle
                  ? () => <Tag background="white">Featured</Tag>
                  : undefined
              }
              footerComponents={() => getButton(articles[0])}
              {...{
                ...articles[0],
                link:
                  articles[0].onDemandLink ||
                  articles[0].eventRegistrationLink ||
                  articles[0].link,
                isExternalLink:
                  articles[0].onDemandLink || articles[0].eventRegistrationLink,
                sizes: [
                  '(min-width: 1420px) 290px',
                  '(min-width: 960px) calc(25vw - 32px - 80px)',
                  'calc((100vw - 80px) * 0.5)'
                ],
                width: 290
              }}
              imageRatio={imageRatio}
            />
          </Grid.Item>
          <Grid.Item width={{ d: 2 / 3 }}>
            <Grid flex gutter="default">
              {articles[1] && (
                <Grid.Item trailingGap="default">
                  <Grid gutter="default" flex>
                    <Grid.Item
                      trailingGap="default"
                      width={hasQuote && { d: 3 / 3 }}
                    >
                      <Card
                        typeConfig={{
                          title: { size: 'heading6', themed: true },
                          subtitle: {
                            size: ['body2', 'body3'],
                            color: themecolor,
                            themed: true
                          }
                        }}
                        horizontal
                        inverse={inverseRow}
                        gridGutter="default"
                        gridWidths={hasQuote ? [1 / 3, 2 / 3] : [1 / 4, 3 / 4]}
                        footerComponents={() => getButton(articles[1])}
                        {...{
                          ...articles[1],
                          link:
                            articles[1].onDemandLink ||
                            articles[1].eventRegistrationLink ||
                            articles[1].link,
                          isExternalLink:
                            articles[1].onDemandLink ||
                            articles[1].eventRegistrationLink,
                          sizes: [
                            '(min-width: 1420px) 206px',
                            '(min-width: 960px) 104px',
                            'calc(75vw - 80px)'
                          ],
                          width: 206
                        }}
                        imageRatio={imageRatio}
                      />
                    </Grid.Item>
                    {hasQuote && (
                      <Surround padded="desktop" bordered="desktop">
                        <Grid.Item width={{ d: 3 / 3 }}>
                          <Blockquote
                            attribution={articles[1].attribution}
                            secondaryAttribution={articles[1].subAttribution}
                            inverse={inverseRow}
                          >
                            {articles[1].quote}
                          </Blockquote>
                        </Grid.Item>
                      </Surround>
                    )}
                  </Grid>
                </Grid.Item>
              )}
              {gridArticles.map((article, i) => (
                <Grid.Item
                  key={i}
                  trailingGap={hasFeaturedArticle ? 'desktop' : null}
                  width={{ d: 1 / 3 }}
                >
                  <Card
                    typeConfig={{
                      title: { size: 'heading6', themed: true },
                      subtitle: {
                        size: ['body2', 'body3'],
                        color: themecolor,
                        themed: true
                      }
                    }}
                    footerComponents={() => getButton(article)}
                    inverse={inverseRow}
                    {...{
                      ...article,
                      link:
                        article.onDemandLink ||
                        article.eventRegistrationLink ||
                        article.link,
                      isExternalLink:
                        article.onDemandLink || article.eventRegistrationLink,
                      sizes: [
                        '(min-width: 1420px) 206px',
                        'calc(16.6666vw - 80px)'
                      ],
                      width: 206
                    }}
                    imageRatio={imageRatio}
                  />
                </Grid.Item>
              ))}
            </Grid>
          </Grid.Item>
        </Grid>
      </Hide>
      <Hide at="desktop">
        <Grid>
          <Grid.Item trailingGap="mobile">
            <Card
              typeConfig={{
                title: { size: 'heading5' },
                subtitle: { color: themecolor }
              }}
              inverse={articles[0].featured || inverseRow}
              inset={articles[0].featured}
              footerComponents={() => getButton(articles[0])}
              {...{
                ...articles[0],
                link:
                  articles[0].onDemandLink ||
                  articles[0].eventRegistrationLink ||
                  articles[0].link,
                isExternalLink:
                  articles[0].onDemandLink || articles[0].eventRegistrationLink,

                sizes: ['calc((100vw - 80px) * 0.5)'],
                width: 550
              }}
              imageRatio={imageRatio}
            />
          </Grid.Item>
          <Grid.Item trailingGap="mobile">
            <Carousel>
              {articles.slice(1).map((article, i) => (
                <div key={i}>
                  <Card
                    typeConfig={{
                      title: { size: 'heading7' },
                      subtitle: { size: ['body2', 'body3'], color: themecolor }
                    }}
                    inverse={inverseRow}
                    footerComponents={() => getButton(article)}
                    {...{
                      ...article,
                      link:
                        article.onDemandLink ||
                        article.eventRegistrationLink ||
                        article.link,
                      isExternalLink:
                        article.onDemandLink || article.eventRegistrationLink,
                      sizes: ['calc(75vw - 80px)'],
                      width: 412
                    }}
                    imageRatio={imageRatio}
                  />
                </div>
              ))}
            </Carousel>
          </Grid.Item>
        </Grid>
      </Hide>
    </>
  )
}

CardGrid2.propTypes = {
  articles: array,
  hasFeaturedArticle: bool
}

export default CardGrid2
