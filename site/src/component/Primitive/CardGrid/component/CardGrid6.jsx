import React, { useContext } from 'react'
import { array } from 'prop-types'

import Grid from '../../Grid'
import Card from '../../Card'
import Surround from '../../Surround'
import Icon from '../../Icon'
import Tag from '../../Tag'
import CardButton from '../../Card/component/CardButton'
import { ThemeContext } from '@/component/Context/ThemeContext'
import brandToColor from '@/lib/brand-to-color'

const CardGrid6 = ({ articles }) => {
  const { theme } = useContext(ThemeContext)
  if (!articles.length) return null
  const themecolor = brandToColor(theme)
  const remainingArticles = articles.slice(1)
  const firstArticleIsLive = articles[0].isLive
  const imageRatio = 126 / 167

  const getButton = (article) => {
    if (!article.startDate || article.contentType !== 'Event')
      return <CardButton>Read more</CardButton>

    const startDate = new Date(article.startDate)
    if (startDate < new Date()) {
      if (article.onDemandLink)
        return <CardButton theme="secondary">Watch on Demand</CardButton>
      return <CardButton theme="tertiary">Find out more</CardButton>
    }
    return <CardButton theme="primary">Register now</CardButton>
  }

  return (
    <Grid flex gutter="default">
      <Grid.Item width={{ m: 1 / 2, d: 1 / 4 }} trailingGap="default">
        <Surround
          bordered={!firstArticleIsLive && 'default'}
          padded={!firstArticleIsLive && 'default'}
        >
          <Card
            typeConfig={{
              title: { size: ['heading7', 'heading6'], themed: true },
              description: { size: ['body2', 'body4'] }
            }}
            inverse={firstArticleIsLive}
            inset={firstArticleIsLive}
            headerComponents={
              firstArticleIsLive
                ? () => (
                    <>
                      <Icon a11ytext="Live" type="live" />
                      <Tag
                        style={{
                          border: `1px solid ${themecolor}`,
                          color: themecolor
                        }}
                        uppercase
                        background="black"
                      >
                        Live on BMG Connect
                      </Tag>
                    </>
                  )
                : undefined
            }
            footerComponents={() => getButton(articles[0])}
            themedEyebrow={firstArticleIsLive}
            {...{
              ...articles[0],
              link:
                articles[0].onDemandLink ||
                articles[0].eventRegistrationLink ||
                articles[0].link,
              isExternalLink:
                articles[0].onDemandLink || articles[0].eventRegistrationLink,
              sizes: [
                '(min-width: 1420px) 322px',
                '(min-width: 960px) calc(25vw - 32px - 80px)',
                'calc((100vw - 80px) * 0.5)'
              ],
              width: 322
            }}
            imageRatio={imageRatio}
          />
        </Surround>
      </Grid.Item>
      {remainingArticles.map((article, i) => (
        <Grid.Item
          hidden={i > 2 ? 'mobile' : null}
          key={i}
          width={{ m: 1 / 2, d: 1 / 4 }}
          trailingGap="default"
        >
          <Surround bordered="default" padded="default">
            <Card
              typeConfig={{
                title: { size: ['heading7', 'heading6'], themed: true },
                description: { size: ['body2', 'body4'] }
              }}
              footerComponents={() => getButton(article)}
              {...{
                ...article,
                link:
                  article.onDemandLink ||
                  article.eventRegistrationLink ||
                  article.link,
                isExternalLink:
                  article.onDemandLink || article.eventRegistrationLink,
                sizes: [
                  '(min-width: 1420px) 322px',
                  '(min-width: 960px) calc(25vw - 32px - 80px)',
                  'calc((100vw - 80px) * 0.5)'
                ],
                width: 322
              }}
              imageRatio={imageRatio}
            />
          </Surround>
        </Grid.Item>
      ))}
    </Grid>
  )
}

CardGrid6.propTypes = {
  articles: array
}

export default CardGrid6
