import React from 'react'
import { array } from 'prop-types'

import Grid from '../../Grid'
import Card from '../../Card'
import Tag from '../../Tag'
import CardButton from '../../Card/component/CardButton'

const CardGrid11 = ({ articles }) => {
  if (!articles.length) return null

  const getButton = (article) => {
    if (!article.startDate || article.contentType !== 'Event')
      return <CardButton inverse>Read more</CardButton>

    const startDate = new Date(article.startDate)
    if (startDate < new Date()) {
      if (article.onDemandLink)
        return (
          <CardButton inverse theme="secondary">
            Watch on Demand
          </CardButton>
        )
      return (
        <CardButton inverse theme="tertiary">
          Find out more
        </CardButton>
      )
    }
    return (
      <CardButton inverse theme="primary">
        Register now
      </CardButton>
    )
  }
  const imageRatio = 455 / 322
  return (
    <Grid flex gutter="default">
      <Grid.Item trailingGap="mobile" width={{ d: 1 / 3 }}>
        <Card
          typeConfig={{
            title: {
              size: ['heading5', 'heading3'],
              themed: true
            },
            subtitle: { size: ['heading7', 'heading6'], color: '#989898' },
            description: { size: 'body2' }
          }}
          inverse={articles[0].featured}
          inset={articles[0].featured}
          headerComponents={() => {
            if (!articles[0].featured) return null
            return (
              <Tag themedFont background="white">
                Featured
              </Tag>
            )
          }}
          footerComponents={() => getButton(articles[0])}
          {...{
            ...articles[0],
            link:
              articles[0].onDemandLink ||
              articles[0].eventRegistrationLink ||
              articles[0].link,
            isExternalLink:
              articles[0].onDemandLink || articles[0].eventRegistrationLink,
            sizes: [
              '(min-width: 1420px) 298px',
              '(min-width: 960px) calc(25vw - 32px - 80px)',
              'calc((100vw - 80px) * 0.5)'
            ],
            width: 298
          }}
          imageRatio={imageRatio}
        />
      </Grid.Item>
      <Grid.Item width={{ d: 2 / 3 }}>
        <Grid flex gutter="default">
          {articles[1] && (
            <Grid.Item trailingGap="default">
              <Card
                typeConfig={{
                  title: {
                    size: ['heading5', 'heading2'],
                    themed: true
                  },
                  subtitle: {
                    size: ['heading7', 'heading3'],
                    color: '#989898'
                  },
                  description: { size: 'body2' }
                }}
                inverse={articles[1].featured}
                inset={articles[1].featured}
                horizontal="desktop"
                gridGutter="default"
                headerComponents={() => {
                  if (!articles[1].featured) return null
                  return (
                    <Tag themedFont background="white">
                      Featured
                    </Tag>
                  )
                }}
                footerComponents={() => getButton(articles[1])}
                {...{
                  ...articles[1],
                  link:
                    articles[1].onDemandLink ||
                    articles[1].eventRegistrationLink ||
                    articles[1].link,
                  isExternalLink:
                    articles[1].onDemandLink ||
                    articles[1].eventRegistrationLink,
                  sizes: [
                    '(min-width: 1420px) 283px',
                    '(min-width: 960px) calc(25vw - 32px - 80px)',
                    'calc(50vw - 80px)'
                  ],
                  width: 283
                }}
                imageRatio={imageRatio}
              />
            </Grid.Item>
          )}
          {articles[2] && (
            <Grid.Item>
              <Card
                typeConfig={{
                  title: {
                    size: ['heading5', 'heading2'],
                    themed: true
                  },
                  subtitle: {
                    size: ['heading7', 'heading3'],
                    color: '#989898'
                  },
                  description: { size: 'body2' }
                }}
                inverse={articles[2].featured}
                inset={articles[2].featured}
                horizontal="desktop"
                gridGutter="default"
                headerComponents={() => {
                  if (!articles[2].featured) return null
                  return (
                    <Tag themedFont background="white">
                      Featured
                    </Tag>
                  )
                }}
                footerComponents={() => getButton(articles[2])}
                {...{
                  ...articles[2],
                  link:
                    articles[2].onDemandLink ||
                    articles[2].eventRegistrationLink ||
                    articles[2].link,
                  isExternalLink:
                    articles[2].onDemandLink ||
                    articles[2].eventRegistrationLink,
                  sizes: [
                    '(min-width: 1420px) 283px',
                    '(min-width: 960px) calc(25vw - 32px - 80px)',
                    'calc(50vw - 80px)'
                  ],
                  width: 283
                }}
                imageRatio={imageRatio}
              />
            </Grid.Item>
          )}
        </Grid>
      </Grid.Item>
    </Grid>
  )
}

CardGrid11.propTypes = {
  articles: array
}

export default CardGrid11
