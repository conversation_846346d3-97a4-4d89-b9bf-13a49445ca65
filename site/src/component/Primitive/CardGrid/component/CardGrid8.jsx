import React, { useContext } from 'react'
import { array, bool } from 'prop-types'

import Grid from '../../Grid'
import Card from '../../Card'
import Surround from '../../Surround'
import Hide from '../../Hide'
import Inline from '../../Inline'
import Tag from '../../Tag'
import CardButton from '../../Card/component/CardButton'
import brandToColor from '@/lib/brand-to-color'
import { ThemeContext } from '@/component/Context/ThemeContext'

const CardGrid8 = ({ articles, displayCategory }) => {
  const { theme } = useContext(ThemeContext)
  if (!articles.length) return null
  const remainingArticles = articles.slice(1, -1)
  const total = articles.length

  const getImageRatio = (type) => {
    const ratioMap = {
      Video: 375 / 668
    }

    return ratioMap[type]
  }

  const getDescription = (article) => {
    const descriptionMap = {
      Event: article.description
    }

    return descriptionMap[article.contentType] || ''
  }

  const getButton = (article, show = false) => {
    if (article.contentType !== 'Event') return null
    if (!article.startDate) return null
    const startDate = new Date(article.startDate)
    if (startDate < new Date()) {
      if (article.onDemandLink)
        return <CardButton theme="secondary">Watch on Demand</CardButton>
      return <CardButton theme="tertiary">Find out more</CardButton>
    }
    return <CardButton theme="primary">Register now</CardButton>
  }

  return (
    <Grid flex gutter="default">
      <Grid.Item trailingGap="default">
        <Hide below="desktop">
          <Card
            typeConfig={{
              title: { size: 'heading2', themed: true },
              subtitle: {
                color: brandToColor(theme),
                themed: true
              }
            }}
            contentBorder={['top', 'bottom']}
            clickableFooter={articles[0].contentType === 'Video'}
            horizontal
            gridGutter="default"
            gridWidths={[5 / 9, 4 / 9]}
            headerComponents={() => {
              if (!articles[0].featured) return null
              return (
                <Tag themedFont background="black">
                  Featured
                </Tag>
              )
            }}
            footerComponents={() => {
              if (articles[0].contentType === 'Video' && !articles[0].hashtags)
                return null
              return (
                <>
                  <Inline gap="small">
                    {articles[0].contentType === 'Video' &&
                      articles[0].hashtags.map((tag, i) => (
                        <Tag
                          key={i}
                          italic
                          uppercase
                          nofollow
                          background="grey"
                          href={`/search?q=${encodeURIComponent(tag)}`}
                        >{`#${tag}`}</Tag>
                      ))}
                    {getButton(articles[0], true)}
                  </Inline>
                </>
              )
            }}
            {...{
              ...articles[0],
              sizes: [
                '(min-width: 1420px) 553px',
                '(min-width: 960px) calc(37.5vw - 32px - 80px)',
                'calc((100vw - 80px) * 0.5)'
              ],
              width: 553
            }}
            imageRatio={getImageRatio(articles[0].contentType) || 126 / 167}
            imageIcon={articles[0].imageIcon}
          />
        </Hide>
        <Hide at="desktop">
          <Surround bordered="mobile" padded="mobile">
            <Card
              typeConfig={{
                title: { size: 'heading7', themed: true },
                subtitle: { themed: true }
              }}
              headerComponents={() => {
                if (!articles[0].featured) return null
                return <Tag background="black">Featured</Tag>
              }}
              footerComponents={() => getButton(articles[0])}
              {...{
                ...articles[0],
                link:
                  articles[0].onDemandLink ||
                  articles[0].eventRegistrationLink ||
                  articles[0].link,
                isExternalLink:
                  articles[0].onDemandLink || articles[0].eventRegistrationLink,
                description: '',
                sizes: ['calc((100vw - 80px) * 0.5)'],
                width: 550
              }}
              imageRatio={getImageRatio(articles[0].contentType) || 126 / 167}
              imageIcon={articles[0].imageIcon}
            />
          </Surround>
        </Hide>
      </Grid.Item>
      {remainingArticles.map((article, i) => (
        <Grid.Item
          hidden={i > 1 ? 'mobile' : null}
          key={i}
          trailingGap={i === 0 ? 'default' : 'desktop'}
          width={{ d: 1 / 3 }}
        >
          <Surround
            padded={i === 0 ? 'mobile' : null}
            bordered={i === 0 ? 'mobile' : null}
          >
            <Card
              typeConfig={{
                title: {
                  size: ['heading7', 'heading6'],
                  weight: 'medium',
                  themed: true
                },
                subtitle: {
                  color: brandToColor(theme),
                  themed: true
                }
              }}
              headerComponents={() => {
                if (!article.category || !displayCategory) return null
                return <Tag>{article.category}</Tag>
              }}
              footerComponents={() => getButton(article)}
              {...{
                ...article,
                link:
                  article.onDemandLink ||
                  article.eventRegistrationLink ||
                  article.link,
                isExternalLink:
                  article.onDemandLink || article.eventRegistrationLink,
                description: getDescription(article),
                sizes: [
                  '(min-width: 1420px) 300px',
                  '(min-width: 960px) calc(25vw - 32px - 80px)',
                  'calc((100vw - 80px) * 0.5)'
                ],
                width: 300
              }}
              imageRatio={getImageRatio(article.contentType) || 126 / 167}
              imageIcon={article.imageIcon}
            />
          </Surround>
        </Grid.Item>
      ))}
      <Grid.Item hidden="mobile" width={{ d: 1 / 3 }} trailingGap="desktop">
        <Card
          typeConfig={{
            title: {
              size: ['heading7', 'heading6'],
              weight: 'medium',
              themed: true
            },
            subtitle: {
              color: brandToColor(theme),
              themed: true
            }
          }}
          headerComponents={() => {
            if (!articles[total - 1].category || !displayCategory) return null
            return <Tag>{articles[total - 1].category}</Tag>
          }}
          footerComponents={() => getButton(articles[total - 1])}
          {...{
            ...articles[total - 1],
            link:
              articles[total - 1].onDemandLink ||
              articles[total - 1].eventRegistrationLink ||
              articles[total - 1].link,
            isExternalLink:
              articles[total - 1].onDemandLink ||
              articles[total - 1].eventRegistrationLink,
            description: getDescription(articles[total - 1]),
            sizes: [
              '(min-width: 1420px) 300px',
              '(min-width: 960px) calc(25vw - 32px - 80px)',
              'calc((100vw - 80px) * 0.5)'
            ],
            width: 300
          }}
          imageRatio={
            getImageRatio(articles[total - 1].contentType) || 126 / 167
          }
          imageIcon={articles[total - 1].imageIcon}
        />
      </Grid.Item>
    </Grid>
  )
}

CardGrid8.propTypes = {
  articles: array,
  displayCategory: bool
}

export default CardGrid8
