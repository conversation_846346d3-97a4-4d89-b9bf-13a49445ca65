// .Hide {}

$HideWidths: (
  tablet: $breakpoint-tablet,
  desktop: $breakpoint-desktop,
  desktopmedium: $breakpoint-desktopMedium,
  desktoplarge: $breakpoint-desktopLarge,
  desktopnav: $breakpoint-desktopNav
);

@each $key, $value in $HideWidths {
  @include mq($value) {
    .at-#{$key} {
      display: none !important;
    }
  }

  @include mq($max: $value - 1) {
    .below-#{$key} {
      display: none !important;
    }
  }
}

// :export {
//   widths: $HideWidths;
// }
