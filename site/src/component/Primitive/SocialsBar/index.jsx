import React from 'react'
import classNames from 'classnames'
import { bool, array } from 'prop-types'

import IconButton from '@/component/Primitive/IconButton'
import List from '@/component/Primitive/List'
import SmartLink from '@/component/Primitive/SmartLink'

import styles from './SocialsBar.module.scss'

const SocialsBar = ({ alternate, socialLinks }) => (
  <div
    className={classNames(styles.Socials, alternate && styles.SocialsAlternate)}
  >
    <List unstyled inline className={styles.SocialsList}>
      {socialLinks?.map(({ type, url }, i) => {
        return (
          <li key={i}>
            <SmartLink href={url} target="_blank">
              <IconButton
                icon={type}
                a11ytext={`${type} icon`}
                className={styles.SocialsIconButton}
              />
            </SmartLink>
          </li>
        )
      })}
    </List>
  </div>
)

SocialsBar.propTypes = {
  alternate: bool,
  socialLinks: array
}

export default SocialsBar
