.Faqs {
  margin-top: 16px;
}

.Details {
  position: relative;
  padding: 20px 80px 20px 20px;

  summary {
    list-style: none;

    svg {
      position: absolute;
      inset: 30px 20px auto auto;
      transition: 250ms ease-in-out;
    }
  }

  &[open] summary,
  &:not([open]) summary:hover {
    svg {
      transform: rotate(180deg);
    }
  }

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: var(--color-theme--secondary);
    z-index: -1;
  }

  &:nth-child(odd)::before {
    opacity: 0.1;
  }

  &:nth-child(even)::before {
    opacity: 0.02;
  }
}

.DetailsContent {
  padding-top: 12px;
}
