import React, { useEffect } from 'react'
import { arrayOf, shape, string } from 'prop-types'
import Prose from '@/component/Primitive/Prose'
import styles from './Faqs.module.scss'
import Icon from '@/component/Primitive/Icon'
import { useFaqs } from '@/component/Context/FaqContext'

const FAQs = ({ title, faqs }) => {
  const { addFaq } = useFaqs()

  useEffect(() => {
    faqs.map(({ question, answer }) => {
      addFaq(question, answer)
    })
  }, [addFaq, faqs])

  return (
    <>
      {title && (
        <Prose>
          <h2>{title}</h2>
        </Prose>
      )}

      {Array.isArray(faqs) && faqs.length && (
        <>
          <div className={styles.Faqs}>
            {faqs.map(({ question, answer }, index) => (
              <details key={index} className={styles.Details}>
                <summary>
                  {question} <Icon width={20} height={20} type="arrow-down" />{' '}
                </summary>

                <div className={styles.DetailsContent}>
                  <Prose dangerousHtml={answer} />
                </div>
              </details>
            ))}
          </div>
        </>
      )}
    </>
  )
}

FAQs.propTypes = {
  title: string,
  faqs: arrayOf(shape({ question: string, answer: string }))
}

export default FAQs
