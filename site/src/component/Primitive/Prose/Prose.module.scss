.Prose {
  // Unset common styles left in place by WYSIWYG editor
  $prose-overrides: 'box-sizing', 'color', 'font-family', 'font-size',
    'font-style', 'font-weight', 'line-height', 'margin', 'padding',
    'text-decoration', 'white-space';

  @each $property in $prose-overrides {
    [style*='#{$property}'] {
      #{$property}: inherit !important;
    }
  }

  font-size: 16px;
  line-height: 1.5;

  &.noTicker a[name][id] {
    margin-top: -75px;
    padding-top: 75px;

    @include mq($breakpoint-desktop) {
      margin-top: -185px;
      padding-top: 185px;
    }
  }

  > * + * {
    margin-top: spacing(3);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: $font-body;
    font-weight: 500;
    line-height: 120%;
  }

  > {
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: spacing(2);

      @include mq($breakpoint-desktop) {
        margin-top: spacing(4);
      }
    }
  }

  //TODO: IMPROVE

  &.march8 {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      font-family: $font-bodoni;
    }
  }

  h1 {
    font-size: 20px;

    & + * {
      margin-top: spacing(1);
    }

    @include mq($breakpoint-desktop) {
      font-size: 32px;

      & + * {
        margin-top: spacing(2);
      }
    }
  }

  h2 {
    font-size: 20px;

    & + * {
      margin-top: spacing(1);
    }

    @include mq($breakpoint-desktop) {
      font-size: 24px;

      & + * {
        margin-top: spacing(2);
      }
    }
  }

  h3 {
    font-size: 18px;

    & + * {
      margin-top: spacing(1);
    }

    @include mq($breakpoint-desktop) {
      font-size: 28px;

      & + * {
        margin-top: spacing(2);
      }
    }
  }

  h4,
  h5,
  h6 {
    font-size: 16px;

    & + * {
      margin-top: spacing(1);
    }
  }

  h4 {
    @include mq($breakpoint-desktop) {
      font-size: 26px;
    }
  }

  h5 {
    @include mq($breakpoint-desktop) {
      font-size: 24px;
    }
  }

  h6 {
    @include mq($breakpoint-desktop) {
      font-size: 20px;
    }
  }

  p {
    line-height: 1.5;
  }

  em {
    font-style: italic;
  }

  li {
    list-style-position: outside;
    margin-left: spacing(2);
  }

  ul {
    li {
      list-style-type: disc;

      li {
        list-style-type: circle;
      }
    }
  }

  ol {
    li {
      list-style-type: decimal;

      li {
        list-style-type: lower-roman;
      }
    }
  }

  a {
    color: var(--color-theme-anchor, currentColor);
    display: inline;
    hyphens: auto;

    // https://css-tricks.com/better-line-breaks-for-long-urls/
    overflow-wrap: break-word;
    text-decoration: underline;
    word-break: break-word;

    /* Adds a hyphen where the word breaks, if supported (No Blink) */
    word-wrap: break-word;

    &:hover,
    &:active,
    &:focus {
      outline: 0;
      text-decoration: none;
    }

    &[name][id] {
      margin-top: -120px;
      padding-top: 120px;

      @include mq($breakpoint-desktop) {
        margin-top: -225px;
        padding-top: 225px;
      }

      &:hover {
        pointer-events: none;
        text-decoration: none;
      }
    }
  }

  p,
  li {
    @include mq($breakpoint-desktop) {
      line-height: 1.5; // 28.8px
    }
  }

  hr {
    border: 0;
    border-bottom: 1px solid currentColor;
    height: 0;
    margin-bottom: -1px;
  }

  iframe[src*='youtube'] {
    height: 180px;
    max-width: 100%;
    width: 320px;

    @include mq($breakpoint-tablet) {
      height: 270px;
      width: 480px;
    }

    @include mq($breakpoint-desktop) {
      height: 360px;
      width: 640px;
    }
  }

  // Last in the group for specificity reasons
  > *:first-child {
    margin-top: 0;
  }
}

.inverse {
  color: $color-white;

  a {
    color: $color-white;

    &:hover,
    &:focus,
    &:active {
      color: $color-white;
    }
  }
}
