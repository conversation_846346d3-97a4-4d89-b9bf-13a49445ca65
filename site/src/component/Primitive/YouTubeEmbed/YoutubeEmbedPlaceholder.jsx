import React, { useEffect, useState } from 'react'
import { string } from 'prop-types'
import YouTubeEmbed, { YouTubeEmbedFallbackUrl } from '.'
import classNames from 'classnames'
import styles from './YoutubeEmbedPlaceholder.module.scss'
import { useInView } from 'react-intersection-observer'
import Icon from '@/component/Primitive/Icon'

const YouTubeEmbedPlaceholder = ({ videoId, ...other }) => {
  const src = `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`
  const { ref, inView } = useInView({ threshold: 0 })
  const [displayVideo, setDisplayVideo] = useState(false)
  const [videoLink, setVideoLink] = useState(null)

  useEffect(() => {
    if (inView && window) {
      if (!navigator.connection || !navigator.connection?.effectiveType)
        setDisplayVideo(true)
      // Non supported browser
      else if (navigator.connection.effectiveType === '4g') {
        setDisplayVideo(true)
      } else {
        setVideoLink(YouTubeEmbedFallbackUrl(videoId))
      }
    }
  }, [inView, videoId])

  return (
    <div data-testid={`id-${videoId}`}>
      {displayVideo && <YouTubeEmbed videoId={videoId} {...other} />}
      <a
        href={videoLink}
        target="_blank"
        rel="noreferrer noopener"
        className={classNames(styles.Wrapper, displayVideo && styles.disabled)}
      >
        {videoLink && (
          <Icon
            width={75}
            height={53}
            className={styles.PlayIcon}
            a11ytext=""
            type="youtube-red"
          />
        )}
        <img
          ref={ref}
          src={src}
          loading="lazy"
          alt="Youtube Placeholder"
          className={classNames(
            styles.Placeholder,
            displayVideo && styles.hide
          )}
        />
      </a>
    </div>
  )
}

YouTubeEmbedPlaceholder.propTypes = {
  videoId: string.isRequired
}

export default YouTubeEmbedPlaceholder

// http://tm.localhost.clockhosting.com:8110/top10/top-10-technology-trends-for-2024
