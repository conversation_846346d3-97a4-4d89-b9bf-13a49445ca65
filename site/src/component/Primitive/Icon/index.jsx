import React from 'react'
import { number, oneOf, oneOfType, string } from 'prop-types'
import classNames from 'classnames'

import svgDimensionsFormatter from '@/lib/svg-dimensions-formatter'
import ratioScaler from '@/lib/ratio-scaler'

import styles from './Icon.module.scss'

const svgs = require.context('../../../asset/svg/icon/', false, /\.svg$/)
const bzkV1Svgs = require.context(
  '../../../asset/svg/icon/bzk_v1/',
  false,
  /\.svg$/
)

const getSvgKeys = (svgs) =>
  svgs.keys().map((key) => key.replace(`./`, '').replace(`.svg`, ''))

// Exported to allow iteration in storybook
export const vAligns = ['baseline', 'bottom', 'middle', 'top']
export const types = [...getSvgKeys(svgs), ...getSvgKeys(bzkV1Svgs)]

const Icon = ({
  a11ytext,
  className,
  type,
  height,
  width,
  vAlign,
  kit = 'default'
}) => {
  if (!type) {
    return <></>
  }

  let SvgType
  switch (kit) {
    case 'bzk':
      SvgType = bzkV1Svgs(`./${type}.svg`).default
      break
    default:
      SvgType = svgs(`./${type}.svg`).default
      break
  }

  const targetDimensions = { width, height }
  const nativeDimensions = svgDimensionsFormatter(SvgType)
  const ratioDimensions = ratioScaler(
    targetDimensions,
    nativeDimensions,
    'ceil'
  )

  return (
    <span
      className={classNames(styles.Icon, vAlign && styles[vAlign], className)}
      {...(a11ytext && {
        role: 'img',
        'aria-label': a11ytext
      })}
      {...(!a11ytext && {
        'aria-hidden': 'true'
      })}
      style={{
        width: `${ratioDimensions.width}px`,
        lineHeight: `${ratioDimensions.height}px`,
        height: `${ratioDimensions.height}px`
      }}
    >
      <SvgType />
    </span>
  )
}

Icon.propTypes = {
  a11ytext: string,
  className: string,
  type: oneOf(types).isRequired,
  height: oneOfType([number, string]),
  width: oneOfType([number, string]),
  vAlign: oneOf(vAligns),
  kit: oneOf(['default', 'bzk'])
}

export default Icon
