import React, { useContext } from 'react'
import { array } from 'prop-types'

import Container from '../Container'

import styles from './SignUp.module.scss'
import Type from '../Type'
import PerspectiveCarousel from '../PerspectiveCarousel'
import List from '../List'
import Icon from '../Icon'
import FieldTemplate from '../FieldTemplate'
import TextControl from '../TextControl'
import ButtonStandard from '../ButtonStandard'
import Hide from '../Hide'
import OutlineHeading from '../OutlineHeading'
import { ThemeContext } from '@/component/Context/ThemeContext'

import { SignUpContext } from '@/component/Context/SignUpContext'

const listItems = [
  'Weekly industry news and updates',
  'Stay informed with exclusive content'
]

const SignUp = ({ items }) => {
  const { theme, brandType } = useContext(ThemeContext)
  const {
    setEmailAddress,
    userIsSubscribed,
    handleModalVisibility
  } = useContext(SignUpContext)

  const onChange = (e) => {
    if (!e?.target) return
    const { value } = e.target
    setEmailAddress(value)
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    handleModalVisibility(true)
    document.body.classList.add('has-locked-scrolling')
  }

  return (
    <div className={styles.SignUp}>
      <Container gutter center size="large">
        <div className={styles.SignUpInner}>
          <div className={styles.Content}>
            <OutlineHeading
              heading={
                userIsSubscribed ? (
                  <>
                    Already <em>subscribed</em>
                  </>
                ) : (
                  <>
                    Get the latest <em>info</em>
                  </>
                )
              }
            />
            <div className={styles.InnerContent}>
              {!userIsSubscribed && (
                <form onSubmit={handleSubmit}>
                  <List className={styles.List} unstyled>
                    {listItems.map((item, i) => (
                      <li key={i}>
                        <Icon type="green-tick" a11ytext="" />
                        <Type
                          className={styles.ListItem}
                          as="span"
                          size={['body2', 'body2']}
                          weight="medium"
                        >
                          {item}
                        </Type>
                      </li>
                    ))}
                  </List>
                  <FieldTemplate
                    label="Email address"
                    controlName="emailAddress"
                    required
                  >
                    <TextControl
                      name="emailAddress"
                      onChange={onChange}
                      required
                    />
                  </FieldTemplate>
                  <ButtonStandard
                    className={styles.Submit}
                    size="bloated"
                    type="submit"
                    primary
                    block
                  >
                    Become part of Our Community
                  </ButtonStandard>
                </form>
              )}

              {userIsSubscribed && (
                <>
                  <Type className={styles.SubscribedMessage} size="body2">
                    You are already subscribed to our newsletter. Keep an eye on
                    your inbox for the latest news and analysis...
                  </Type>
                  <ButtonStandard
                    className={styles.SubscribedButton}
                    size="bloated"
                    primary
                    to="/magazines"
                  >
                    Read the latest issue
                  </ButtonStandard>
                </>
              )}
            </div>
          </div>
          <Hide below="desktop">
            <div className={styles.Carousel}>
              <PerspectiveCarousel items={items} />
            </div>
          </Hide>
        </div>
      </Container>
      {brandType === 'Lifestyle' && (
        <img
          src={`/image/texture/texture-${theme}.png`}
          alt=""
          width={808}
          height={800}
          className={styles.Texture}
          loading="lazy"
        />
      )}
    </div>
  )
}

SignUp.propTypes = {
  items: array
}

export default SignUp
