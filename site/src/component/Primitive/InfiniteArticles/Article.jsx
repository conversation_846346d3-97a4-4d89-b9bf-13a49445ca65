import React, { useEffect, useRef, useState } from 'react'
import Row from '@/component/Structure/Row'
import FixedColumnRowLayout from '@/component/Structure/Layout/component/FixedColumnRowLayout'
import StandardRowLayout from '@/component/Structure/Layout/component/StandardRowLayout'
import Stack from '@/component/Primitive/Stack'
import useReadingTime from '@/hook/useReadingTime'
import { object, func } from 'prop-types'

const Article = (props) => {
  const {
    instance,
    latestMagazineIssue,
    pageData,
    article,
    setArticleCTA,
    setMetaTitle
  } = props
  const section = article.section
  const [articleRef, stats] = useReadingTime()
  const [hasViewed, setHasViewed] = useState(false)
  const partialUrl = new URL(article.fullUrlPath, window.location.href)
  if (window.location.hash) partialUrl.hash = window.location.hash
  const queryString = window.location.search
  const searchParams = new URLSearchParams(queryString)
  searchParams.forEach((value, key) => {
    partialUrl.searchParams.set(key, value)
  })
  const url = partialUrl?.toString()
  const inViewRef = useRef(null)

  useEffect(() => {
    const current = inViewRef.current
    if (!current) {
      return
    }

    const updateDataLayer = (article) => {
      window.dataLayer = window.dataLayer || []
      let tagstringsArray = []
      if (article.tags) {
        tagstringsArray = article.tags.map((tagObj) => tagObj.tag)
      }
      window.dataLayer.article = {
        canonical: article.canonicalUrl,
        title: article.headline.replace(/"/g, '\\"'),
        author: article.author?.name || '',
        category: article.category,
        tags: tagstringsArray,
        published: article.displayDate,
        contentType: article.contentType,
        featured: article.featured
      }

      // eslint-disable-next-line
      console.log('updateDataLayer', window.dataLayer.article)
      // Push an 'article_refreshed' event to the DataLayer so GTM knows it has been updated
      window.dataLayer.push({
        event: 'article_refreshed'
      })
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          window.history.replaceState(
            { source: 'intersection-observer' },
            '',
            url
          )

          setArticleCTA({ ...article })
          setMetaTitle(article.headline)

          // Update data layer with current article
          updateDataLayer(article)

          if (!hasViewed) {
            if (typeof window?.cX?.sendPageViewEvent === 'function') {
              window.cX.sendPageViewEvent({ location: url })
              // eslint-disable-next-line
              console.log('Loading new article...')
            }

            if (typeof window.gtag === 'function') {
              window.gtag('event', 'page_view', {
                page_title: article.headline,
                page_location: url
              })
            }

            setHasViewed(true)
          }
        }
      },
      {
        root: null,
        rootMargin: '10%',
        threshold: 0.1
      }
    )

    observer.observe(current)

    return () => {
      observer.unobserve(current)
    }
    // eslint-disable-next-line
  }, [inViewRef, hasViewed])

  const articlePageData = { ...pageData, url }
  article.readingTime = stats && Math.round(stats.minutes)
  const layout =
    section &&
    section.layouts &&
    section.layouts.article &&
    section.layouts.article.layout

  if (layout.length) {
    return (
      <div ref={inViewRef}>
        <div ref={articleRef}>
          <Stack>
            {layout.map((row, rowIndex) => {
              const rowLayoutProps = {
                __url: url,
                article,
                section,
                instance,
                pageData: articlePageData,
                row,
                latestMagazineIssue,
                rowIndex
              }
              const rowHasAFixedColumn =
                row.attributes && row.attributes.includes('fixed-column')

              if (rowHasAFixedColumn) {
                return (
                  <Row
                    flex={rowHasAFixedColumn}
                    key={rowIndex}
                    row={row}
                    isArticle={!!article}
                  >
                    <FixedColumnRowLayout
                      isArticle={!!article}
                      {...rowLayoutProps}
                    />
                  </Row>
                )
              }

              return (
                <Row key={rowIndex} row={row} isArticle={!!article}>
                  <StandardRowLayout {...rowLayoutProps} />
                </Row>
              )
            })}
          </Stack>
        </div>
      </div>
    )
  }

  return <></>
}

Article.propTypes = {
  pageData: object,
  article: object,
  instance: object,
  latestMagazineIssue: object,
  originalData: object,
  setArticleCTA: func,
  setMetaTitle: func
}

export default Article
