import React, { useEffect } from 'react'
import { string } from 'prop-types'
import styles from './BrandContact.module.scss'
import Container from '@/component/Primitive/Container'
import Type from '@/component/Primitive/Type'
import OffsetAnchor from '@/component/Primitive/OffsetAnchor'

import { createInlineForm } from '@formcrafts/embed'

const BrandContact = ({ retUrl, brandName }) => {
  useEffect(() => {
    createInlineForm({
      form: 'e8131e61',
      target: document.getElementById('formcrafts'),
      seamless: true,
      redirectWithin: false,
      values: {
        field17: retUrl,
        field16: brandName
      }
    })
  })

  return (
    <>
      <OffsetAnchor identifier="media-kit-download" />
      <div className={styles.BrandContact}>
        <Container size="large" className={styles.Container} gutter center>
          <Type
            as="h2"
            size={['heading3', 'heading0']}
            weight="bold"
            className={styles.Title}
          >
            Get in <span>touch</span>
          </Type>
          <div id="formcrafts" className={styles.Formcrafts} />
        </Container>
      </div>
    </>
  )
}

BrandContact.propTypes = {
  retUrl: string,
  brandName: string
}

export default BrandContact
