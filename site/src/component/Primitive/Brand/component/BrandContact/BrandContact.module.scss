.BrandContact {
  color: var(--row-background-color, $color-black-primary);
  padding: spacing(4) 0 spacing(3);

  input,
  select {
    height: 56px;
  }

  @include mq($breakpoint-desktop) {
    padding: spacing(10) 0 spacing(10.5);
    text-align: center;
  }
}

.Title {
  margin-bottom: spacing(2);

  span {
    color: var(--color-theme--secondary);
  }

  @include mq($breakpoint-desktop) {
    justify-content: center;
    margin-bottom: spacing(4.5);
  }
}

.Description {
  margin: 0 auto spacing(4);
  max-width: 900px;

  @include mq($breakpoint-desktop) {
    margin: 0 auto spacing(5.5);
  }
}

.FieldWrapper {
  margin-bottom: spacing(3);
  text-align: left;
}

.FieldTitle {
  text-align: left;
}

.Captcha {
  display: flex;
  justify-content: center;
  margin: spacing(0.5) 0 spacing(3.5);
  overflow: hidden;
  padding: spacing(0.5) 0;
}

.FauxSelect {
  display: none;
}

.Select {
  border: 0 !important;
  border-radius: 0 !important;
  box-shadow: 0 0 0 1px inset $color-black-primary,
    0 0 0 1px $color-black-primary;
  height: 56px;

  > div {
    align-items: center;
    font-size: 16px;
    padding-left: 8px;
  }

  > div:nth-child(2) {
    svg {
      fill: $color-grey-secondary;
      transform: scale(1.3) translateY(2px);
      transform-origin: center center;

      [aria-expanded='true'] & {
        transform: scale(1.3) translateY(2px) translateX(3px);
      }
    }
  }

  &:hover,
  &[aria-expanded='true'] {
    box-shadow: 0 0 0 1px inset var(--color-theme--secondary),
      0 0 0 1px var(--color-theme--secondary) !important;
  }

  &[aria-expanded='true'] {
    > div:nth-child(2) {
      svg {
        transform: scale(1.3) translateY(2px) translateX(-4px);
      }
    }

    > div:last-child {
      align-items: flex-start;
      background: $color-black-primary;
      border: 0;
      border-radius: 0;
      box-shadow: 0 4px 28px rgb($color-black, 0.5);
      margin-top: -6px;
      padding-left: 0;

      span {
        border-bottom: 1px solid $color-misc-divider;
        color: $color-white-primary;
        font-size: 16px;
        padding: 13px 6px 9px 12px;
        width: 100%;

        &:hover,
        &[aria-selected='true'] {
          background-color: var(--color-theme--secondary);
        }

        @include mq($breakpoint-desktop) {
          font-size: 14px;
        }
      }

      @include mq($breakpoint-desktop) {
        width: 50%;
      }
    }
  }
}

.Submit {
  padding: spacing(2) spacing(3);
  width: 100%;

  @include mq($breakpoint-desktop) {
    max-width: 190px;
  }
}

/** New CSS **/

.Formcrafts {
  min-height: 950px; // 950px is the height of the mobile form as of Dec 2024

  @include mq($breakpoint-tablet) {
    min-height: 570px; // 570px is the height of the desktop form as of Dec 2024
  }

  > iframe {
    width: 100% !important;
  }
}
