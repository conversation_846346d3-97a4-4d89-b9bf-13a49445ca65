import React from 'react'
import { arrayOf, shape, string } from 'prop-types'

import styles from './AdvertiseFeatures.module.scss'
import Container from '@/component/Primitive/Container'
import Type from '@/component/Primitive/Type'
import Icon from '@/component/Primitive/Icon'

const AdvertiseFeatures = ({ features }) => {
  if (!features || features.length < 1) return null
  return (
    <div className={styles.AdvertiseFeatures}>
      <Container size="large" gutter center>
        <div className={styles.FeatureGrid}>
          {features.map(({ title, description }, i) => (
            <div key={`Feature-${i}`} className={styles.Feature}>
              <div className={styles.FeatureTitle}>
                <Icon
                  type="tick"
                  width={29}
                  height={26}
                  a11ytext="Feature"
                  className={styles.TitleIcon}
                />
                <Type
                  weight="bold"
                  themed
                  as="h3"
                  size={['heading6', 'heading4']}
                >
                  {title}
                </Type>
              </div>
              <Type size={['body3', 'body2']}>{description}</Type>
            </div>
          ))}
        </div>
      </Container>
    </div>
  )
}

AdvertiseFeatures.propTypes = {
  features: arrayOf(
    shape({
      title: string.isRequired,
      description: string.isRequired
    })
  )
}

export default AdvertiseFeatures
