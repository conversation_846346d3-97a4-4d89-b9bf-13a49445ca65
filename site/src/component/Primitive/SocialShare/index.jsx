import React from 'react'
import PropTypes from 'prop-types'

import Type from '@/component/Primitive/Type'
import IconButton from '@/component/Primitive/IconButton'

import styles from './SocialShare.module.scss'

const iconMap = {
  facebook: 'facebook',
  twitter: 'twitter'
}

const SocialShare = ({ links }) => {
  return (
    <div className={styles.SocialShare}>
      <Type className={styles.SocialShareHeading} as="h4" size="base">
        Share
      </Type>
      <ul className={styles.SocialShareList}>
        {links?.map((link, i) => {
          if (!iconMap[link.type]) return null
          return (
            <li
              className={styles.SocialShareItem}
              key={`SocialIcon-${link.type}-${i}`}
            >
              <IconButton
                icon={iconMap[link.type]}
                href={link.url}
                a11ytext={`Visit us on ${link.type}`}
                rounded
                target="_blank"
                rel="noopener noreferrer"
              />
            </li>
          )
        })}
      </ul>
    </div>
  )
}

SocialShare.propTypes = {
  links: PropTypes.arrayOf(
    PropTypes.shape({
      type: PropTypes.oneOf(Object.keys(iconMap)),
      url: PropTypes.string
    })
  )
}

export default SocialShare
