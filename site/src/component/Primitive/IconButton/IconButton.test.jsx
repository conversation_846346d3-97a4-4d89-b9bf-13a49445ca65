import React from 'react'
import validateRequiredProps from '@/lib/validate-required-props'
import { render } from '@testing-library/react'
import IconButton from './'

const requiredProps = () => ({
  a11ytext: '',
  icon: '_placeholder'
})

describe('Component: IconButton', function () {
  validateRequiredProps(IconButton, requiredProps())

  test('should output the expected markup with default props', function () {
    const { getByRole } = render(<IconButton {...requiredProps()} />)
    expect(getByRole('button')).toBeTruthy()
  })

  test('should output additional styles when `rounded` prop passed', function () {
    const { container } = render(<IconButton {...requiredProps()} rounded />)
    expect(container.firstChild).toHaveClass('rounded')
  })

  test('should output additional styles when `themed` prop passed', function () {
    const { container } = render(<IconButton {...requiredProps()} themed />)
    expect(container.firstChild).toHaveClass('themed')
  })

  test('should output additional styles when `increaseHitArea` prop passed', function () {
    const { container } = render(
      <IconButton {...requiredProps()} increaseHitArea />
    )
    expect(container.firstChild).toHaveClass('increaseHitArea')
  })

  test('should output additional content when `children` prop passed', function () {
    const { getByText } = render(
      <IconButton {...requiredProps()}>Example</IconButton>
    )
    expect(getByText('Example')).toBeTruthy()
  })
})
