import React from 'react'
import { bool, number, oneOf, string } from 'prop-types'
import classNames from 'classnames'

import styles from './IconButton.module.scss'

import Icon from '../Icon'
import SmartLink from '../SmartLink'

const IconButton = ({
  a11ytext,
  children,
  className,
  icon,
  iconHeight,
  iconWidth,
  increaseHitArea,
  rounded,
  shadow,
  size = 'medium',
  themed,
  ...other
}) => (
  <SmartLink
    className={classNames(
      styles.IconButton,
      increaseHitArea && styles.increaseHitArea,
      rounded && styles.rounded,
      themed && styles.themed,
      shadow && styles.shadow,
      size && styles[size],
      className
    )}
    {...other}
  >
    <div className={styles.IconButtonInner}>
      <Icon
        type={icon}
        height={iconHeight}
        width={iconWidth}
        a11ytext={a11ytext}
      />
      {children && <span className={styles.IconButtonText}>{children}</span>}
    </div>
  </SmartLink>
)

IconButton.propTypes = {
  a11ytext: string.isRequired,
  children: string,
  className: string,
  icon: string.isRequired,
  iconHeight: number,
  iconWidth: number,
  increaseHitArea: bool,
  rounded: bool,
  shadow: bool,
  size: oneOf(['tiny', 'small', 'medium', 'large']),
  themed: bool
}

export default IconButton
