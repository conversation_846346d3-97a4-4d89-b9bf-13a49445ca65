import React from 'react'
import { storiesOf } from '@storybook/react'

import RootColorVariables from '@/component/Structure/RootColorVariables'

import IconButton from '.'

const stories = storiesOf('Core/IconButton', module)

stories.add(
  'Info',
  () => (
    <IconButton type="button" icon="_placeholder" a11ytext="Placeholder icon" />
  ),
  {
    info: {
      inline: true,
      text: `
        A simple button component used to present clickable Icons
      `
    }
  }
)

stories.add('Default state', () => (
  <>
    <RootColorVariables color="#19b2e2" />
    <IconButton type="button" icon="_placeholder" a11ytext="Placeholder icon" />
  </>
))

stories.add('Solid colour', () => (
  <>
    <RootColorVariables color="#19b2e2" />
    <IconButton
      type="button"
      icon="_placeholder"
      a11ytext="Placeholder icon"
      theme
    />
  </>
))

stories.add('Rounded', () => (
  <>
    <RootColorVariables color="#19b2e2" />
    <IconButton
      type="button"
      icon="_placeholder"
      a11ytext="Placeholder icon"
      theme
      rounded
    />
  </>
))

stories.add('Custom icon size', () => (
  <>
    <RootColorVariables color="#19b2e2" />
    <IconButton
      type="button"
      icon="_placeholder"
      a11ytext="Placeholder icon"
      iconWidth={16}
      theme
    />
  </>
))

stories.add('Rounded, Solid, Shadow, Custom Size Icon', () => (
  <>
    <RootColorVariables color="#19b2e2" />
    <IconButton
      type="button"
      icon="arrow-left"
      a11ytext="Placeholder icon"
      iconHeight={12}
      theme
      rounded
      shadow
    />
  </>
))

stories.add('With increased hit-area', () => (
  <>
    <RootColorVariables color="#19b2e2" />
    <IconButton
      type="button"
      icon="_placeholder"
      a11ytext="Placeholder icon"
      increaseHitArea
      small
    />
  </>
))

stories.add('With additional content', () => (
  <>
    <RootColorVariables color="#19b2e2" />
    <IconButton type="button" icon="_placeholder" a11ytext="" small transparent>
      Example content
    </IconButton>
  </>
))
