import React, { useContext } from 'react'
import { array, number, bool } from 'prop-types'
import { format } from 'date-fns'

import { ThemeContext } from '@/component/Context/ThemeContext'

import brandToColor from '@/lib/brand-to-color'
import Grid from '@/component/Primitive/Grid'
import Card from '@/component/Primitive/Card'
import Surround from '@/component/Primitive/Surround'
import Tag from '@/component/Primitive/Tag'
import CardButton from '@/component/Primitive/Card/component/CardButton'

const imageConfig = {
  Interview: {
    ratio: 455 / 322,
    crop: 'thumbnail_portrait_286'
  },
  Whitepaper: {
    ratio: 455 / 322,
    crop: 'thumbnail_portrait_286'
  },
  'Company Report': {
    ratio: 455 / 322,
    crop: 'thumbnail_portrait_286'
  },
  Video: {
    ratio: 375 / 668,
    crop: 'thumbnail_widescreen_553'
  }
}

const showSubtitle = (item) =>
  ['Event', 'Interview', 'Company Report'].includes(item.contentType)

const getSubtitle = (article) => {
  return {
    Event: article.startDate
      ? `${format(new Date(article.startDate), 'EE dd MMM, yyyy')} • ${format(
          new Date(article.startDate),
          'HH:mm'
        )} • ${article.address}`
      : null,
    Interview: article.subAttribution
  }
}

const getCardIcon = (type) => {
  const iconMap = {
    Video: 'play',
    Podcast: 'listen'
  }

  return iconMap[type]
}

const determineCardBorder = (index, numResults, numPerRow) => {
  if (numResults > Math.ceil(index / numPerRow) * numPerRow) return 'default'
  if (index < numResults) return 'mobile'
}

const TypeSpecificCardGrid = ({ results, numPerRow, hideCta }) => {
  const { theme } = useContext(ThemeContext)
  const themecolor = brandToColor(theme)

  const getButton = (article) => {
    if (!article.startDate || article.contentType !== 'Event')
      return <CardButton>Read more</CardButton>

    const startDate = new Date(article.startDate)
    if (startDate < new Date()) {
      if (article.onDemandLink)
        return <CardButton theme="secondary">Watch on Demand</CardButton>
      return <CardButton theme="tertiary">Find out more</CardButton>
    }
    return <CardButton theme="primary">Register now</CardButton>
  }

  return (
    <Grid grow flex gutter="default" fullHeight>
      {results &&
        results.length > 0 &&
        results.map((item, i) => {
          const imageRatio = imageConfig[item.contentType]?.ratio || 126 / 167
          const imageSrc =
            item.images[
              imageConfig[item.contentType]?.crop || 'thumbnail_landscape_322'
            ][0]?.url

          return (
            <Grid.Item
              trailingGap="default"
              width={{ d: 1 / numPerRow }}
              key={i}
            >
              <Surround
                bordered={determineCardBorder(i + 1, results.length, numPerRow)}
                padded={determineCardBorder(i + 1, results.length, numPerRow)}
              >
                <Card
                  typeConfig={{
                    title: { size: 'heading6', themed: true },
                    subtitle: {
                      size: ['body2', 'body3'],
                      color: themecolor,
                      themed: true
                    }
                  }}
                  to={item.eventBaseSlug ? '/eventArticle' : '/article'}
                  imageRatio={imageRatio}
                  imageIcon={getCardIcon(item.contentType) || null}
                  src={imageSrc}
                  title={item.headline}
                  shouldShowSubtitle={showSubtitle(item)}
                  subtitle={
                    showSubtitle(item)
                      ? getSubtitle(item)[item.contentType]
                      : null
                  }
                  description={
                    item.contentType !== 'Video' &&
                    item.contentType !== 'Interview'
                      ? item.sell
                      : null
                  }
                  hideDescriptionOnMobile={
                    !['Whitepaper', 'Company Report'].includes(item.contentType)
                  }
                  link={item.fullUrlPath}
                  headerComponents={() => {
                    if (!item.featured) return null
                    return (
                      <Tag themedFont background="black">
                        Featured
                      </Tag>
                    )
                  }}
                  footerComponents={!hideCta && (() => getButton(item))}
                  {...{
                    ...item,
                    link:
                      item.onDemandLink ||
                      item.eventRegistrationLink ||
                      item.fullUrlPath,
                    isExternalLink:
                      item.onDemandLink || item.eventRegistrationLink
                  }}
                />
              </Surround>
            </Grid.Item>
          )
        })}
    </Grid>
  )
}

TypeSpecificCardGrid.propTypes = {
  results: array,
  numPerRow: number,
  hideCta: bool
}

export default TypeSpecificCardGrid
