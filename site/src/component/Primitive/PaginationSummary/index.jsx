import React from 'react'
import classNames from 'classnames'
import { func, number, object } from 'prop-types'

import IconButton from '@/component/Primitive/IconButton'
import Hide from '@/component/Primitive/Hide'

import styles from './PaginationSummary.module.scss'

const PaginationSummary = ({
  onNext,
  onPrevious,
  currentSlide,
  totalSlides,
  options = {}
}) => {
  const previousDisabled = currentSlide <= 1
  const nextDisabled =
    currentSlide >= totalSlides - (options.nextDisabledOffsetFromEnd ?? 2)

  return (
    <div className={styles.PaginationSummary}>
      <Hide below="desktop">
        <IconButton
          className={classNames(styles.PaginationSummaryButton, styles.prev)}
          {...(previousDisabled && { disabled: true })}
          {...(!previousDisabled && { onClick: onPrevious })}
          a11ytext={
            previousDisabled ? 'No previous slides' : 'See previous slide'
          }
          iconWidth={32}
          icon="navigate-previous"
          rounded
          themed
        />
        <IconButton
          className={classNames(styles.PaginationSummaryButton, styles.next)}
          {...(nextDisabled && { disabled: true })}
          {...(!nextDisabled && { onClick: onNext })}
          a11ytext={previousDisabled ? 'No further slides' : 'See next slide'}
          iconWidth={32}
          icon="navigate-next"
          rounded
          themed
        />
      </Hide>
      <Hide at="desktop">
        <div className={styles.PaginationSummaryProgress}>
          <div
            style={{
              width: `${100 / totalSlides}%`,
              left: `${100 * ((currentSlide - 1) / totalSlides)}%`
            }}
            className={styles.PaginationSummaryProgressSegment}
          />
        </div>
      </Hide>
    </div>
  )
}

PaginationSummary.defaultProps = {
  onNext: () => {},
  onPrevious: () => {},
  currentSlide: 1,
  totalSlides: 5
}

PaginationSummary.propTypes = {
  onNext: func.isRequired,
  onPrevious: func.isRequired,
  currentSlide: number,
  totalSlides: number.isRequired,
  options: object
}

export default PaginationSummary
