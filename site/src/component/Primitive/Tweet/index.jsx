import React from 'react'
import { node } from 'prop-types'

import styles from './Tweet.module.scss'
import SmartLink from '../SmartLink'
import Icon from '../Icon'

const Tweet = ({ tweetId, tweetText }) => (
  <div className={styles.Tweet}>
    <div
      dangerouslySetInnerHTML={{ __html: tweetText }}
      className={styles.TweetContent}
    />
    <div className={styles.Retweet}>
      <SmartLink
        href={`https://twitter.com/intent/retweet?tweet_id=${tweetId}`}
        target="_blank"
      >
        Retweet{' '}
        <Icon
          className={styles.IconArrow}
          type="arrow-right"
          a11ytext="Retweet"
        />
      </SmartLink>
      <Icon
        className={styles.IconTwitter}
        width={20}
        height={20}
        type="twitter"
        a11ytext="Twitter"
      />
    </div>
  </div>
)

Tweet.propTypes = {
  tweetId: node.isRequired,
  tweetText: node.isRequired
}

export default Tweet
