import React, { useState, useEffect } from 'react'
import { array, string } from 'prop-types'
import styles from './MagazineLatestCTA.module.scss'
import ButtonStandard from '@/component/Primitive/ButtonStandard'
import cx from 'classnames'

const MagazineLatestCTA = ({ title, to, magazineIssues }) => {
  const magazines = []
  const positionClasses = [
    styles.MagazineLatestCTAImageCentre,
    styles.MagazineLatestCTAImageRight,
    styles.MagazineLatestCTAImageLeft
  ]
  const [positionOffset, setPositionOffset] = useState(0)

  for (const magazineIssue of magazineIssues) {
    const image = magazineIssue?.images?.cover_321x446_321?.[0]?.url
    magazines.push({
      image
    })
  }

  useEffect(() => {
    const rotationInterval = setInterval(() => {
      setPositionOffset((prevOffset) => (prevOffset + 1) % 3)
    }, 4000)

    // Clean up interval on component unmount
    return () => clearInterval(rotationInterval)
  }, [])

  const getRotatingClass = (index) => {
    const rotatedPosition = (index + positionOffset) % 3
    return positionClasses[rotatedPosition]
  }

  return (
    <div className={styles.MagazineLatestCTA}>
      <div>
        <div className={styles.MagazineLatestCTAImageHolder}>
          {magazines.map((magazineIssue, index) => (
            <div
              key={index}
              className={cx(
                styles.MagazineLatestCTAImage,
                getRotatingClass(index)
              )}
            >
              <img
                className={styles.MagazineImageTop}
                src={magazineIssue.image}
                alt=""
              />

              <div className={styles.MagazineImageBottom}>
                <img src={magazineIssue.image} alt="" />
              </div>
            </div>
          ))}
        </div>

        <ButtonStandard
          to={to}
          size="small"
          className={styles.MagazineLatestCTALink}
          icon="arrow-right"
        >
          {title || 'Latest magazines'}
        </ButtonStandard>
      </div>
    </div>
  )
}

MagazineLatestCTA.propTypes = {
  title: string.isRequired,
  magazineIssues: array.isRequired,
  to: string.isRequired
}

export default MagazineLatestCTA
