.MagazineLatestCTA {
  container-name: magazinelatestctacontainer;
  container-type: inline-size;
  justify-content: center;
  overflow: hidden;
  position: relative;
  text-align: center;

  &::after {
    aspect-ratio: 322/90;
    background-color: #202121;
    clip-path: polygon(3% 0, 97% 0, 100% 100%, 0 100%);
    content: '';
    display: block;
    inset: auto 0 19px;
    position: absolute;
    width: 100%;
  }
}

@container magazinelatestctacontainer (width > 768px) {
  .MagazineLatestCTAInner {
    align-items: center;
    display: grid;
    grid-template-columns: 296px 1fr;
    padding: 20px;

    &::after {
      display: none;
    }
  }
}

.MagazineLatestCTAImageHolder {
  aspect-ratio: 296/233;
  margin-inline: 20px;
  perspective: 114px;
  position: relative;
  z-index: 2;
}

.MagazineLatestCTAImage {
  height: auto;
  position: absolute;
  top: 50%;
  transition: 250ms ease-in-out;
  translate: 0 -50%;
  width: 40%;
}

.MagazineLatestCTALink {
  background-color: var(
    --color-theme-button-background,
    --color-theme--secondary
  );
  color: var(--color-theme-button-foreground, --color-theme--primary);
  margin-inline: auto;
  margin-top: 16px;
  z-index: 2;

  &::before {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &::after {
    content: '';
    inset: 0;
    position: absolute;
    z-index: 10;
  }
}

.MagazineLatestCTAImageLeft {
  left: 0;
  transform: translateX(0) translateZ(0);
  z-index: 0;
}

.MagazineLatestCTAImageCentre {
  left: 50%;
  transform: translateX(-50%);
  width: calc(40% + 28px) !important;
  z-index: 10;
}

.MagazineLatestCTAImageRight {
  left: 100%;
  transform: translateX(-100%) translateZ(0);
  z-index: 0;
}

.MagazineImageBottom {
  inset: calc(100% - 1px) 0 auto;
  pointer-events: none;
  position: absolute;
  transform: rotate(180deg) scaleX(-1);
  z-index: -1;

  img {
    filter: blur(1px) opacity(0.66);
    mask-image: linear-gradient(to top, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0) 25%);
  }
}
