import React from 'react'
import { func, bool, node } from 'prop-types'
import classNames from 'classnames'

import Hide from '../Hide'
import IconButton from '../IconButton'

import styles from './MagazineIssueModal.module.scss'

const MagazineIssueModal = ({ compact, onClose, children }) => {
  return (
    <div
      className={classNames(
        styles.MagazineIssueModal,
        compact && styles.compact
      )}
    >
      <div className={styles.Inner}>
        <Hide below="desktop">
          <IconButton
            icon="close"
            rounded
            className={styles.Button}
            onClick={onClose}
            a11ytext="Close"
          />
        </Hide>

        {children}
      </div>
    </div>
  )
}

MagazineIssueModal.propTypes = {
  compact: bool,
  onClose: func.isRequired,
  children: node
}

export default MagazineIssueModal
