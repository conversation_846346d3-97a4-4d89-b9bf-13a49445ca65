import SubscribeMessage from './SubscribeMessage'
import React, { useContext, useEffect, useRef } from 'react'
import { func, string, bool, oneOfType, shape, any } from 'prop-types'
import styles from './SignUpModal.module.scss'
import ReCAPTCHA from 'react-google-recaptcha'

import FieldTemplate from '../../FieldTemplate'
import TextControl from '../../TextControl'
import Grid from '../../Grid'
import ButtonStandard from '../../ButtonStandard'
import TextAlign from '../../TextAlign'
import Stack from '../../Stack'
import Type from '../../Type'
import Hide from '../../Hide'
import IconButton from '../../IconButton'
import OutlineHeading from '../../OutlineHeading'
import classNames from 'classnames'

import { SignUpContext } from '@/component/Context/SignUpContext'

const SignUpModal = ({
  onSubmit,
  userIsSubscribed,
  subHeading,
  onClose,
  onChange,
  recaptchaRef,
  onReCAPTCHAChange,
  error
}) => {
  const { emailAddress, existingSubscriber } = useContext(SignUpContext)
  const emailField = useRef(null)

  useEffect(() => {
    emailField.current.value = emailAddress
  }, [emailAddress, existingSubscriber])

  return (
    <div
      className={classNames(
        styles.SignUpModal,
        userIsSubscribed && styles.subscribed
      )}
    >
      <div className={styles.Inner}>
        <Hide below="desktop">
          <IconButton
            icon="close"
            rounded
            className={styles.Button}
            onClick={onClose}
            a11ytext="Close"
          />
        </Hide>
        <div className={styles.Heading}>
          <OutlineHeading
            subHeading={subHeading}
            heading={
              existingSubscriber ? (
                <>
                  Already <em>subscribed</em>
                </>
              ) : (
                <>
                  Sign up to our <em>newsletter</em>
                </>
              )
            }
          />
        </div>
        {!userIsSubscribed ? (
          <form className={styles.Content} onSubmit={onSubmit}>
            {error && (
              <Stack gap="small">
                <Type size="body2" color="red">
                  {error}
                </Type>
              </Stack>
            )}
            <Type themed weight={['medium', 'bold']}>
              Personal Info
            </Type>
            <div className={styles.FieldWrapper}>
              <Grid gutter="large">
                <Grid.Item trailingGap="default" width={{ d: 1 / 2 }}>
                  <FieldTemplate
                    label="First name"
                    required
                    controlName="firstName"
                  >
                    <TextControl
                      name="firstName"
                      onChange={onChange}
                      required
                    />
                  </FieldTemplate>
                </Grid.Item>
                <Grid.Item trailingGap="default" width={{ d: 1 / 2 }}>
                  <FieldTemplate
                    label="Last name"
                    required
                    controlName="lastName"
                  >
                    <TextControl name="lastName" onChange={onChange} required />
                  </FieldTemplate>
                </Grid.Item>
                <Grid.Item width={{ d: 1 / 2 }}>
                  <FieldTemplate
                    label="Email Address"
                    required
                    controlName="emailAddress"
                  >
                    <TextControl
                      name="emailAddress"
                      onChange={onChange}
                      required
                      ref={emailField}
                    />
                  </FieldTemplate>
                </Grid.Item>
              </Grid>
            </div>
            <Type themed weight={['medium', 'bold']}>
              Company Info
            </Type>
            <div className={styles.FieldWrapper}>
              <Grid gutter="large">
                <Grid.Item trailingGap="mobile" width={{ d: 1 / 2 }}>
                  <FieldTemplate label="Company name" controlName="companyName">
                    <TextControl
                      name="companyName"
                      onChange={onChange}
                      required
                    />
                  </FieldTemplate>
                </Grid.Item>
                <Grid.Item width={{ d: 1 / 2 }}>
                  <FieldTemplate
                    label="Job Title"
                    required
                    controlName="jobTitle"
                  >
                    <TextControl name="jobTitle" onChange={onChange} required />
                  </FieldTemplate>
                </Grid.Item>
              </Grid>
            </div>
            <div className={styles.CaptchaWrapper}>
              <ReCAPTCHA
                ref={recaptchaRef}
                sitekey="6Ldot24dAAAAAOtGyb7-e6uenTEM91w4Mxw4WNnf"
                onChange={onReCAPTCHAChange}
              />
            </div>
            <TextAlign center className={styles.CTAWrapper}>
              <Stack>
                <ButtonStandard
                  type="submit"
                  size="bloated"
                  primary
                  className={styles.Submit}
                >
                  Subscribe Now
                </ButtonStandard>
                <Hide at="desktop">
                  <ButtonStandard
                    outline
                    inverse
                    size="bloated"
                    onClick={onClose}
                    className={styles.Submit}
                  >
                    Cancel
                  </ButtonStandard>
                </Hide>
              </Stack>
            </TextAlign>
          </form>
        ) : (
          <SubscribeMessage
            existingSubscriber={existingSubscriber}
            onClose={onClose}
          />
        )}
      </div>
    </div>
  )
}

SignUpModal.propTypes = {
  onSubmit: func.isRequired,
  onClose: func.isRequired,
  subHeading: string.isRequired,
  userIsSubscribed: bool.isRequired,
  onChange: func.isRequired,
  error: string,
  onReCAPTCHAChange: func,
  recaptchaRef: oneOfType([
    func,
    shape({
      current: any
    })
  ])
}

export default SignUpModal
