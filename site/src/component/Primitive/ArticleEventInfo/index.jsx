import React from 'react'
import { node, string, object } from 'prop-types'
import { format, parseISO } from 'date-fns'
import { getTimezoneAbbreviation } from '@/lib/timezoneAbbreviationMiddleware'

import Icon from '../Icon'
import SmartLink from '../SmartLink'
import Type from '../Type'

import styles from './ArticleEventInfo.module.scss'

const InfoBlock = ({
  children,
  heading,
  ctaText,
  ctaUrl,
  icon,
  hooks = {}
}) => {
  return (
    <div className={styles.InfoBlock}>
      <Type
        size={['heading8', 'heading6']}
        weight="bold"
        className={styles.InfoBlockHeading}
      >
        {heading}
      </Type>
      {children}
      {ctaUrl && !hooks?.doNotRenderSmartLinkIf?.() && (
        <SmartLink target="_blank" className={styles.CTA} href={ctaUrl}>
          <Type size="heading8" weight="medium" as="span">
            {ctaText}
          </Type>

          <Icon className={styles.Icon} type={icon} />
        </SmartLink>
      )}
    </div>
  )
}

InfoBlock.propTypes = {
  children: node,
  heading: string,
  ctaText: string,
  ctaUrl: string,
  icon: string,
  hooks: object
}

const ArticleEventInfo = ({
  _id,
  address,
  startDate,
  endDate,

  localeSafeStartTime,
  localeSafeEndTime,
  timezone
}) => {
  const formattedAddress = address.split(', ')
  const calendarCtaUrl = `/calendar-link?id=${encodeURIComponent(_id)}`

  const timezoneAbbreviation = getTimezoneAbbreviation(endDate, timezone)

  // Use timezone-aware times if available, otherwise fall back to original format
  const displayStartTime =
    localeSafeStartTime && localeSafeStartTime !== 'unset'
      ? localeSafeStartTime
      : format(parseISO(startDate), 'HH:mm')
  const displayEndTime =
    localeSafeEndTime && localeSafeEndTime !== 'unset'
      ? localeSafeEndTime
      : format(parseISO(endDate), 'HH:mm')
  const displayTimezone =
    timezoneAbbreviation && timezoneAbbreviation !== 'unset'
      ? ` (${timezoneAbbreviation})`
      : ''

  return (
    <div className={styles.ArticleEventInfo}>
      <div className={styles.Header}>
        <Type size="body2" uppercase weight="bold">
          <span className={styles.muted}>event</span>
          <span>info</span>
        </Type>
      </div>
      {timezone && (
        <InfoBlock
          icon="calendar"
          heading="Date & Time"
          ctaText="Add to calendar"
          ctaUrl={calendarCtaUrl}
        >
          <Type size={['body2', 'heading8']} weight="medium" as="p">
            {format(parseISO(startDate), 'E d MMM')} -{' '}
            {format(parseISO(endDate), 'E d MMM, yyyy')}
          </Type>
          <Type size={['body2', 'heading8']} weight="medium" as="p">
            {displayStartTime} - {displayEndTime}
            {displayTimezone}
          </Type>
        </InfoBlock>
      )}
      {formattedAddress && (
        <InfoBlock
          icon="map-pin"
          heading="Location"
          ctaText="View on map"
          ctaUrl={`https://www.google.com/maps?q=${address}`}
          hooks={{ doNotRenderSmartLinkIf: () => address === 'Online' }}
        >
          <Type size={['body2', 'heading8']} weight="medium" as="p">
            {formattedAddress.map((item, i) => (
              <span key={`EventInfoBlock--FormattedAddress--${i}`}>
                {item}
                {i !== formattedAddress.length - 1 && <br />}
              </span>
            ))}
          </Type>
        </InfoBlock>
      )}
    </div>
  )
}

ArticleEventInfo.propTypes = {
  address: string,
  startDate: string,
  endDate: string,
  _id: string,
  localeSafeStartTime: string,
  localeSafeEndTime: string,
  timezone: string
}

export default ArticleEventInfo
