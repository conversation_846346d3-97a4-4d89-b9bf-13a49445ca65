import { arrayOf, bool, number, shape, string } from 'prop-types'
import React from 'react'
import { Splide, SplideSlide, SplideTrack } from '@splidejs/react-splide'
import styles from './FullWidthMagPromoCarousel.module.scss'
import ResponsiveImage from '@/component/Primitive/ResponsiveImage'
import ButtonStandard from '@/component/Primitive/ButtonStandard'
import cx from 'classnames'

const imageDefinition = {
  src: string,
  caption: string,
  link: string,
  alt: string,
  ratio: number,
  height: number,
  width: number
}

const slidesDefinitionItem = shape({
  title: string,
  content: string,
  linkText: string,
  destination: string,
  opensInNewTab: bool,
  hasBorder: bool,
  primaryColor: string,
  contrastColor: string,
  images: shape({
    logo_640: arrayOf(shape(imageDefinition)),
    main_640: arrayOf(shape(imageDefinition)),
    main_768: arrayOf(shape(imageDefinition)),
    background_640: arrayOf(shape(imageDefinition)),
    background_768: arrayOf(shape(imageDefinition)),
    background_992: arrayOf(shape(imageDefinition)),
    background_1200: arrayOf(shape(imageDefinition)),
    background_1600: arrayOf(shape(imageDefinition)),
    background_2000: arrayOf(shape(imageDefinition))
  })
})

export const slidesDefinition = {
  slides: arrayOf(slidesDefinitionItem)
}

/**
 * A functional React component that renders a responsive image using
 * a set of sorted image sources provided as a prop. It uses
 * the first image in the sorted images array for displaying the primary image
 * details such as `src`, `alt`, `width`, and `height`, while the entire array
 * is used for the `srcSet` attribute to support responsive behavior.
 *
 * @param {Object} props - The component's props.
 * @param {Array} props.sortedImages - An array of image objects sorted in a desired order,
 * each containing `src`, `alt`, `width`, and `height` properties. The first image
 * in the array must be the primary image.
 * @returns {JSX.Element} A responsive image component if `sortedImages` is provided,
 * or an empty fragment if the `sortedImages` array is undefined or null.
 */
const ResponsiveImageSet = ({ sortedImages }) => {
  if (!sortedImages) {
    return <></>
  }

  return (
    <ResponsiveImage
      alt={sortedImages[0]?.alt}
      src={sortedImages[0]?.src}
      srcSet={sortedImages}
      width={sortedImages[0]?.width}
      height={sortedImages[0]?.height}
    />
  )
}

ResponsiveImageSet.propTypes = {
  sortedImages: arrayOf(shape(imageDefinition))
}

/**
 * Slide is a functional React component used to render a content slide with various customizable properties.
 *
 * @param {Object} props - The input properties for the Slide component.
 * @param {Object} props.slide - The data object containing all the details and configuration for the slide.
 * @param {string} props.slide.content - The text content to display within the slide.
 * @param {string} props.slide.contrastColor - The CSS color value for the slide's contrast color, used for text or visual elements.
 * @param {string} props.slide.destination - The URL destination of the button or anchor link in the slide.
 * @param {boolean} props.slide.hasBorder - Determines whether the slide should have a border rendered around it.
 * @param {Object} props.slide.images - An object containing categorized image arrays, organized by image type (e.g., background, logo, main).
 * @param {string} props.slide.linkText - The text displayed on the link button in the slide. Defaults to "Read Now".
 * @param {boolean} props.slide.opensInNewTab - Specifies whether the link should open in a new browser tab.
 *        This corresponds to the `target` attribute of the anchor: `_blank` for true, `_self` for false.
 * @param {string} props.slide.primaryColor - The primary color value for the slide’s styling, applied via CSS variables.
 * @param {string} props.slide.title - The title of the slide, which may include HTML content for rich text rendering.
 *
 * @returns {React.Element} A JSX element that renders a slide containing images, text, button, and background content.
 */
const Slide = ({
  slide: {
    content,
    contrastColor,
    destination,
    hasBorder,
    images,
    linkText,
    opensInNewTab,
    primaryColor,
    title
  }
}) => {
  const sortAndGroupImages = (imageName) => {
    const groupedImages = []

    Object.keys(images).map((key) => {
      if (key.includes(imageName)) {
        return groupedImages.push(...images[key])
      }
    })

    if (groupedImages.length) {
      return groupedImages.sort((a, b) => b?.width - a?.width)
    }

    return []
  }

  const backgroundImages = sortAndGroupImages('background')
  const logoImages = sortAndGroupImages('logo')
  const mainImages = sortAndGroupImages('main')

  return (
    <div
      className={cx(styles.Slide, hasBorder && styles.Border)}
      style={
        primaryColor && {
          '--slide-primary-color': primaryColor,
          '--slide-contrast-color': contrastColor
        }
      }
    >
      <div className={styles.Background}>
        <ResponsiveImageSet sortedImages={backgroundImages} />
      </div>

      <div className={styles.Content}>
        <div className={styles.ContentInner}>
          <div className={styles.ContentLeft}>
            <div className={styles.Logo}>
              <ResponsiveImageSet sortedImages={logoImages} />
            </div>

            {title && <h2 dangerouslySetInnerHTML={{ __html: title }} />}

            {content && <p>{content}</p>}

            {destination && (
              <div>
                <ButtonStandard
                  className={primaryColor && styles.BackgroundOverride}
                  href={destination}
                  target={opensInNewTab ? '_blank' : '_self'}
                  size="small"
                  icon="arrow-right"
                >
                  {linkText || 'Read Now'}
                </ButtonStandard>
              </div>
            )}
          </div>

          <div className={styles.ContentRight}>
            <ResponsiveImageSet sortedImages={mainImages} />
          </div>
        </div>
      </div>
    </div>
  )
}

Slide.propTypes = {
  slide: slidesDefinitionItem
}

/**
 * FullWidthMagPromoCarousel is a functional component that renders a full-width
 * promotional carousel using the provided slides. It is designed to display
 * content like promotional items, images, or other dynamic content in a carousel format.
 *
 * @function FullWidthMagPromoCarousel
 * @param {Object} props - The props object for the component.
 * @param {Array} props.slides - An array of slides, where each slide represents
 * content to be displayed in the carousel.
 * @returns {ReactElement} A React component that renders the promotional carousel.
 */
const FullWidthMagPromoCarousel = ({ slides }) => {
  if (!Array.isArray(slides)) {
    return null
  }

  if (!slides.length) {
    return null
  }

  /**
   * @type {import('@splidejs/splide').Options}
   */
  const OPTIONS = {
    pagination: true,
    arrows: false,
    autoplay: true,
    interval: 8000,
    perPage: 1,
    type: 'loop',
    pauseOnHover: false,
    pauseOnFocus: false,
    autoHeight: true
  }

  return (
    <div className={styles.Holder}>
      {slides?.length > 1 ? (
        <Splide options={OPTIONS} hasTrack={false}>
          <div className={cx(styles.Pagination, 'splide__pagination')} />
          <SplideTrack>
            {slides.map((slide, index) => (
              <SplideSlide key={index}>
                <Slide slide={slide} />
              </SplideSlide>
            ))}
          </SplideTrack>
        </Splide>
      ) : (
        <Slide slide={slides[0]} />
      )}
    </div>
  )
}

FullWidthMagPromoCarousel.propTypes = {
  slides: arrayOf(slidesDefinitionItem)
}

export default FullWidthMagPromoCarousel
