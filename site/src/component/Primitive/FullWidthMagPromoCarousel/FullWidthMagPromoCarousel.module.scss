.Slide {
  display: grid;
  grid-template: 1fr / 1fr;
  color: $color-white-primary;
  height: 100%;

  & > * {
    grid-area: 1 / 1 / 2 / 2;
  }
}

.Border {
  border: 4px solid var(--slide-primary-color, currentColor);
}

@media (min-width: 640px) {
  .Border {
    border-width: 8px;
  }
}

.Background {
  background-color: #333;
  position: relative;

  img {
    position: absolute;
    inset: 0;
    object-fit: cover;
    width: 100%;
    height: 100%;
  }
}

.Content {
  position: relative;
  padding-inline: 20px;
  height: 100%;
  display: grid;
  place-items: center;
}

.ContentInner {
  max-width: 1100px;
  display: grid;
  gap: 24px;
  place-items: center;
  margin-inline: auto;
}

@media (min-width: 640px) {
  .ContentInner {
    grid-template-columns: 320px 1fr;
  }
}

.ContentLeft {
  display: grid;
  gap: 20px;
  line-height: 1.2;
  padding-block: 24px;

  h2 {
    font-weight: 800;
    font-size: 28px;

    span {
      color: var(--slide-primary-color, currentColor);
    }
  }

  p {
    font-size: 16px;
    font-weight: 700;
  }
}

@media (min-width: 640px) {
  .ContentLeft {
    gap: 32px;
    padding-block: 48px;

    h2 {
      font-size: 40px;
    }
  }
}

.Logo {
  max-width: 221px;
}

.BackgroundOverride {
  background-color: var(--slide-primary-color);
  color: var(--slide-contrast-color);
}

.Pagination {
  display: flex;
  gap: 12px;
  inset: 20px 20px auto auto;
  position: absolute;
  z-index: 10;

  button {
    background-color: #d9d9d9;
    border-radius: 50%;
    display: block;
    height: 10px;
    opacity: 0.5;
    transition: 250ms ease-out;
    width: 10px;

    &:is([aria-selected='true']),
    &:hover,
    &:focus,
    &:active {
      background-color: var(--color-theme--secondary);
      opacity: 1;
    }
  }
}

@media (min-width: 768px) {
  .Pagination {
    inset: 40px 28px auto auto;
  }
}
