import React from 'react'
import { object } from 'prop-types'
import Primitive from '@/component/Primitive/CompanyPortal/ExecutiveMeta'
import withCompanyPortalFeature from '@/component/Widgets/CompanyPortal/withCompanyPortalFeature'

const ExecutiveMeta = ({ pageData }) => {
  const { executive } = pageData
  if (!executive) {
    return null
  }

  return <Primitive executive={executive} />
}

ExecutiveMeta.propTypes = {
  pageData: object
}

export default withCompanyPortalFeature(ExecutiveMeta)
