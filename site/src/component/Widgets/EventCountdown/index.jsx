import React from 'react'
import { string, shape } from 'prop-types'
import { EventCountdown } from '@/component/Primitive/Events'

const EventCountdownWidget = ({ eventUmbrella }) => {
  if (!eventUmbrella?.event) {
    return null
  }

  return <EventCountdown event={eventUmbrella.event} />
}

EventCountdownWidget.propTypes = {
  eventUmbrella: shape({
    slug: string
  })
}

export default EventCountdownWidget
