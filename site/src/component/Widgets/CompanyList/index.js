import React from 'react'
import { array, shape } from 'prop-types'
import CompanyList from '@/component/Primitive/CompanyPortal/CompanyList'

const CompanyListWidget = ({ article }) => {
  const { companies } = article

  const listItems = []
  for (const company of companies) {
    const obj = {
      title: company.name,
      imgSrc: company?.images?.logo_free_127?.[0]?.url
    }
    if (company.showProfilePage) {
      obj.link = `/company/${company.slug}`
    }
    listItems.push(obj)
  }

  if (!listItems.length) {
    return null
  }

  listItems.sort((a, b) => a.title.localeCompare(b.title))

  return (
    <CompanyList
      title="Company Portals"
      listItems={listItems}
      noMarginBottom
      clickToShowMoreCount={10}
    />
  )
}

CompanyListWidget.propTypes = {
  article: shape({ companies: array })
}

export default CompanyListWidget
