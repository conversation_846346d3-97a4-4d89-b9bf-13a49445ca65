import React from 'react'
import { array, object, string } from 'prop-types'
import { EventStats } from '@/component/Primitive/Events'
import createButtonGroup from '../lib/button-group-creator'

const EventStatsWidget = ({ images, pageData, buttonGroup, ...other }) => {
  const getImage = (context, ratio, size) =>
    images?.[`${context}_${ratio}_${size}`]?.[0]

  const backgroundImage = getImage('desktop_background', '72x17', 720)?.url

  return (
    <EventStats
      buttonGroup={createButtonGroup(pageData, buttonGroup)}
      backgroundImage={backgroundImage}
      {...other}
    />
  )
}

EventStatsWidget.propTypes = {
  stats: array,
  images: object,
  theme: string,
  buttonGroup: array,
  pageData: object
}

export default EventStatsWidget
