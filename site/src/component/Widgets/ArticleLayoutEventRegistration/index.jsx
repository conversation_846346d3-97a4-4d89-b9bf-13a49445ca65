import React from 'react'
import PropTypes from 'prop-types'

import ArticleEventButton from '@/component/Primitive/ArticleEventButton'

const ArticleLayoutEventRegistrationWidget = ({ article }) => {
  if (
    !article ||
    article.contentType !== 'Event' ||
    !article.eventRegistrationLink
  ) {
    return null
  }

  const handleOnDemandLink = article.onDemandLink

  return (
    <ArticleEventButton
      eventRegistrationLink={article.eventRegistrationLink}
      startDate={article.startDate}
      onDemandLink={handleOnDemandLink}
    />
  )
}

ArticleLayoutEventRegistrationWidget.propTypes = {
  article: PropTypes.object
}

export default ArticleLayoutEventRegistrationWidget
