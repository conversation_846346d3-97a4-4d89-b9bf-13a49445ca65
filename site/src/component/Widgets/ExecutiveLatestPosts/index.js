import React from 'react'
import { array, bool, number, object } from 'prop-types'
import CompanyLatestPosts from '@/component/Primitive/CompanyPortal/CompanyLatestPosts'
import withCompanyPortalFeature from '@/component/Widgets/CompanyPortal/withCompanyPortalFeature'

const ExecutiveLatestPostsWidget = ({
  pageData,
  latestPosts,
  company,
  showTitle,
  skip
}) => {
  const { executive } = pageData
  if (!company || !executive) return null
  const title = showTitle ? 'Related Content' : null
  const hideMoreArticlesText = skip < 4

  return (
    <CompanyLatestPosts
      title={title}
      latestPosts={latestPosts}
      hideMoreArticlesText={hideMoreArticlesText}
    />
  )
}

ExecutiveLatestPostsWidget.propTypes = {
  pageData: object,
  latestPosts: array,
  company: object,
  showTitle: bool,
  skip: number
}

export default withCompanyPortalFeature(ExecutiveLatestPostsWidget, {
  requireCompany: true
})
