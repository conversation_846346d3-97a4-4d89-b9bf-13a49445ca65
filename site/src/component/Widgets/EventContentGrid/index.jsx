import React from 'react'
import { array, object, shape, string } from 'prop-types'
import { EventContentGrid } from '@/component/Primitive/Events'
import generateArticleImage from './lib/article-image-generator'
import generateVideoImage from './lib/video-image-generator'
import generateSponsorImages from './lib/sponsor-image-generator'
import generateSpeakerImage from './lib/speaker-image-generator'
import generateAgendaItemImage from './lib/agenda-item-image-generator'
import generateAgendaItemSpeakerImage from './lib/agenda-item-speaker-image-generator'
import generateEventImage from './lib/event-image-generator'
import generateEventUmbrellaImage from './lib/event-umbrella-image-generator'
import createButtonGroup from '../lib/button-group-creator'
import { getTimezoneAbbreviation } from '@/lib/timezoneAbbreviationMiddleware'

const EventContentGridWidget = (props) => {
  const { list, buttonGroup, pageData, eventUmbrella } = props

  if (!list?.length) return null

  const createEventUrl = (path) => {
    let finalPath
    if (!eventUmbrella?.event) return null
    if (!path) finalPath = ''
    else finalPath = '/' + path
    return (
      'events/' +
      eventUmbrella?.slug +
      '/' +
      eventUmbrella?.event?.slug +
      finalPath
    )
  }
  const listWithImages = list
    ?.map((content) => {
      switch (content.type) {
        case 'article': {
          const article = content.article
          if (!article) return null
          content.image = generateArticleImage(article.images)
          content.fullUrlPath = createEventUrl(
            article.eventBaseSlug + '/' + article.slug
          )
          break
        }
        case 'video': {
          const video = content.video
          if (!video) return null
          content.image = generateVideoImage(video.images)
          content.fullUrlPath = video.fullUrlPath
          content.align = 'left'
          break
        }
        case 'sponsor': {
          const sponsor = content.sponsor
          if (!sponsor) return null
          const { logo } = generateSponsorImages(sponsor.images)
          content.image = logo
          if (sponsor.enableSponsorBooth)
            content.fullUrlPath = createEventUrl(`sponsor/${sponsor.slug}`)
          content.align = 'center'
          break
        }
        case 'speaker': {
          const speaker = content.speaker
          if (!speaker) return null
          const { image, logo } = generateSpeakerImage(
            speaker.images,
            speaker.companyImages
          )
          content.image = image
          content.logo = logo
          content.fullUrlPath = speaker._url && createEventUrl(speaker._url)
          content.timezone = eventUmbrella?.event?.timezone
          content.align = 'center'
          content.registerButtonClassName =
            eventUmbrella?.event?._registerButtonClassName
          content.registerAction = eventUmbrella?.event?._registerAction
          if (content.speaker.nextAgendaItem) {
            content.speaker.nextAgendaItem.speakers = content.speaker.nextAgendaItem.speakers.map(
              (s) => ({
                ...s,
                image: generateAgendaItemSpeakerImage(s.images)
              })
            )
            content.timezoneAbbreviation = eventUmbrella?.event?.timezone
              ? getTimezoneAbbreviation(
                  content.speaker.nextAgendaItem.endDate,
                  eventUmbrella?.event?.timezone
                )
              : 'unset'
          }
          break
        }
        case 'agendaItem': {
          const agendaItem = content.agendaItem
          let timezoneAbbreviation
          if (eventUmbrella?.event?.timezone) {
            timezoneAbbreviation = getTimezoneAbbreviation(
              content.agendaItem.endDate,
              eventUmbrella?.event?.timezone
            )
          }
          if (!agendaItem) return null
          content._id = agendaItem._id
          content.localeSafeStartTime = agendaItem.localeSafeStartTime
          content.localeSafeEndTime = agendaItem.localeSafeEndTime
          content.startDate = agendaItem.startDate
          content.image = generateAgendaItemImage(agendaItem.images)
          content.timezoneAbbreviation = timezoneAbbreviation ?? 'unset'
          content.align = 'center'
          content.registerButtonClassName =
            eventUmbrella?.event?._registerButtonClassName
          content.registerAction = eventUmbrella?.event?._registerAction
          content.agendaItem.speakers = content.agendaItem.speakers.map(
            (s) => ({
              ...s,
              image: generateAgendaItemSpeakerImage(s.images)
            })
          )
          break
        }
        case 'event': {
          const event = content.event
          if (!event) return null
          content.image = generateEventImage(event.images)
          content.fullUrlPath = event._fullUrl
          content.align = 'left'
          break
        }
        case 'eventUmbrella': {
          const eventUmbrella = content.eventUmbrella
          if (!eventUmbrella) return null
          content.image = generateEventUmbrellaImage(eventUmbrella.images)
          content.fullUrlPath = eventUmbrella._fullUrl
          content.align = 'center'
          break
        }
        default:
          break
      }
      return content
    })
    .filter((content) => content)

  return (
    <EventContentGrid
      {...props}
      buttonGroup={createButtonGroup(pageData, buttonGroup)}
      list={listWithImages}
      baseUrl={createEventUrl()}
      timezone={eventUmbrella?.event?.timezone}
    />
  )
}

EventContentGridWidget.propTypes = {
  list: array,
  images: object,
  buttonGroup: array,
  pageData: object,
  eventUmbrella: shape({
    slug: string
  })
}

export default EventContentGridWidget
