import React from 'react'
import { array, string, object } from 'prop-types'
import { EventDetails } from '@/component/Primitive/Events'
import createButtonGroup from '../lib/button-group-creator'

const EventDetailsWidget = ({
  details,
  images,
  pageData,
  theme,
  variant,
  buttonGroup,
  title,
  description
}) => {
  const getImage = (context, ratio, size) =>
    images?.[`${context}_${ratio}_${size}`]?.[0]

  const backgroundImage = getImage('desktop_background', '72x17', 720)?.url

  const detailsWithButtonGroups = details.map((d) => ({
    ...d,
    buttonGroup: createButtonGroup(pageData, d.buttonGroup)
  }))

  return (
    <EventDetails
      details={detailsWithButtonGroups}
      title={title}
      description={description}
      theme={theme}
      backgroundImage={backgroundImage}
      variant={variant}
      buttonGroup={createButtonGroup(pageData, buttonGroup)}
    />
  )
}

EventDetailsWidget.propTypes = {
  title: string,
  description: string,
  details: array,
  images: object,
  theme: string,
  pageData: object,
  variant: string,
  buttonGroup: array
}

export default EventDetailsWidget
