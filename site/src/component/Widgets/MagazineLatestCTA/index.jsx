import React from 'react'
import { array, string } from 'prop-types'
import MagazineLatestCTA from '@/component/Primitive/MagazineLatestCTA'

const MagazineLatestCTAWidget = ({ title, magazineIssues }) => {
  if (!magazineIssues) return null
  const widgetProps = {
    to: '/magazine',
    title,
    magazineIssues
  }

  return <MagazineLatestCTA {...widgetProps} />
}

MagazineLatestCTAWidget.propTypes = {
  title: string.isRequired,
  magazineIssues: array.isRequired
}

export default MagazineLatestCTAWidget
