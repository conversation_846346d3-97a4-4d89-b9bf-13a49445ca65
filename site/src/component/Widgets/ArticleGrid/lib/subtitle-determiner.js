import { format } from 'date-fns'
import { getTimezoneAbbreviation } from '@/lib/timezoneAbbreviationMiddleware'

const determineSubtitle = (article) => {
  const timezoneAbbreviation = getTimezoneAbbreviation(
    article.startDate,
    article.timezone
  )

  const displayStartTime =
    article.localeSafeStartTime && article.localeSafeStartTime !== 'unset'
      ? article.localeSafeStartTime
      : format(new Date(article.startDate), 'HH:mm')

  const displayTimezone =
    timezoneAbbreviation && timezoneAbbreviation !== 'unset'
      ? ` (${timezoneAbbreviation})`
      : ''
  const subtitleMap = {
    Event: `${format(new Date(article.startDate), 'EE dd MMM, yyyy')} • ${
      article.timezone ? `${displayStartTime} ${displayTimezone} • ` : ''
    } ${article.address}`,
    Interview: article.subAttribution
  }

  return subtitleMap[article.contentType] || ''
}

export default determineSubtitle
