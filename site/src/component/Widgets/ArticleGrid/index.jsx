import React from 'react'
import { string, bool, object, number } from 'prop-types'

import determineImageCrop from './lib/image-crop-determiner'
import determineSubtitle from './lib/subtitle-determiner'
import determineIconType from './lib/icon-type-determiner'
import changeFileExtension from '@/lib/filename-extension-modifier'

import CardGrid from '@/component/Primitive/CardGrid'

const ArticleGrid = ({
  articles,
  displayType,
  displayCategory,
  rowIndex,
  pageData
}) => {
  const { isDesktop } = pageData
  if (!articles || !articles.results) return null
  const formattedArticles = articles.results.map((article, index) => {
    const imageCrop = determineImageCrop(
      displayType,
      article.contentType,
      index
    )

    const imageSizeMap = {
      landscape: [138, 206, 290, 412, 580, 900],
      portrait: [104, 208, 290, 580, 720, 900],
      widescreen: [322, 553, 644, 1106]
    }

    const getCtaText = () => {
      if (article.contentType === 'Event') {
        if (!article.startDate) return 'Read More'
        const startDate = new Date(article.startDate)
        if (startDate < new Date()) {
          return 'Read More'
        }
        return 'Register now'
      }
      return 'Read More'
    }

    return {
      id: article._id,
      title: article.headline,
      subtitle: determineSubtitle(article),
      shouldShowSubtitle:
        ['2', '11'].includes(displayType) ||
        (displayType === '8' && article.contentType === 'Event'),
      description: article.sell,
      link: article.fullUrlPath,
      siblingInstance: article.siblingInstance,
      featured: article.featured,
      quote: article.quote,
      category: article.category,
      hashtags: article.tags && article.tags.map((t) => t.tag),
      onDemandLink: article.onDemandLink,
      eventRegistrationLink: article.eventRegistrationLink,
      attribution: article.attribution,
      imageIcon: determineIconType(article.contentType) || null,
      subAttribution: article.subAttribution,
      ctaText: getCtaText(),
      contentType: article.contentType,
      eventId: article.eventId,
      eventBaseSlug: article.eventBaseSlug,
      startDate: article.startDate,
      endDate: article.endDate,
      rowIndex,
      articleIndex: index,
      isDesktop,
      // eslint-disable-next-line camelcase
      src: article.images?.[`thumbnail_${imageCrop}_290`]?.[0]?.url,
      srcSet: imageSizeMap[imageCrop].map((size) => {
        return {
          width: size,
          src: changeFileExtension(
            article.images?.[`thumbnail_${imageCrop}_${size}`]?.[0]?.url,
            'webp'
          )
        }
      })
    }
  })

  return (
    <CardGrid
      articles={formattedArticles}
      variation={displayType}
      displayCategory={displayCategory}
    />
  )
}

ArticleGrid.propTypes = {
  articles: object,
  displayType: string,
  displayCategory: bool,
  rowIndex: number,
  pageData: object
}

export default ArticleGrid
