import React from 'react'
import { array, object, shape, string } from 'prop-types'
import { EventContentBlock } from '@/component/Primitive/Events'
import generateArticleImage from './lib/article-image-generator'
import generateVideoImage from './lib/video-image-generator'
import generateSponsorImages from './lib/sponsor-image-generator'
import generateSpeakerImage from './lib/speaker-image-generator'
import generateAgendaItemImage from './lib/agenda-item-image-generator'
import generateAgendaItemSponsorImage from './lib/agenda-item-sponsor-image-generator'
import generateAgendaItemSpeakerImage from './lib/agenda-item-speaker-image-generator'
import generateEventImage from './lib/event-image-generator'
import generateEventUmbrellaImage from './lib/event-umbrella-image-generator'
import createButtonGroup from '../lib/button-group-creator'
import { getTimezoneAbbreviation } from '@/lib/timezoneAbbreviationMiddleware'

const EventContentBlockWidget = (props) => {
  const { list, pageData, id, eventUmbrella } = props

  const createEventUmbrellaUrl = () => 'events/' + eventUmbrella?.slug

  const createEventUrl = (path) =>
    'events/' +
    eventUmbrella?.slug +
    '/' +
    eventUmbrella?.event?.slug +
    '/' +
    path

  const listWithImages = list
    .map((content) => {
      content.buttonGroup = createButtonGroup(pageData, content.buttonGroup)
      switch (content.type) {
        case 'article': {
          const article = content.article
          if (!article) return null
          content.image = generateArticleImage(article.images)
          content.fullUrlPath = createEventUrl(
            article.eventBaseSlug + '/' + article.slug
          )
          break
        }
        case 'video': {
          const video = content.video
          if (!video) return null
          content.image = generateVideoImage(video.images)
          content.fullUrlPath = createEventUmbrellaUrl(
            `on-demand/${video.slug}`
          )
          break
        }
        case 'sponsor': {
          const sponsor = content.sponsor
          if (!sponsor) return null
          const { image, logo } = generateSponsorImages(sponsor.images)
          content.image = image
          content.logo = logo
          if (sponsor.enableSponsorBooth)
            content.fullUrlPath = createEventUrl(`sponsor/${sponsor.slug}`)
          break
        }
        case 'speaker': {
          const speaker = content.speaker
          if (!speaker) return null
          content.image = generateSpeakerImage(speaker.images)
          break
        }
        case 'agendaItem': {
          const agendaItem = content.agendaItem
          if (!agendaItem) return null
          content.image = generateAgendaItemImage(agendaItem.images)
          content.localeSafeStartTime = agendaItem.localeSafeStartTime
          content.localeSafeEndTime = agendaItem.localeSafeEndTime
          content.timezoneAbbreviation = eventUmbrella?.event?.timezone
            ? getTimezoneAbbreviation(
                content.agendaItem.endDate,
                eventUmbrella?.event?.timezone
              )
            : 'unset'
          if (agendaItem.sponsor && agendaItem.sponsor?.images) {
            content.logo = generateAgendaItemSponsorImage(
              agendaItem.sponsor.images
            )
          }
          content.timezone = eventUmbrella?.event?.timezone
          content.registerButtonClassName =
            eventUmbrella?.event?._registerButtonClassName
          content.registerAction = eventUmbrella?.event?._registerAction
          content.agendaItem.speakers = content.agendaItem.speakers.map(
            (s) => ({
              ...s,
              image: generateAgendaItemSpeakerImage(s.images)
            })
          )
          break
        }
        case 'event': {
          const event = content.event
          if (!event) return null
          content.image = generateEventImage(event.images)
          content.fullUrlPath = event._fullUrl
          break
        }
        case 'eventUmbrella': {
          const eventUmbrella = content.eventUmbrella
          if (!eventUmbrella) return null
          content.image = generateEventUmbrellaImage(eventUmbrella.images)
          break
        }
        default:
          break
      }
      return content
    })
    .filter((content) => content)

  return <EventContentBlock {...props} list={listWithImages} widgetId={id} />
}

EventContentBlockWidget.propTypes = {
  list: array,
  images: object,
  id: string,
  pageData: object,
  eventUmbrella: shape({
    slug: string
  })
}

export default EventContentBlockWidget
