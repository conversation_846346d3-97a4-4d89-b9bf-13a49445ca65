import React from 'react'
import CompanyGallery, {
  propTypes
} from '@/component/Primitive/CompanyPortal/CompanyGallery'
import withCompanyPortalFeature from '@/component/Widgets/CompanyPortal/withCompanyPortalFeature'

const CompanyGalleryWidget = ({ galleryImages }) => {
  if (!galleryImages?.image_768) {
    return null
  }

  return <CompanyGallery galleryImages={galleryImages} />
}

CompanyGalleryWidget.propTypes = propTypes

export default withCompanyPortalFeature(CompanyGalleryWidget, {
  verifiedOnly: true
})
