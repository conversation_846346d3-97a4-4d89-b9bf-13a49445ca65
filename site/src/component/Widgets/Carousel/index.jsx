import React, { useContext } from 'react'
import PropTypes from 'prop-types'
import { format } from 'date-fns'
import { getTimezoneAbbreviation } from '@/lib/timezoneAbbreviationMiddleware'

import determineImageCrop from './lib/image-crop-determiner'
import changeFileExtension from '@/lib/filename-extension-modifier'
import Card from '@/component/Primitive/Card'
import CarouselComponent from '@/component/Primitive/Carousel'

import brandToColor from '@/lib/brand-to-color'
import { ThemeContext } from '@/component/Context/ThemeContext'
import CardButton from '@/component/Primitive/Card/component/CardButton'

const Carousel = ({ articles, itemsPerRow }) => {
  const { theme } = useContext(ThemeContext)

  const determineSubtitle = (article) => {
    const timezoneAbbreviation = getTimezoneAbbreviation(
      article.startDate,
      article.timezone
    )

    const displayStartTime =
      article.localeSafeStartTime && article.localeSafeStartTime !== 'unset'
        ? article.localeSafeStartTime
        : format(new Date(article.startDate), 'HH:mm')

    const displayTimezone =
      timezoneAbbreviation && timezoneAbbreviation !== 'unset'
        ? ` (${timezoneAbbreviation})`
        : ''
    const subtitleMap = {
      Event: `${format(new Date(article.startDate), 'EE dd MMM, yyyy')} • ${
        article.timezone ? `${displayStartTime} ${displayTimezone} • ` : ''
      } ${article.address}`,
      Interview: article.subAttribution
    }

    return subtitleMap[article.contentType] || ''
  }

  const formattedArticles = articles.results?.map((article) => {
    const imageCrop = determineImageCrop(article.contentType)

    const imageSizeMap = {
      landscape: [138, 206, 290, 412, 580, 900],
      portrait: [104, 208, 290, 580, 720, 900],
      widescreen: [322, 553, 644, 1106]
    }

    return {
      id: article._id,
      title: article.headline,
      subtitle: determineSubtitle(article),
      startDate: article.startDate,
      link: article.fullUrlPath,
      onDemandLink: article.onDemandLink,
      eventRegistrationLink: article.eventRegistrationLink,
      isExternalLink: article.onDemandLink || article.eventRegistrationLink,
      siblingInstance: article.siblingInstance,
      contentType: article.contentType,
      description: article.contentType === 'Event' ? article.sell : null,
      src: article.images?.thumbnail_landscape_290?.[0]?.url,
      srcSet: imageSizeMap[imageCrop].map((size) => {
        return {
          width: size,
          src: changeFileExtension(
            article.images?.[`thumbnail_${imageCrop}_${size}`]?.[0]?.url,
            'webp'
          )
        }
      })
    }
  })

  const getImageIcon = (type) => {
    const typeMap = {
      Video: 'play'
    }
    return typeMap[type] || null
  }

  const getButton = (article) => {
    if (!article.startDate || article.contentType !== 'Event') return null

    const startDate = new Date(article.startDate)
    if (startDate < new Date()) {
      if (article.onDemandLink)
        return <CardButton theme="secondary">Watch on Demand</CardButton>
      return <CardButton theme="tertiary">Find out more</CardButton>
    }
    return <CardButton theme="primary">Register now</CardButton>
  }

  return (
    <CarouselComponent itemsPerRow={itemsPerRow}>
      {formattedArticles?.map((article, i) => (
        <div
          key={`CarouselComponent--Article--${i}`}
          style={{
            width: `calc(${100 / itemsPerRow}% - ${
              ((itemsPerRow - 1) * 24) / itemsPerRow
            }px)`
          }}
        >
          <Card
            link={
              article.onDemandLink ||
              article.eventRegistrationLink ||
              article.link
            }
            isExternalLink={article.isExternalLink}
            siblingInstance={article.siblingInstance}
            title={article.title}
            src={article.src}
            subtitle={article.subtitle}
            typeConfig={{
              title: { themed: true },
              subtitle: {
                color: brandToColor(theme)
              }
            }}
            footerComponents={() => getButton(article)}
            description={article.description}
            imageRatio={9 / 16}
            imageIcon={getImageIcon(article.contentType)}
            srcSet={article.srcSet}
            sizes={[
              '(min-width: 1420px) 330px',
              '(min-width: 960px) calc(25vw - 32px - 80px)',
              'calc(75vw - 80px)'
            ]}
            width={330}
            shouldShowSubtitle={article.contentType === 'Event'}
            truncateTitleAt={article.contentType === 'Video' ? 2 : undefined}
          />
        </div>
      ))}
    </CarouselComponent>
  )
}

Carousel.propTypes = {
  articles: PropTypes.object,
  itemsPerRow: PropTypes.number
}

export default Carousel
