import React from 'react'
import { arrayOf, shape, string } from 'prop-types'
import UpcomingEventsCarousel, {
  EVENT
} from '@/component/Primitive/UpcomingEventsCarousel'

const UpcomingEventsCarouselWidget = ({
  events,
  overArchingEventLightLogoUrl,
  overArchingEventDarkLogoUrl
}) => {
  if (!events.length) {
    return null
  }

  return (
    <UpcomingEventsCarousel
      events={events}
      overArchingEventLightLogoUrl={overArchingEventLightLogoUrl}
      overArchingEventDarkLogoUrl={overArchingEventDarkLogoUrl}
    />
  )
}

UpcomingEventsCarouselWidget.propTypes = {
  events: arrayOf(shape(EVENT)),
  overArchingEventLightLogoUrl: string,
  overArchingEventDarkLogoUrl: string
}

export default UpcomingEventsCarouselWidget
