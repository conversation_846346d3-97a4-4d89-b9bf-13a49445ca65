import React from 'react'
import { array, object } from 'prop-types'
import EventPanels from '@/component/Primitive/Events/components/EventPanels'
import generateAgendaItemImage from '@/component/Widgets/EventContentBlock/lib/agenda-item-image-generator'
import generateAgendaItemSponsorImage from '@/component/Widgets/EventContentBlock/lib/agenda-item-sponsor-image-generator'
import generateAgendaItemSpeakerImage from '@/component/Widgets/EventContentGrid/lib/agenda-item-speaker-image-generator'
import { getTimezoneAbbreviation } from '@/lib/timezoneAbbreviationMiddleware'

const EventPanelsWidget = ({ buttonGroup, agendaItems, eventUmbrella }) => {
  const filteredPanels = agendaItems?.filter(
    (item) => item.sessionType.shortName.toLowerCase() === 'panel'
  )

  const contentReadyPanels = filteredPanels?.map((content, index) => {
    content.image = generateAgendaItemImage(content.images)
    content.timezoneAbbreviation = eventUmbrella?.event?.timezone
      ? getTimezoneAbbreviation(content.endDate, eventUmbrella?.event?.timezone)
      : 'unset'
    if (content.sponsor && content.sponsor?.images) {
      content.logo = generateAgendaItemSponsorImage(content.sponsor.images)
    }
    content.timezone = eventUmbrella?.event?.timezone
    content.registerButtonClassName =
      eventUmbrella?.event?._registerButtonClassName
    content.registerAction = eventUmbrella?.event?._registerAction
    content.speakers = content.speakers.map((s) => ({
      ...s,
      image: generateAgendaItemSpeakerImage(s.images)
    }))

    return {
      ...content,
      tag: 'Event Agenda',
      title: content.title || content.name,
      subtitle: content.subtitle || content.sessionType.name,
      description: content.description || '...description',
      ctaLabel: content.ctaLabel || 'Read about topic',
      theme: content.theme || null,
      inverse: index % 2,
      logo: content.logo,
      timeRange: `${content.localeSafeStartTime} - ${content.localeSafeEndTime} (${content.timezoneAbbreviation})`,
      id: `agenda-item-${content.agendaItemId || index}`,
      agendaItem: content
    }
  })

  if (!contentReadyPanels.length) {
    return null
  }

  return (
    <EventPanels agendaItems={contentReadyPanels} buttonGroup={buttonGroup} />
  )
}

EventPanelsWidget.propTypes = {
  agendaItems: array,
  buttonGroup: array,
  eventUmbrella: object
}

export default EventPanelsWidget
