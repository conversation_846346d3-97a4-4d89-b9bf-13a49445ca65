import React from 'react'
import { array, shape } from 'prop-types'
import CompanyExecutiveGrid from '@/component/Primitive/CompanyPortal/CompanyExecutivesGrid'

const CompanyExecutivesAllWidget = ({ pageData }) => {
  const { company } = pageData
  if (!company) {
    return null
  }

  const { executives, executiveSortOrder } = company
  if (!executives) {
    return null
  }

  return (
    <CompanyExecutiveGrid
      executives={executives}
      preferredOrder={executiveSortOrder || []}
      company={company}
    />
  )
}

CompanyExecutivesAllWidget.propTypes = {
  pageData: shape({
    company: shape({
      executives: array,
      preferredOrder: array
    })
  })
}

export default CompanyExecutivesAllWidget
