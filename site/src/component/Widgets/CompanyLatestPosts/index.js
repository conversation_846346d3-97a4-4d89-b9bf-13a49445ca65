import React from 'react'
import { array, object, string } from 'prop-types'
import CompanyLatestPosts from '@/component/Primitive/CompanyPortal/CompanyLatestPosts'
import withCompanyPortalFeature from '@/component/Widgets/CompanyPortal/withCompanyPortalFeature'

const CompanyLatestPostsWidget = ({ companyName, latestPosts, company }) => {
  const title = companyName ? `Latest with ${companyName}` : 'Latest'

  return (
    <CompanyLatestPosts
      title={title}
      latestPosts={latestPosts}
      company={company}
    />
  )
}

CompanyLatestPostsWidget.propTypes = {
  companyName: string,
  latestPosts: array,
  company: object
}

export default withCompanyPortalFeature(CompanyLatestPostsWidget, {
  extractCompanyData: (company) => ({ companyName: company?.name, company })
})
