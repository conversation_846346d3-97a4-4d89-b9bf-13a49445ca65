import React from 'react'
import { EventFeatureBoxes } from '@/component/Primitive/Events'
import { arrayOf, bool, object, shape, string } from 'prop-types'

const EventFeatureBoxesWidget = ({ items }) => {
  const featureBoxes = items.map((item) => {
    const image = item?.images?.feature_box_640?.[0] || {}

    return {
      title: item.title,
      url: item.destination,
      opensInNewTab: item.opensInNewTab,
      image: image
    }
  })

  if (!featureBoxes.length) {
    return <></>
  }

  return <EventFeatureBoxes featureBoxes={featureBoxes} />
}

EventFeatureBoxesWidget.propTypes = {
  items: arrayOf(
    shape({
      title: string,
      destination: string,
      opensInNewTab: bool,
      images: object
    })
  )
}

export default EventFeatureBoxesWidget
