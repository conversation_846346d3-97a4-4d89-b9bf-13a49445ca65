import React from 'react'
import CompanyList from '@/component/Primitive/CompanyPortal/CompanyList'
import { array, object } from 'prop-types'
import withCompanyPortalFeature from '@/component/Widgets/CompanyPortal/withCompanyPortalFeature'

const CompanyExecutivesList = ({ company, executives }) => {
  const listItems = []

  if (executives?.length) {
    for (const index in executives) {
      const { images, name, jobTitle, slug } = executives[index]
      const execData = {
        title: name,
        subTitle: jobTitle,
        link: `/company/${company.slug}/executives/${slug}`,
        hasBackgroundImage: true
      }

      if (images?.headshotv2_1x1_220?.length) {
        execData.imgSrc = images.headshotv2_1x1_220?.[0]?.url
        execData.hasBackgroundImage = false
      } else if (images?.headshot_220x347_220?.length) {
        execData.imgSrc = images.headshot_220x347_220?.[0]?.url
      }

      listItems.push(execData)

      if (index >= 4) {
        break
      }
    }
  }

  if (!listItems.length) return null

  const title = 'Executives'
  const props = {
    title,
    listItems
  }

  if (company?.portalPermissions?.enableExecutivesNavigation) {
    props.link = {
      title: `View All ${title}`,
      url: `/company/${company.slug}/executives`
    }
  }

  return <CompanyList {...props} />
}

CompanyExecutivesList.propTypes = {
  company: object,
  executives: array
}

export default withCompanyPortalFeature(CompanyExecutivesList)
