import React from 'react'
import { shape, string, object } from 'prop-types'

import ArticleHeader from '@/component/Primitive/ArticleHeader'

const ArticleLayoutHeader = ({ article, pageData, instance }) => {
  article.__shareUrl = encodeURI(article?.fullUrlPath || pageData?.url)
  article.__linkedinFollowSubdomain = instance?.subdomain
  return <ArticleHeader {...article} />
}

ArticleLayoutHeader.propTypes = {
  article: shape({
    headline: string
  }),
  pageData: shape({
    url: string
  }),
  instance: object
}

export default ArticleLayoutHeader
