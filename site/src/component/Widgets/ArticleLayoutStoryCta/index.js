import React from 'react'
import PropTypes from 'prop-types'

import StoryCTA from '@/component/Primitive/StoryCTA'

const ArticleLayoutStoryCtaWidget = ({ article, magazine }) => {
  if (!article || !magazine) return null

  const pageNumber = article?.pageNumber

  if (!pageNumber) {
    return null
  }

  const widgetProps = {
    to: `/magazine${pageNumber ? `?page=${pageNumber}` : ''}`,
    as: `/magazine/${magazine.slug}${pageNumber ? `?page=${pageNumber}` : ''}`,
    title: 'Read full article here',
    imageUrl: magazine.images?.cover_321x446_150?.[0]?.url
  }

  return <StoryCTA {...widgetProps} />
}

ArticleLayoutStoryCtaWidget.propTypes = {
  article: PropTypes.object,
  magazine: PropTypes.object
}

export default ArticleLayoutStoryCtaWidget
