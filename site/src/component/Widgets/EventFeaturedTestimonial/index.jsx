import React from 'react'
import { string, object, bool, array } from 'prop-types'
import { EventFeaturedTestimonial } from '@/component/Primitive/Events'
import createButtonGroup from '../lib/button-group-creator'

const EventFeaturedTestimonialWidget = ({
  images,
  speaker,
  buttonGroup,
  pageData,
  ...other
}) => {
  const getImage = (imageList, context, ratio, size) =>
    imageList?.[`${context}_${ratio}_${size}`]?.[0]

  const backgroundImage = getImage(images, 'desktop_background', '72x17', 720)
    ?.url

  if (speaker) {
    speaker.image = {
      alt: '',
      sizes: ['min-width(960px) 50vw', '100vw'],
      src:
        getImage(images, 'headshot', '1x1', 320)?.url ||
        getImage(speaker?.images, 'speaker', '1x1', 320)?.url
    }
  }

  return (
    <EventFeaturedTestimonial
      speaker={speaker}
      backgroundImage={backgroundImage}
      buttonGroup={createButtonGroup(pageData, buttonGroup)}
      {...other}
    />
  )
}

EventFeaturedTestimonialWidget.propTypes = {
  title: string,
  subtitle: string,
  description: string,
  buttonGroup: array,
  theme: string,
  inverse: bool,
  images: object,
  speaker: object,
  pageData: object
}

export default EventFeaturedTestimonialWidget
