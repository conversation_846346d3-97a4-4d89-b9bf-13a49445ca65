import React from 'react'
import { array, number, string, object } from 'prop-types'
import CompanyArticleSection from '@/component/Primitive/CompanyPortal/CompanyArticleSection'
import CompanyTitle from '@/component/Primitive/CompanyPortal/CompanyTitle'
import withCompanyPortalFeature from '@/component/Widgets/CompanyPortal/withCompanyPortalFeature'

const CompanyArticleSectionWidget = ({
  articleType,
  items,
  display,
  company
}) => {
  const { portalPermissions } = company
  const canView = () => {
    const articleTypeNoWhitespace = articleType.replace(/\s+/g, '') || ''

    if (typeof portalPermissions === 'object') {
      for (const permission in portalPermissions) {
        if (permission.includes(articleTypeNoWhitespace)) {
          return !!portalPermissions[permission]
        }
      }
    }

    return true
  }

  if (!canView()) {
    return <></>
  }

  if (!Array.isArray(items) || !items.length) {
    return <></>
  }

  return (
    <div>
      <CompanyTitle title={`${articleType}s`} />

      <CompanyArticleSection
        items={items}
        articleType={articleType}
        display={display}
        company={company}
        isVerified={company?.isVerified}
      />
    </div>
  )
}

CompanyArticleSectionWidget.propTypes = {
  items: array,
  articleType: string,
  display: number,
  company: object
}

export default withCompanyPortalFeature(CompanyArticleSectionWidget)
