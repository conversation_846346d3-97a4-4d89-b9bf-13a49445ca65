import React, { useState } from 'react'
import { object, bool, number } from 'prop-types'
import InfiniteScroll from 'react-infinite-scroll-component'
import createLoadMoreQuery from '@/query/widget/simple-article-grid-widget-load-more'

import TextAlign from '@/component/Primitive/TextAlign'
import ButtonStandard from '@/component/Primitive/ButtonStandard'
import Spinner from '@/component/Primitive/Spinner'

import TypeSpecificCardGrid from '@/component/Primitive/TypeSpecificCardGrid'

import styles from './SimpleArticleGrid.module.scss'

const SimpleArticleGrid = ({
  articles,
  loadMoreEnabled,
  numPerPage,
  numPerRow,
  rowIndex,
  pageData
}) => {
  const { isDesktop } = pageData
  const totalItems = articles.total
  articles = articles.results
  numPerRow = numPerRow || 4
  const indexedArticles = articles.map((a, i) => ({
    ...a,
    rowIndex,
    articleIndex: i,
    isDesktop
  }))

  const [results, setResults] = useState(indexedArticles)
  const [pageNum, setPageNum] = useState(1)
  const [infiniteScrollEnabled, setInfiniteScrollEnabled] = useState(false)
  const [canLoadMore, setCanLoadMore] = useState(
    loadMoreEnabled && totalItems > numPerPage
  )

  const onMoreClick = async () => {
    const url = window.location.origin + '/graphql'
    const query = createLoadMoreQuery()
    const vars = {
      widgetType: 'simpleArticleGrid',
      page: pageNum,
      url: window.location.href,
      currentArticleIds: window.__CLIENT_DEDUPE.list()
    }
    const response = await fetch(url, {
      headers: { 'content-type': 'application/json' },
      method: 'POST',
      body: JSON.stringify({ query, variables: vars })
    })
    const newResults = await response.json()
    const updatedResults = [
      ...results,
      ...newResults.data.paginatedWidget.articles.results
    ]
    setResults(updatedResults)
    setPageNum(pageNum + 1)
    setCanLoadMore(updatedResults.length < totalItems)
    setInfiniteScrollEnabled(true)
  }
  return (
    <div className={styles.Container}>
      <InfiniteScroll
        dataLength={results.length}
        next={onMoreClick}
        hasMore={canLoadMore && infiniteScrollEnabled}
        scrollThreshold={0.5}
        loader={
          <TextAlign center>
            <Spinner />
          </TextAlign>
        }
      >
        <TypeSpecificCardGrid results={results} numPerRow={numPerRow} />
      </InfiniteScroll>

      {canLoadMore && !infiniteScrollEnabled && (
        <TextAlign center>
          <ButtonStandard
            className={styles.Button}
            primary
            onClick={onMoreClick}
          >
            Show More
          </ButtonStandard>
        </TextAlign>
      )}
    </div>
  )
}

SimpleArticleGrid.propTypes = {
  articles: object,
  loadMoreEnabled: bool,
  numPerPage: number,
  numPerRow: number,
  rowIndex: number,
  pageData: object
}

export default SimpleArticleGrid
