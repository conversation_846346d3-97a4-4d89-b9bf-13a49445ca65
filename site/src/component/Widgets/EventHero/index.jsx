import React from 'react'
import { string, object, bool, array } from 'prop-types'
import { EventHero } from '@/component/Primitive/Events'
import createButtonGroup from '../lib/button-group-creator'
import generateSponsorLogo from './lib/sponsor-image-generator'

const EventHeroWidget = ({
  images,
  pageData,
  buttonGroup,
  sponsor,
  themeColorOverride,
  useMaxHeight,
  lightLogoUrl,
  darkLogoUrl,
  eventUmbrella,
  isTitleVisuallyHidden,
  ...other
}) => {
  let sponsorLogo
  if (sponsor) sponsorLogo = generateSponsorLogo([sponsor?.images])

  const backgroundImage = images?.desktop_background_72x17_1440?.[0]?.url ? (
    <img
      src={images?.desktop_background_72x17_1440?.[0]?.url}
      alt=""
      width={images?.desktop_background_72x17_1440?.[0]?.width}
      height={images?.desktop_background_72x17_1440?.[0]?.height}
      decoding="async"
      fetchpriority="high"
    />
  ) : null

  // Get logos from either the proxy event (via GraphQL) or from the page data
  let lightLogo = lightLogoUrl
  let darkLogo = darkLogoUrl

  // If no proxy logos, fall back to the page data
  if (!lightLogo || !darkLogo) {
    if (eventUmbrella.event) {
      lightLogo = lightLogo || eventUmbrella.event?.lightLogoUrl
      darkLogo = darkLogo || eventUmbrella.event?.darkLogoUrl
    } else {
      lightLogo = lightLogo || eventUmbrella?.lightLogoUrl
      darkLogo = darkLogo || eventUmbrella?.darkLogoUrl
    }
  }

  return (
    <EventHero
      {...other}
      buttonGroup={createButtonGroup(pageData, buttonGroup)}
      backgroundImage={backgroundImage}
      logos={{ light: lightLogo, dark: darkLogo }}
      sponsorLogo={sponsorLogo}
      themeColorOverride={themeColorOverride}
      useMaxHeight={useMaxHeight}
      isTitleVisuallyHidden={isTitleVisuallyHidden}
    />
  )
}

EventHeroWidget.propTypes = {
  title: string,
  subtitle: string,
  description: string,
  buttonGroup: array,
  theme: string,
  themeColorOverride: string,
  useMaxHeight: bool,
  parallax: bool,
  useAsHeading: bool,
  hideEventLogo: bool,
  pageData: object,
  images: object,
  sponsor: object,
  lightLogo: string,
  darkLogo: string,
  lightLogoUrl: string,
  darkLogoUrl: string,
  eventUmbrella: object,
  isTitleVisuallyHidden: bool
}

export default EventHeroWidget
