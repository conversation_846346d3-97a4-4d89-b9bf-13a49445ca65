@import '~backline-normalize/src/backline-normalize';
@import 'node_modules/react-modal-video/scss/modal-video.scss';
@import 'font-face';
@import 'ckeditor-custom';
@import '~@splidejs/splide/dist/css/splide-core.min.css';

// Let browsers know about available colour schemes
// https://drafts.csswg.org/css-color-adjust-1/#color-scheme-prop
// :root {
//   color-scheme: light dark;
// }

// Remove ALL animations/transitions by making them (almost) immediately skip
// to their finished state. May be overkill, but is a sensible start.
// https://css-tricks.com/revisiting-prefers-reduced-motion-the-reduced-motion-media-query/
@media screen and (prefers-reduced-motion: reduce), (update: slow) {
  * {
    animation-duration: 0.001ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.001ms !important;
  }
}

html {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

body {
  color: $color-black;
  font-family: $font-body;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.4; // 19.6px

  @include mq($breakpoint-desktop) {
    font-size: 20px;
    line-height: 1.6; // 32px

    &.has-locked-scrolling-desktop {
      left: 0;
      position: fixed;
      right: 0;
    }
  }

  &.has-locked-scrolling {
    left: 0;
    position: fixed;
    right: 0;
  }
}

::selection {
  background-color: var(
    --color-theme-button-background,
    --color-theme--secondary
  );
  color: var(--color-theme-button-foreground);
  text-shadow: none;
}

*[data-has-anchor='true'] {
  display: block;
  position: relative;
  top: -250px;
  visibility: hidden;
}

.tp-iframe-wrapper {
  box-shadow: none !important;
}
