import genericFields from '../resource/generic-fields'
import widgetArea from '../widget/widget-area'
import createEventSectionLayoutWidgets from './lib/layout-widgets'
import genericEventUmbrellaFields from './lib/generic-event-umbrella-fields'
import genericEventFields from './lib/generic-event-fields'

const imageProperties = `
  url
  caption
  link
`

const layout = `
slug
noSpacingTop
noSpacingBottom
pianoRegWallEnabled
metaTitle
metaDescription
shareTitle
advertSectionId
upcomingEventRedirectUrl
liveEventRedirectUrl
pastEventRedirectUrl
type
layout {
  attributes
  background
cols {
    width
    attributes
    widgetArea {
      ${widgetArea(createEventSectionLayoutWidgets())}
    }
  }
}
`

const navItemsProperties = `
  title
  url
  parent
  id
  type
`

const createQuery = () => {
  const query = `query Resource($url:String!, $slug:String) {
    ${genericFields()}
    resource(url:$url) {
      __typename
      ...on Redirect {
        redirectUrl
      }
      ...on EventUmbrella {
        ${genericEventUmbrellaFields()}
        _id
        name
        slug
        primaryInstance
        secondaryInstances
        brandColor
        event {
          ${genericEventFields()}
          _id
          name
          slug
          description
          pianoResourceIds
          timezone
          brandColor
          sidebarColorOverride
          buildingName
          startDate
          endDate
          locations {
            key
            name
            vimeoId
          }
          tiers {
            name
            rank
          }
          videos {
            name
          }
          speakers {
            name
          }
          sponsors {
            displayName
          }
          images {
            cover_16x9_668 {
              ${imageProperties}
            }
            cover_21x9_1336 {
              ${imageProperties}
            }
            cover_21x9_668 {
              ${imageProperties}
            }
            cover_1x1_668 {
              ${imageProperties}
            }
            cover_1x1_320 {
              ${imageProperties}
            }
          }
          agenda {
            name
            visibility
            sessionTypeKey
            sessionType {
              name
              shortName
            }
            startDate
            endDate
            localeSafeStartTime
            localeSafeEndTime
            description
            tags
            location {
              name
            }
            images {
              cover_21x9_1336 {
                ${imageProperties}
              }
              cover_21x9_668 {
                ${imageProperties}
              }
              cover_1x1_668 {
                ${imageProperties}
              }
              cover_1x1_320 {
                ${imageProperties}
              }
            }
            speakers {
              name
              jobTitle
              companyName
              images {
                speaker_1x1_320 {
                  ${imageProperties}
                }
              }
            }
            sponsor {
              images {
                logo_1x1_320 {
                  ${imageProperties}
                }
                logo_1x1_668 {
                  ${imageProperties}
                }
              }
            }
          }
          agendaSnapshot {
            info {
              liveStreamStatus
              completedAgendaCount
            }
            currentAgenda {
              name
              sessionTypeKey
              sessionType {
                name
                shortName
              }
              description
              location {
                name
              }
              images {
                cover_21x9_1336 {
                  ${imageProperties}
                }
                cover_21x9_668 {
                  ${imageProperties}
                }
                cover_1x1_668 {
                  ${imageProperties}
                }
                cover_1x1_320 {
                  ${imageProperties}
                }
              }
            }
            nextAgenda {
              name
              sessionTypeKey
              sessionType {
                name
                shortName
              }
              description
              location {
                name
              }
              images {
                cover_21x9_1336 {
                  ${imageProperties}
                }
                cover_21x9_668 {
                  ${imageProperties}
                }
                cover_1x1_668 {
                  ${imageProperties}
                }
                cover_1x1_320 {
                  ${imageProperties}
                }
              }
            }
          }
          showLogoInNavigation
          navItems {
            eventSectionKey
            ${navItemsProperties}
            subItems {
              eventSectionKey
              ${navItemsProperties}
            }
          }
          layoutProperties(slug:$slug) {
            ${layout}
          }
        }
      }
    }
  }
  `
  return query
}

export default createQuery

/*
agenda (~3000),
agendaSnapshot (~2000),
layoutProperties (~25,000),
eventContentGrid: manual speaker (~17,000)
eventContentGrid: agenda (~2000)
eventContentGrid: sponsor (~4000)
eventContentGrid: video (~1000)
*/
