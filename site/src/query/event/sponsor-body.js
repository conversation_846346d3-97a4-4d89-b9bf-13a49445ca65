import widgetArea from '../widget/widget-area'
import widget from '../widget/widget'
import articleGrid from '../widgets/general/article-grid'
import eventArticleGrid from '../widgets/general/event-article-grid'
import eventArticleGridSnippet from '../widgets/general/event-article-grid-snippet'
import eventTextBlock from '../widgets/general/event-text-block'
import eventButtonGroup from '../widgets/general/event-button-group'
import htmlWidget from '../widgets/general/html'
import inlineImageWidget from '../widgets/general/inline-image'
import inlineVideoWidget from '../widgets/general/inline-video'
import textWidget from '../widgets/general/text'
import paginatedListWidget from '../widgets/general/paginated-list'
import genericCarousel from '../widgets/general/generic-carousel'
import eventHero from '../widgets/general/event-hero'
import eventImageSplitHero from '../widgets/general/event-image-split-hero'
import eventVideoSplitHero from '../widgets/general/event-video-split-hero'
import eventCountdown from '../widgets/general/event-countdown'
import eventStats from '../widgets/general/event-stats'
import eventDetails from '../widgets/general/event-details'
import eventFeaturedTestimonial from '../widgets/general/event-featured-testimonial'
import eventTieredSponsor from '../widgets/general/event-tiered-sponsor'
import eventContentGrid from '../widgets/general/event-content-grid'
import eventContentBlock from '../widgets/general/event-content-block'
import eventSponsorHero from '../widgets/general/event-sponsor-hero'
import eventHeaderWidget from '@/query/widgets/general/event-header'
import eventFeatureBoxes from '@/query/widgets/general/event-feature-boxes'
import eventImages from '@/query/widgets/general/event-images'
import eventLogoScroller from '@/query/widgets/general/event-logo-scroller'
import logoGrid from '@/query/widgets/general/logo-grid'
import faqs from '@/query/widgets/general/faqs'

const widgets = [
  widget(),
  articleGrid(),
  eventArticleGrid(),
  eventArticleGridSnippet(),
  eventTextBlock(),
  eventButtonGroup(),
  htmlWidget(),
  inlineImageWidget(),
  inlineVideoWidget(),
  textWidget(),
  paginatedListWidget(),
  genericCarousel(),
  eventHero(),
  eventImageSplitHero(),
  eventVideoSplitHero(),
  eventCountdown(),
  eventStats(),
  eventDetails(),
  eventFeaturedTestimonial(),
  eventTieredSponsor(),
  eventContentGrid(),
  eventContentBlock(),
  eventSponsorHero(),
  eventHeaderWidget(),
  eventFeatureBoxes(),
  eventImages(),
  eventLogoScroller(),
  logoGrid(),
  faqs()
]

const createQuery = (bodyType = 'body') => `
  ${bodyType} {
    ${widgetArea(widgets)}
  }
`

export default createQuery
