import genericFields from '../resource/generic-fields'
import articleBody from './article-body'

import widgetArea from '../widget/widget-area'
import createEventSectionLayoutWidgets from './lib/layout-widgets'
import genericEventUmbrellaFields from './lib/generic-event-umbrella-fields'
import genericEventFields from './lib/generic-event-fields'

const layout = `
slug
noSpacingTop
noSpacingBottom
pianoRegWallEnabled
layout {
  attributes
  background
cols {
    width
    attributes
    widgetArea {
      ${widgetArea(createEventSectionLayoutWidgets())}
    }
  }
}
`

const navItemsProperties = `
  title
  url
  parent
  id
  type
`

const imageProperties = `
  url
  caption
  link
`

const createQuery = () => {
  const articleProperties = `
        _id
        headline
        sell
        metaTitle
        metaDescription
        shareTitle
        shareDescription
        instance
        displayDate
        advertSlotName
        eventRegistrationLink
        onDemandLink
        issue
        issueSlug
        pageNumber
        contentType
        address
        startDate
        endDate
        author {
          name
          slug
        }
        category
        eventId
        eventBaseSlug
        eventArticleCategoryKey
        subAttribution
        companies {
          _id
          name
          slug
          description
          shortDescription
          images {
            logo_free_127 {
              url
            }
          }
        }
        partners {
          _id
          name
          website
        }
        executives {
          name
          slug
          bio
          companyId
          images {
            headshot_220x347_220 {
              url
            }
          }
        }
        price
        downloadUrl
        tags {
          tag
        }
        signUpRequired
        images {
          thumbnail_landscape_322 {
            ${imageProperties}
          }
          thumbnail_portrait_144 {
            ${imageProperties}
          }
          thumbnail_portrait_286 {
            ${imageProperties}
          }
          thumbnail_portrait_576 {
            ${imageProperties}
          }
          thumbnail_widescreen_553 {
            ${imageProperties}
          }
          hero_landscape_320 {
            ${imageProperties}
          }
          hero_landscape_668 {
            ${imageProperties}
          }
          hero_landscape_900 {
            ${imageProperties}
          }
          hero_landscape_1336 {
            ${imageProperties}
          }
          hero_landscape_1800 {
            ${imageProperties}
          }
          hero_portrait_144 {
            ${imageProperties}
          }
          hero_widescreen_320 {
            ${imageProperties}
          }
          hero_widescreen_668 {
            ${imageProperties}
          }
          hero_widescreen_900 {
            ${imageProperties}
          }
          hero_widescreen_1336 {
            ${imageProperties}
          }
          hero_widescreen_1800 {
            ${imageProperties}
          }
          thumbnail_landscape_138 {
            ${imageProperties}
          }
          thumbnail_landscape_206 {
            ${imageProperties}
          }
          share_widescreen_1200 {
            ${imageProperties}
          }
        }
        videoProvider
        videoId
        issuPublicationId
        issuIssueId
        magazineOrigin
        slug
        migratedFromId
        canonicalUrl
        ${articleBody()}
        `

  const query = `
  query EventResource($url:String!, $layoutSlug:String!, $layoutType:String!, $articleSlug:String!) {
    ${genericFields()}
    resource(url:$url) {
      __typename
      ...on Redirect {
        redirectUrl
      }
      ...on EventUmbrella {
            ${genericEventUmbrellaFields()}
            _id
            name
            slug
            primaryInstance
            secondaryInstances
            brandColor
            event {
              ${genericEventFields()}
              article(slug:$articleSlug) {
                ${articleProperties}
              }
              _id
              name
              slug
              pianoResourceIds
              timezone
              _registerButtonClassName
              brandColor
              description
              videos {
                name
              }
              speakers {
                name
              }
              sponsors {
                displayName
              }
              agenda {
                name
                sessionTypeKey
                sessionType {
                  name
                  shortName
                }
              }
              showLogoInNavigation
              navItems {
                eventSectionKey
                ${navItemsProperties}
                subItems {
                  eventSectionKey
                  ${navItemsProperties}
                }
              }
              layoutProperties(slug:$layoutSlug, layoutType:$layoutType) {
                ${layout}
              }
            }
          }
        }
      }`
  return query
}

export default createQuery
