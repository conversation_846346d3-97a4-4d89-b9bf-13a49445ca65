import genericFields from '../resource/generic-fields'
import widgetArea from '../widget/widget-area'
import createEventSectionLayoutWidgets from './lib/layout-widgets'
import genericEventUmbrellaFields from './lib/generic-event-umbrella-fields'

const imageProperties = `
  url
  caption
  link
`

const layout = `
slug
noSpacingTop
noSpacingBottom
pianoRegWallEnabled
metaTitle
metaDescription
shareTitle
advertSectionId
type
layout {
  attributes
  background
cols {
    width
    attributes
    widgetArea {
      ${widgetArea(createEventSectionLayoutWidgets())}
    }
  }
}
`

/*
const navItemsProperties = `
  title
  url
  parent
  id
  type
`

BELOW TAKEN FROM UMBRELLA FRAGMENT WHILE UMBRELLAS ARE BEING HIDDEN
 navItems {
    eventUmbrellaSectionKey
    ${navItemsProperties}
    subItems {
    eventUmbrellaSectionKey
      ${navItemsProperties}
    }
  }
*/

const createQuery = () => {
  const query = `query Resource($url:String!, $key:String, $videoSlug:String!) {
    ${genericFields()}
    resource(url:$url) {
      __typename
      ...on Redirect {
        redirectUrl
      }
      ...on EventUmbrella {
        ${genericEventUmbrellaFields()}
        _id
        name
        slug
        primaryInstance
        secondaryInstances
        brandColor
        showLogoInNavigation
        layoutProperties(key:$key) {
          ${layout}
        }
        video(videoSlug:$videoSlug) {
            _id
            name
            slug
            youtubeId
            description
            duration
            uploadDate
            createdDate
            eventName
            eventFullUrlPath
            event {
              _fullUrl
              offersUrl
              startDate
              endDate
              description
              locationName
              locationStreetAddress
              locationAddressLocality
              locationPostalCode
              locationAddressRegion
              locationAddressCountry
            }
            images {
              cover_21x9_1336 {
                ${imageProperties}
              }
              cover_21x9_668 {
                ${imageProperties}
              }
              cover_16x9_668 {
                ${imageProperties}
              }
              cover_1x1_668 {
                ${imageProperties}
              }
              cover_1x1_320 {
                ${imageProperties}
              }
            }
          }
      }
    }
  }
  `
  return query
}

export default createQuery
