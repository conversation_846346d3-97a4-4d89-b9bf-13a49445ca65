import widget from '../../widget/widget'

import htmlWidget from '../../widgets/general/html'
import imageGridWidget from '../../widgets/general/image-grid'
import inlineImageWidget from '../../widgets/general/inline-image'
import textWidget from '../../widgets/general/text'
import blockquoteWidget from '../../widgets/general/blockquote'
import editorialIntroWidget from '../../widgets/general/editorial-intro'
import editorialContactFormWidget from '../../widgets/general/editorial-contact-form'
import advertWidget from '../../widgets/general/advert'
import videoGridWidget from '../../widgets/general/video-grid'
import articleStackWidget from '../../widgets/general/article-stack'
import carouselWidget from '../../widgets/general/carousel'
import aboutBrandWidget from '../../widgets/general/about-brand'
import advertiseHeroWidget from '../../widgets/general/advertise-hero'
import advertiseInfoWidget from '../../widgets/general/advertise-info'
import newsletterSignupWidget from '../../widgets/general/newsletter-signup'
import mostPopularOrViewedWidget from '../../widgets/general/most-popular-or-viewed'
import articleGrid from '../../widgets/general/article-grid'
import simpleArticleGrid from '../../widgets/general/simple-article-grid'
import gridWithAdvertWidget from '../../widgets/general/grid-with-advert'
import headerWidget from '../../widgets/general/header'
import magazineCtaWidget from '../../widgets/general/magazine-cta'
import latestMagazineIssueWidget from '../../widgets/general/latest-magazine-issue'
import featuredMagazine from '../../widgets/general/featured-magazine'
import magazineGrid from '../../widgets/general/magazine-grid'
import pastMagazineIssuesWidget from '../../widgets/general/past-magazine-issues'
import brandStatsWidget from '../../widgets/general/brand-stats'
import aboutBizClikWidget from '../../widgets/general/about-bizclik'
import aboutCtaWidget from '../../widgets/general/about-cta'
import aboutLiveEventsWidget from '../../widgets/general/about-live-events'
import advertiseFeaturesWidget from '../../widgets/general/advertise-features'
import brandLocationWidget from '../../widgets/general/brand-location'
import eventFormWidget from '../../widgets/general/event-form'
import prnewswireWidget from '../../widgets/general/prnewswire'
import advertiseSalesforceFormWidget from '../../widgets/general/advertise-salesforce-form'
import advertiseTestimonialWidget from '../../widgets/general/advertise-testimonial'
import eventLatestCTA from '../../widgets/general/event-latest-cta'
import magazineLatestCtaWidget from '../../widgets/general/magazine-latest-cta'
import logoGridWidget from '../../widgets/general/logo-grid'
import upcomingEventsCarousel from '@/query/widgets/general/upcoming-events-carousel'
import formCraftBuilder from '@/query/widgets/general/form-craft-builder'
import fullWidthMagPromoCarousel from '@/query/widgets/general/full-width-mag-promo-carousel'
import faqs from '@/query/widgets/general/faqs'

const createSectionLayoutWidgets = () => [
  widget(),
  htmlWidget(),
  imageGridWidget(),
  inlineImageWidget(),
  textWidget(),
  blockquoteWidget(),
  editorialIntroWidget(),
  editorialContactFormWidget(),
  advertWidget(),
  videoGridWidget(),
  articleStackWidget(),
  carouselWidget(),
  aboutBrandWidget(),
  advertiseInfoWidget(),
  advertiseTestimonialWidget(),
  newsletterSignupWidget(),
  mostPopularOrViewedWidget(),
  advertiseHeroWidget(),
  articleGrid(),
  simpleArticleGrid(),
  gridWithAdvertWidget(),
  headerWidget(),
  magazineCtaWidget(),
  latestMagazineIssueWidget(),
  featuredMagazine(),
  magazineGrid(),
  pastMagazineIssuesWidget(),
  brandStatsWidget(),
  aboutLiveEventsWidget(),
  aboutBizClikWidget(),
  aboutCtaWidget(),
  advertiseFeaturesWidget(),
  brandLocationWidget(),
  prnewswireWidget(),
  advertiseSalesforceFormWidget(),
  eventFormWidget(),
  magazineLatestCtaWidget(),
  eventLatestCTA(),
  logoGridWidget(),
  upcomingEventsCarousel(),
  formCraftBuilder(),
  fullWidthMagPromoCarousel(),
  faqs()
]

export default createSectionLayoutWidgets
