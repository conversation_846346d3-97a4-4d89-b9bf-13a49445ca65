import imageProperties from '../lib/image-properties'

const createQuery = () => `
  ... on SimpleArticleGridWidget {
    dedupe
    limit
    lists
    loadMoreEnabled
    numPerPage
    numPerRow
    articles {
      total
      results {
        _id
        headline   
        eventId
        eventBaseSlug
        eventArticleCategoryKey   
        fullUrlPath
        siblingInstance {
          logoUrl
          subdomain
        }
        featured
        quote
        category
        contentType
        tags {
          tag
        }
        attribution
        subAttribution
        sell
        images {
          thumbnail_landscape_322 {
            ${imageProperties}
          }
          thumbnail_portrait_286 {
            ${imageProperties}
          }
          thumbnail_widescreen_553 {
            ${imageProperties}
          }
        }
        address
        startDate
        endDate
        onDemandLink
        eventRegistrationLink
        timezone
        startDateISOWithoutTZ
        endDateISOWithoutTZ
        subAttribution
      }
    }
  }
`
export default createQuery
