const imageProperties = `src: url
caption
link
alt
width
height
ratio`

const createQuery = () => `
  ... on FullWidthMagPromoCarouselWidget {
    slides {
      title
      content
      linkText
      destination
      opensInNewTab
      images {
        logo_640 {
          ${imageProperties}
        }
        main_640 {
          ${imageProperties}
        }
        main_768 {
          ${imageProperties}
        }
        background_640 {
          ${imageProperties}
        }
        background_768 {
          ${imageProperties}
        }
        background_992 {
          ${imageProperties}
        }
        background_1200 {
          ${imageProperties}
        }
        background_1600 {
          ${imageProperties}
        }
        background_2000 {
          ${imageProperties}
        }
      }
      hasBorder
      primaryColor
      contrastColor
    }
  }
`
export default createQuery
