const createQuery = () => `
  ... on AuthorArticlesWidget {
    id
    authorArticles {
      type
      articles {
        _id
        headline
        fullUrlPath
        featured
        quote
        category
        contentType
        eventId
        eventBaseSlug
        eventArticleCategoryKey
        tags {
          tag
        }
        attribution
        subAttribution
        sell
        images {
          thumbnail_landscape_322 {
            url
          }
          thumbnail_portrait_286 {
            url
          }
          thumbnail_widescreen_553 {
            url
          }
        }
        address
        startDate
        onDemandLink
        subAttribution
      }
    }
  }
`

export default createQuery
