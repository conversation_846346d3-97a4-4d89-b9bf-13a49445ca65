import imageProperties from '../lib/image-properties'

const createQuery = () => `
  ... on ArticleGridWidget {
    displayCategory
    dedupe
    limit
    lists
    displayType
    articles {
      results {
        _id
        headline
        eventId
        eventBaseSlug
        eventArticleCategoryKey
        fullUrlPath
        siblingInstance {
          logoUrl
          subdomain
        }
        featured
        quote
        category
        contentType
        startDate
        endDate
        onDemandLink
        eventRegistrationLink
        timezone
        startDateISOWithoutTZ
        endDateISOWithoutTZ
        localeSafeStartTime
        localeSafeEndTime
        address
        tags {
          tag
        }
        attribution
        subAttribution
        sell
        images {
          thumbnail_landscape_138 {
            ${imageProperties}
          }
          thumbnail_landscape_206 {
            ${imageProperties}
          }
          thumbnail_landscape_290 {
            ${imageProperties}
          }
          thumbnail_landscape_330 {
            ${imageProperties}
          }
          thumbnail_landscape_412 {
            ${imageProperties}
          }
          thumbnail_landscape_580 {
            ${imageProperties}
          }
          thumbnail_landscape_900 {
            ${imageProperties}
          }
          thumbnail_portrait_104 {
            ${imageProperties}
          }
          thumbnail_portrait_208 {
            ${imageProperties}
          }
          thumbnail_portrait_290 {
            ${imageProperties}
          }
          thumbnail_portrait_580 {
            ${imageProperties}
          }
          thumbnail_portrait_720 {
            ${imageProperties}
          }
          thumbnail_portrait_900 {
            ${imageProperties}
          }
          thumbnail_widescreen_322 {
            ${imageProperties}
          }
          thumbnail_widescreen_553 {
            ${imageProperties}
          }
          thumbnail_widescreen_644 {
            ${imageProperties}
          }
          thumbnail_widescreen_1106 {
            ${imageProperties}
          }
          thumbnail_widescreen_290 {
            ${imageProperties}
          }
        }
      }
    }
  }
`
export default createQuery
