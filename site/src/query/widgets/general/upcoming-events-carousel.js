import eventButtonGroupProperties from '@/query/widgets/lib/event-button-group-properties'

const createQuery = () => `
  ... on UpcomingEventsCarouselWidget {
    events {
      name
      startDate
      endDate
      _fullUrl
      backgroundVideoId
      darkLogoUrl
      lightLogoUrl
      portfolioLogoOverrideUrl
      shortDescription
      buildingName
      brandColor
      eventButtonForegroundColor
      buttonGroup {
        ${eventButtonGroupProperties}
      }
      sponsors {
        id
        displayName
        orderIndex
        logoSponsorUrl
        logoSponsorSize
        tier {
          rank
        }
      }
      specialActions {
        register {
            key
            type
            buttonId
            buttonClassName
            eventLayoutKey
            eventLayoutSlug
            baseUrl
            link
            openInNewTab
            labelOverride
        }
        live {
            key
            type
            buttonId
            buttonClassName
            eventLayoutKey
            eventLayoutSlug
            baseUrl
            link
            openInNewTab
            labelOverride
        }
        navigation_cta {
            key
            type
            buttonId
            buttonClassName
            eventLayoutKey
            eventLayoutSlug
            baseUrl
            link
            openInNewTab
            labelOverride
        }
        navigation_awards_cta {
            key
            type
            buttonId
            buttonClassName
            eventLayoutKey
            eventLayoutSlug
            baseUrl
            link
            openInNewTab
            labelOverride
        }
        navigation_logo {
            key
            type
            buttonId
            buttonClassName
            eventLayoutKey
            eventLayoutSlug
            baseUrl
            link
            openInNewTab
            labelOverride
        }
      }
    }
    overArchingEventLightLogoUrl
    overArchingEventDarkLogoUrl
  }
`

export default createQuery
