import imageProperties from '../lib/image-properties'

const createQuery = () => `
  ... on CarouselWidget {
    dedupe
    limit
    itemsPerRow
    articles {
      results {
        _id
        headline
        eventId
        eventBaseSlug
        eventArticleCategoryKey
        sell
        fullUrlPath
        siblingInstance {
          logoUrl
          subdomain
        }
        contentType
        startDate
        endDate
        address
        timezone
        localeSafeStartTime
        localeSafeEndTime
        onDemandLink
        eventRegistrationLink
        timezone
        startDateISOWithoutTZ
        endDateISOWithoutTZ
        images {
          thumbnail_landscape_290 {
            ${imageProperties}
          }
          thumbnail_landscape_412 {
            ${imageProperties}
          }
          thumbnail_landscape_580 {
            ${imageProperties}
          }
          thumbnail_landscape_900 {
            ${imageProperties}
          }
          thumbnail_widescreen_322 {
            ${imageProperties}
          }
          thumbnail_widescreen_553 {
            ${imageProperties}
          }
          thumbnail_widescreen_644 {
            ${imageProperties}
          }
          thumbnail_widescreen_1106 {
            ${imageProperties}
          }
          thumbnail_landscape_138 {
            ${imageProperties}
          }
          thumbnail_landscape_206 {
            ${imageProperties}
          }
        }
      }
    }
  }
`

export default createQuery
