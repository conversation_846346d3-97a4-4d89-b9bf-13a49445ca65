import eventWidgetExtraProperties from '../lib/event-widget-extra-properties'
import eventButtonGroupProperties from '../lib/event-button-group-properties'
import imageProperties from '../lib/image-properties'

const speakerProperties = `
  name
  jobTitle
  testimonial
  roles
  eventName
  companyName
  images {
    speaker_1x1_320 {
      ${imageProperties}
    }
  }
`

const createQuery = () => `
  ... on EventFeaturedTestimonialWidget {
    ${eventWidgetExtraProperties}
    speaker {
      ${speakerProperties}
    }
    headline
    quote
    quoteeOverride
    buttonGroup {
      ${eventButtonGroupProperties}
    }
    theme
    images {
      desktop_background_72x17_720 {
        ${imageProperties}
      }
      desktop_background_72x17_1440 {
        ${imageProperties}
      }
      mobile_background_72x17_320 {
        ${imageProperties}
      }
      mobile_background_72x17_640 {
        ${imageProperties}
      }
      headshot_1x1_320 {
        ${imageProperties}
      }
    }
  }
`

export default createQuery
