import renderResource from '../../resource/render-resource'
import createWidgetArea from '../../widget/widget-area'
import creatLayoutWidgets from './layout-widgets'
import creatSectionLayoutWidgets from '../../section/lib/layout-widgets'
import widgetDefaultParams from '../../section/lib/widget-default-params'
import buildDefaultParameters from '../../section/lib/build-query'
import { createCompanyQuery } from './company-query-fragments'

/**
 * Shared utility for building company queries with consistent structure
 * Eliminates repetition across all company render query files
 */

// Standard widget area setup used across all company queries
const createStandardWidgetArea = () => {
  return createWidgetArea([
    ...creatSectionLayoutWidgets(),
    ...creatLayoutWidgets()
  ])
}

/**
 * Creates a complete company query with renderResource wrapper
 * @param {Object} options - Configuration options
 * @param {string} options.layoutKey - Layout key for entitySection (e.g., 'company', 'partnerships', 'executives')
 * @param {boolean} options.includePortalFields - Whether to include company portal fields
 * @param {boolean} options.includeExecutives - Whether to include executive data
 * @param {boolean} options.includeExecutiveSortOrder - Whether to include executive sort order
 * @param {string} options.additionalFields - Any additional GraphQL fields to include
 * @returns {string} Complete GraphQL query wrapped with renderResource
 */
export const createCompanyRenderQuery = (options = {}) => {
  const {
    layoutKey = 'company',
    includePortalFields = true,
    includeExecutives = false,
    includeExecutiveSortOrder = false,
    additionalFields = ''
  } = options

  const widgetArea = createStandardWidgetArea()

  const query = createCompanyQuery({
    includePortalFields,
    includeExecutives,
    includeExecutiveSortOrder,
    layoutKey,
    widgetArea,
    additionalFields
  })

  return renderResource(
    query,
    buildDefaultParameters(query, widgetDefaultParams)
  )
}

/**
 * Creates the legacy companyPortalQueries structure for backward compatibility
 * @param {Object} options - Configuration options
 * @returns {Object} companyPortalQueries object
 */
export const createCompanyPortalQueries = (options = {}) => {
  const {
    includeExecutives = true,
    includeExecutiveSortOrder = false,
    layoutKey = 'company',
    additionalFields = ''
  } = options

  const widgetArea = createStandardWidgetArea()

  let companyFields = ''

  if (includeExecutives) {
    companyFields += `
      executives {
        _id
        name
        slug
        isVerified
        jobTitle
        images {
          headshot_220x347_220 {
            url
            caption
            link
          }
          headshot_220x347_576 {
            url
            caption
            link
          }
        }
      }
    `
  }

  if (includeExecutiveSortOrder) {
    companyFields += `executiveSortOrder`
  }

  companyFields += `
    portalPermissions {
      isVerified
      enableContentHubNavigation
      enableExecutivesNavigation
      enablePartnershipsNavigation
      enableGetInTouchNavigation
      enableContentHubArticle
      enableContentHubEvent
      enableContentHubInterview
      enableContentHubPodcast
      enableContentHubCompanyReport
      enableContentHubVideo
      enableContentHubWebinar
      enableContentHubWhitepaper
    }
    heroVideoId
    platforms {
      _id
      name
      subdomain
    }
    clients {
      logo_free_480 {
        url
        width
        height
      }
    }
    stats {
      stat
      title
      description
      images {
        background_640 {
          url
          width
          height
        }
        logo_640 {
          url
          width
          height
          caption
        }
      }
    }
    companyArticles {
      type
      articles {
        _id
        headline
        fullUrlPath
        eventId
        eventBaseSlug
        eventArticleCategoryKey
        featured
        quote
        category
        contentType
        tags {
          tag
        }
        attribution
        subAttribution
        sell
        images {
          thumbnail_landscape_322 {
            url
          }
          thumbnail_portrait_286 {
            url
          }
          thumbnail_widescreen_553 {
            url
          }
        }
        address
        startDate
        subAttribution
      }
    }
  `

  if (layoutKey) {
    companyFields += `
      entitySection {
        _id
        name
        slug
        fullUrlPath
        metaTitle
        metaDescription
        shareTitle
        shareDescription
        advertSectionId
        keyValueTargeting {
          key
          value
        }
        layouts {
          ${layoutKey} {
            layout {
              background
              attributes
              cols {
                width
                attributes
                widgetArea {
                  ${widgetArea}
                }
              }
            }
          }
        }
      }
    `
  }

  if (additionalFields) {
    companyFields += additionalFields
  }

  return {
    Company: companyFields
  }
}
