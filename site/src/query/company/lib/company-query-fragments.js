import imageProperties from '@/query/widgets/lib/image-properties'

/**
 * Shared GraphQL fragments for company queries to eliminate repetition
 */

// Basic company information fields used across all company queries
export const companyBasicFields = `
  _id
  name
  slug
  shortDescription
  description
  linkedinId
  website
  emailAddress
  oneLiner
  city
  state
  country
  portalPermissions {
    isVerified
    enableContentHubNavigation
    enableExecutivesNavigation
    enablePartnershipsNavigation
    enableGetInTouchNavigation
    enableContentHubArticle
    enableContentHubEvent
    enableContentHubInterview
    enableContentHubPodcast
    enableContentHubCompanyReport
    enableContentHubVideo
    enableContentHubWebinar
    enableContentHubWhitepaper
  }
  aiMeta {
    label
    value
  }
  latestContent {
    _id
    headline
    fullUrlPath
    category
  }
`

// Standard company image variations used in most queries
export const companyImageFields = `
  images {
    logo_free_127 {
      url
    }
    thumbnail_1x1_220 {
      url
    }
    thumbnail_1x1_576 {
      url
    }
    thumbnail_1x1_720 {
      url
    }
    thumbnail_1x1_900 {
      url
    }
  }
  heroImages {
    hero_72x17_720 {
      ${imageProperties}
    }
    hero_72x17_1440 {
      ${imageProperties}
    }
    hero_72x17_320 {
      ${imageProperties}
    }
    hero_72x17_640 {
      ${imageProperties}
    }
  }
`

// Company portal specific fields
export const companyPortalFields = `
  hasArticles
  heroVideoId
  platforms {
    _id
    name
    subdomain
  }
  clients {
    logo_free_480 {
      url
      width
      height
    }
  }
  stats {
    stat
    title
    description
    images {
      background_640 {
        url
        width
        height
      }
      logo_640 {
        url
        width
        height
        caption
      }
    }
  }
  companyArticles {
    type
    articles {
      _id
      headline
      fullUrlPath
      eventId
      eventBaseSlug
      eventArticleCategoryKey
      featured
      quote
      category
      contentType
      tags {
        tag
      }
      attribution
      subAttribution
      sell
      images {
        thumbnail_landscape_322 {
          url
        }
        thumbnail_portrait_286 {
          url
        }
        thumbnail_widescreen_553 {
          url
        }
      }
      address
      startDate
      subAttribution
    }
  }
`

// Executive fields for company portal
export const companyExecutiveFields = `
  executives {
    _id
    name
    slug
    jobTitle
    isVerified
    images {
      headshot_220x347_220 {
        url
        caption
        link
      }
      headshot_220x347_576 {
        url
        caption
        link
      }
      headshot_220x347_720 {
        url
      }

      headshotv2_1x1_720 {
        url
      }
    }
  }
`

// Entity section layout structure - parameterized by layout key
export const createEntitySectionLayout = (layoutKey) => `
  entitySection {
    _id
    name
    slug
    fullUrlPath
    metaTitle
    metaDescription
    shareTitle
    shareDescription
    advertSectionId
    keyValueTargeting {
      key
      value
    }
    layouts {
      ${layoutKey} {
        layout {
          attributes
          cols {
            width
            attributes
            widgetArea {
              WIDGET_AREA_PLACEHOLDER
            }
          }
        }
      }
    }
  }
`

// Complete company query builder with optional fields
export const createCompanyQuery = (options = {}) => {
  const {
    includePortalFields = true,
    includeExecutives = false,
    includeExecutiveSortOrder = true,
    layoutKey = 'company',
    widgetArea = '',
    additionalFields = ''
  } = options

  let query = `
    ${companyBasicFields}
    ${companyImageFields}
  `

  if (includePortalFields) {
    query += companyPortalFields
  }

  if (includeExecutives) {
    query += companyExecutiveFields
  }

  if (includeExecutiveSortOrder) {
    query += `executiveSortOrder`
  }

  if (layoutKey && widgetArea) {
    const entitySection = createEntitySectionLayout(layoutKey)
    query += entitySection.replace('WIDGET_AREA_PLACEHOLDER', widgetArea)
  }

  if (additionalFields) {
    query += additionalFields
  }

  return `...on Company {
    ${query}
  }`
}

// Simplified image-only company query for article pages
export const companyImageOnlyFields = `
  images {
    thumbnail_1x1_220 {
      url
    }
    thumbnail_1x1_576 {
      url
    }
    thumbnail_1x1_720 {
      url
    }
    thumbnail_1x1_900 {
      url
    }
  }
`
