const createQuery = () => `
  query PaginatedQuery($url:String!, $page: Int = 1, $widgetType: String!, $currentArticleIds: JSON) {
    paginatedWidget(url: $url, widgetType: $widgetType, currentArticleIds: $currentArticleIds) {
      ... on SimpleArticleGridWidget {
        articles(page: $page) {
          total
          results {
            _id
            headline
            eventId
            eventBaseSlug
            eventArticleCategoryKey
            fullUrlPath
            featured
            quote
            category
            contentType
            tags {
              tag
            }
            attribution
            subAttribution
            sell
            images {
              thumbnail_landscape_322 {
                url
              }
              thumbnail_portrait_286 {
                url
              }
              thumbnail_widescreen_553 {
                url
              }
            }
            address
            startDate
            endDate
          }
        }
      }
    }
  }
`
export default createQuery
