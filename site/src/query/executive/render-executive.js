import renderResource from '../resource/render-resource'
import imageProperties from '@/query/widgets/lib/image-properties'
import createWidgetArea from '../widget/widget-area'
import creatLayoutWidgets from './lib/layout-widgets'
import creatSectionLayoutWidgets from '../section/lib/layout-widgets'
import widgetDefaultParams from '../section/lib/widget-default-params'
import buildDefaultParameters from '../section/lib/build-query'

const widgetArea = `
${createWidgetArea([...creatSectionLayoutWidgets(), ...creatLayoutWidgets()])}
`

const createQuery = () => {
  const query = `...on Executive {
    _id
    name
    slug
    bio
    jobTitle
    linkedinProfileUrlV2
    isVerified
    city
    state
    country

    entitySection {
      layouts {
        executive {
          layout {
            attributes
            background
          cols {
              width
              attributes
              widgetArea {
                ${widgetArea}
              }
            }
          }
        }
      }
    }
    images {
      headshot_220x347_220 {
        url
        caption
        link
      }
      headshot_220x347_576 {
        url
        caption
        link
      }
      headshot_220x347_720 {
        url
        caption
        link
      }
      headshot_220x347_900 {
        url
        caption
        link
      }
      headshotv2_1x1_220 {
        url
        caption
        link
      }
      headshotv2_1x1_576 {
        url
        caption
        link
      }
      headshotv2_1x1_720 {
        url
        caption
        link
      }
      headshotv2_1x1_900 {
        url
        caption
        link
      }
    }
    milestones {
      year
      event
    }
    history {
      startYear
      startMonth
      endYear
      endMonth
      jobTitle
      fallbackCompanyName
      company {
        isVerified
        name
        images {
          logo_free_127 {
            url
          }
        }
      }
    }
    company {
      name
      slug
      shortDescription
      description
      portalPermissions {
        isVerified
        enableContentHubNavigation
        enableExecutivesNavigation
        enablePartnershipsNavigation
        enableGetInTouchNavigation
        enableContentHubArticle
        enableContentHubEvent
        enableContentHubInterview
        enableContentHubPodcast
        enableContentHubCompanyReport
        enableContentHubVideo
        enableContentHubWebinar
        enableContentHubWhitepaper
      }
      city
      state
      country
      images {
        logo_free_127 {
          url
        }
        thumbnail_1x1_220 {
          url
        }
        thumbnail_1x1_576 {
          url
        }
        thumbnail_1x1_720 {
          url
        }
        thumbnail_1x1_900 {
          url
        }
      }
      heroImages {
        hero_72x17_720 {
          ${imageProperties}
        }
        hero_72x17_1440 {
          ${imageProperties}
        }
        hero_72x17_320 {
          ${imageProperties}
        }
        hero_72x17_640 {
          ${imageProperties}
        }
      }
      heroVideoId
      platforms {
        _id
        name
        subdomain
      }
    }
  }`

  return renderResource(
    query,
    buildDefaultParameters(query, widgetDefaultParams)
  )
}

export default createQuery
