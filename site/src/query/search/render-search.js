import genericFields from '../resource/generic-fields'

const createQuery = () => `
query Search($url: String!, $searchTerm: String, $chosenFacets: [ChosenFacets], $sort: String, $page: Int, $type: String) {
  ${genericFields()}
  search(url: $url, searchTerm: $searchTerm, chosenFacets: $chosenFacets, sort: $sort, page: $page, type: $type) {
    facets {
      name
      values {
        value
        count
      }
    }
    results {
      _id
      slug
      headline
      eventId
      eventBaseSlug
      eventArticleCategoryKey
      fullUrlPath
      displayDate
      images {
        thumbnail_portrait_147 {
          url
        }
        thumbnail_landscape_307 {
          url
        }
        thumbnail_widescreen_307 {
          url
        }
      }
      sell
      category
      contentType
      sections
    }
    companiesResults {
      _id
      name
      slug
      images {
        logo_free_127 {
          url
        }
        thumbnail_1x1_220 {
          url
        }
      }
    }
    executivesResults {
      _id
      name
      slug
      jobTitle
      images {
        headshot_220x347_220 {
          url
        }
      }
    }
    total
  }
}
`

export default createQuery
