const createQuery = () => `
  articlesRelatedToEntity(url: $url, type: $type, contentType: $contentType, page: $page) {
    total
    results {
      _id
      headline
      eventId
      eventBaseSlug
      eventArticleCategoryKey
      fullUrlPath
      featured
      quote
      category
      contentType
      tags {
        tag
      }
      attribution
      subAttribution
      sell
      images {
        thumbnail_widescreen_553 {
          url
        }
        thumbnail_landscape_322 {
          url
        }
        thumbnail_landscape_138 {
          url
        }
        thumbnail_landscape_206 {
          url
        }
        thumbnail_landscape_290 {
          url
        }
        thumbnail_landscape_330 {
          url
        }
        thumbnail_landscape_412 {
          url
        }
        thumbnail_landscape_580 {
          url
        }
        thumbnail_landscape_900 {
          url
        }
        thumbnail_portrait_144 {
          url
        }
        thumbnail_portrait_286 {
          url
        }
        thumbnail_portrait_576 {
          url
        }
      }
      address
      startDate
      onDemandLink
      subAttribution
    }
  }`

export default createQuery
