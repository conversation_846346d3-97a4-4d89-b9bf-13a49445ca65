import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import getUserAgent from '../../pages/lib/get-user-agent'

/**
 * Shared utility for company page getInitialProps
 * Handles common data fetching and processing logic
 */
export const getCompanyPageProps = async (
  context,
  createRenderQuery,
  additionalVars = {}
) => {
  const params = context?.query
  const query = createRenderQuery()
  const data = await retrieveAndProcessDataForPage(
    context,
    query,
    additionalVars
  )

  if (data.error) return data

  const company = { ...data?.response?.resource }

  const section = company?.entitySection
  const { isDesktop } = getUserAgent(context)
  const ctx = { isDesktop }
  const url = data?.vars?.url

  // Clean up layouts from company data
  delete data?.response?.resource?.layouts

  const pageData = { url, params, company, section }

  if (company?.portalPermissions) {
    company.isVerified =
      company.portalPermissions?.isVerified || company.isVerified
  }

  return {
    ...data.response,
    company,
    section,
    pageData,
    context: ctx
  }
}
