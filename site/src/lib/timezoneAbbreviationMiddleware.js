import { DateTime } from 'luxon'

/**
 * Timezone abbreviation middleware that handles consistent timezone abbreviation
 * generation and transformations across the application.
 *
 * This middleware addresses the need to convert certain timezone abbreviations
 * to more user-friendly formats (e.g., GMT+1 → BST).
 *
 * @param {string} dateString - ISO date string
 * @param {string} timezone - Timezone identifier (e.g., 'Europe/London')
 * @returns {string} - Processed timezone abbreviation or 'unset' if invalid
 */
export const getTimezoneAbbreviation = (dateString, timezone) => {
  // Return early if required parameters are missing
  if (!dateString || !timezone) {
    return 'unset'
  }

  try {
    // Generate the timezone abbreviation using Luxon
    const timezoneAbbreviation = DateTime.fromISO(dateString, {
      zone: timezone
    }).toFormat('ZZZZ')

    // Apply transformations for better user experience
    return transformTimezoneAbbreviation(timezoneAbbreviation)
  } catch (error) {
    // Handle any parsing errors gracefully
    console.warn('Error generating timezone abbreviation:', error)
    return 'unset'
  }
}

/**
 * Transforms timezone abbreviations to more user-friendly formats.
 *
 * Current transformations:
 * - GMT+1 → BST (British Summer Time)
 *
 * @param {string} abbreviation - Raw timezone abbreviation
 * @returns {string} - Transformed timezone abbreviation
 */
const transformTimezoneAbbreviation = (abbreviation) => {
  // Handle the GMT+1 to BST conversion
  if (abbreviation === 'GMT+1') {
    return 'BST'
  }

  // Add more transformations here as needed
  // Examples:
  // if (abbreviation === 'GMT+2') return 'CEST'
  // if (abbreviation === 'GMT-5') return 'EST'
  // if (abbreviation === 'GMT-4') return 'EDT'

  // Return the original abbreviation if no transformation is needed
  return abbreviation
}

/**
 * Formats a timezone abbreviation for display purposes.
 *
 * @param {string} timezoneAbbreviation - The timezone abbreviation
 * @param {boolean} includeParentheses - Whether to wrap in parentheses (default: true)
 * @returns {string} - Formatted timezone string or empty string if 'unset'
 */
export const formatTimezoneAbbreviation = (
  timezoneAbbreviation,
  includeParentheses = true
) => {
  if (!timezoneAbbreviation || timezoneAbbreviation === 'unset') {
    return ''
  }

  if (includeParentheses) {
    return ` (${timezoneAbbreviation})`
  }

  return timezoneAbbreviation
}

/**
 * Convenience function that combines timezone abbreviation generation and formatting.
 *
 * @param {string} dateString - ISO date string
 * @param {string} timezone - Timezone identifier
 * @param {boolean} includeParentheses - Whether to wrap in parentheses (default: true)
 * @returns {string} - Formatted timezone string ready for display
 */
export const getFormattedTimezoneAbbreviation = (
  dateString,
  timezone,
  includeParentheses = true
) => {
  const abbreviation = getTimezoneAbbreviation(dateString, timezone)
  return formatTimezoneAbbreviation(abbreviation, includeParentheses)
}

/**
 * Legacy compatibility function for components that expect the old pattern.
 * This maintains backward compatibility while using the new middleware.
 *
 * @param {string} dateString - ISO date string
 * @param {string} timezone - Timezone identifier
 * @returns {string} - Timezone abbreviation (same as getTimezoneAbbreviation)
 */
export const timezoneAbbreviationMiddleware = (dateString, timezone) => {
  return getTimezoneAbbreviation(dateString, timezone)
}

// Default export for convenience
export default {
  getTimezoneAbbreviation,
  formatTimezoneAbbreviation,
  getFormattedTimezoneAbbreviation,
  timezoneAbbreviationMiddleware
}
