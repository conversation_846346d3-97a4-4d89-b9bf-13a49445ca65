# Timezone Abbreviation Usage Analysis

This document lists all the places where `timezoneAbbreviation` is used across the codebase and the associated widgets/components.

## Current GMT+1 to BST Conversion

Currently, there's a hardcoded conversion in `EventContentGrid` widget:
```javascript
if (timezoneAbbreviation === 'GMT+1') timezoneAbbreviation = 'BST'
```

## Files Using timezoneAbbreviation

### 1. **EventContentGrid Widget**
- **File**: `site/src/component/Widgets/EventContentGrid/index.jsx`
- **Usage**: Converts GMT+1 to BST for agenda items
- **Pattern**: `DateTime.fromISO(content.agendaItem.endDate, { zone: eventUmbrella?.event?.timezone }).toFormat('ZZZZ')`

### 2. **ArticleHeader3 Component**
- **File**: `site/src/component/Primitive/ArticleHeader/component/ArticleHeader3.jsx`
- **Usage**: Displays timezone abbreviation in article headers for events
- **Pattern**: `DateTime.fromISO(endDate, { zone: timezone }).toFormat('ZZZZ')`

### 3. **ArticleEventInfo Component**
- **File**: `site/src/component/Primitive/ArticleEventInfo/index.jsx`
- **Usage**: Shows timezone in event information sections
- **Pattern**: `DateTime.fromISO(endDate, { zone: timezone }).toFormat('ZZZZ')`

### 4. **ArticleGrid Subtitle Determiner**
- **File**: `site/src/component/Widgets/ArticleGrid/lib/subtitle-determiner.js`
- **Usage**: Determines subtitle for articles with timezone info
- **Pattern**: `DateTime.fromISO(article.startDate, { zone: article.timezone }).toFormat('ZZZZ')`

### 5. **EventAgendaNavigator Components**
- **File**: `site/src/component/Primitive/Events/components/EventAgendaNavigator/index.jsx`
- **Usage**: Navigation component for event agendas
- **Pattern**: `DateTime.fromISO(firstEventEndDate, { zone }).toFormat('ZZZZ')`

### 6. **EventAgendaSplitHero Components**
- **File**: `site/src/component/Primitive/Events/components/EventAgendaSplitHero/index.jsx`
- **File**: `site/src/component/Widgets/EventAgendaSplitHero/index.jsx`
- **Usage**: Split hero layout with agenda and timezone display
- **Pattern**: `DateTime.fromISO(firstEventEndDate, { zone }).toFormat('ZZZZ')`

### 7. **EventContentBlock Components**
- **File**: `site/src/component/Primitive/Events/components/EventContentBlock/index.jsx`
- **File**: `site/src/component/Widgets/EventContentBlock/index.jsx`
- **Usage**: Content blocks showing event information with timezone
- **Pattern**: Used in timeRange display for agenda items

### 8. **Event Agenda List/Table Views**
- **File**: `site/src/component/Primitive/Events/components/EventAgendaNavigator/components/List/ListView.jsx`
- **File**: `site/src/component/Primitive/Events/components/EventAgendaNavigator/components/List/ListViewTable.jsx`
- **Usage**: List and table views of event agendas
- **Pattern**: Passed as prop and used in time display

### 9. **Event Agenda Timeline Views**
- **File**: `site/src/component/Primitive/Events/components/EventAgendaNavigator/components/Timeline/TimelineView.jsx`
- **File**: `site/src/component/Primitive/Events/components/Timeline/Timeline.jsx`
- **Usage**: Timeline visualization of events
- **Pattern**: Used for time display in timeline format

### 10. **EventContentGrid SpeakerCard**
- **File**: `site/src/component/Primitive/Events/components/EventContentGrid/components/SpeakerCard.jsx`
- **Usage**: Speaker cards within event content grids
- **Pattern**: Timezone display for speaker sessions

### 11. **EventPanels Widget**
- **File**: `site/src/component/Widgets/EventPanels/index.js`
- **Usage**: Panel-style event display widget
- **Pattern**: Timezone abbreviation for event timing

### 12. **Carousel Widget**
- **File**: `site/src/component/Widgets/Carousel/index.jsx`
- **Usage**: Carousel component that may display event content
- **Pattern**: Timezone handling for carousel items

## Common Patterns

All components follow similar patterns:

1. **Generation**: `DateTime.fromISO(dateField, { zone: timezone }).toFormat('ZZZZ')`
2. **Validation**: Check if `timezoneAbbreviation !== 'unset'`
3. **Display**: Format as ` (${timezoneAbbreviation})` or similar
4. **Current Issue**: Only EventContentGrid has GMT+1 → BST conversion

## Implemented Solution

✅ **COMPLETED**: Created a centralized `timezoneAbbreviationMiddleware` function in `site/lib/timezoneAbbreviationMiddleware.js` that:
1. Handles the timezone abbreviation generation using `getTimezoneAbbreviation()`
2. Applies consistent transformations (GMT+1 → BST, etc.)
3. Can be imported and used across all components
4. Maintains consistent formatting and validation logic
5. Provides multiple utility functions for different use cases

## Implementation Status

### ✅ Updated Components (Applied Middleware)

1. **EventContentGrid Widget** - `site/src/component/Widgets/EventContentGrid/index.jsx`
2. **ArticleHeader3 Component** - `site/src/component/Primitive/ArticleHeader/component/ArticleHeader3.jsx`
3. **ArticleEventInfo Component** - `site/src/component/Primitive/ArticleEventInfo/index.jsx`
4. **ArticleGrid Subtitle Determiner** - `site/src/component/Widgets/ArticleGrid/lib/subtitle-determiner.js`
5. **EventAgendaNavigator Component** - `site/src/component/Primitive/Events/components/EventAgendaNavigator/index.jsx`
6. **EventAgendaSplitHero Widget** - `site/src/component/Widgets/EventAgendaSplitHero/index.jsx`
7. **EventPanels Widget** - `site/src/component/Widgets/EventPanels/index.js`
8. **Carousel Widget** - `site/src/component/Widgets/Carousel/index.jsx`
9. **EventContentBlock Widget** - `site/src/component/Widgets/EventContentBlock/index.jsx`

### ✅ Components Using Props (No Changes Needed)

These components receive `timezoneAbbreviation` as props from parent components that have been updated:

1. **EventContentBlock Components** (Primitive) - Uses `content.timezoneAbbreviation` prop
2. **EventContentGrid SpeakerCard** - Uses `timezoneAbbreviation` prop
3. **Event Agenda List/Table Views** - Uses `timezoneAbbreviation` prop
4. **Event Agenda Timeline Views** - Uses `timezoneAbbreviation` prop

## Middleware Functions Available

- `getTimezoneAbbreviation(dateString, timezone)` - Core function for generating timezone abbreviations
- `formatTimezoneAbbreviation(abbreviation, includeParentheses)` - Formats abbreviations for display
- `getFormattedTimezoneAbbreviation(dateString, timezone, includeParentheses)` - Combined generation and formatting
- `timezoneAbbreviationMiddleware(dateString, timezone)` - Legacy compatibility function
