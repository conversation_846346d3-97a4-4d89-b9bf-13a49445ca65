const parseQsOptions = (qsOptions) => {
  const {
    q,
    'contentType[]': chosenContentTypes = [],
    'category[]': chosenCategories = [],
    sort,
    page,
    searchType
  } = qsOptions

  const parsedChosenContentTypes = !Array.isArray(chosenContentTypes)
    ? [chosenContentTypes]
    : chosenContentTypes
  const parsedChosenCategories = !Array.isArray(chosenCategories)
    ? [chosenCategories]
    : chosenCategories

  return {
    q,
    sort: sort || 'Most Relevant',
    page: page ? parseInt(page) : 1,
    chosenContentTypes: parsedChosenContentTypes,
    chosenCategories: parsedChosenCategories,
    searchType: searchType || 'simple'
  }
}

export default parseQsOptions
