import React from 'react'
import { func, object } from 'prop-types'

import createRenderQuery from '@/query/company/render-company'
import CompanyPageLayout from '@/component/Structure/CompanyPageLayout'
import { getCompanyPageProps } from '@/lib/company-page-props'

const CompanyPage = ({
  company,
  section,
  instance,
  pageData,
  __url,
  latestMagazineIssue
}) => {
  return (
    <CompanyPageLayout
      company={company}
      section={section}
      instance={instance}
      pageData={pageData}
      __url={__url}
      latestMagazineIssue={latestMagazineIssue}
    />
  )
}

CompanyPage.propTypes = {
  company: object,
  section: object,
  instance: object,
  pageData: object,
  __url: func.isRequired,
  latestMagazineIssue: object
}

CompanyPage.getInitialProps = async (context) => {
  // const { company } = props
  // if (!company?.hasArticles) {
  //   return generatePageError(404, { ...props })
  // }

  return await getCompanyPageProps(context, createRenderQuery)
}

export default CompanyPage
