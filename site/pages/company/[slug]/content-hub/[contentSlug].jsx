import React, { useState } from 'react'
import { string, object, func } from 'prop-types'

import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import createRenderQuery from '@/query/company/render-company-content-hub-all-articles'

import StandardMeta from '@/component/Meta/Standard'
import CompanyHero from '@/component/Primitive/CompanyPortal/CompanyHero'
import CompanyNavigation from '@/component/Primitive/CompanyPortal/CompanyNavigation'
import CompanyTitle from '@/component/Primitive/CompanyPortal/CompanyTitle'
import { generatePageError } from 'next-with-error'
import createQuery from '@/query/author/all-articles-load-more'
import InfiniteScroll from 'react-infinite-scroll-component'
import TextAlign from '@/component/Primitive/TextAlign'
import Spinner from '@/component/Primitive/Spinner'
import CompanyButton from '@/component/Primitive/CompanyPortal/CompanyButton'
import CompanyArticleSection from '@/component/Primitive/CompanyPortal/CompanyArticleSection'
import Stack from '@/component/Primitive/Stack'
import Row from '@/component/Structure/Row'

import articleContentTypes from '../../../../../components/service/article/content-types.json'

const CompanyHubArticleTypePage = ({
  company,
  instance,
  pageData,
  __url,
  latestMagazineIssue,
  contentType,
  articlesRelatedToEntity
}) => {
  const [results, setResults] = useState(articlesRelatedToEntity.results)
  const [pageNum, setPageNum] = useState(2)
  const [infiniteScrollEnabled, setInfiniteScrollEnabled] = useState(false)
  const [canLoadMore, setCanLoadMore] = useState(
    articlesRelatedToEntity.total > 9
  )

  const onLoadMore = async () => {
    const url = window.location.origin + '/graphql'
    const query = createQuery()
    const vars = {
      contentType,
      type: 'company',
      page: pageNum,
      url: window.location.href
    }
    const response = await fetch(url, {
      headers: { 'content-type': 'application/json' },
      method: 'POST',
      body: JSON.stringify({ query, variables: vars })
    })
    const newResults = await response.json()
    const updatedResults = [
      ...results,
      ...newResults.data.articlesRelatedToEntity.results
    ]
    setResults(updatedResults)
    setPageNum(pageNum + 1)
    setCanLoadMore(updatedResults.length < articlesRelatedToEntity.total)
    setInfiniteScrollEnabled(true)
  }

  const platformsWithUrls = []
  const currentURL = new URL(pageData.url)

  pageData.company = company

  for (const platform of company.platforms) {
    const fullUrl = new URL(
      `https://${platform.subdomain}/company/${company.slug}`
    )
    platform.fullUrl = fullUrl.toString()
    platform.isCurrent = fullUrl.hostname === currentURL.hostname
    platformsWithUrls.push(platform)
  }

  company.platformsWithUrls = platformsWithUrls

  return (
    <>
      <StandardMeta
        title={`${company.name} Content Hub ${contentType}s`}
        seoTitle={`${company.name} Content Hub ${contentType}s`}
        canonicalUrl={pageData.url}
      />

      <div className="company-portal">
        <CompanyHero company={company} />

        <CompanyNavigation {...company} />

        <Stack gap="large">
          <Row
            row={{
              attributes: ['default'],
              cols: [{ widgetArea: { widgets: [{}, {}] } }]
            }}
          >
            <div className="WidgetWrapper">
              <div className="_Anchor_" />

              <CompanyTitle title={`Content Hub - ${contentType}s`} />

              <InfiniteScroll
                dataLength={results.length}
                next={onLoadMore}
                hasMore={canLoadMore && infiniteScrollEnabled}
                scrollThreshold={0.8}
                loader={
                  <TextAlign center>
                    <Spinner />
                  </TextAlign>
                }
              >
                <CompanyArticleSection
                  items={results}
                  articleType={contentType}
                  display={99999}
                  showPlaceholder={false}
                />
              </InfiniteScroll>

              {canLoadMore && !infiniteScrollEnabled && (
                <div style={{ marginTop: '32px' }}>
                  <TextAlign center>
                    <CompanyButton onClick={onLoadMore}>
                      Show More
                    </CompanyButton>
                  </TextAlign>
                </div>
              )}
            </div>
          </Row>
        </Stack>
      </div>
    </>
  )
}

CompanyHubArticleTypePage.propTypes = {
  contentType: string,
  company: object,
  pageData: object,
  articlesRelatedToEntity: object,
  instance: object,
  latestMagazineIssue: object,
  __url: func
}

CompanyHubArticleTypePage.getInitialProps = async (context) => {
  const contentSlug = context?.query?.contentSlug
  const contentType = articleContentTypes.find(
    (type) => type?.companyPortal?.slug === contentSlug
  )?.type
  if (!contentType) {
    return generatePageError(404, {})
  }

  const params = context?.query
  const query = createRenderQuery()
  const vars = { type: 'company', contentType }
  const data = await retrieveAndProcessDataForPage(context, query, vars)

  if (data.error) {
    // return data
    return generatePageError(404, { ...data })
  }

  function toTitleCase(str) {
    return str.replace(
      /\w\S*/g,
      (text) => text.charAt(0).toUpperCase() + text.substring(1).toLowerCase()
    )
  }

  const company = data?.response?.resource
  const url = data?.vars?.url
  const pageData = { url, params, company }
  company.isVerified = company?.portalPermissions?.isVerified

  if (
    !company.isVerified ||
    !data.response?.articlesRelatedToEntity?.results?.length
  ) {
    return generatePageError(404, { ...data })
  }

  return {
    ...data.response,
    company,
    pageData,
    contentType: toTitleCase(context?.query?.contentSlug)?.replace(/-/g, ' ')
  }
}

export default CompanyHubArticleTypePage
