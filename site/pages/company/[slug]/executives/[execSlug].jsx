import React from 'react'
import { object, func } from 'prop-types'

import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import createRenderQuery from '@/query/executive/render-executive'
import StandardMeta from '@/component/Meta/Standard'

import Layout from '@/component/Structure/Layout'
import getUserAgent from '../../../lib/get-user-agent'
import CompanyHero from '@/component/Primitive/CompanyPortal/CompanyHero'
import CompanyNavigation from '@/component/Primitive/CompanyPortal/CompanyNavigation'
import { generatePageError } from 'next-with-error'

const ExecutivePage = ({ executive, section, instance, pageData, __url }) => {
  const { company } = executive
  const layout = executive?.entitySection?.layouts?.executive?.layout

  if (company) {
    pageData.company = company
    const platformsWithUrls = []
    const currentURL = new URL(pageData.url)

    for (const platform of company.platforms) {
      const fullUrl = new URL(
        `https://${platform.subdomain}/company/${company.slug}`
      )
      platform.fullUrl = fullUrl.toString()
      platform.isCurrent = fullUrl.hostname === currentURL.hostname
      platformsWithUrls.push(platform)
    }

    company.platformsWithUrls = platformsWithUrls
  }

  return (
    <>
      <StandardMeta
        title={executive.name}
        seoTitle={executive.name}
        canonicalUrl={pageData.url}
      />

      <div className="company-portal">
        {company && (
          <>
            <CompanyHero company={company} instance={instance} />

            <CompanyNavigation {...company} />
          </>
        )}

        <Layout
          layout={layout}
          __url={__url}
          section={section}
          instance={instance}
          pageData={pageData}
        />
      </div>
    </>
  )
}

ExecutivePage.propTypes = {
  executive: object,
  section: object,
  instance: object,
  pageData: object,
  __url: func.isRequired
}

ExecutivePage.getInitialProps = async (context) => {
  const params = context?.query
  const query = createRenderQuery()
  const facadeAs = `/executive/${params.execSlug}`
  const altContext = {
    ...context,
    url: facadeAs,
    asPath: facadeAs
  }

  const data = await retrieveAndProcessDataForPage(altContext, query)
  if (data.error) return data

  const executive = { ...data?.response?.resource }
  const section = executive?.entitySection
  const { isDesktop } = getUserAgent(context)
  const ctx = { isDesktop }
  const url = data?.vars?.url
  delete data?.response?.resource?.layouts

  const pageData = { url, params, executive, section }
  executive.company.isVerified =
    executive?.company?.portalPermissions?.isVerified

  const props = {
    data,
    ...data.response,
    executive,
    section,
    pageData,
    context: ctx
  }

  if (!executive || !executive?.company) {
    return generatePageError(404, { ...props })
  }

  return props
}

export default ExecutivePage
