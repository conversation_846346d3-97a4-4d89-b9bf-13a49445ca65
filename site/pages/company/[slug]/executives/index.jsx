import React from 'react'
import { func, object } from 'prop-types'

import StandardMeta from '@/component/Meta/Standard'
import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import createRenderExecutivesQuery from '@/query/company/render-company-executives'
import CompanyPageLayout from '@/component/Structure/CompanyPageLayout'

const CompanyExecutivesPage = ({
  company,
  pageData,
  latestMagazineIssue,
  instance,
  __url
}) => {
  const section = company?.entitySection

  const platformsWithUrls = []
  const currentURL = new URL(pageData.url)

  for (const platform of company.platforms) {
    const fullUrl = new URL(
      `https://${platform.subdomain}/company/${company.slug}`
    )
    platform.fullUrl = fullUrl.toString()
    platform.isCurrent = fullUrl.hostname === currentURL.hostname
    platformsWithUrls.push(platform)
  }

  company.platformsWithUrls = platformsWithUrls

  return (
    <>
      <StandardMeta
        title={company.name}
        seoTitle={company.name}
        canonicalUrl={pageData.url}
      />

      <CompanyPageLayout
        company={company}
        section={section}
        instance={instance}
        pageData={pageData}
        __url={__url}
        latestMagazineIssue={latestMagazineIssue}
        metaTitle={`${company.name} Executives`}
        metaSeoTitle={`${company.name} Executives`}
        layoutKey="executives"
      />
    </>
  )
}

CompanyExecutivesPage.propTypes = {
  company: object,
  pageData: object,
  instance: object,
  latestMagazineIssue: object,
  __url: func
}

CompanyExecutivesPage.getInitialProps = async (context) => {
  const params = context?.query
  const query = createRenderExecutivesQuery()
  const data = await retrieveAndProcessDataForPage(context, query)
  if (data.error) return data
  const company = data?.response?.resource
  company.isVerified = company?.portalPermissions?.isVerified
  const url = data?.vars?.url
  const pageData = { url, params, company }
  return { ...data.response, company, pageData, contentType: params.type }
}

export default CompanyExecutivesPage
