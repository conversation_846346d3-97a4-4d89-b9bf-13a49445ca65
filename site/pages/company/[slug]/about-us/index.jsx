// Handles large no. /about-us redirects, easier to do for now within codebase.

const CompanyAboutUsPage = () => null

CompanyAboutUsPage.propTypes = {}

CompanyAboutUsPage.getInitialProps = async (context) => {
  const homeUrl = context.asPath
  if (homeUrl) {
    context.res.writeHead(302, {
      Location: homeUrl.replace('/about-us', '')
    })
    context.res.end()
  }

  return {}
}

export default CompanyAboutUsPage
