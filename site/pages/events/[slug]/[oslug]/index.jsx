// Libraries
import React, { useEffect } from 'react'
import { object, func } from 'prop-types'
// Data
import createRenderQuery from '@/query/event/render-section'
import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import Layout from '@/component/Structure/Layout'
import EventGuard from '@/component/Helper/EventGuard'
import redirector from '../../lib/redirector'
import EventMeta from '@/component/Meta/Event'

const Section = ({
  __url,
  eventUmbrella,
  pageData,
  instance,
  latestMagazineIssue
}) => {
  const layoutProperties = eventUmbrella.event?.layoutProperties
  const layout = layoutProperties.layout
  const images =
    eventUmbrella.event.images.cover_16x9_668 &&
    eventUmbrella.event.images.cover_16x9_668?.length
      ? [
          {
            url: `${eventUmbrella.event.images.cover_16x9_668[0].url}.jpg`,
            width: 668,
            height: 375.75,
            alt: eventUmbrella.event.name
          }
        ]
      : [
          {
            url: `${eventUmbrella.event.lightLogoUrl}.jpg`,
            width: 668,
            height: 375.75,
            alt: eventUmbrella.event.name
          }
        ]

  useEffect(() => {
    // Vimeo Script Appending on Client
    const vimeoScript = document.createElement('script')
    vimeoScript.src = 'https://player.vimeo.com/api/player.js'
    document.body.appendChild(vimeoScript)

    const youtubeScript = document.createElement('script')
    youtubeScript.src = 'https://www.youtube.com/iframe_api'
    document.body.appendChild(youtubeScript)
  }, [])

  if (!eventUmbrella.event?.layoutProperties || !layout)
    return <div>No layout found</div>

  return (
    <EventGuard config={{ eventUmbrella }}>
      <EventMeta
        images={images}
        event={eventUmbrella.event}
        title={
          layoutProperties.metaTitle ||
          layoutProperties.name ||
          eventUmbrella.event.metaTitle ||
          eventUmbrella.metaTitle ||
          eventUmbrella.event.name
        }
        description={
          layoutProperties.metaDescription ||
          eventUmbrella.event.metaDescription ||
          eventUmbrella.metaDescription
        }
        seoTitle={
          layoutProperties.shareTitle ||
          layoutProperties.metaTitle ||
          eventUmbrella.event.shareTitle ||
          eventUmbrella.event.metaTitle ||
          eventUmbrella.shareTitle ||
          eventUmbrella.metaTitle ||
          eventUmbrella.event.name
        }
        seoDescription={
          layoutProperties.shareDescription ||
          layoutProperties.metaDescription ||
          eventUmbrella.event.shareDescription ||
          eventUmbrella.event.metaDescription ||
          eventUmbrella.shareDescription ||
          eventUmbrella.metaDescription ||
          eventUmbrella.event.description
        }
        canonicalUrl={pageData.canonicalUrl}
        instance={instance}
        url={pageData.url}
      />
      <Layout
        layout={layout}
        __url={__url}
        section={eventUmbrella.event}
        instance={instance}
        pageData={pageData}
        latestMagazineIssue={latestMagazineIssue}
        eventUmbrella={eventUmbrella}
        gap="large"
      />
    </EventGuard>
  )
}

Section.propTypes = {
  __url: func.isRequired,
  eventUmbrella: object,
  pageData: object,
  instance: object,
  latestMagazineIssue: object
}

Section.getInitialProps = async (context) => {
  const params = context?.query
  const layoutSlug = params.eventLayoutSlug
  const vars = { slug: layoutSlug || '' }
  const query = createRenderQuery()
  const data = await retrieveAndProcessDataForPage(context, query, vars)
  if (data.error) return data
  const instance = data?.response.instance
  const eventUmbrella = data?.response?.resource
  const latestMagazineIssue = data?.response?.latestMagazineIssue

  redirector(context, data)

  const url = data?.vars?.url
  let canonicalUrl = eventUmbrella.event._fullUrl
  if (layoutSlug) {
    canonicalUrl += `/${layoutSlug}`
  }

  const pageData = { url, params, canonicalUrl }

  return { eventUmbrella, pageData, instance, latestMagazineIssue }
}

export default Section
