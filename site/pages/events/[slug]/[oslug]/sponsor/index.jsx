// Libraries
import React from 'react'
import { object, func } from 'prop-types'
// Data
import createRenderQuery from '@/query/event/render-sponsor'
import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import Layout from '@/component/Structure/Layout'
import StandardMeta from '@/component/Meta/Standard'
import EventGuard from '@/component/Helper/EventGuard'
import Head from 'next/head'
import redirector from '../../../lib/redirector'

const Sponsor = ({
  __url,
  eventUmbrella,
  pageData,
  instance,
  latestMagazineIssue
}) => {
  if (!eventUmbrella) return null
  const layoutProperties = eventUmbrella.event?.layoutProperties
  const layout = layoutProperties?.layout
  const images = eventUmbrella?.event?.sponsor?.images.cover_16x9_668 &&
    eventUmbrella.event.sponsor.images.cover_16x9_668?.length && [
      {
        url: `${eventUmbrella.event.sponsor.images.cover_16x9_668[0].url}.jpg`,
        width: 668,
        height: 375.75,
        alt: eventUmbrella.event.name
      }
    ]
  if (!eventUmbrella.event?.layoutProperties || !layout)
    return <div>No layout found (on demand)</div>

  return (
    <EventGuard config={{ eventUmbrella }}>
      <Head>
        <script src="https://www.youtube.com/iframe_api" />
      </Head>
      <StandardMeta
        images={images}
        title={layoutProperties.metaTitle || layoutProperties.name}
        description={layoutProperties.metaDescription}
        seoTitle={
          eventUmbrella?.event?.sponsor?.displayName ||
          layoutProperties.shareTitle ||
          layoutProperties.metaTitle ||
          eventUmbrella.event.shareTitle ||
          eventUmbrella.event.metaTitle ||
          eventUmbrella.event.name
        }
        seoDescription={
          eventUmbrella?.event?.sponsor?.description ||
          layoutProperties.shareDescription ||
          layoutProperties.metaDescription ||
          eventUmbrella.event.shareDescription ||
          eventUmbrella.event.metaDescription ||
          eventUmbrella.event.description
        }
        canonicalUrl={pageData.canonicalUrl}
      />
      <Layout
        layout={layout}
        __url={__url}
        section={eventUmbrella.event}
        instance={instance}
        pageData={pageData}
        latestMagazineIssue={latestMagazineIssue}
        eventUmbrella={eventUmbrella}
        gap="large"
      />
    </EventGuard>
  )
}

Sponsor.propTypes = {
  __url: func.isRequired,
  eventUmbrella: object,
  pageData: object,
  instance: object,
  latestMagazineIssue: object
}

Sponsor.getInitialProps = async (context) => {
  const params = context?.query
  const sponsorSlug = params.entitySlug
  const vars = { slug: 'sponsor', sponsorSlug: sponsorSlug }
  const query = createRenderQuery()
  const data = await retrieveAndProcessDataForPage(context, query, vars)
  if (data.error) return data

  const instance = data?.response.instance
  const eventUmbrella = data?.response?.resource
  const latestMagazineIssue = data?.response?.latestMagazineIssue

  redirector(context, data)

  const url = data?.vars?.url
  let canonicalUrl = eventUmbrella.event._fullUrl
  if (sponsorSlug) {
    canonicalUrl += `/${sponsorSlug}`
  }
  const pageData = {
    url,
    params,
    eventUmbrella,
    canonicalUrl
  }

  return { eventUmbrella, pageData, instance, latestMagazineIssue }
}

export default Sponsor
