// Libraries
import React, { useEffect } from 'react'
import { object, func } from 'prop-types'
// Data
import createRenderQuery from '@/query/event/render-on-demand'
import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import Layout from '@/component/Structure/Layout'
import EventVideoMeta from '@/component/Meta/EventVideo'
import EventGuard from '@/component/Helper/EventGuard'
import redirector from '../../lib/redirector'
import { generatePageError } from 'next-with-error'

const OnDemand = ({
  __url,
  eventUmbrella,
  pageData,
  instance,
  latestMagazineIssue
}) => {
  const layoutProperties = eventUmbrella?.layoutProperties
  const layout = layoutProperties?.layout
  const images = eventUmbrella.video.images.cover_16x9_668 &&
    eventUmbrella.video.images.cover_16x9_668?.length && [
      {
        url: `${eventUmbrella.video.images.cover_16x9_668[0].url}.jpg`,
        width: 668,
        height: 375.75,
        alt: eventUmbrella.video.name
      }
    ]

  useEffect(() => {
    // Vimeo Script Appending on Client
    const vimeoScript = document.createElement('script')
    vimeoScript.src = 'https://player.vimeo.com/api/player.js'
    document.body.appendChild(vimeoScript)

    const youtubeScript = document.createElement('script')
    youtubeScript.src = 'https://www.youtube.com/iframe_api'
    document.body.appendChild(youtubeScript)
  }, [])
  if (!eventUmbrella?.layoutProperties || !layout)
    return <div>No layout found (on demand)</div>
  return (
    <EventGuard config={{ eventUmbrella }}>
      <EventVideoMeta
        instance={instance}
        images={images}
        title={
          layoutProperties.metaTitle ||
          layoutProperties.name ||
          eventUmbrella.metaTitle ||
          eventUmbrella.name
        }
        description={
          layoutProperties.metaDescription || eventUmbrella.metaDescription
        }
        seoTitle={
          eventUmbrella?.video?.name ||
          layoutProperties.shareTitle ||
          eventUmbrella.shareTitle ||
          layoutProperties.metaTitle ||
          eventUmbrella.metaTitle ||
          eventUmbrella?.event?.name
        }
        seoDescription={
          eventUmbrella?.video?.description ||
          layoutProperties.shareDescription ||
          eventUmbrella.shareDescription ||
          layoutProperties.metaDescription ||
          eventUmbrella.metaDescription
        }
        canonicalUrl={pageData.canonicalUrl}
        video={eventUmbrella?.video}
        eventName={eventUmbrella?.event?.name || eventUmbrella?.name}
        eventUrl={eventUmbrella?._fullUrl}
        publishedDate={
          eventUmbrella?.video?.uploadDate || eventUmbrella?.video?.createdDate
        }
        duration={eventUmbrella?.video?.duration}
        event={eventUmbrella?.video?.event}
      />
      <Layout
        layout={layout}
        __url={__url}
        section={eventUmbrella}
        instance={instance}
        pageData={pageData}
        latestMagazineIssue={latestMagazineIssue}
        eventUmbrella={eventUmbrella}
        gap="large"
      />
    </EventGuard>
  )
}

OnDemand.propTypes = {
  __url: func.isRequired,
  eventUmbrella: object,
  pageData: object,
  instance: object,
  latestMagazineIssue: object
}

OnDemand.getInitialProps = async (context) => {
  const params = context?.query
  const videoSlug = params.entitySlug
  const vars = { key: 'video', videoSlug: videoSlug }
  const query = createRenderQuery()
  const data = await retrieveAndProcessDataForPage(context, query, vars)
  if (data.error) return generatePageError(500, { ...data })
  const instance = data?.response.instance
  const eventUmbrella = data?.response?.resource
  if (!eventUmbrella) return generatePageError(404, { ...data, instance })
  if (!eventUmbrella?.video?.eventFullUrlPath)
    return generatePageError(404, { ...data, instance })
  const latestMagazineIssue = data?.response?.latestMagazineIssue

  // Refresh YouTube cache for the video when page is requested
  if (eventUmbrella?.video?._id && context.query.graphqlManager) {
    try {
      const refreshMutation = `
        mutation RefreshCache($eventVideoId: String!) {
          refreshEventVideoYouTubeCache(eventVideoId: $eventVideoId)
        }
      `
      await context.query.graphqlManager.query(refreshMutation, {
        eventVideoId: eventUmbrella.video._id
      })
      // eslint-disable-next-line no-console
      console.log(
        `Refreshed YouTube cache for video: ${eventUmbrella.video._id}`
      )
    } catch (error) {
      console.error('Failed to refresh YouTube cache:', error)
      // Don't fail page load if cache refresh fails
    }
  }

  const canonicalUrl = eventUmbrella._fullUrl + `/${videoSlug}`

  redirector(context, data)

  const url = data?.vars?.url
  const pageData = { canonicalUrl, url, params }

  return { eventUmbrella, pageData, instance, latestMagazineIssue }
}

export default OnDemand
