import React from 'react'
import { array, object, string } from 'prop-types'

import createQuery from '../../src/query/globenewswire/render-globenewswire'
import retrieveData from '../../src/lib/retrieve-data'
import { generatePageError } from 'next-with-error'

import Container from '@/component/Primitive/Container'
import Stack from '@/component/Primitive/Stack'
import Grid from '@/component/Primitive/GlobeNewswire/components/Grid'
import StandardMeta from '@/component/Meta/Standard'

import styles from './index.module.scss'

const GlobenewswireListPage = ({ pressReleases, pagination, canonicalUrl }) => {
  const title = 'Press Releases - GlobeNewswire'
  const description =
    'Latest press releases and news from GlobeNewswire. Stay updated with the latest corporate announcements and business news.'

  // Use the GlobeNewswire logo as the image
  const images = [
    {
      url: '/image/globenewswire/eq-notified-dark.svg',
      width: 300,
      height: 150,
      alt: 'GlobeNewswire Logo'
    }
  ]

  return (
    <>
      <StandardMeta
        title={title}
        description={description}
        canonicalUrl={canonicalUrl}
        images={images}
      />
      <Container gutter center size="wide">
        <Stack gap="medium">
          <img
            src="/image/globenewswire/eq-notified-dark.svg"
            alt="GlobeNewswire Logo"
            style={{ maxWidth: 300 + 'px', height: 'auto' }}
            width={300}
            height={150}
            className={styles.Logo}
          />
          <Grid pressReleases={pressReleases} pagination={pagination} />
        </Stack>
      </Container>
    </>
  )
}

GlobenewswireListPage.propTypes = {
  pressReleases: array,
  pagination: object,
  canonicalUrl: string
}

GlobenewswireListPage.getInitialProps = async (context) => {
  const { req, query: urlQuery } = context
  const query = createQuery()
  const { data } = await retrieveData(context, query)
  const instance =
    (data && data.instance) || (req && req.instance) || context.instance

  // Get page from URL query parameters
  const page = urlQuery.page ? parseInt(urlQuery.page) : 1

  // Use v2 API with pagination
  let endpoint
  if (typeof window !== 'undefined') {
    endpoint = new URL(
      '/api/globenewswire/v2/press-releases',
      window.location.origin
    )
  } else {
    // Use proper protocol and domain construction for production
    const protocol = instance.subdomain.includes('localhost') ? 'http' : 'https'
    const port = instance.subdomain.includes('localhost') ? ':8110' : ''
    const baseUrl = `${protocol}://${instance.subdomain}${port}`

    endpoint = new URL('/api/globenewswire/v2/press-releases', baseUrl)
  }

  // Add pagination parameters
  endpoint.searchParams.set('page', page.toString())
  endpoint.searchParams.set('limit', '20')
  const pressReleases = await fetch(endpoint, {
    headers: {
      'User-Agent': 'BizClik-Internal-Service/1.0 (compatible; Node.js)',
      Accept: 'application/json',
      // Include Host header so server knows which instance this is for
      Host: instance.subdomain
    }
  })

  if (!pressReleases.ok) {
    return generatePageError(500, { ...data })
  }
  const pressReleasesJson = await pressReleases.json()
  if (pressReleasesJson.error) {
    return generatePageError(500, { ...data })
  }
  // V2 API returns paginated structure: { results, total, page, limit, hasNextPage, etc. }
  data.pressReleases = pressReleasesJson.results || []
  data.pagination = {
    total: pressReleasesJson.total || 0,
    page: pressReleasesJson.page || 1,
    limit: pressReleasesJson.limit || 20,
    hasNextPage: pressReleasesJson.hasNextPage || false,
    hasPreviousPage: pressReleasesJson.hasPreviousPage || false,
    resultsLeft: pressReleasesJson.resultsLeft || 0
  }

  // Create canonical URL for the list page
  const protocol = instance.subdomain.includes('localhost') ? 'http' : 'https'
  const port = instance.subdomain.includes('localhost') ? ':8110' : ''
  const baseUrl = `${protocol}://${instance.subdomain}${port}`
  const canonicalUrl = `${baseUrl}/globenewswire`

  return { ...data, instance, canonicalUrl }
}

export default GlobenewswireListPage
