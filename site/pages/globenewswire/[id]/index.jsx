import React from 'react'
import { object, string } from 'prop-types'

import createQuery from '../../../src/query/globenewswire/render-globenewswire'
import retrieveData from '../../../src/lib/retrieve-data'
import { generatePageError } from 'next-with-error'
import Container from '@/component/Primitive/Container'
import Stack from '@/component/Primitive/Stack'
import Prose from '@/component/Primitive/Prose'
import Type from '@/component/Primitive/Type'
import GlobeNewswireMeta from '@/component/Meta/GlobeNewswire'

import styles from './index.module.scss'

const GlobenewswirePressReleasePage = ({
  pressRelease,
  instance,
  canonicalUrl
}) => {
  if (!pressRelease) return null

  // Format dates for meta tags
  const publishedTime = pressRelease.releaseDateTime
    ? new Date(pressRelease.releaseDateTime).toISOString()
    : null
  const modifiedTime = pressRelease['gn:ModifiedDate']
    ? new Date(pressRelease['gn:ModifiedDate']).toISOString()
    : null

  // Create a clean description for meta tags (strip HTML)
  const cleanDescription = pressRelease.description
    ? pressRelease.description.replace(/<[^>]*>/g, '').substring(0, 160)
    : `Press release from GlobeNewswire: ${pressRelease.title}`

  return (
    <>
      <GlobeNewswireMeta
        title={pressRelease.title}
        description={cleanDescription}
        canonicalUrl={canonicalUrl}
        publishedTime={publishedTime}
        modifiedTime={modifiedTime}
        identifier={pressRelease.identifier?.toString()}
        subdomain={instance?.subdomain}
      />
      <Container gutter center size="medium" className={styles.Container}>
        <Stack gap="medium">
          <Type themed as="h1" size={['heading3', 'display2']} weight="bold">
            {pressRelease.title}
          </Type>
          <img
            src="/image/globenewswire/eq-notified-dark.svg"
            alt="GlobeNewswire Logo"
            width="640"
            height="423"
            className={styles.Logo}
          />
          <Prose dangerousHtml={pressRelease.description} />
        </Stack>
      </Container>
    </>
  )
}

GlobenewswirePressReleasePage.propTypes = {
  pressRelease: object,
  instance: object,
  canonicalUrl: string
}

GlobenewswirePressReleasePage.getInitialProps = async (context) => {
  const { req } = context
  const query = createQuery()
  const { data } = await retrieveData(context, query)
  const instance =
    (data && data.instance) || (req && req.instance) || context.instance

  // Use proper protocol and domain construction for production
  const protocol = instance.subdomain.includes('localhost') ? 'http' : 'https'
  const port = instance.subdomain.includes('localhost') ? ':8110' : ''
  const baseUrl = `${protocol}://${instance.subdomain}${port}`

  // Extract ID from URL
  const splitUrl = req.url.split('/')
  const id = splitUrl[splitUrl.length - 1]

  // V2 API uses ID as URL parameter instead of query parameter
  const apiEndpoint = new URL(
    `/api/globenewswire/v2/press-releases/${id}`,
    baseUrl
  )

  const pressRelease = await fetch(apiEndpoint, {
    headers: {
      'User-Agent': 'BizClik-Internal-Service/1.0 (compatible; Node.js)',
      Accept: 'application/json',
      // Include Host header so server knows which instance this is for
      Host: instance.subdomain
    }
  })
  if (!pressRelease.ok) {
    return generatePageError(404, { ...data })
  }
  const pressReleaseJson = await pressRelease.json()
  if (pressReleaseJson.error) {
    return generatePageError(500, { ...data })
  }
  // V2 API returns the press release object directly
  data.pressRelease = {
    title: pressReleaseJson['gn:Title'],
    description: pressReleaseJson['gn:Content'],
    releaseDateTime: pressReleaseJson['gn:ReleaseDateTime'],
    url: pressReleaseJson['gn:Url'],
    identifier: pressReleaseJson['gn:Identifier'],
    crawlDate: pressReleaseJson.crawlDate,
    instanceId: pressReleaseJson.instanceId,
    // Keep the raw data for any additional fields
    ...pressReleaseJson
  }
  // Create canonical URL for the press release
  const canonicalUrl = `${baseUrl}/globenewswire/${id}`

  return { ...data, instance, canonicalUrl }
}

export default GlobenewswirePressReleasePage
