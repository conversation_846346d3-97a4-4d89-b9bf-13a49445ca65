import React from 'react'
import { object, func } from 'prop-types'
import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import createRenderQuery from '@/query/executive/render-executive'
import StandardMeta from '@/component/Meta/Standard'
import Layout from '@/component/Structure/Layout'
import getUserAgent from '../lib/get-user-agent'
import CompanyHero from '@/component/Primitive/CompanyPortal/CompanyHero'
import CompanyNavigation from '@/component/Primitive/CompanyPortal/CompanyNavigation'

const ExecutivePage = ({ executive, section, instance, pageData, __url }) => {
  const layout = section?.layouts?.executive?.layout
  const platformsWithUrls = []
  const currentURL = new URL(pageData.url)
  const { company } = executive

  if (company) {
    pageData.company = company
    for (const platform of company.platforms) {
      const fullUrl = new URL(
        `https://${platform.subdomain}/company/${company.slug}`
      )
      platform.fullUrl = fullUrl.toString()
      platform.isCurrent = fullUrl.hostname === currentURL.hostname
      platformsWithUrls.push(platform)
    }
    company.platformsWithUrls = platformsWithUrls
  }

  return (
    <>
      <StandardMeta
        title={executive.name}
        seoTitle={executive.name}
        canonicalUrl={pageData.url}
      />

      <CompanyHero company={company} />

      <CompanyNavigation {...company} />

      <Layout
        layout={layout}
        __url={__url}
        section={section}
        instance={instance}
        pageData={pageData}
      />
    </>
  )
}

ExecutivePage.propTypes = {
  executive: object,
  section: object,
  instance: object,
  pageData: object,
  __url: func.isRequired
}

ExecutivePage.getInitialProps = async (context) => {
  const params = context?.query
  const query = createRenderQuery()
  const data = await retrieveAndProcessDataForPage(context, query)
  if (data.error) return data
  const executive = { ...data?.response?.resource }
  const section = executive?.entitySection
  const { isDesktop } = getUserAgent(context)
  const ctx = { isDesktop }
  const url = data?.vars?.url
  delete data?.response?.resource?.layouts
  const pageData = { url, params, executive, section }
  executive.company.isVerified =
    executive?.company?.portalPermissions?.isVerified
  return { ...data.response, executive, section, pageData, context: ctx }
}

export default ExecutivePage
