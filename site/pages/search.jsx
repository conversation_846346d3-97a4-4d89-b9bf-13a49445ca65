import React, { useReducer, useEffect, useState } from 'react'
import Head from 'next/head'
import { object } from 'prop-types'
import qs from 'qs'

import { useRouter } from 'next/router'
import retrieveData from '@/lib/retrieve-data'

import SearchResults from '@/component/Composition/SearchLayout'
import SearchResultsV2 from '@/component/Composition/SearchLayoutV2'

import parseQsOptions from '@/lib/search-query-string-options-parser'

import createRenderSearchQuery from '@/query/search/render-search'

import useFeatureFlagHelper from '@/lib/feature-flag-helper'

const formatFacet = (facetName, facets) => {
  const data = facets.find((f) => f.name === facetName)
  if (!data) return []
  const formattedFacets = data.values
    .filter((v) => v.count)
    .map((v) => v.value)
    .sort((a, b) => {
      if (a < b) return -1
      if (a > b) return 1
      return 0
    })
  return formattedFacets
}

const formatToQs = (state) => {
  const data = { ...state }
  delete data.initial
  return qs.stringify(data, { arrayFormat: 'brackets' })
}

const toggleArray = (value, currentValues) => {
  if (value === 'All') return []
  if (currentValues.includes(value))
    return currentValues.filter((v) => v !== value)
  return [...currentValues, value]
}

const setInitialState = (qsOptions) => {
  return {
    initial: true,
    q: qsOptions.q,
    category: qsOptions.chosenCategories,
    contentType: qsOptions.chosenContentTypes,
    sort: qsOptions.sort,
    page: qsOptions.page,
    searchType: qsOptions.searchType
  }
}

const reducer = (state, action) => {
  let newState = null
  switch (action.type) {
    case 'q':
      newState = {
        ...state,
        q: action.value,
        category: [],
        contentType: []
      }
      break
    case 'page':
      return {
        ...state,
        [action.type]: action.value,
        initial: false
      }
    case 'category':
    case 'contentType':
      newState = {
        ...state,
        [action.type]: toggleArray(action.value, state[action.type])
      }
      break
    default:
      newState = {
        ...state,
        [action.type]: action.value
      }
      break
  }
  newState.page = 1
  newState.initial = false
  return newState
}

const SearchPage = ({ searchResults, qsOptions, instance }) => {
  const { isFeatureEnabled } = useFeatureFlagHelper(instance)
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [shouldReset, setShouldReset] = useState(false)
  const [infiniteScrollEnabled, setInfiniteScrollEnabled] = useState(false)
  const [state, dispatch] = useReducer(reducer, setInitialState(qsOptions))
  const resultsReducer = (currentResults, newResults) => {
    const currentAndNewAreTheSame =
      currentResults &&
      newResults &&
      currentResults[0] &&
      newResults[0] &&
      currentResults[0]._id === newResults[0]._id
    return shouldReset || currentAndNewAreTheSame
      ? newResults
      : [...currentResults, ...newResults]
  }
  const [results, setResults] = useReducer(
    resultsReducer,
    searchResults.results
  )

  const handleSearch = (searchTerm) => {
    dispatch({ type: 'q', value: searchTerm })
    setShouldReset(true)
  }
  const handleContentTypeChange = (contentType) => {
    dispatch({ type: 'contentType', value: contentType })
    setShouldReset(true)
  }
  const handleCategoryChange = (category) => {
    dispatch({ type: 'category', value: category })
    setShouldReset(true)
  }
  const handleSortChange = (sort) => {
    dispatch({ type: 'sort', value: sort })
    setShouldReset(true)
  }
  const handleSearchTypeChange = (searchType) => {
    setResults([])
    setIsLoading(true)
    dispatch({ type: 'searchType', value: searchType })
    setShouldReset(true)
  }
  const handleLoadMore = () => {
    setShouldReset(false)
    setInfiniteScrollEnabled(true)
    dispatch({ type: 'page', value: state.page + 1 })
  }

  useEffect(() => {
    setResults(searchResults.results)
    setIsLoading(false)
  }, [searchResults.results])

  useEffect(() => {
    if (!state.initial) {
      const query = formatToQs(state)
      setIsLoading(true)
      router.push(`/search?${query}`)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state])

  const filters = {
    contentTypeOptions: formatFacet('contentType', searchResults.facets),
    categoryOptions: formatFacet('category', searchResults.facets),
    sortOptions: [
      'Most Relevant',
      'Most Recent',
      'Ascending (A-Z)',
      'Descending (Z-A)'
    ]
  }

  const selectedFilters = {
    categories: state.category,
    contentTypes: state.contentType,
    sortBy: state.sort,
    searchType: state.searchType
  }

  let Component
  if (isFeatureEnabled('advancedSearch')) {
    Component = SearchResultsV2
    // eslint-disable-next-line
    console.log('search results: ', searchResults)
  } else {
    Component = SearchResults
  }

  return (
    <>
      <Head>
        <meta name="googlebot" content="noindex" />
        <meta name="robots" content="noindex" />
      </Head>

      <Component
        onSearch={handleSearch}
        searchTerm={qsOptions.q}
        onLoadMore={handleLoadMore}
        sortOption={qsOptions.sort}
        results={results}
        total={searchResults.total}
        onCategoryChange={handleCategoryChange}
        onContentTypeChange={handleContentTypeChange}
        onSortChange={handleSortChange}
        onSearchTypeChange={handleSearchTypeChange}
        loading={isLoading}
        infiniteScrollEnabled={infiniteScrollEnabled}
        selectedFilters={selectedFilters}
        filters={filters}
        companiesResults={searchResults.companiesResults}
        executivesResults={searchResults.executivesResults}
        instance={instance}
      />
    </>
  )
}

SearchPage.propTypes = {
  searchResults: object,
  qsOptions: object,
  instance: object
}

SearchPage.getInitialProps = async (context) => {
  const parsedQsOptions = parseQsOptions(context.query)
  const query = createRenderSearchQuery()
  const chosenFacets = [
    { name: 'category', values: parsedQsOptions.chosenCategories },
    { name: 'contentType', values: parsedQsOptions.chosenContentTypes }
  ]
  const { data } = await retrieveData(context, query, {
    searchTerm: parsedQsOptions.q,
    sort: parsedQsOptions.sort,
    chosenFacets,
    page: parsedQsOptions.page,
    type: parsedQsOptions.searchType === 'advanced' ? 'Advanced' : 'Simple'
  })

  return {
    ...data,
    searchResults: data.search,
    searchTerm: parsedQsOptions.q,
    qsOptions: parsedQsOptions
  }
}

export default SearchPage
