import React from 'react'
import { NextScript } from 'next/document'

const nextZeroConfigBrowsersRegex = /Edge?\/((79|8[01])\.0|(8[3-9]|9\d|1(0\d|1\d|2[0-5]))\.0)(\.\d+|)|Firefox\/(6[7-9]|[78]\d|9\d|1(0\d|1\d|2[0-7]))\.0(\.\d+|)|Chrom(ium|e)\/((6[4-9]|7\d|8[01])\.0|(8[3-9]|9\d|1(0\d|1\d|2[0-6]))\.0)(\.\d+|)([\d.]+$|.*<PERSON><PERSON>\/(?![\d.]+ Edge\/[\d.]+$))|(Maci|X1{2}).+ Version\/(12\.[01]|13\.[01]|14\.[01]|15\.[0-6]|16\.[0-6]|17\.[0-5])([,.]\d+|)( \(\w+\)|)( Mobile\/\w+|) Safari\/|Chrome.+OPR\/(5[1-8]\.0|60\.0|(6[2-9]|[78]\d|9\d|1(0\d|10))\.0)\.\d+/

class DeferredNextScript extends NextScript {
  constructor(props) {
    super(props)
    this.props = props
  }

  getScripts() {
    return super.getScripts().map((script) => {
      return React.cloneElement(script, {
        ...script.props,
        key: script.props.src,
        async: true
      })
    })
  }

  getDynamicChunks() {
    return super.getDynamicChunks().map((script) => {
      return React.cloneElement(script, {
        ...script.props,
        key: script.props.src,
        async: true
      })
    })
  }

  render() {
    const userAgent = this.props.userAgent || ''
    const polyfillFiles = !userAgent.match(nextZeroConfigBrowsersRegex)
      ? this.context.buildManifest.polyfillFiles
      : []
    const fileData = [
      ...polyfillFiles,
      ...this.context.files,
      ...this.context.buildManifest.lowPriorityFiles
    ]

    const __NEXT_FILES__DATA = fileData
      .filter((file) => file.endsWith('.js'))
      .map((file) => `/_next/${file}`)
      .join(',')

    return (
      <>
        <script
          id="__NEXT_DATA__"
          type="application/json"
          dangerouslySetInnerHTML={{
            __html: NextScript.getInlineScriptSource(this.context)
          }}
        />
        <script id="__NEXT_FILES__DATA" type="application/json">
          {__NEXT_FILES__DATA}
        </script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
      document.addEventListener('DOMContentLoaded', function () {
        var scriptElement = document.querySelector('#__NEXT_FILES__DATA');
        var files = scriptElement.textContent.split(',');
        files.forEach(function(file) {
          var script = document.createElement('script');
          script.src = file;
          script.async = true;
          document.body.appendChild(script);
        });
      })
    `
          }}
        />
      </>
    )
  }
}

export default DeferredNextScript
