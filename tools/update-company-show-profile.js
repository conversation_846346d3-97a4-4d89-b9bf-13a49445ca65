#!/usr/bin/env node

/**
 * update-company-show-profile.js
 *
 * This script looks at all companies created between now and July 2025,
 * and sets showProfilePage to true if at least 1 published article has been written about them.
 */

const { MongoClient } = require('mongodb')
const path = require('path')
const createConfigury = require('@clocklimited/configury')
const { join } = require('path')
const mongodbUri = require('mongodb-uri')

// Get environment variables from .env file if available
try {
  require('dotenv').config({ path: path.resolve(__dirname, '../.env') })
} catch (e) {
  console.log('No .env file found, using environment variables')
}

const config = createConfigury(join(__dirname, '/../config.json'))(
  process.env.NODE_ENV
)

// Configure MongoDB connection
const connectionUri =
  process.env.MONGO_URL ||
  process.env.NF_DATABASE_MONGO_SRV ||
  config.databaseUrl

async function updateCompanyShowProfile() {
  let client

  try {
    // Connect to MongoDB
    console.log(`Connecting to MongoDB at ${connectionUri}...`)
    client = await MongoClient.connect(connectionUri, {
      useUnifiedTopology: true
    })
    const { database } = mongodbUri.parse(connectionUri)
    const db = client.db(database)

    // Get collections
    const companyCollection = db.collection('company')
    const articleCollection = db.collection('article')

    // Define date range: now to July 2025
    const now = new Date()
    const july2025 = new Date('2025-07-31T23:59:59.999Z')

    console.log(
      `Looking for companies created between ${now.toISOString()} and ${july2025.toISOString()}`
    )

    // Find companies created in the specified date range
    const companies = await companyCollection
      .find({
        createdDate: {
          $lte: now,
          $gte: july2025
        }
      })
      .toArray()

    console.log(`Found ${companies.length} companies in the date range`)

    if (companies.length === 0) {
      console.log('No companies found in the specified date range')
      return
    }

    // Stats for reporting
    let companiesWithArticles = 0
    let companiesWithoutArticles = 0
    let companiesUpdated = 0

    // Process each company
    for (const company of companies) {
      const companyId = company._id
      const companyName = company.name || companyId.toString()

      console.log(`\nProcessing company: ${companyName} (${companyId})`)

      // Find published articles linked to this company
      const articleCount = await articleCollection.countDocuments({
        'companies.company': companyId.toString(),
        state: 'Published'
      })

      console.log(`  Found ${articleCount} published articles for this company`)

      if (articleCount > 0) {
        companiesWithArticles++

        // Check if showProfilePage is already true
        if (company.showProfilePage === true) {
          console.log(`  showProfilePage is already true, skipping update`)
        } else {
          // Update showProfilePage to true
          await companyCollection.updateOne(
            { _id: companyId },
            {
              $set: {
                showProfilePage: true,
                modifiedDate: new Date()
              }
            }
          )
          companiesUpdated++
          console.log(`  ✓ Updated showProfilePage to true`)
        }
      } else {
        companiesWithoutArticles++
        console.log(`  No articles found, leaving showProfilePage unchanged`)
      }
    }

    // Print summary
    console.log(`\n======== UPDATE SUMMARY ========`)
    console.log(`Total companies processed: ${companies.length}`)
    console.log(`Companies with articles: ${companiesWithArticles}`)
    console.log(`Companies without articles: ${companiesWithoutArticles}`)
    console.log(`Companies updated: ${companiesUpdated}`)
    console.log(`================================`)
  } catch (error) {
    console.error('Error updating company showProfile:', error)
    process.exit(1)
  } finally {
    if (client) {
      await client.close()
      console.log('Database connection closed')
    }
  }
}

// Run the update function
updateCompanyShowProfile()
  .then(() => {
    console.log('Company showProfile update completed successfully')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Failed to update company showProfile:', error)
    process.exit(1)
  })
