const { MongoClient } = require('mongodb')
const { join } = require('path')
const createConfigury = require('@clocklimited/configury')
const config = createConfigury(join(__dirname, '/../config.json'))(
  process.env.NODE_ENV
)

const connectionUri =
  process.env.MONGO_URL ||
  process.env.NF_DATABASE_MONGO_SRV ||
  config.databaseUrl

let isDryRun = false
let articlesUpdated = 0

// Add --dry-run flag
if (process.argv.includes('--dry-run')) {
  console.log('*** DRY RUN MODE - No changes will be made ***')
  isDryRun = true
}

MongoClient.connect(
  connectionUri,
  { useNewUrlParser: true },
  async (err, client) => {
    if (err) return console.error(err)

    const db = client.db()
    const collection = db.collection('section')

    try {
      const query = {}
      const count = await collection.countDocuments(query)
      console.log(`Found ${count} sections with images to remove`)
      if (count === 0) {
        console.log('No sections with images found')
        client.close()
        return
      }

      if (!isDryRun) {
        const result = await collection.updateMany(query, {
          $set: { images: { widgets: [] } }
        })

        articlesUpdated = result.modifiedCount
      }

      console.log('Articles Updated: ', articlesUpdated)
      client.close()
    } catch (error) {
      console.error('Failed to update articles:', error)
      client.close()
      process.exit(1)
    }
  }
)
