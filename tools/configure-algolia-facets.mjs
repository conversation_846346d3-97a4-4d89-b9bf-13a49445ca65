#!/usr/bin/env node
import { algoliasearch } from 'algoliasearch'
import { join } from 'path'
import createConfigury from '@clocklimited/configury'

const config = createConfigury(join(process.cwd(), '/config.json'))(
  process.env.NODE_ENV
)
const secrets = createConfigury(join(process.cwd(), '/secrets.json'))(
  process.env.NODE_ENV
)

console.log(
  '🔑 Initializing Algolia client...',
  secrets.algolia.applicationId,
  secrets.algolia.importApiKey?.slice(0, 5) + '...'
)

const algoliaClient = algoliasearch(
  secrets.algolia.applicationId,
  secrets.algolia.importApiKey
)

async function configureArticleIndexFacets() {
  try {
    console.log('🔧 Configuring facets for article index...')

    // Configure index settings with facets
    const settings = {
      attributesForFaceting: [
        'searchable(contentType)',
        'searchable(category)',
        'filterOnly(instance)'
      ],
      searchableAttributes: [
        'headline',
        'sell',
        'body',
        'author',
        'legacyAuthorName'
      ],
      attributesToRetrieve: [
        '_id',
        'headline',
        'sell',
        'displayDate',
        '__fullUrlPath',
        'images',
        'author',
        'legacyAuthorName',
        'featured',
        'contentType',
        'category',
        'slug',
        'instance',
        'eventId',
        'eventBaseSlug',
        'eventArticleCategoryKey'
      ]
    }

    const result = await algoliaClient.setSettings({
      indexName: 'article',
      indexSettings: settings
    })

    console.log('✅ Article index facets configured successfully!')
    console.log('📋 Task ID:', result.taskID)

    // Wait for the task to complete
    console.log('⏳ Waiting for settings to be applied...')
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Verify the settings
    const currentSettings = await algoliaClient.getSettings({
      indexName: 'article'
    })
    console.log(
      '📋 Current facet settings:',
      currentSettings.attributesForFaceting
    )
  } catch (error) {
    console.error('❌ Error configuring facets:', error)
    throw error
  }
}

async function main() {
  try {
    await configureArticleIndexFacets()
    console.log('🎉 Algolia facet configuration completed successfully!')
  } catch (error) {
    console.error('💥 Fatal Error:', error)
    process.exit(1)
  }
}

// Allow direct execution from command line
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export default main
