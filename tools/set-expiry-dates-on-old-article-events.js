const { MongoClient, ObjectId } = require('mongodb')
const { join } = require('path')
const createConfigury = require('@clocklimited/configury')
const config = createConfigury(join(__dirname, '/../config.json'))(
  process.env.NODE_ENV
)

const connectionUri =
  process.env.MONGO_URL ||
  process.env.NF_DATABASE_MONGO_SRV ||
  config.databaseUrl

let isDryRun = false
let articlesUpdated = 0

// Add --dry-run flag
if (process.argv.includes('--dry-run')) {
  console.log('*** DRY RUN MODE - No changes will be made ***')
  isDryRun = true
}

MongoClient.connect(
  connectionUri,
  { useNewUrlParser: true },
  async (err, client) => {
    if (err) return console.error(err)

    const db = client.db()
    const collection = db.collection('article')

    try {
      const cursor = await collection.find({
        contentType: 'Event',
        state: 'Published',
        endDate: { $lt: new Date() },
        $or: [{ expiryDate: null }, { expiryDate: { $exists: false } }]
      })

      while (await cursor.hasNext()) {
        const article = await cursor.next()
        if (!article.endDate) continue

        console.log(
          `Updating ${article.headline} and setting expiryDate to ${article.endDate}`
        )
        if (!isDryRun) {
          await db
            .collection('article')
            .updateOne(
              { _id: ObjectId(article._id) },
              { $set: { expiryDate: article.endDate } }
            )
        }
        articlesUpdated++
      }

      console.log('Articles Updated: ', articlesUpdated)
      client.close()
    } catch (error) {
      console.error('Failed to update articles:', error)
      client.close()
      process.exit(1)
    }
  }
)
