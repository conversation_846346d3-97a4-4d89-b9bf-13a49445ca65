#!/usr/bin/env node

/**
 * fetch-linkedin-company-ids.js
 *
 * This script fetches all instances with linkedinId (LinkedIn company URL slug),
 * calls the Scrapin API to get the LinkedIn company ID for each,
 * and saves the results to linkedinIds.json in the format:
 * { "subdomain": "linkedInId", ... }
 */

const { MongoClient } = require('mongodb')
const path = require('path')
const fs = require('fs').promises
const createConfigury = require('@clocklimited/configury')
const { join } = require('path')
const mongodbUri = require('mongodb-uri')

// Get environment variables from .env file if available
try {
  require('dotenv').config({ path: path.resolve(__dirname, '../.env') })
} catch (e) {
  console.log('No .env file found, using environment variables')
}

const config = createConfigury(join(__dirname, '/../config.json'))(
  process.env.NODE_ENV
)
const secrets = createConfigury(join(__dirname, '/../secrets.json'))(
  process.env.NODE_ENV
)

// Configure MongoDB connection
const connectionUri =
  process.env.MONGO_URL ||
  process.env.NF_DATABASE_MONGO_SRV ||
  config.databaseUrl

const SCRAPIN_API_KEY = secrets.scrapin.apiKey
const RATE_LIMIT_DELAY = 150 // 150ms delay between requests (400 requests per minute = ~150ms)

// Fetch LinkedIn company data from Scrapin API
async function fetchLinkedInCompanyId(linkedinId) {
  const linkedInUrl = `https://www.linkedin.com/company/${linkedinId}/`
  const url = `https://api.scrapin.io/v1/enrichment/company?apikey=${SCRAPIN_API_KEY}&linkedInUrl=${encodeURIComponent(
    linkedInUrl
  )}`

  try {
    console.log(`  🔍 Fetching LinkedIn data for: ${linkedInUrl}`)
    const response = await fetch(url)
    const data = await response.json()

    if (!response.ok) {
      console.error(
        `  ❌ API error for ${linkedInUrl}: ${data.error || 'Unknown error'}`
      )
      return null
    }

    if (!data.success) {
      console.error(`  ❌ Request failed for ${linkedInUrl}`)
      return null
    }

    console.log(
      `  ✓ Success! LinkedIn ID: ${data.company?.linkedInId || 'N/A'}`
    )
    console.log(
      `  📊 Credits consumed: ${data.credits_consumed}, Credits left: ${data.credits_left}`
    )

    return data.company?.linkedInId || null
  } catch (error) {
    console.error(`  ❌ Fetch error for ${linkedInUrl}: ${error.message}`)
    return null
  }
}

// Add delay between requests
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

async function fetchAllLinkedInCompanyIds() {
  let client

  try {
    // Connect to MongoDB
    console.log(`\n🔌 Connecting to MongoDB...`)
    client = await MongoClient.connect(connectionUri, {
      useUnifiedTopology: true
    })
    const { database } = mongodbUri.parse(connectionUri)
    const db = client.db(database)

    // Get instance collection
    const instanceCollection = db.collection('instance')

    // Find all instances with linkedinId field populated
    console.log(`\n📋 Fetching instances with LinkedIn IDs...`)
    const instances = await instanceCollection
      .find({
        $or: [
          { linkedinId: { $exists: true, $ne: null } },
          { linkedinId: { $ne: '' } }
        ]
      })
      .toArray()

    console.log(`\n✓ Found ${instances.length} instances with LinkedIn IDs\n`)

    if (instances.length === 0) {
      console.log('No instances found with LinkedIn IDs')
      return
    }

    const results = {}
    let successCount = 0
    let failureCount = 0

    // Process each instance
    for (let i = 0; i < instances.length; i++) {
      const instance = instances[i]
      const subdomain = instance.subdomain
      const linkedinId = instance.linkedinId

      console.log(
        `\n[${i + 1}/${
          instances.length
        }] Processing: ${subdomain} (${linkedinId})`
      )

      const companyLinkedInId = await fetchLinkedInCompanyId(linkedinId)

      if (companyLinkedInId) {
        results[subdomain] = companyLinkedInId
        successCount++
      } else {
        failureCount++
      }

      // Add delay between requests to respect rate limits
      if (i < instances.length - 1) {
        await delay(RATE_LIMIT_DELAY)
      }
    }

    // Save results to JSON file
    const outputPath = join(__dirname, 'linkedinIds.json')
    await fs.writeFile(outputPath, JSON.stringify(results, null, 2), 'utf8')

    // Print summary
    console.log(`\n\n======== SUMMARY ========`)
    console.log(`Total instances processed: ${instances.length}`)
    console.log(`Successful fetches: ${successCount}`)
    console.log(`Failed fetches: ${failureCount}`)
    console.log(`Output file: ${outputPath}`)
    console.log(`=========================\n`)
  } catch (error) {
    console.error('Error fetching LinkedIn company IDs:', error)
    process.exit(1)
  } finally {
    if (client) {
      await client.close()
      console.log('Database connection closed')
    }
  }
}

// Run the script
fetchAllLinkedInCompanyIds()
  .then(() => {
    console.log('✓ Script completed successfully')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Failed to fetch LinkedIn company IDs:', error)
    process.exit(1)
  })
