const { MongoClient, ObjectId } = require('mongodb')
const { join } = require('path')
const createConfigury = require('@clocklimited/configury')
const config = createConfigury(join(__dirname, '/../config.json'))(
  process.env.NODE_ENV
)

// Force UK timezone for consistent behavior across environments
process.env.TZ = 'Europe/London'
console.log('🇬🇧 Timezone set to:', process.env.TZ)

// Verify timezone is set correctly
const testDate = new Date('2024-07-15T12:00:00Z') // Summer date (BST)
console.log(`🌍 Timezone verification: ${testDate.toString()}`)
console.log(`🕐 Timezone offset: ${testDate.getTimezoneOffset()} minutes`)

// Check for dry-run flag
const isDryRun = process.argv.includes('--dry-run')
const isOverwrite = process.argv.includes('--overwrite')

if (isDryRun) {
  console.log('🔍 DRY RUN MODE - No database changes will be made')
  console.log('='.repeat(50))
}

if (!isOverwrite) {
  console.log(
    '🔍 OVERWRITE MODE - Existing startDateISOWithoutTZ will be overwritten'
  )
  console.log('='.repeat(50))
}

const connectionUri =
  process.env.MONGO_URL ||
  process.env.NF_DATABASE_MONGO_SRV ||
  config.databaseUrl

MongoClient.connect(
  connectionUri,
  {
    useNewUrlParser: true
  },
  async (err, client) => {
    if (err) return console.error(err)

    const db = client.db()
    const collection = db.collection('event')

    try {
      // Update events
      console.log('Updating events...')
      const eventCursor = collection.find({})

      let eventCount = 0
      while (await eventCursor.hasNext()) {
        const event = await eventCursor.next()

        if (!event.timezone) {
          console.log(`Skipping event ${event._id} - no timezone found`)
          continue
        }

        if (!event.startDate || !event.endDate) {
          console.log(`Skipping event ${event._id} - no start or end date`)
          continue
        }

        if (event.startDateISOWithoutTZ && !isOverwrite) {
          console.log(
            `Skipping event ${event._id} - startDateISOWithoutTZ already exists`
          )
          continue
        }

        if (isOverwrite) {
          console.log(
            `Overwriting event ${event._id} - startDateISOWithoutTZ already exists`
          )
        }

        const startTimezoneOffset = event.startDate.getTimezoneOffset()
        const endTimezoneOffset = event.endDate.getTimezoneOffset()

        const startDateISOWithoutTZ = new Date(
          event.startDate.getTime() - startTimezoneOffset * (60 * 1000)
        ).toISOString()

        const endDateISOWithoutTZ = new Date(
          event.endDate.getTime() - endTimezoneOffset * (60 * 1000)
        ).toISOString()

        console.log(
          `${isDryRun ? '[DRY RUN] Would update' : 'Updating'} event ${
            event._id
          } - startDate from ${event.startDate.toISOString()} to ${startDateISOWithoutTZ}`
        )

        if (!isDryRun) {
          await collection.updateOne(
            { _id: event._id },
            {
              $set: {
                startDateISOWithoutTZ,
                endDateISOWithoutTZ
              },
              $unset: {
                timezoneCorrectEndDate: '',
                timezoneCorrectStartDate: ''
              }
            }
          )
        }

        eventCount++
      }
      console.log(`Completed events update. Total: ${eventCount}`)

      // Update articles
      console.log('\nUpdating articles...')
      const articleCollection = db.collection('article')
      const articleCursor = articleCollection.find({
        contentType: 'Event',
        startDate: { $exists: true },
        endDate: { $exists: true },
        timezone: { $exists: true }
      })

      let articleCount = 0
      while (await articleCursor.hasNext()) {
        const article = await articleCursor.next()

        if (!article.timezone) {
          console.log(`Skipping article ${article._id} - no timezone found`)
          continue
        }

        if (!article.startDate || !article.endDate) {
          console.log(`Skipping article ${article._id} - no start or end date`)
          continue
        }

        if (article.startDateISOWithoutTZ && !isOverwrite) {
          console.log(
            `Skipping article ${article._id} - startDateISOWithoutTZ already exists`
          )
          continue
        }

        if (isOverwrite) {
          console.log(
            `Overwriting article ${article._id} - startDateISOWithoutTZ already exists`
          )
        }

        const startTimezoneOffset = article.startDate.getTimezoneOffset()
        const endTimezoneOffset = article.endDate.getTimezoneOffset()

        const startDateISOWithoutTZ = new Date(
          article.startDate.getTime() - startTimezoneOffset * (60 * 1000)
        ).toISOString()

        const endDateISOWithoutTZ = new Date(
          article.endDate.getTime() - endTimezoneOffset * (60 * 1000)
        ).toISOString()

        console.log(
          `${isDryRun ? '[DRY RUN] Would update' : 'Updating'} article ${
            article._id
          } - startDate from ${article.startDate.toISOString()} to ${startDateISOWithoutTZ}`
        )

        if (!isDryRun) {
          await articleCollection.updateOne(
            { _id: article._id },
            {
              $set: {
                startDateISOWithoutTZ,
                endDateISOWithoutTZ
              },
              $unset: {
                timezoneCorrectEndDate: '',
                timezoneCorrectStartDate: ''
              }
            }
          )
        }

        articleCount++
      }
      console.log(`Completed articles update. Total: ${articleCount}`)

      // Update agenda items
      console.log('\nUpdating agenda items...')
      const agendaCollection = db.collection('eventAgendaItem')
      const agendaCursor = agendaCollection.find({})

      let agendaCount = 0
      while (await agendaCursor.hasNext()) {
        const agendaItem = await agendaCursor.next()
        const event = await collection.findOne({
          _id: ObjectId(agendaItem.eventId)
        })
        const timezone = event?.timezone

        if (!timezone) {
          console.log(
            `Skipping agenda item ${agendaItem._id} - no timezone found`
          )
          continue
        }

        if (agendaItem.startDateISOWithoutTZ && !isOverwrite) {
          console.log(
            `Skipping agenda item ${agendaItem._id} - startDateISOWithoutTZ already exists`
          )
          continue
        }

        if (isOverwrite) {
          console.log(
            `Overwriting agenda item ${agendaItem._id} - startDateISOWithoutTZ already exists`
          )
        }

        const startTimezoneOffset = agendaItem.startDate.getTimezoneOffset()
        const endTimezoneOffset = agendaItem.endDate.getTimezoneOffset()

        const startDateISOWithoutTZ = new Date(
          agendaItem.startDate.getTime() - startTimezoneOffset * (60 * 1000)
        ).toISOString()

        const endDateISOWithoutTZ = new Date(
          agendaItem.endDate.getTime() - endTimezoneOffset * (60 * 1000)
        ).toISOString()

        // log updating time from x to y
        console.log(
          `${isDryRun ? '[DRY RUN] Would update' : 'Updating'} agenda item ${
            agendaItem._id
          } - startDate from ${agendaItem.startDate.toISOString()} to ${startDateISOWithoutTZ}`
        )

        if (!isDryRun) {
          await agendaCollection.updateOne(
            { _id: agendaItem._id },
            {
              $set: {
                startDateISOWithoutTZ,
                endDateISOWithoutTZ
              },
              $unset: {
                timezoneCorrectEndDate: '',
                timezoneCorrectStartDate: ''
              }
            }
          )
        }

        agendaCount++
      }
      console.log(`Completed agenda items update. Total: ${agendaCount}`)

      console.log(`\n=== ${isDryRun ? 'DRY RUN ' : ''}SUMMARY ===`)
      console.log(
        `Events ${isDryRun ? 'that would be ' : ''}updated: ${eventCount}`
      )
      console.log(
        `Articles ${isDryRun ? 'that would be ' : ''}updated: ${articleCount}`
      )
      console.log(
        `Agenda items ${
          isDryRun ? 'that would be ' : ''
        }updated: ${agendaCount}`
      )
      console.log(
        `Total records ${isDryRun ? 'that would be ' : ''}updated: ${
          eventCount + articleCount + agendaCount
        }`
      )

      if (isDryRun) {
        console.log(
          '\n🔍 This was a dry run - no changes were made to the database'
        )
        console.log('Run without --dry-run to perform actual updates')
      }

      client.close()
      console.log('Done!')
    } catch (error) {
      console.error('Failed to update times:', error)
      client.close()
      process.exit(1)
    }
  }
)
