#!/usr/bin/env node

const { MongoClient, ObjectId } = require('mongodb')
const { join } = require('path')
const createConfigury = require('@clocklimited/configury')
const config = createConfigury(join(__dirname, '/../config.json'))(
  process.env.NODE_ENV
)

// Parse command line arguments
const args = process.argv.slice(2)
const isDryRun = args.includes('--dry-run')
const instanceId = args.find((arg, index) => args[index - 1] === '--instanceId')

// Show help if requested
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Usage: node tools/disable-event-carousels.js --instanceId <instanceId> [options]

Description:
  Disables upcoming events carousel widgets across all sections in a specific instance
  by setting their 'visible' field to false. This does not delete the widgets.

Options:
  --instanceId <id>  Required. The instance ID to target (e.g., FinTech or InsurTech)
  --dry-run          Show what would be changed without making actual updates
  --help, -h         Show this help message

Examples:
  node tools/disable-event-carousels.js --instanceId 507f1f77bcf86cd799439011
  node tools/disable-event-carousels.js --instanceId 507f1f77bcf86cd799439011 --dry-run
  `)
  process.exit(0)
}

// Validate required arguments
if (!instanceId) {
  console.error('Error: --instanceId argument is required')
  console.log('Run with --help for usage information')
  process.exit(1)
}

const connectionUri =
  process.env.MONGO_URL ||
  process.env.NF_DATABASE_MONGO_SRV ||
  config.databaseUrl

// Function to process widgets in a layout and disable upcomingEventsCarousel
function processLayoutForCarouselDisabling(
  layout,
  layoutName,
  disabledCount = { count: 0 }
) {
  if (!Array.isArray(layout)) {
    return layout
  }

  return layout.map((layoutRow) => {
    if (!layoutRow.cols || !Array.isArray(layoutRow.cols)) {
      return layoutRow
    }

    const updatedCols = layoutRow.cols.map((col) => {
      if (
        !col.widgetArea ||
        !col.widgetArea.widgets ||
        !Array.isArray(col.widgetArea.widgets)
      ) {
        return col
      }

      const updatedWidgets = col.widgetArea.widgets.map((widget) => {
        if (
          widget.type === 'upcomingEventsCarousel' &&
          widget.visible !== false
        ) {
          disabledCount.count++
          console.log(
            `    Disabling upcomingEventsCarousel widget (ID: ${widget.id}) in ${layoutName} layout, row "${layoutRow.title}"`
          )
          return {
            ...widget,
            visible: false
          }
        }
        return widget
      })

      return {
        ...col,
        widgetArea: {
          ...col.widgetArea,
          widgets: updatedWidgets
        }
      }
    })

    return {
      ...layoutRow,
      cols: updatedCols
    }
  })
}

function processSectionLayouts(section) {
  if (!section.layouts) {
    console.log(`Skipping section ${section.slug}: No layouts found`)
    return null
  }

  const updatedLayouts = {}
  let totalDisabled = 0
  let hasChanges = false

  // Process each layout type (section, article, home, category, etc.)
  for (const [layoutKey, layoutData] of Object.entries(section.layouts)) {
    if (!layoutData) continue

    console.log(`  Processing ${layoutKey} layout...`)

    const disabledCount = { count: 0 }

    // Check if this layout has a layout array (most layouts should)
    if (layoutData.layout && Array.isArray(layoutData.layout)) {
      const updatedLayout = processLayoutForCarouselDisabling(
        layoutData.layout,
        layoutKey,
        disabledCount
      )

      updatedLayouts[layoutKey] = {
        ...layoutData,
        layout: updatedLayout
      }
    } else {
      // Handle edge cases where layout might be structured differently
      updatedLayouts[layoutKey] = layoutData
      console.log(
        `    Warning: ${layoutKey} layout doesn't have expected structure`
      )
    }

    if (disabledCount.count > 0) {
      console.log(
        `    Disabled ${disabledCount.count} upcomingEventsCarousel widget(s) in ${layoutKey} layout`
      )
      totalDisabled += disabledCount.count
      hasChanges = true
    } else {
      console.log(
        `    No visible upcomingEventsCarousel widgets found in ${layoutKey} layout`
      )
    }
  }

  if (!hasChanges) {
    console.log(
      `  No visible upcomingEventsCarousel widgets found in section ${section.slug}`
    )
    return null
  }

  console.log(`  Total disabled in section ${section.slug}: ${totalDisabled}`)

  return {
    updateOne: {
      filter: { _id: ObjectId(section._id) },
      update: { $set: { layouts: updatedLayouts } }
    }
  }
}

// Main function
async function main() {
  let client

  try {
    console.log('Starting upcoming events carousel disabling script...')

    if (isDryRun) {
      console.log('*** DRY RUN MODE - No changes will be made ***')
    }

    console.log(`Targeting instance ID: ${instanceId}`)

    client = await MongoClient.connect(connectionUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    })

    const db = client.db()

    // Check if collections exist
    const collectionExists = async (db, collectionName) => {
      const collections = await db
        .listCollections({ name: collectionName })
        .toArray()
      return collections.length > 0
    }

    const sectionsCollectionExists = await collectionExists(db, 'section')
    const instancesCollectionExists = await collectionExists(db, 'instance')

    if (!sectionsCollectionExists) {
      console.error(
        "Error: 'section' collection does not exist in the database"
      )
      process.exit(1)
    }

    if (!instancesCollectionExists) {
      console.error(
        "Error: 'instance' collection does not exist in the database"
      )
      process.exit(1)
    }

    console.log("Collections verified: 'section' and 'instance' exist")

    // Verify the instance exists
    const instance = await db
      .collection('instance')
      .findOne({ _id: ObjectId(instanceId) })

    if (!instance) {
      console.error(`Error: Instance with ID '${instanceId}' not found`)
      process.exit(1)
    }

    console.log(`Found instance: ${instance.name}`)

    // Build query to find sections with layouts for this instance
    const query = {
      instance: instanceId,
      layouts: { $exists: true, $ne: null }
    }

    // Find sections that might have upcomingEventsCarousel widgets
    const sections = await db.collection('section').find(query).toArray()

    if (sections.length === 0) {
      console.log(
        `No sections with layouts found for instance: ${instance.name}`
      )
      return
    }

    console.log(
      `Found ${sections.length} section(s) to check for upcomingEventsCarousel widgets`
    )

    const bulkOps = []
    let sectionsWithChanges = 0

    for (const section of sections) {
      console.log(`\nChecking section: ${section.slug}`)

      const result = processSectionLayouts(section)
      if (result) {
        sectionsWithChanges++
        if (isDryRun) {
          console.log(`Would update section: ${section.slug}`)
        } else {
          bulkOps.push(result)
        }
      }
    }

    if (!isDryRun && bulkOps.length > 0) {
      console.log(`\nUpdating ${bulkOps.length} section(s)...`)
      const result = await db.collection('section').bulkWrite(bulkOps)
      console.log(`Successfully updated ${result.modifiedCount} section(s)`)
    } else if (isDryRun) {
      console.log(
        `\nDry run complete. Would have updated ${sectionsWithChanges} section(s)`
      )
    } else {
      console.log(
        'No sections required updates - no visible upcomingEventsCarousel widgets found'
      )
    }

    console.log(`\nProcessed ${sections.length} sections total`)
    console.log(
      `Sections with upcomingEventsCarousel widgets: ${sectionsWithChanges}`
    )
    console.log('\n=== SCRIPT COMPLETE ===')
  } catch (error) {
    console.error('Error during script execution:', error)
    process.exit(1)
  } finally {
    if (client) {
      await client.close()
    }
  }
}

// Run the main function
main().catch((error) => {
  console.error('Unhandled error:', error)
  process.exit(1)
})
