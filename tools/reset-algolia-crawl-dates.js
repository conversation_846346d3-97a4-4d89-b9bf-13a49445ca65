#!/usr/bin/env node

const { MongoClient } = require('mongodb')
const { join } = require('path')
const createConfigury = require('@clocklimited/configury')

const config = createConfigury(join(__dirname, '/../config.json'))(
  process.env.NODE_ENV
)

const parseArgs = () => {
  const args = process.argv.slice(2)
  let collections = [] // Options: article, company, executive
  let dryRun = false

  // eslint-disable-next-line no-console
  console.log('\n')
  // eslint-disable-next-line no-console
  console.group('Args')
  // eslint-disable-next-line no-console
  console.log('Raw args: ', args, '\n')

  args.forEach((arg) => {
    if (arg.startsWith('--collections=')) {
      collections = arg.split('=')[1].split(',')
    }
    if (arg === '--dry-run') {
      dryRun = true
    }
  })

  // eslint-disable-next-line no-console
  console.log(
    'Collections: ',
    collections.length ? collections : 'None specified'
  )
  // eslint-disable-next-line no-console
  console.log('Dry run: ', dryRun)
  // eslint-disable-next-line no-console
  console.log('\n')
  // eslint-disable-next-line no-console
  console.groupEnd()

  return { collections, dryRun }
}

async function resetCrawlDates(db, collections, dryRun) {
  // eslint-disable-next-line no-console
  console.log('🧹 Starting algoliaLastCrawlDate reset...\n')

  let totalAffected = 0

  for (const collectionName of collections) {
    try {
      // eslint-disable-next-line no-console
      console.log(`📋 Processing ${collectionName} collection...`)

      const collection = db.collection(collectionName)

      // Count documents that have algoliaLastCrawlDate
      const countWithCrawlDate = await collection.countDocuments({
        algoliaLastCrawlDate: { $exists: true }
      })

      // eslint-disable-next-line no-console
      console.log(
        `   📊 Documents with algoliaLastCrawlDate: ${countWithCrawlDate}`
      )

      if (countWithCrawlDate === 0) {
        // eslint-disable-next-line no-console
        console.log(`   ✨ No documents to reset in ${collectionName}`)
        // eslint-disable-next-line no-console
        console.log('')
        continue
      }

      if (dryRun) {
        // eslint-disable-next-line no-console
        console.log(
          `   🔍 DRY RUN: Would reset ${countWithCrawlDate} documents`
        )
        totalAffected += countWithCrawlDate
      } else {
        // Actually remove the field
        const result = await collection.updateMany(
          { algoliaLastCrawlDate: { $exists: true } },
          { $unset: { algoliaLastCrawlDate: '' } }
        )

        // eslint-disable-next-line no-console
        console.log(
          `   ✅ Reset algoliaLastCrawlDate for ${result.modifiedCount} documents`
        )
        totalAffected += result.modifiedCount
      }

      // eslint-disable-next-line no-console
      console.log('')
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`❌ Error processing ${collectionName}: ${error.message}`)
    }
  }

  // Summary
  // eslint-disable-next-line no-console
  console.log('🎯 SUMMARY:')
  if (dryRun) {
    // eslint-disable-next-line no-console
    console.log(
      `   Would reset algoliaLastCrawlDate for ${totalAffected} documents total`
    )
    // eslint-disable-next-line no-console
    console.log(`\n💡 To actually perform the reset, run without --dry-run:`)
    // eslint-disable-next-line no-console
    console.log(
      `   node tools/reset-algolia-crawl-dates.js --collections=${collections.join(
        ','
      )}`
    )
  } else {
    // eslint-disable-next-line no-console
    console.log(
      `   ✅ Reset algoliaLastCrawlDate for ${totalAffected} documents total`
    )
    // eslint-disable-next-line no-console
    console.log(
      `\n🔄 These documents will now be picked up by the next sync operation`
    )
  }
}

// Main function
async function main() {
  // eslint-disable-next-line no-console
  console.log('🚀 Algolia Crawl Date Reset Tool')
  let mongoClient
  const { collections, dryRun } = parseArgs()

  if (collections.length === 0) {
    // eslint-disable-next-line no-console
    console.error(
      '❌ No collections specified. Use --collections=article,company,executive'
    )
    process.exit(1)
  }

  try {
    // eslint-disable-next-line no-console
    console.log('🔄 Connecting to MongoDB...')
    const connectionUri =
      process.env.MONGO_URL ||
      process.env.NF_DATABASE_MONGO_SRV ||
      config.databaseUrl

    mongoClient = await MongoClient.connect(connectionUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    })

    const db = mongoClient.db()
    // eslint-disable-next-line no-console
    console.log('✅ Database connected successfully\n')

    await resetCrawlDates(db, collections, dryRun)
  } catch (error) {
    console.error('💥 Fatal Error:', error)
    process.exit(1)
  } finally {
    if (mongoClient) {
      // eslint-disable-next-line no-console
      console.log('\n🔒 Closing database connection...')
      await mongoClient.close()
      // eslint-disable-next-line no-console
      console.log('✅ Database connection closed')
    }
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { resetCrawlDates }
