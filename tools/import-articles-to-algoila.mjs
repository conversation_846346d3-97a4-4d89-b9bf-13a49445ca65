#!/usr/bin/env node
import { algoliasearch } from 'algoliasearch'
import { convert } from 'html-to-text'
import { MongoClient } from 'mongodb'
import { join } from 'path'
import createConfigury from '@clocklimited/configury'
const config = createConfigury(join(process.cwd(), '/config.json'))(
  process.env.NODE_ENV
)
const secrets = createConfigury(join(process.cwd(), '/secrets.json'))(
  process.env.NODE_ENV
)

// Sleep function for delay between requests
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

console.log(
  '🔑 Initializing Algolia client...',
  secrets.algolia.applicationId,
  secrets.algolia.importApiKey?.slice(0, 5) + '...'
)
const algoliaClient = algoliasearch(
  secrets.algolia.applicationId,
  secrets.algolia.importApiKey
)

const connectionUri =
  process.env.MONGO_URL ||
  process.env.NF_DATABASE_MONGO_SRV ||
  config.databaseUrl

// Main function
async function main() {
  let client
  console.log('🚀 Starting article import to Algolia...')

  try {
    console.log('📡 Connecting to MongoDB...')
    client = await MongoClient.connect(connectionUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    })
    console.log('✅ MongoDB connection established')

    const db = client.db()

    const batchSize = 100
    const articleCollection = await db.collection('article')
    const query = {
      state: 'Published',
      displayDate: { $gte: new Date('2024-01-01') }
    }
    const count = await articleCollection.countDocuments(query)
    const numBatches = Math.ceil(count / batchSize)
    let page = 1

    console.log(`📊 Total articles: ${count}`)
    console.log(`📦 Batch size: ${batchSize}`)
    console.log(`🔄 Total batches: ${numBatches}`)

    const importArticles = async (page) => {
      console.log(`\n🔍 Fetching batch ${page}/${numBatches}...`)
      const startTime = Date.now()

      const batch = await articleCollection
        .find(query)
        .limit(batchSize)
        .skip((page - 1) * batchSize)
        .project({
          _id: 1,
          headline: 1,
          sell: 1,
          displayDate: 1,
          __fullUrlPath: 1,
          images: 1,
          author: 1,
          legacyAuthorName: 1,
          featured: 1,
          body: 1,
          contentType: 1,
          slug: 1
        })
        .sort({ displayDate: -1 })
        .toArray()

      console.log(`📄 Processing ${batch.length} articles...`)

      const compressedBatch = batch
        .map((article) => {
          const compressedBody = article.body.widgets
            .filter((w) => w.type === 'text')
            .map((w) => convert(w.html))
            .join(' ')
          const compressedArticle = {
            _id: article._id,
            headline: article.headline,
            sell: article.sell,
            displayDate: article.displayDate,
            __fullUrlPath: article.__fullUrlPath,
            images: article.images,
            author: article.author,
            legacyAuthorName: article.legacyAuthorName,
            featured: article.featured,
            body: compressedBody,
            contentType: article.contentType,
            slug: article.slug,
            objectID: String(article._id),
            // New Fields
            __importDate: new Date()
          }
          const size = Buffer.byteLength(
            JSON.stringify(compressedArticle),
            'utf8'
          )
          if (size > 10000) {
            console.log(
              `⚠️  Further compression needed for article "${
                article.headline
              }" - size exceeds 10KB (${size - 10000} bytes too large)`
            )
            const bytesTooLarge = size - 10000
            const hyperCompressedBody = compressedArticle.body.slice(
              0,
              compressedArticle.body.length - bytesTooLarge
            )
            compressedArticle.body = hyperCompressedBody

            const newSize = Buffer.byteLength(
              JSON.stringify(compressedArticle),
              'utf8'
            )
            console.log(
              `✅ Article "${article.headline}" - size now ${newSize} bytes - ${bytesTooLarge} bytes removed`
            )

            if (newSize > 10000) {
              console.log(
                `❌ Article "${article.headline}" - size still exceeds 10KB after hyper compression - skipping`
              )
              return null
            }
            return compressedArticle
          }

          return compressedArticle
        })
        .filter(Boolean)

      console.log(
        `📥 Uploading ${compressedBatch.length} articles to Algolia...`
      )
      await algoliaClient.saveObjects({
        indexName: 'article',
        objects: compressedBatch
      })

      const duration = ((Date.now() - startTime) / 1000).toFixed(2)
      console.log(`✅ Batch ${page} completed in ${duration}s`)
      console.log(`📈 Progress: ${((page / numBatches) * 100).toFixed(1)}%`)
    }

    while (page < numBatches) {
      await importArticles(page)
      console.log(`😴 Sleeping for 1 seconds to avoid rate limits...`)
      await sleep(1000)
      page += 1
    }

    console.log('\n🎉 Import completed successfully!')
  } catch (error) {
    console.error('❌ Error occurred:', error)
    console.error('Stack trace:', error.stack)
  } finally {
    if (client) {
      console.log('👋 Closing MongoDB connection...')
      await client.close()
      console.log('✅ MongoDB connection closed')
    }
  }
}

// Run the main function
console.log('🏁 Starting import process...')
main().catch((error) => {
  console.error('💥 Fatal error:', error)
  process.exit(1)
})
