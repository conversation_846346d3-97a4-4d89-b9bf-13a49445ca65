#!/usr/bin/env node

/**
 * update-company-layouts.js
 *
 * This script updates all company, executive, and partnership section layouts
 * to a standardized structure. It skips the Procurement section as specified.
 * It will create partnership sections for instances where they don't exist yet.
 */

const { MongoClient } = require('mongodb')
const path = require('path')
const {
  createCompanyLayouts,
  createExecutiveLayouts,
  createPartnershipsLayouts
} = require('./lib/company-layout')
const createConfigury = require('@clocklimited/configury')
const { join } = require('path')
const mongodbUri = require('mongodb-uri')

// Get environment variables from .env file if available
try {
  require('dotenv').config({ path: path.resolve(__dirname, '../.env') })
} catch (e) {
  console.log('No .env file found, using environment variables')
}

const config = createConfigury(join(__dirname, '/../config.json'))(
  process.env.NODE_ENV
)

// Simplified version:
const selectedCollections =
  process.argv
    .find((a) => a.startsWith('--collections='))
    ?.replace('--collections=', '')
    .split(',')
    .filter((c) => ['company', 'executive', 'partnership'].includes(c)) || []

if (selectedCollections.length === 0) {
  console.log('No collection selected. Exiting...')
  process.exit(0)
}
console.log('Selected Collections: ', selectedCollections)

const isDryRun = process.argv.includes('--dry-run')

// Configure MongoDB connection
const connectionUri =
  process.env.MONGO_URL ||
  process.env.NF_DATABASE_MONGO_SRV ||
  config.databaseUrl

async function updateAllLayouts() {
  let client

  try {
    // Connect to MongoDB
    console.log(`Connecting to MongoDB at ${connectionUri}...`)
    client = await MongoClient.connect(connectionUri, {
      useUnifiedTopology: true
    })
    const { database } = mongodbUri.parse(connectionUri)
    const db = client.db(database)

    // Get the sections collection where all section types are stored
    const sectionsCollection = db.collection('section')

    // Corrected: collection is 'instance' (singular), not 'instances' (plural)
    const instanceCollection = db.collection('instance')

    // Check if instance collection exists
    const collectionExists = async (db, collectionName) => {
      const collections = await db
        .listCollections({ name: collectionName })
        .toArray()
      return collections.length > 0
    }

    // Verify if the instance collection exists
    const instanceCollectionExists = await collectionExists(db, 'instance')
    if (!instanceCollectionExists) {
      console.error('Error: instance collection does not exist in the database')
      process.exit(1)
    }

    // Get all instances
    const instances = await instanceCollection.find({}).toArray()
    console.log(`Found ${instances.length} instances`)

    if (instances.length === 0) {
      console.log('No instances found. Listing available collections:')
      const collections = await db.listCollections().toArray()
      console.log(collections.map((c) => c.name).join(', '))
      throw new Error(
        'No instances found in the database. Please check your database configuration.'
      )
    }

    // Stats for reporting
    let updatedCompanySections = 0
    let updatedExecutiveSections = 0
    let updatedPartnershipSections = 0
    let createdPartnershipSections = 0

    // Process each instance
    for (const instance of instances) {
      const instanceId = instance._id
      const instanceName = instance.name || instanceId.toString()
      console.log(`\nProcessing instance: ${instanceName}`)

      if (selectedCollections.includes('company')) {
        // ------------------------------
        // 1. Handle Company Sections
        // ------------------------------
        console.log(
          `Looking for company sections for instance: ${instanceName}`
        )

        // Find all company sections for this instance
        const companySections = await sectionsCollection
          .find({
            instance: instanceId.toString(),
            systemType: 'company'
          })
          .toArray()

        console.log({
          instance: instanceId.toString(),
          systemType: 'company'
        })

        console.log(`Found ${companySections.length} company sections`)

        // Generate company layouts for this instance
        const companyLayoutsForInstance = createCompanyLayouts(instance)

        // Update each company section
        for (const companySection of companySections) {
          // Skip specific sections if needed
          if (companySection.slug === 'procurement') {
            console.log(`Skipping Procurement section as requested`)
            continue
          }

          console.log(
            `Updating company section: ${
              companySection.name || companySection.slug
            }`
          )

          // Update the section with the new layouts
          if (!isDryRun) {
            await sectionsCollection.updateOne(
              { _id: companySection._id },
              {
                $set: {
                  layouts: companyLayoutsForInstance,
                  modifiedDate: new Date()
                }
              }
            )
          }

          updatedCompanySections++
        }
      }

      if (selectedCollections.includes('executive')) {
        // ------------------------------
        // 2. Handle Executive Sections
        // ------------------------------
        console.log(
          `Looking for executive sections for instance: ${instanceName}`
        )

        // Find all executive sections for this instance
        const executiveSections = await sectionsCollection
          .find({
            instance: instanceId.toString(),
            systemType: 'executive'
          })
          .toArray()

        console.log(`Found ${executiveSections.length} executive sections`)

        // Generate executive layouts for this instance
        const executiveLayoutsForInstance = createExecutiveLayouts(instance)

        // Update each executive section
        for (const executiveSection of executiveSections) {
          console.log(
            `Updating executive section: ${
              executiveSection.name || executiveSection.slug
            }`
          )

          // Update the section with the new layouts
          if (!isDryRun) {
            await sectionsCollection.updateOne(
              { _id: executiveSection._id },
              {
                $set: {
                  layouts: executiveLayoutsForInstance,
                  modifiedDate: new Date()
                }
              }
            )
          }

          updatedExecutiveSections++
        }
      }

      if (selectedCollections.includes('partnership')) {
        // ------------------------------
        // 3. Handle Partnership Sections
        // ------------------------------
        console.log(
          `Looking for partnership sections for instance: ${instanceName}`
        )

        // Find partnership section for this instance
        const partnershipSection = await sectionsCollection.findOne({
          instance: instanceId.toString(),
          systemType: 'partnership'
        })

        // Generate partnership layouts for this instance
        const partnershipLayoutsForInstance = {
          partnership: createPartnershipsLayouts(instance)
        }

        // Create partnership section if it doesn't exist
        if (!partnershipSection) {
          console.log(
            `Creating new partnership section for instance: ${instanceName}`
          )

          // Get the account ID from the instance
          const accountId = instance.account

          // Create a new section
          const newPartnershipSection = {
            root: false,
            systemType: 'partnership',
            instance: instanceId.toString(),
            name: 'Partnerships',
            slug: 'partnership',
            advertSectionId: null,
            fullUrlPath: '/partnership',
            parent: null,
            order: null,
            layouts: partnershipLayoutsForInstance,
            visible: true,
            liveDate: null,
            expiryDate: null,
            metaTitle: null,
            metaDescription: null,
            shareTitle: null,
            shareDescription: null,
            createdFromCategoryIndex: null,
            previewId: `partnership_${Math.random()
              .toString(36)
              .substring(2, 9)}`,
            account: accountId,
            createdDate: new Date(),
            modifiedDate: new Date(),
            keyValueTargeting: [],
            setArticleIdAsKeyValuePair: false,
            articleIdKeyToSet: 'partnership'
          }

          if (!isDryRun) {
            await sectionsCollection.insertOne(newPartnershipSection)
          }
          createdPartnershipSections++
        } else {
          // Update existing partnership section
          console.log(
            `Updating partnership section for instance: ${instanceName}`
          )

          if (!isDryRun) {
            await sectionsCollection.updateOne(
              { _id: partnershipSection._id },
              {
                $set: {
                  layouts: partnershipLayoutsForInstance,
                  modifiedDate: new Date()
                }
              }
            )
          }

          updatedPartnershipSections++
        }
      }
    }

    // ------------------------------
    // Print summary
    // ------------------------------
    console.log(`\n======== UPDATE SUMMARY ========`)
    console.log(`Company sections updated: ${updatedCompanySections}`)
    console.log(`Executive sections updated: ${updatedExecutiveSections}`)
    console.log(`Partnership sections updated: ${updatedPartnershipSections}`)
    console.log(`Partnership sections created: ${createdPartnershipSections}`)
    console.log(`================================`)
  } catch (error) {
    console.error('Error updating layouts:', error)
    process.exit(1)
  } finally {
    if (client) {
      await client.close()
      console.log('Database connection closed')
    }
  }
}

// Run the update function
updateAllLayouts()
  .then(() => {
    console.log('All section layouts update completed successfully')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Failed to update layouts:', error)
    process.exit(1)
  })
