// Widget factory functions for better maintainability
const hat = require('hat')
const createBaseWidget = (type, additionalProps = {}) => ({
  id: hat(),
  type,
  className: null,
  visible: true,
  liveDate: null,
  expiryDate: null,
  tiers: [],
  displayOptions: ['desktop', 'tablet', 'mobile'],
  essential: true,
  showWhenEventIs: [],
  ...additionalProps
})

const createAdvert = (
  size,
  alignment,
  displayOptions = ['desktop', 'tablet', 'mobile'],
  background = false
) =>
  createBaseWidget('advert', {
    displayOptions,
    slotName: null,
    size,
    suffix: null,
    background,
    keyValueTargeting: [],
    alignment,
    ...(alignment === 'right' && { titleColor: null, copyColor: null })
  })

const createCompanyLayouts = (instance) => ({
  company: {
    name: 'Company Layout',
    description: 'This is the layout that all company pages will use',
    isBuiltIn: true,
    type: 'company',
    key: 'company',
    layout: [
      {
        id: 0,
        title: 'Full-width',
        cols: [
          {
            id: '0:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [createBaseWidget('companyProfile')]
            }
          }
        ],
        attributes: ['full']
      },
      {
        id: 1,
        title: 'Default',
        cols: [
          {
            id: '1:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [createBaseWidget('companyGallery')]
            }
          }
        ],
        attributes: ['default']
      },
      {
        id: 2,
        title: '3:1 (Sticky)',
        cols: [
          {
            id: '2:0',
            order: 1,
            width: 3,
            attributes: [],
            widgetArea: {
              widgets: [createBaseWidget('companyLatestPosts')]
            }
          },
          {
            id: '2:1',
            order: 2,
            width: 1,
            attributes: ['fixed-width'],
            widgetArea: {
              widgets: [
                createAdvert('300x250', 'right'),
                createBaseWidget('magazineLatestCTA', { title: 'Read Now' }),
                createAdvert('300x600', 'right', ['desktop'])
              ]
            }
          }
        ],
        attributes: ['default', 'fixed-column']
      },
      {
        id: 3,
        title: '3:1',
        cols: [
          {
            id: '3:0',
            order: 1,
            width: 3,
            attributes: [],
            widgetArea: {
              widgets: [
                createBaseWidget('companyArticleSection', {
                  articleType: 'Video',
                  display: 3
                })
              ]
            }
          },
          {
            id: '3:1',
            order: 2,
            width: 1,
            attributes: ['fixed-width'],
            widgetArea: {
              widgets: [createAdvert('300x250', 'right')]
            }
          }
        ],
        attributes: ['default']
      },
      {
        id: 4,
        title: 'Full-width',
        cols: [
          {
            id: '4:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [
                createAdvert('970x250', 'centre', ['desktop'], true),
                createAdvert('320x50', 'centre', ['tablet', 'mobile'])
              ]
            }
          }
        ],
        attributes: ['full']
      },
      {
        id: 5,
        title: 'Full-width',
        cols: [
          {
            id: '5:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [
                createBaseWidget('companyArticleCarousel'),
                createBaseWidget('companyClients')
              ]
            }
          }
        ],
        attributes: ['full']
      },
      {
        id: 6,
        title: 'Full-width (Tight)',
        cols: [
          {
            id: '6:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [
                createAdvert('970x250', 'centre', ['desktop'], true),
                createAdvert('320x50', 'centre', ['tablet', 'mobile'])
              ]
            }
          }
        ],
        attributes: ['full', 'tight']
      }
    ]
  },
  contentHub: {
    name: 'Content Hub layout',
    description: null,
    isBuiltIn: true,
    type: 'contentHub',
    key: 'contentHub',
    layout: [
      {
        id: 0,
        title: 'Default',
        cols: [
          {
            id: '0:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [
                createBaseWidget('companyFeaturedArticles', {
                  skip: null,
                  display: null
                })
              ]
            }
          }
        ],
        attributes: ['default']
      },
      {
        id: 1,
        title: '3:1 (Sticky)',
        cols: [
          {
            id: '1:0',
            order: 1,
            width: 3,
            attributes: [],
            widgetArea: {
              widgets: [
                createBaseWidget('companyFeaturedArticles', {
                  skip: 1,
                  display: 4
                }),
                createBaseWidget('companyArticleSection', {
                  articleType: 'Video',
                  display: 6
                }),
                createBaseWidget('companyArticleSection', {
                  articleType: 'Article',
                  display: 3
                }),
                createBaseWidget('companyArticleSection', {
                  articleType: 'Podcast',
                  display: 3
                }),
                createBaseWidget('companyArticleSection', {
                  articleType: 'Interview',
                  display: 3
                }),
                createBaseWidget('companyArticleSection', {
                  articleType: 'Event',
                  display: 3
                }),
                createBaseWidget('companyArticleSection', {
                  articleType: 'Webinar',
                  display: 3
                }),
                createBaseWidget('companyArticleSection', {
                  articleType: 'Company Report',
                  display: 3
                }),
                createBaseWidget('companyArticleSection', {
                  articleType: 'Whitepaper',
                  display: 3
                })
              ]
            }
          },
          {
            id: '1:1',
            order: 2,
            width: 1,
            attributes: ['fixed-width'],
            widgetArea: {
              widgets: [
                createAdvert('300x250', 'right'),
                createBaseWidget('magazineLatestCTA', {
                  title: 'Read Now'
                }),
                createAdvert('300x250', 'right', ['tablet', 'mobile']),
                createAdvert('300x600', 'centre', ['desktop'])
              ]
            }
          }
        ],
        attributes: ['default', 'fixed-column']
      },
      {
        id: 2,
        title: 'Default',
        cols: [
          {
            id: '2:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [createBaseWidget('companyStats')]
            }
          }
        ],
        attributes: ['default']
      },
      {
        id: 3,
        title: 'Full-width',
        cols: [
          {
            id: '3:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [
                createAdvert('970x250', 'centre', ['desktop'], true),
                createAdvert('320x50', 'centre', ['tablet', 'mobile'])
              ]
            }
          }
        ],
        attributes: ['full']
      }
    ]
  },
  executives: {
    name: 'Executives layout',
    description: null,
    isBuiltIn: true,
    type: 'executives',
    key: 'executives',
    layout: [
      {
        id: 0,
        title: '3:1 (Sticky)',
        cols: [
          {
            id: '0:0',
            order: 1,
            width: 3,
            attributes: [],
            widgetArea: {
              widgets: [createBaseWidget('companyExecutivesAll')]
            }
          },
          {
            id: '0:1',
            order: 2,
            width: 1,
            attributes: ['fixed-width'],
            widgetArea: {
              widgets: [
                createAdvert('300x250', 'right'),
                createBaseWidget('magazineLatestCTA', { title: 'Read Now' }),
                createAdvert('300x600', 'right', ['desktop']),
                createAdvert('300x250', 'right')
              ]
            }
          }
        ],
        attributes: ['default', 'fixed-column']
      },
      {
        id: 1,
        title: 'Default',
        cols: [
          {
            id: '1:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [createBaseWidget('companyLeadership')]
            }
          }
        ],
        attributes: ['default']
      }
    ]
  },
  companyContact: {
    name: 'Get In Touch layout',
    description: null,
    isBuiltIn: true,
    type: 'companyContact',
    key: 'companyContact',
    layout: [
      {
        id: 0,
        title: 'Default',
        cols: [
          {
            id: '0:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [createBaseWidget('companyForm')]
            }
          }
        ],
        attributes: ['default']
      }
    ]
  },
  partnerships: {
    name: 'Partnerships layout',
    description: null,
    isBuiltIn: true,
    type: 'partnerships',
    key: 'partnerships',
    layout: [
      {
        id: 0,
        title: 'Full-width (Tight)',
        cols: [
          {
            id: '0:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [
                createBaseWidget('companyPortalBanner', {
                  title: `${instance.name}'s Corporate Partner Program:`,
                  subtitle: 'Amplify Your Brand. Empower Your Partners.'
                })
              ]
            }
          }
        ],
        attributes: ['full', 'tight']
      },
      {
        id: 1,
        title: 'Default',
        cols: [
          {
            id: '1:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [
                createBaseWidget('text', {
                  html: null
                }),
                createBaseWidget('text', {
                  html:
                    '<p>The&nbsp;Corporate Partner Program is a fully managed service designed to elevate your brand and provide strategic exposure for your most valued partners. Created to deliver measurable results, the program enables companies to tap into a powerful mix of co-branded content, digital advertising, and thought leadership, all delivered across&nbsp;Supply Chain Digital&#39;s&nbsp;industry-leading platform.<br />\n<br />\nEach month, participating partners benefit from premium placement across&nbsp;the digital magazine, website, newsletter, and social media platforms,&nbsp;ensuring visibility in front of engaged, decision-making audiences.</p>'
                }),
                createBaseWidget('companyPartnerships', {
                  isCarousel: false
                })
              ]
            }
          }
        ],
        attributes: ['default']
      },
      {
        id: 2,
        title: 'Full-width',
        cols: [
          {
            id: '2:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [
                createBaseWidget('companyClients', {
                  isCarousel: false
                }),
                createAdvert('970x250', 'centre', ['desktop'], true),
                createAdvert('320x50', 'centre', ['tablet', 'mobile'])
              ]
            }
          }
        ],
        attributes: ['full']
      },
      {
        id: 3,
        title: 'Default',
        cols: [
          {
            id: '3:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [createBaseWidget('companyPortalBenefits')]
            }
          }
        ],
        attributes: ['default']
      },
      {
        id: 4,
        title: 'Full-width (Tight)',
        cols: [
          {
            id: '4:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [createBaseWidget('companyCtaStrip')]
            }
          }
        ],
        attributes: ['full', 'tight']
      }
    ]
  }
})

const createExecutiveLayouts = (instance) => ({
  executive: {
    name: 'Executive Layout',
    description: 'This is the layout that all executive pages will use',
    isBuiltIn: true,
    type: 'executive',
    key: 'executive',
    layout: [
      {
        id: 0,
        title: '3:1',
        cols: [
          {
            id: '0:0',
            order: 1,
            width: 3,
            attributes: [],
            widgetArea: {
              widgets: [
                createBaseWidget('executiveProfile'),
                createBaseWidget('executiveLatestPosts', {
                  skip: 0,
                  display: 1,
                  showTitle: false
                }),
                createBaseWidget('executiveMeta')
              ]
            }
          },
          {
            id: '0:1',
            order: 2,
            width: 1,
            attributes: [],
            widgetArea: {
              widgets: [
                createAdvert('300x250', 'right'),
                createBaseWidget('magazineLatestCTA', { title: 'Read Now' }),
                createBaseWidget('companyExecutivesList'),
                createAdvert('300x600', 'right', ['desktop']),
                createAdvert('300x250', 'right')
              ]
            }
          }
        ],
        attributes: ['default']
      },
      {
        id: 1,
        title: 'Default',
        cols: [
          {
            id: '1:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [createBaseWidget('executiveGallery')]
            }
          }
        ],
        attributes: ['default']
      },
      {
        id: 2,
        title: '3:1 (Sticky)',
        cols: [
          {
            id: '2:0',
            order: 1,
            width: 3,
            attributes: [],
            widgetArea: {
              widgets: [
                createBaseWidget('executiveLatestPosts', {
                  skip: 1,
                  display: 5,
                  showTitle: true
                })
              ]
            }
          },
          {
            id: '2:1',
            order: 2,
            width: 1,
            attributes: ['fixed-width'],
            widgetArea: {
              widgets: [
                createAdvert('300x600', 'right', ['desktop']),
                createAdvert('300x250', 'right', ['tablet', 'mobile'])
              ]
            }
          }
        ],
        attributes: ['default', 'fixed-column']
      },
      {
        id: 3,
        title: 'Full-width',
        cols: [
          {
            id: '3:0',
            order: 1,
            width: 4,
            attributes: [],
            widgetArea: {
              widgets: [
                createAdvert('970x250', 'centre', ['desktop'], true),
                createAdvert('320x50', 'centre', ['tablet', 'mobile'])
              ]
            }
          }
        ],
        attributes: ['full']
      }
    ]
  }
})

const createPartnershipsLayouts = (instance) => ({
  name: 'Partnership Layout',
  description: 'This is the layout that this Partnership will have',
  isBuiltIn: true,
  type: 'partnership',
  key: 'partnership',
  layout: [
    {
      id: 0,
      title: 'Full-width',
      cols: [
        {
          id: '0:0',
          order: 1,
          width: 4,
          attributes: [],
          widgetArea: {
            widgets: [createBaseWidget('partnershipHero')]
          }
        }
      ],
      attributes: ['full']
    },
    {
      id: 1,
      title: 'Default',
      cols: [
        {
          id: '1:0',
          order: 1,
          width: 4,
          attributes: [],
          widgetArea: {
            widgets: [
              createBaseWidget('partnershipContent'),
              createBaseWidget('partnershipsFeaturedArticles', {
                skip: 0,
                display: 1
              })
            ]
          }
        }
      ],
      attributes: ['default']
    },
    {
      id: 2,
      title: '3:1 (Sticky)',
      cols: [
        {
          id: '2:0',
          order: 1,
          width: 3,
          attributes: [],
          widgetArea: {
            widgets: [
              createBaseWidget('partnershipsFeaturedArticles', {
                skip: 1,
                display: 4
              })
            ]
          }
        },
        {
          id: '2:1',
          order: 2,
          width: 1,
          attributes: ['fixed-width'],
          widgetArea: {
            widgets: [
              createAdvert('300x250', 'right'),
              createBaseWidget('magazineLatestCTA', {
                title: 'Read Now'
              })
            ]
          }
        }
      ],
      attributes: ['default', 'fixed-column']
    },
    {
      id: 3,
      title: 'Full-width',
      cols: [
        {
          id: '3:0',
          order: 1,
          width: 4,
          attributes: [],
          widgetArea: {
            widgets: [
              createAdvert('970x250', 'centre', ['desktop'], true),
              createAdvert('320x50', 'centre', ['tablet', 'mobile']),
              createBaseWidget('companyPortalBanner', {
                title: `${instance.name}'s Corporate Partner Program:`,
                subtitle: 'Amplify Your Brand. Empower Your Partners.'
              })
            ]
          }
        }
      ],
      attributes: ['full']
    },
    {
      id: 4,
      title: 'Default',
      cols: [
        {
          id: '4:0',
          order: 1,
          width: 4,
          attributes: [],
          widgetArea: {
            widgets: [
              createBaseWidget('text', {
                html:
                  '<p>The&nbsp;Corporate Partner Program is a fully managed service designed to elevate your brand and provide strategic exposure for your most valued partners. Created to deliver measurable results, the program enables companies to tap into a powerful mix of co-branded content, digital advertising, and thought leadership, all delivered across&nbsp;Procurement Magazine&rsquo;s&nbsp;industry-leading platform.<br />\n<br />\nEach month, participating partners benefit from premium placement across&nbsp;the digital magazine, website, newsletter, and social media platforms,&nbsp;ensuring visibility in front of engaged, decision-making audiences.</p>'
              }),
              createBaseWidget('companyPortalBenefits')
            ]
          }
        }
      ],
      attributes: ['default']
    }
  ]
})

module.exports = {
  createCompanyLayouts,
  createExecutiveLayouts,
  createPartnershipsLayouts
}
