//
// JCROP STYLES
// ============
// Default Jcrop styles, converted to Stylus.
//


.jcrop-holder
  direction ltr
  text-align left

// Selection Border
.jcrop-vline,
.jcrop-hline
  background-color $color--white
  background-image versionPath('/assets/img/content/j-crop.gif')
  font-size 0
  position absolute

.jcrop-vline
  height 100%
  width 1px !important //@stylint ignore

.jcrop-vline.right
  right 0

.jcrop-hline
  height 1px !important //@stylint ignore
  width 100%

.jcrop-hline.bottom
  bottom 0

// Invisible click targets
.jcrop-tracker
  height 100%
  width 100%
  // 'turn off' link highlight
  -webkit-tap-highlight-color transparent
  // disable callout, image save panel
  -webkit-touch-callout none
  // disable cut copy paste
  user-select none

.jcrop-dragger
  // Rule of thirds grid - both vertical lines using linear gradients
  &::before
    content ''
    position absolute
    top 0
    left 0
    width 100%
    height 100%
    background-image linear-gradient(to right, transparent 33.33%, white 33.33%, white calc(33.33% + 1px), transparent calc(33.33% + 1px)), linear-gradient(to right, transparent 66.66%, white 66.66%, white calc(66.66% + 1px), transparent calc(66.66% + 1px))
    pointer-events none
    z-index 250
    filter drop-shadow(0 0 1px rgba(0, 0, 0, 0.8))

  // Rule of thirds grid - both horizontal lines using linear gradients
  &::after
    content ''
    position absolute
    top 0
    left 0
    width 100%
    height 100%
    background-image linear-gradient(to bottom, transparent 33.33%, white 33.33%, white calc(33.33% + 1px), transparent calc(33.33% + 1px)), linear-gradient(to bottom, transparent 66.66%, white 66.66%, white calc(66.66% + 1px), transparent calc(66.66% + 1px))
    pointer-events none
    z-index 250
    filter drop-shadow(0 0 1px rgba(0, 0, 0, 0.8))

// Selection Handles
.jcrop-handle
  background-color $color--black
  border 1px $color--grey--light solid
  width 7px
  height 7px
  font-size 1px

.jcrop-handle.ord-n
  left 50%
  margin-left -4px
  margin-top -4px
  top 0

.jcrop-handle.ord-s
  bottom 0
  left 50%
  margin-bottom -4px
  margin-left -4px

.jcrop-handle.ord-e
  margin-right -4px
  margin-top -4px
  right 0
  top 50%

.jcrop-handle.ord-w
  left 0
  margin-left -4px
  margin-top -4px
  top 50%

.jcrop-handle.ord-nw
  left 0
  margin-left -4px
  margin-top -4px
  top 0

.jcrop-handle.ord-ne
  margin-right -4px
  margin-top -4px
  right 0
  top 0

.jcrop-handle.ord-se
  bottom 0
  margin-bottom -4px
  margin-right -4px
  right 0

.jcrop-handle.ord-sw
  bottom 0
  left 0
  margin-bottom -4px
  margin-left -4px

// Dragbars
.jcrop-dragbar.ord-n,
.jcrop-dragbar.ord-s
  height 7px
  width 100%

.jcrop-dragbar.ord-e,
.jcrop-dragbar.ord-w
  height 100%
  width 7px

.jcrop-dragbar.ord-n
  margin-top -4px

.jcrop-dragbar.ord-s
  bottom 0
  margin-bottom -4px

.jcrop-dragbar.ord-e
  margin-right -4px
  right 0

.jcrop-dragbar.ord-w
  margin-left -4px

// The 'jcrop-light' class/extension
.jcrop-light .jcrop-vline,
.jcrop-light .jcrop-hline
  background $color--white
  opacity 0.7 !important //@stylint ignore

.jcrop-light .jcrop-handle
  background-color $color--pure-black
  border-color $color--white
  border-radius 3px

// The 'jcrop-dark' class/extension
.jcrop-dark .jcrop-vline,
.jcrop-dark .jcrop-hline
  background $color--pure-black
  opacity 0.7 !important //@stylint ignore

.jcrop-dark .jcrop-handle
  background-color $color--white
  border-color $color--pure-black
  border-radius 3px

// Simple macro to turn off the antlines
.solid-line .jcrop-vline,
.solid-line .jcrop-hline
  background $color--white

// Fix for twitter bootstrap et al.
.jcrop-holder img,
img.jcrop-preview
  max-width none
