//
// FINGERPRINT STYLES
// =================
//

// Option 1: List-based
.asset-fingerprinted-list
  margin 0
  margin-bottom rem(24)
  padding 0
  list-style none
  

  &__item
    display inline-block
    background $color--whitesmoke
    border 1px solid $color--border
    border-radius $base--border-radius
    padding 0.3em 0.6em
    margin 0 0.5em 0.5em 0
    font-family $font--monospace
    font-size rem(12)
    color $color--grey--dark

// Option 2: Paragraph-based
.fingerprint
  font-family $font--monospace
  font-size rem(12)
  line-height 1.6
  background $color--whitesmoke
  border 1px solid $color--border
  border-radius $base--border-radius
  padding 0.8em 1em
  color $color--grey--dark
  word-break break-all

.fingeprinted-list-header
  display flex
  justify-content space-between
  align-items center
  padding rem(24)
  background $color--white
  border 1px solid $color--border
  border-bottom none
  border-radius $base--border-radius $base--border-radius 0 0
  cursor pointer
  transition background-color 0.2s ease

  &:hover
    background-color shade($color--white, 3%)

  h4
    margin-bottom 0
    flex-grow 1

  .js-toggle-btn
    margin-left spacing(1)
    display flex
    align-items center
    gap 0.5em //@stylint ignore

    .js-toggle-icon
      font-size 0.8em
      transition all 0.2s ease

.fingerprinted-list-content
  background $color--whitesmoke
  border 1px solid $color--border
  border-top none
  border-radius 0 0 $base--border-radius $base--border-radius
  transition all 0.3s ease
  padding rem(24)

  .fingerprinted-list-inner
    // When empty
    &:empty
      min-height 100px
      background-image linear-gradient(45deg, $color--border 25%, transparent 25%), linear-gradient(-45deg, $color--border 25%, transparent 25%)
      background-size 20px 20px
      background-color $color--white
      opacity 0.5

    // Grid layout for items
    .js-items
      > *
        animation fadeIn 0.3s ease-in-out
        padding-left 0

  
  .no-items-text
    margin-bottom 0

// Collapsed state
.fingerprinted-list--collapsed
  .fingeprinted-list-header
    border-radius $base--border-radius
    border-bottom 1px solid $color--border

@keyframes fadeIn
  from
    opacity 0
    transform translateY(10px)
  to
    opacity 1
    transform translateY(0)
