//
// ASSET SELECTOR
// ==============
// All styles which are specific to the upload, modification and display of assets.
//

// TODO: Refactor to use common colours
$asset-item--yellow-1 = #f7f7ee
$asset-item--yellow-2 = #dad9ab

.asset-file-input
  display none

.drop-overlay
  position fixed
  top 0
  left 0
  bottom 0
  right 0
  box-shadow inset 0 0 100px rgba($color--primary, 0.8)
  z-index 100
  text-align center

.asset-item
  position relative
  background-color $color--whitesmoke
  border 1px solid $color--border
  box-shadow 0 1px 2px rgba($color--black, 0.1)

.in-progress-text
.in-progress-link
  font-size 12px
  color $color--base
  margin-right 5px

.in-progress-link
  text-decoration underline
  cursor pointer

  &:hover
    color $color--default

.asset-in-progress
  border 1px solid $asset-item--yellow-2
  background-color $asset-item--yellow-1
  border-radius 5px
  padding 10px
  margin-bottom 20px
  display none
  max-height 200px
  overflow-y auto

  .in-progress-header

    h4
      float left
      margin-bottom 0

    .clear
      @extend .in-progress-link //@stylint ignore
      float right
      margin-bottom 5px

.in-progress-item
  margin-top 5px
  padding-top 5px
  border-top 1px solid darken($asset-item--yellow-1, 10%)
  clear both

  .name
  .info
  .error
  .progress-text
  .clear
    @extend .in-progress-text //@stylint ignore
    float left

  .error
    color $color--error

  .progress-text
  .clear
    float right

  .clear
    display none
    @extend .in-progress-link //@stylint ignore

  .progress
    clear both
    margin-bottom 0

.asset-picker-grid
  height 500px
  margin-bottom 20px
  overflow-y auto


.asset-picker-controls
  position absolute
  top 15px
  right 20px

  .label
    position relative
    top 2px
    margin-left 10px

  span
    position relative
    top 2px
    margin 0 10px

.asset-info
  text-align center
  margin-top 20px
  margin-bottom 20px
  border-top 1px solid $color--border
  border-bottom 1px solid $color--border
  padding 12px 0 0

.asset-preview-large
  text-align center
  background-image versionPath('/assets/img/content/checkerboard-bg.gif')
  background-repeat repeat
  box-shadow 0 1px 3px rgba($color--black, 0.5)

.image-select-preview
  margin-top 5px
  margin-bottom 5px
  box-shadow 0 0 4px rgba($color--black, 0.4)
  display flex
  justify-content center
  align-items center

  a
    display inline-block

.image-select-controls
  max-height 192px
  overflow-y auto
