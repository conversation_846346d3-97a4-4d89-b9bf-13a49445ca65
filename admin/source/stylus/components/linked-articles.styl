/* Linked Articles Section Styles - Smart Actions Inspired */
.linked-articles-section
  background $color--white
  border 1px solid $color--grey--light
  border-radius 6px
  margin-top 20px
  margin-bottom 20px
  box-shadow 0 1px 2px rgba(0, 0, 0, 0.05)

  legend
    background $color--white
    border 1px solid $color--grey--light
    border-radius 4px
    padding 6px 12px
    margin-bottom 0
    font-weight 600
    color $color--black
    font-size 13px

.linked-articles-container
  background $color--white
  border-radius 6px
  overflow hidden

// Loading state matching smart actions
.linked-articles-loading
  height 400px
  padding 32px 16px
  text-align center
  place-content center //@stylint ignore
  background $color--white

  .loading-state
    display flex
    align-items center
    justify-content center
    color $color--grey--mid
    font-size 12px

    > *
      margin-right 8px

      &:last-child
        margin-right 0

    .spinner
      width 16px
      height 16px
      border 2px solid $color--success
      border-top 2px solid $color--grey--mid
      border-radius 50%
      animation spin 1s linear infinite

    @keyframes spin
      0% { transform: rotate(0deg) }
      100% { transform: rotate(360deg) }

// Header matching smart actions popup header
.linked-articles-header
  padding 12px 16px
  border-bottom 1px solid $color--grey--light
  background $color--white

  h3
    margin 0
    margin-bottom 4px
    font-size 14px
    font-weight 600
    color $color--black

  .articles-summary
    font-size 12px
    color $color--grey--mid

// Article list matching smart actions popup content
.linked-articles-list
  max-height 400px
  overflow-y auto
  background $color--white

// Article items matching smart actions popup items
.article-item
  padding 12px 16px
  border-bottom 1px solid $color--grey--lightest
  background $color--white

  &:last-child
    border-bottom 0

  &:hover
    background $color--whitesmoke

.article-header
  display flex
  justify-content space-between
  align-items flex-start
  margin-bottom 8px

  .article-title
    font-weight 600
    color $color--black
    font-size 14px
    margin 0
    flex 1

    .article-link
      color $color--black
      text-decoration none

      &:hover
        color $color--io
        text-decoration underline

.article-meta
  display flex
  flex-wrap wrap
  margin-bottom 8px

  > *
    margin-right 4px
    margin-bottom 4px

    &:last-child
      margin-right 0

  .article-badge
    font-size 10px
    padding 2px 6px
    border-radius 10px
    font-weight 500

    &.badge--type
      background $color--grey--lightest
      color $color--io

    &.badge--state
      &.state--published
        background $color--grey--lightest
        color $color--success

      &.state--draft
        background $color--grey--lightest
        color $color--warning

      &.state--archived
        background $color--grey--lightest
        color $color--primary

.article-date
  font-size 12px
  color $color--grey--mid
  margin-bottom 8px

.article-actions
  .action-btn
    padding 6px 12px
    border-radius 4px
    font-size 12px
    font-weight 500
    cursor pointer
    border 0
    transition all 0.2s ease
    background $color--io
    color $color--white
    text-decoration none
    display inline-block

    &:hover
      background $color--primary
      color $color--white
      text-decoration none

// Empty state matching smart actions
.linked-articles-empty
  background $color--white

  .no-articles-state
    padding 32px 16px
    text-align center
    color $color--grey--mid

    .empty-icon
      font-size 48px
      color $color--border
      display block
      margin-bottom 16px

    p
      margin 8px 0
      font-size 14px

      &.help-text
        font-size 12px
        color $color--default
        font-style italic

// Responsive adjustments
@media (max-width: 768px)
  .article-header
    flex-direction column
    align-items flex-start

    > *
      margin-bottom 8px

      &:last-child
        margin-bottom 0

  .article-actions
    align-self flex-start

  .article-meta
    > *
      margin-right 6px

      &:last-child
        margin-right 0

// LinkedIn Posts specific styling (using LinkedIn Blue apparently)
.js-linkedin-posts-container
  .article-item
    border-left 3px solid #0077B5  //@stylint ignore

    .post-text
      line-height 1.4
      color $color--black

    .badge--type
      background #0077B5 //@stylint ignore
      color white
      border-radius 3px
      padding 2px 6px
      font-size 10px
      font-weight 600
