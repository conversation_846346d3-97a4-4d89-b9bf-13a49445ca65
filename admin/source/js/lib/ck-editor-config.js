module.exports = function () {
  // Use this to configure ck-editor
  return {
    extraPlugins: 'wordcount,notification',
    toolbarGroups: [
      { name: 'clipboard', groups: ['clipboard', 'undo'] },
      { name: 'editing', groups: ['find', 'selection'] },
      { name: 'links' },
      { name: 'insert', groups: ['insert'] },
      { name: 'forms' },
      { name: 'tools' },
      { name: 'document', groups: ['mode', 'document', 'doctools'] },
      { name: 'others' },
      '/',
      { name: 'basicstyles', groups: ['basicstyles', 'cleanup'] },
      {
        name: 'paragraph',
        groups: ['list', 'indent', 'blocks', 'align', 'bidi', 'paragraph']
      },
      { name: 'styles' },
      { name: 'colors' }
    ],
    format_tags: 'h1;h2;p',
    stylesSet: [
      {
        name: 'Larger Text (130%)',
        element: 'ul',
        attributes: { class: 'ckeditor-text-130' }
      },
      {
        name: 'Larger Text (120%)',
        element: 'span',
        attributes: { class: 'ckeditor-text-120' }
      },
      {
        name: 'Larger Text (110%)',
        element: 'span',
        attributes: { class: 'ckeditor-text-110' }
      },
      {
        name: 'Smaller Text (90%)',
        element: 'span',
        attributes: { class: 'ckeditor-text-90' }
      },
      {
        name: 'Smaller Text (80%)',
        element: 'span',
        attributes: { class: 'ckeditor-text-80' }
      },
      {
        name: 'Citation',
        element: 'cite',
        attributes: { class: 'ckeditor-citation' }
      }
    ],
    removeButtons: 'Image,Table',
    removeDialogTabs: 'image:advanced',
    forcePasteAsPlainText: false,
    disableNativeSpellChecker: false,
    height: 500,
    scayt_sLang: 'en_GB',
    allowedContent:
      'div h1 h2 h3 h4 h5 h6 ol p pre ul li hr strong em{text-align}; a[id,href,name,rel,target]; img[src,alt,width,height];',
    bodyClass: 'prose',
    contentsCss: [
      window.CKEDITOR.basePath + 'contents.css',
      '/assets/static-css/ck-editor-editor-styles.css'
    ],
    wordcount: {
      showCharCount: true
    },
    on: {
      instanceReady: function () {
        this.dataProcessor.htmlFilter.addRules({
          elements: {
            a(el) {
              if (!el.attributes.target) {
                el.attributes.target = '_blank'
              }
              const { rel, href, id } = el.attributes

              const isAnchorTarget = !href && id
              const isAnchorLink = href && href.startsWith('#')
              if (isAnchorTarget || isAnchorLink) {
                // This gets added by default in ckeditor-link-target-blank, but we don't want it for anchors
                delete el.attributes.target
              }
              if (!rel) {
                el.attributes.rel = 'noopener noreferrer'
              }
            }
          }
        })

        // Auto-convert double dash (--) to en dash (–) and triple dash (---) to em dash (—)
        this.on('key', function (evt) {
          if (evt.data.keyCode === 189 || evt.data.keyCode === 173) {
            // 189 is dash on most keyboards, 173 on Firefox
            const editor = this
            setTimeout(function () {
              const selection = editor.getSelection()
              if (!selection) return

              const range = selection.getRanges()[0]
              if (!range) return

              const textNode = range.startContainer
              if (textNode.type !== window.CKEDITOR.NODE_TEXT) return

              const text = textNode.getText()
              const offset = range.startOffset

              // Check if the last two characters are en dash + dash (–-)
              // This happens when user types third dash after -- was converted to –
              if (offset >= 2 && text.substring(offset - 2, offset) === '–-') {
                // Replace en dash + dash with an em dash
                const newText =
                  text.substring(0, offset - 2) + '—' + text.substring(offset)
                textNode.setText(newText)

                // Move cursor after the em dash
                range.setStart(textNode, offset - 1)
                range.setEnd(textNode, offset - 1)
                selection.selectRanges([range])
              }
              // Check if the last two characters before cursor are "--"
              else if (
                offset >= 2 &&
                text.substring(offset - 2, offset) === '--'
              ) {
                // Replace the two dashes with an en dash
                const newText =
                  text.substring(0, offset - 2) + '–' + text.substring(offset)
                textNode.setText(newText)

                // Move cursor after the en dash
                range.setStart(textNode, offset - 1)
                range.setEnd(textNode, offset - 1)
                selection.selectRanges([range])
              }
            }, 10)
          }
        })
      }
    }
  }
}
