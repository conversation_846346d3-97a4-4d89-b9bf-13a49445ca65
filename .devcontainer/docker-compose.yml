services:
  production-database:
    image: mongo:5.0.21
    pull_policy: missing
    container_name: production-database
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: production
    ports:
      - "27017:27017"
    volumes:
      - production-db-data:/data/db
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

  darkroom-production-database:
    image: mongo:5.0.21
    pull_policy: missing
    container_name: darkroom-production-database
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: darkroom
    ports:
      - "27018:27017"
    volumes:
      - darkroom-db-data:/data/db
    healthcheck:
      test: ["C<PERSON>", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

  app-builder:
    build:
      context: ..
      dockerfile: Dockerfile
      target: devcontainers
    image: bizclik-app:latest
    pull_policy: build
    command: ["sh", "-c", "/app_mounted/.devcontainer/build.sh"]
    environment:
      UNISONLOCALHOSTNAME: unison-container
    volumes:
      - app:/app
      - ../:/app_mounted
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
      - unison:/root/.unison
  
  unison-container: 
    image: bizclik-app:latest
    pull_policy: never
    command: ["sh", "-c", "/app_mounted/.devcontainer/sync.sh -watch"]
    environment:
      UNISONLOCALHOSTNAME: unison-container
    volumes:
      - app:/app
      - ../:/app_mounted
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
      - unison:/root/.unison
    depends_on:
      app-builder:
        condition: service_completed_successfully

  admin-production:
    image: bizclik-app:latest
    pull_policy: never
    command: ["sh", "-c", "./node_modules/.bin/npm-run-all --parallel dev:admin dev:admin-watch"]
    environment:
      NODE_ENV: development
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "7001:7001"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_healthy
      app-builder:
        condition: service_completed_successfully
      api-production:
        condition: service_healthy

  api-production:
    image: bizclik-app:latest
    pull_policy: never
    command: ["sh", "-c", "npm run dev:api"]
    environment:
      NODE_ENV: development
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "7002:7002"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_healthy
      app-builder:
        condition: service_completed_successfully
    healthcheck:
      test: curl --fail http://localhost:7002/_health || exit 1
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 60s

  site-production:
    image: bizclik-app:latest
    pull_policy: never
    command: ["sh", "-c", "npm run dev:site"]
    environment:
      NODE_ENV: development
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "7000:7000"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_healthy
      app-builder:
        condition: service_completed_successfully
      api-production:
        condition: service_healthy

  message-bus-production:
    image: bizclik-app:latest
    pull_policy: never
    command: ["sh", "-c", "npm run dev:message-bus"]
    environment:
      NODE_ENV: development
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_healthy
      app-builder:
        condition: service_completed_successfully
      api-production:
        condition: service_healthy

  worker-production:
    image: bizclik-app:latest
    pull_policy: never
    command: ["sh", "-c", "npm run worker"]
    environment:
      NODE_ENV: development
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "8114:8114"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_healthy
      app-builder:
        condition: service_completed_successfully
      api-production:
        condition: service_healthy

  varnish-production:
    build:
      context: ../infra/varnish
      dockerfile: Dockerfile
      args:
        NODE_ENV: development
    image: varnish:latest
    pull_policy: build
    environment:
      NODE_ENV: development
      VARNISH_SIZE: 100M
      VARNISH_HTTP_PORT: 8080
    ports:
      - "8080:8080"
      - "80:8080"
    depends_on:
      - site-production
      - api-production
      - admin-production

  darkroom-production:
    build:
      context: https://github.com/clocklimited/Darkroom-api.git
      dockerfile: Dockerfile
    image: darkroom:latest
    pull_policy: build
    environment:
      NODE_ENV: development
      DATABASE_URI: *************************************************************************************
      SALT: 53370bcf28896ade6f6d810d528ef3d4
      KEY: 717d5fe4fcf607b3ee4f7287cf6cdfec
    ports:
      - "17999:17999"
    depends_on:
      darkroom-production-database:
        condition: service_healthy

volumes:
  production-db-data:
  darkroom-db-data:
  app-dist:
  app:
  app-node_modules:
  unison:
