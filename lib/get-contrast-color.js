module.exports = getContrastColor

/**
 * Determines a suitable contrast color (black or white) for a given background color in hexadecimal format.
 *
 * @param {string} hex - The hex color code of the background, with or without a leading '#'.
 * @return {string} The hex color code ('#ffffff' or '#000000') that provides better contrast with the input color.
 */
function getContrastColor(hex) {
  // Remove '#' if present
  hex = hex.replace(/^#/, '')

  // Convert shorthand hex (e.g., #abc) to full form (#aabbcc)
  if (hex.length === 3) {
    hex = hex
      .split('')
      .map((char) => char + char)
      .join('')
  }

  // Parse r, g, b values
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)

  // Calculate relative luminance (sRGB standard)
  const luminance = [r, g, b]
    .map((v) => {
      v /= 255
      return v <= 0.03928 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4)
    })
    .reduce((acc, val, i) => acc + val * [0.2126, 0.7152, 0.0722][i], 0)

  // Compare contrast ratio with white (1.0) and black (0.0)
  const contrastWithWhite = 1.05 / (luminance + 0.05)
  const contrastWithBlack = (luminance + 0.05) / 0.05

  // Return whichever provides the better contrast
  return contrastWithWhite > contrastWithBlack ? '#ffffff' : '#000000'
}
