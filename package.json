{"name": "bizclik", "description": "BizClik Site", "organization": "clocklimited", "author": "clocklimited", "version": "1.638.0", "licence": "ISC", "private": true, "engines": {"node": "^18.15.0", "yarn": "^1.7.0"}, "scripts": {"clean": "pliers -a clean ; rm -rf dist site/static-file-map.json", "build": "NODE_OPTIONS='--max-old-space-size=4096' nave use $(cat .naverc) run-s -n build:default build-site build:app build:copy-dist-json build:copy-site build:copy-changelog", "build-all": "run-s -n build:default build-site build:app build:copy-dist-json build:copy-site build:copy-changelog", "build:default": "BABEL_ENV=browser pliers -a build && node ./tools/build-static-map site public", "build:copy-dist-json": "cp config.json secrets.json package.json dist", "build:copy-site": "cp -Rnv site/* site/.[!.]* dist/site || true", "build:app": "for i in site/server api worker lib components; do BABEL_DISABLE_CACHE=1 BABEL_ENV=server babel $i --copy-files -s --out-dir dist/$i; done", "build:copy-changelog": "cp changelog.md dist", "build-site": "export NODE_OPTIONS=--openssl-legacy-provider && next build site/", "export-site": "next export site/", "setup": "nave use $(cat .naverc) yarn install", "db:fetch": "node tools/database-fetcher.js", "db:update": "node tools/database-updater.js", "db:test": "node tools/database-update-tester.js", "check:event-pages": "node tools/check-event-pages.js", "check:event-pages:nocache": "node tools/check-event-pages.js --fingerprint", "fix:lint:js": "eslint --ext '.js,.jsx' -f unix --fix .", "fix:lint:stylelint": "stylelint \"site/src/**/*.scss\" --fix", "fix:lint": "npm-run-all fix:site:*", "qa": "npm-run-all --parallel qa:*", "qa:lint": "eslint --ext '.js,.jsx' -f unix .", "qa:stylint": "stylint admin/", "qa:stylelint": "stylelint \"site/src/**/*.scss\"", "qa:prettier": "prettier --ignore-path .es<PERSON><PERSON><PERSON> --check '**/*.{js,jsx,scss}'", "dev": "npm-run-all build:default --parallel dev:*", "dev:admin": "nodemon --watch admin admin/app.js", "dev:admin-watch": "BABEL_ENV=browser pliers -t admin/pliers.js watch", "dev:api": "BABEL_ENV=server nodemon --exec babel-node --watch api --watch components/api --watch components/service api/app.js", "dev:site": "export NODE_OPTIONS=--openssl-legacy-provider && BABEL_ENV=server nodemon --exec babel-node --watch site/server --watch components/site --watch components/service site/server/app.js", "dev:message-bus": "node message-bus/app.js", "worker": "BABEL_ENV=server nodemon --exec babel-node worker/app.js", "watch-test": "BABEL_ENV=test mocha -w --recursive --timeout 40000 -r ./test/babel-compiler.js -r jsdom-global/register $@", "run-test": "BABEL_ENV=test mocha --recursive --timeout 40000 -r ./test/babel-compiler.js -r jsdom-global/register $@", "test": "npm-run-all --parallel qa test:**", "test:site": "jest --config site/jest.config.js site/", "test:site-server": "BABEL_ENV=test mocha --full-trace --trace-deprecation -R dot --timeout 15000 -r ./test/babel-compiler components/site/**/*.test.js", "test:api": "BABEL_ENV=test mocha --exit --full-trace --trace-deprecation -R dot --timeout 15000 -r ./test/babel-compiler {components/api/**/*.test.js,api/**/*.test.js}", "test:admin": "BABEL_ENV=test mocha --full-trace --trace-deprecation -R dot --timeout 15000 {components/admin/**/*.test.js,admin/**/*.test.js}", "test:service": "BABEL_ENV=test mocha --full-trace --trace-deprecation -R dot --timeout 15000 -r ./test/babel-compiler components/service/**/*.test.js", "test:worker": "BABEL_ENV=test mocha --full-trace --trace-deprecation -R dot --timeout 15000 -r ./test/babel-compiler.js components/worker/**/*.test.js", "test-watch:site": "jest site/ --config site/jest.config.js --coverage --watch --verbose", "coverage": "jest --config site/jest.config.js --coverage", "storybook": "start-storybook -c site/.storybook -s ./site/public -p 3351", "build-storybook": "build-storybook -c site/.storybook -s ./site/public", "postbuild-storybook": "node tools/zip-client-components.js", "write-prettier": "prettier --ignore-path .es<PERSON><PERSON><PERSON> --write '**/*.{js,jsx,css,scss}'", "analyze": "ANALYZE=true yarn build", "test:globenewswire": "BABEL_ENV=test mocha --full-trace --trace-deprecation -R dot --timeout 15000 -r ./test/babel-compiler components/service/globenewswire/test/service.test.js"}, "dependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.6.4", "@babel/plugin-transform-modules-commonjs": "^7.7.0", "@babel/plugin-transform-runtime": "^7.6.2", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.4.5", "@babel/register": "^7.0.0", "@babel/runtime": "^7.6.3", "@clocklimited/cf-crud-service-api-builder": "2.1.0", "@clocklimited/cf-list-aggregator": "^2.5.0", "@clocklimited/cf-text-search": "^1.2.1", "@clocklimited/configury": "^0.4.0", "@clocklimited/darkroom-url-builder": "^1.6.1", "@clocklimited/evnet": "^1.2.0", "@clocklimited/redirect-trailing-slash": "^1.1.0", "@clocklimited/save-mongodb": "^4.0.0", "@clocklimited/schemata": "^7.0.5", "@clocklimited/validity": "^1.1.3", "@clocklimited/validity-compose": "^1.0.3", "@clocklimited/validity-email": "^1.1.0", "@clocklimited/validity-integer": "^1.2.1", "@clocklimited/validity-required": "^1.0.2", "@clocklimited/validity-url": "^1.0.1", "@clocklimited/validity-validate-if": "^1.1.1", "@clocklimited/validity-validate-if-property-equals": "0.0.2", "@clocklimited/validity-validate-if-property-set": "^0.0.1", "@clocklimited/validity-validate-if-set": "^0.0.1", "@faker-js/faker": "^8.4.1", "@formcrafts/embed": "^1.0.35", "@hookform/resolvers": "^2.8.0", "@jaames/iro": "^5.5.2", "@next/bundle-analyzer": "^9.4.4", "@pinecone-database/pinecone": "^3.0.3", "@sentry/browser": "5.20.1", "@sentry/node": "^5.20.1", "@serby/logger": "^3.1.0", "@serby/logger-sentry-processor": "^1.1.1", "@splidejs/react-splide": "^0.7.12", "@storybook/addon-a11y": "^6.0.15", "@storybook/addon-actions": "^6.0.15", "@storybook/addon-info": "^5.3.19", "@storybook/addon-toolbars": "^6.0.15", "@storybook/addon-viewport": "^6.0.15", "@storybook/addons": "^6.0.15", "@storybook/react": "^6.0.15", "@storybook/theming": "^6.0.15", "@svgr/webpack": "^5.4.0", "@testing-library/jest-dom": "^5.8.0", "@testing-library/react": "^10.4.6", "@whitespace/storybook-addon-html": "1.2.1", "@zeit/next-source-maps": "^0.0.3", "agenda": "4", "algoliasearch": "^5.37.0", "any-newer-files": "^0.0.4", "anytime": "^1.4.2", "archiver": "^5.0.0", "async": "^3.0.1", "autoprefixer": "^9.8.5", "autoprefixer-stylus": "^1.0.0", "aws-sdk": "^2.582.0", "axios": "^1.6.0", "babel-jest": "^24.9.0", "babel-loader": "^8.0.6", "babel-plugin-inline-react-svg": "^1.1.0", "babel-plugin-require-context-hook": "^1.0.0", "babel-plugin-rewire": "^1.2.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babelify": "^10.0.0", "backbone": "1.4.0", "backline-mixins": "^1.0.3", "backline-normalize": "^1.0.1", "bcryptjs": "^2.3.0", "browjadify": "^2.7.0", "browjadify-compile": "^0.1.0", "browser-sync": "^2.26.3", "browserify": "^16.2.3", "bytes": "^3.1.0", "capitalize": "^2.0.0", "cf-api": "^2.5.0", "cf-auth-middleware": "^3.1.0", "cf-base-model": "^0.0.1", "cf-base-view": "^0.1.0", "cf-crud-service-api-builder": "^1.0.1", "cf-dedupe-list-aggregator": "2.1.0", "cf-form-cancel-delegate": "^3.0.0", "cf-formatter": "^1.0.2", "cf-health-check": "^1.3.0", "cf-image-url-builder": "^2.1.0", "cf-map-form-to-object": "^1.0.0", "cf-metrics": "^3.2.0", "cf-signature": "^2.0.1", "cf-signpost": "^0.2.0", "cf-visibility-check": "^0.1.1", "chale": "^1.1.0", "cheerio": "^0.22.0", "classnames": "^2.2.6", "clean-css": "^4.2.3", "color": "^3.1.2", "component-loader": "0", "component-loader-extends": "^0.0.0", "compression": "^1.7.3", "connect-mongo": "^2.0.3", "cookie-parser": "^1.4.6", "cookies-next": "^4.1.1", "cross-fetch": "^4.0.0", "crud-service": "^1.1.2", "cssnano": "^4.1.10", "csv-writer": "^1.6.0", "csvtojson": "^2.0.10", "data-sync": "^2.0.1", "date-fns": "^2.28.0", "debug": "3", "decamelize": "^3.2.0", "deep-diff": "^1.0.2", "deep-equal": "^1.0.0", "dev-null": "^0.1.1", "doorman": "^0.0.2", "email-validator": "^2.0.4", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.1", "exorcist": "^1.0.1", "express": "^4.17.1", "express-graceful-shutdown": "^1.1.3", "express-graphql": "^0.9.0", "express-http-context": "^1.2.3", "express-server-cluster": "^3.0.0", "express-session": "^1.17.0", "fast-xml-parser": "^5.2.5", "fecha": "4.2.0", "feed": "^4.2.2", "filesize": "^6.0.1", "fingerprintjs2": "^2.1.0", "firebase": "^7.5.0", "firebase-admin": "^8.8.0", "form-serialize": "^0.7.2", "get-value": "^3.0.1", "glob": "^7.1.3", "graphql": "^14.5.8", "graphql-fields": "^2.0.3", "graphql-iso-date": "^3.6.1", "graphql-request": "^1.8.2", "graphql-scalars": "^1.24.1", "graphql-type-json": "^0.3.2", "html-minifier": "^4.0.0", "html-to-text": "^9.0.5", "i18next": "19.6.3", "icalendar": "^0.7.1", "identity-obj-proxy": "^3.0.0", "isomorphic-unfetch": "^3.0.0", "jade": "^1.11.0", "jest": "^24.9.0", "jest-watch-typeahead": "^0.6.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.11", "lodash.assign": "^4.2.0", "lodash.bindall": ">=2", "lodash.clonedeep": ">=2", "lodash.compact": ">=2", "lodash.escaperegexp": "^4.1.2", "lodash.filter": ">=2", "lodash.find": ">=2", "lodash.findindex": "^4.6.0", "lodash.groupby": "^4.6.0", "lodash.isempty": "^4.4.0", "lodash.isequal": ">=2", "lodash.max": "^4.0.1", "lodash.omit": ">=2", "lodash.pick": "^4.4.0", "lodash.pluck": ">=2", "lodash.sortby": "^4.7.0", "lodash.template": ">=2", "lodash.trunc": "^3.0.4", "lodash.uniq": ">=2", "lodash.uniqby": "^4.7.0", "lodash.uniqwith": "^4.5.0", "lodash.values": "^4.3.0", "lookup-dns-cache": "^2.1.0", "luxon": "^3.5.0", "mc-logger": "^0.0.0", "merstone": "^0.1.1", "microplugin": "^0.0.3", "mime": "^2.4.0", "mime-types": "^2.1.26", "modal": "^1.2.0", "moment": "^2.24.0", "moment-timezone": "^0.5.48", "mongodb": "^4.15.0", "morgan": "^1.9.1", "next": "^9.5.2", "next-compose-plugins": "^2.2.0", "next-images": "^1.4.0", "next-seo": "^4.7.3", "next-with-error": "^1.2.0", "nodemailer": "^6.2.1", "npm-run-all": "^4.1.5", "object-to-csv-stream": "^0.0.2", "on-headers": "^1.0.2", "openai": "^4.55.7", "p-queue": "^6.2.1", "planby": "^1.1.7", "postcss-flexbugs-fixes": "^4.2.1", "process": "0.11.9", "prop-types": "^15.7.2", "qrcode": "^1.3.3", "react": "^16.13.1", "react-a11y-dialog": "^4.2.0", "react-dfp": "^0.21.0", "react-dom": "^16.13.1", "react-dropdown-select": "^4.8.2", "react-fast-marquee": "^1.2.1", "react-flickity-component": "3.4.0", "react-google-recaptcha": "^2.1.0", "react-gtm-module": "^2.0.8", "react-helmet": "^5.2.1", "react-hook-form": "^7.15.2", "react-hook-form-schemata-resolver": "^1.0.1", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.10.3", "react-modal-video": "^2.0.1", "react-plx": "^1.3.15", "react-share": "^4.4.1", "react-spring": "^8.0.27", "react-to-print": "^2.5.1", "react-truncate": "^2.4.0", "reading-time": "^1.5.0", "regg": "^0.0.2", "request": "^2.88.0", "resize-observer-polyfill": "^1.5.1", "responsive-grid": "^1.1.0", "saml2-js": "^4.0.2", "sass": "^1.26.10", "sass-loader": "^9.0.2", "sass-true": "^5.0.0", "save": "^2.3.3", "scroll-into-view": "^1.14.2", "secure": "^0.1.3", "server-timing": "^3.3.1", "service-locator": "^1.0.0", "sifter": "^0.5.2", "sitemap": "^5.1.0", "slugg": "^1.2.1", "speakeasy": "^2.0.0", "storybook-addon-paddings": "^2.0.2", "stylelint": "^13.6.1", "stylelint-config-css-modules": "^2.2.0", "stylelint-config-prettier": "^8.0.2", "stylelint-config-standard": "^20.0.0", "stylelint-scss": "^3.18.0", "stylus": "^0.54.5", "stylus-mixins": "^0.4.0", "stylus-renderer": "^1.1.0", "supertest": "^4.0.2", "surge": "^0.21.3", "timeout-as-promise": "^1.0.0", "ua-compatible": "0", "uber-cache": "^2.2.3", "uber-memoize": "^1.1.2", "uglify-js": "^3.6.2", "universal-cookie": "^4.0.4", "useragent": "^2.3.0", "validate-prop-types": "^1.0.3", "validity-cf-image-context-selection": "^0.5.0", "validity-date-before-property": "^0.0.1", "validity-equal-field": "^0.0.1", "validity-length": "^2.0.0", "validity-number-in-range": "^0.1.0", "validity-regex-match": "^2.0.0", "validity-require-one": "^0.1.1", "validity-required-options": "^0.0.3", "validity-unique-property": "^0.2.1", "validity-url-optional-tlds": "^0.2.0", "validity-validate-if-property-in": "^1.0.1", "vanilla-tilt": "^1.8.1", "ventnor": "^0.2.0", "versionator": "^2.0.0", "voyageai": "^0.0.8", "watchify": "^3.11.0", "webpack-filter-warnings-plugin": "^1.2.1", "x-frame-options": "^1.0.0", "youtube-sr": "^4.3.12"}, "devDependencies": {"@babel/node": "^7.4.5", "assert-diff": "^2.0.3", "babel-eslint": "^10.1.0", "babel-plugin-module-resolver": "^4.1.0", "babel-plugin-transform-require-context": "^0.1.1", "database-updates": "^2.0.0", "depcheck": "^0.8.0", "ent": "^2.2.0", "eslint": "^6.5.1", "eslint-config-prettier": "6.4.0", "eslint-config-prettier-standard": "^3.0.1", "eslint-config-standard": "^14.1.0", "eslint-config-standard-jsx": "^8.1.0", "eslint-config-standard-react": "^9.2.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-node": "^10.0.0", "eslint-plugin-prettier": "3.1.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.6", "eslint-plugin-standard": "^4.0.0", "hat": "^0.0.3", "jquery": "^3.3.1", "jsdom": "^15.2.0", "jsdom-global": "^3.0.2", "jsinspect": "^0.12.4", "mocha": "^6.1.4", "mockdate": "^2.0.1", "mongodb-uri": "^0.9.7", "nock": "^10.0.6", "node-notifier": "^5.3.0", "nodemon": "^1.19.4", "npm-run-all": "^4.1.5", "pliers": "^1.2.1", "pliers-modernizr": "^1.1.0", "prettier": "^2.0.5", "rewire": "^4.0.1", "serve": "^11.2.0", "stylint": "^1.5.6"}}